package eu.untill.license.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.Fiscal;
import eu.untill.license.shared.FiscalOrganisation;

public class FiscalHandler {

	static final Logger LOG = LoggerFactory.getLogger(FiscalHandler.class);

//	private CommonHelper commonHelper;
//	private EmailHelper emailHelper;
//	private FiscalHelper fiscalHelper;
//
//	public FiscalHandler(CommonHelper commonHelper, EmailHelper emailHelper, FiscalHelper fiscalHelper) {
//		this.commonHelper = commonHelper;
//		this.emailHelper = emailHelper;
//		this.fiscalHelper = fiscalHelper;
//	}
//
	public List<FiscalOrganisation> getFiscalOrganisations(Connection conn, Dealer dealer, long dealerId) throws SQLException {
		String sql = "SELECT * FROM LICENSES WHERE ID_DEALERS = ? AND EXTRA_DATA IS NOT NULL";
		Map<String, FiscalOrganisation> fiscalOrganisationsById = new HashMap<>();

		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, dealerId);
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					DbLicense license = Common.getDbLicenseFromResultSet(rs);
					Fiscal fiscal = license.getFiscal();
					if (fiscal != null && fiscal.getOrganisationId() != null) {
						String orgIdLowerCase = fiscal.getOrganisationId().toLowerCase();
						if (fiscalOrganisationsById.containsKey(orgIdLowerCase)) {
							FiscalOrganisation fiscalOrganisation = fiscalOrganisationsById.get(orgIdLowerCase);
							if (fiscal.getStoreId() != null) {
								fiscalOrganisation.getStoreNumbers().add(fiscal.getStoreNumber());
							}
						} else {
							FiscalOrganisation fiscalOrganisation = new FiscalOrganisation();
							fiscalOrganisation.setVatNumber(fiscal.getVatNumber());
							fiscalOrganisation.setBusinessIdentificationNumber(fiscal.getBusinessIdentificationNumber());
							fiscalOrganisation.setFiscalCountry(fiscal.getFiscalCountry());
							fiscalOrganisation.setFiscalYearStartMonth(fiscal.getFiscalYearStartMonth());
							fiscalOrganisation.setOrganisationCaption(fiscal.getOrganisationCaption());
							fiscalOrganisation.setOrganisationAddress(fiscal.getOrganisationAddress());
							fiscalOrganisation.setOrganisationId(fiscal.getOrganisationId());
							fiscalOrganisation.setStoreNumbers(new ArrayList<>());
							if (fiscal.getStoreId() != null) {
								fiscalOrganisation.getStoreNumbers().add(fiscal.getStoreNumber());
							}
							fiscalOrganisationsById.put(orgIdLowerCase, fiscalOrganisation);
						}
					}
				}
			}
		}

		List<FiscalOrganisation> result = new ArrayList<>(fiscalOrganisationsById.values());
		result.sort((a, b) -> a.getOrganisationCaption().compareToIgnoreCase(b.getOrganisationCaption()));
		return result;
	}

}
