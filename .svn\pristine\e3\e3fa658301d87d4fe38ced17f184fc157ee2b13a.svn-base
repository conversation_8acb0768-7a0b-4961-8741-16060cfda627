/**
 *
 */
package eu.untill.license.server;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Properties;
import java.util.Set;
import java.util.prefs.Preferences;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.servlet.ServletContext;

import org.ini4j.Wini;


/**
 * <AUTHOR>
 * Parameters obtain in such order:
 *  from a context
 *  from the register
 *  from .properties file (it is possible to adjust through unTill License Manager),
 *  from default value
 */
public class Settings {

	public final static String prefsNode = "eu/untill/license/server";
	public final static String workFileAttribute = "javax.servlet.context.tempdir";
	public final static String propsFileName = "settings.properties";

	public final static int DEFAULT_AUTO_PROLONG_FROM_DAY = 16;
	public final static int DEFAULT_AUTO_PROLONG_DAYS_BEFORE_EXPIRED = 14; // 2 weeks

	private String buildInTemplatesPath;

	private String propsPath = null;

	private String errorMessage = null;
	private String errorParameter = null;

	private static class Parameter {
		public Parameter(String description) { this.description = description; }
		public Parameter(String description, boolean required) { this.description = description; this.required = required; }
		public Parameter(String description, String defaultValue) { this.defaultValue = defaultValue; this.description = description; }
		public Parameter(String description, boolean required, String defaultValue) { this.defaultValue = defaultValue; this.required = required; this.description = description; }
		public String description;
		public boolean required = false;
		public String defaultValue = null;
		public String preferencesValue = null;
		public String contextValue = null;
		public String value = null;
	}

	private final LinkedHashMap<String, Parameter> parameters = new LinkedHashMap<String, Parameter>();
	{
		parameters.put("AdminPasswordHash", new Parameter("Admin password", "030458B2D54EB130C34F37823AA6DF98"));

		parameters.put("JDBCDriverClassName", new Parameter("JDBC driver class name", "org.firebirdsql.jdbc.FBDriver"));
		parameters.put("JDBCUrl", new Parameter("JDBC URL", true, "jdbc:firebirdsql:localhost/3050:C:\\unTill\\DB\\LICENSES.fdb"));
		parameters.put("JDBCUsername", new Parameter("JDBC username", "untilluser"));
		parameters.put("JDBCPassword", new Parameter("JDBC password", "1945"));
		parameters.put("UntillPath", new Parameter("Untill path", true, "C:\\unTill\\unTill.exe"));
		parameters.put("UntillDb", new Parameter("Untill database file name", true, "LICENSES.fdb"));
		parameters.put("EmailHostName", new Parameter("SMTP server address", true));
		parameters.put("EmailSmtpPort", new Parameter("SMTP server port number"));
		parameters.put("EmailSecurity", new Parameter("SMTP: use secure connection (SSL, TLS)"));
		parameters.put("EmailFromEmail", new Parameter("Server e-mail address", true));
		parameters.put("EmailFromName", new Parameter("Server e-mail name", "unTill License Server"));
		parameters.put("EmailAuthUserName", new Parameter("Username for the SMTP server"));
		parameters.put("EmailAuthPassword", new Parameter("Password for the SMTP server"));
//		parameters.put("EmailToDealerSubjectTemplate", new Parameter("Dealer e-mail subject template", "unTill license file"));
//		parameters.put("EmailToDealerTextMsgTemplate", new Parameter("Dealer e-mail text message template", "Thank you for purchasing our program. Load this license file from registration window"));
		//"Thank you for purchasing our program. Load this license file from registration window\ndealer.name = {0}\nlicense.issueDate={1}\nlicense.expiredDate={2}\nlicense.clientName={3}\nlicense.clientAddress={4}\nlicense.clientEmail={5}\nlicense.clientPhone={6}\nlicense.hardCode={7}"
//		parameters.put("EmailToDealerHtmlMsgTemplate", new Parameter("Dealer e-mail html message template", "Thank you for purchasing our program. Load this license file from registration window</html>"));
		parameters.put("OwnerEmail", new Parameter("E-mail address of owner", true));
		parameters.put("OwnerName", new Parameter("Owner name", false));
//		parameters.put("EmailToOwnerSubjectTemplate", new Parameter("Owner e-mail subject template", "unTill license file sent to dealer {0}"));
//		parameters.put("EmailToOwnerTextMsgTemplate", new Parameter("Owner e-mail text message template", "License for client ({3}) sent to dealer ({0})"));
//		new Parameter("EmailToOwnerHtmlMsgTemplate", "Owner e-mail html message template", ""),
		parameters.put("TemplatesPath", new Parameter("Folder containing user-defined templates"));
		parameters.put("InvoicesPath", new Parameter("Folder for generated invoices"));
		parameters.put("InvoiceGeneratorPeriod",
				new Parameter("Period between invoice generation (in sec.)", "60"));
		parameters.put("NextInvoiceNumber", new Parameter("Next positive invoice number", "1"));
		parameters.put("NextNegativeInvoiceNumber", new Parameter("Next negative invoice number", "1"));
		parameters.put("ManualArticlesDepartment", new Parameter("Manual article department", "Licenses"));
		parameters.put("EventsStorageJdbcDriver",
				new Parameter("Events Storage JDBC driver class name", "com.microsoft.sqlserver.jdbc.SQLServerDriver"));
		parameters.put("EventsStorageJdbcUrl", new Parameter("Events Storage JDBC URL"));
		parameters.put("EventsStorageJdbcUsername", new Parameter("Events Storage JDBC username"));
		parameters.put("EventsStorageJdbcPassword", new Parameter("Events Storage JDBC password"));
		parameters.put("EventsStorageSyncPeriod",
				new Parameter("Period sync with Events Storage (in hours)", "24"));
		parameters.put("EventsStorageLastSyncData",
				new Parameter("Data of last success sync with Events Storage", null));
		parameters.put("EnablePermanentJLoggingForCountries",
				new Parameter("Enable Permanent JLogging for Countries (regexp)", "France|Germany"));
		parameters.put("FrenchFdmForCountries",
				new Parameter("French FDM for Countries (regexp)", "France"));
		parameters.put("DefaultOnlineForDealers",
				new Parameter("Default create Online license for Dealers (regexp)", ".*"));
		parameters.put("KeysDir", new Parameter("Directory of public and private keys", true));
		parameters.put("ServletUrl", new Parameter("Servlet URL", null));
		parameters.put("MaxDefinitiveMaxVersion", new Parameter("Maximum available version for Definitive license", "115"));

		parameters.put("HwmEnabledTasks", new Parameter("Hosted WM: enabled tasks (prepare, order, send)", ""));
		parameters.put("HwmNextMonthForInvoiceGeneration", new Parameter("Hosted WM: next month for invoice generation", "2017-01"));
		parameters.put("HwmHqPriceFormula", new Parameter("Hosted WM: (Full) price formula (quantity)",
				"base = 20\n"
				+ "x = 20\n"
				+ "purchasePrice = 700 / 80\n"
				+ "minMargin = .30\n"
				+ "minPriceRoundedExtraLocation = Math.ceil(purchasePrice * (1 + minMargin) * 2) / 2\n"
				+ "\n"
				+ "function price(quantity) {\n"
				+ "    if (quantity <= 1) return base\n"
				+ "    D = 1 - quantity / x\n"
				+ "    addForTheLocation = base * D\n"
				+ "    rounded = Math.max(addForTheLocation, minPriceRoundedExtraLocation)\n"
				+ "    return rounded + price(quantity - 1)\n"
				+ "}\n"
				+ "\n"
				+ "price(quantity) / quantity\n"
		));
		parameters.put("HwmCentralBoPriceFormula", new Parameter("Hosted WM: (Back Office only) price formula (quantity)",
				"base = 5\n"
				+ "x = 20\n"
				+ "purchasePrice = 700 / 80 / 3\n"
				+ "minMargin = .35\n"
				+ "minPriceRoundedExtraLocation = Math.ceil(purchasePrice * (1 + minMargin) * 2) / 2\n"
				+ "\n"
				+ "function price(quantity) {\n"
				+ "    if (quantity <= 1) return base\n"
				+ "    D = 1 - quantity / x\n"
				+ "    addForTheLocation = base * D\n"
				+ "    rounded = Math.max(addForTheLocation, minPriceRoundedExtraLocation)\n"
				+ "    return rounded + price(quantity - 1)\n"
				+ "}\n"
				+ "\n"
				+ "price(quantity) / quantity\n"
		));

		// TODO: AddDiscount, AddNegativeArticle, reopenBill
		parameters.put("SalesArea", new Parameter("Default Sales Area", null));
		parameters.put("SalesAreaForLA", new Parameter("Default Sales Area for Large Accounts", null));

		parameters.put("RetailForceApiBasePath", new Parameter("RetailForce API base path", "https://api.retailforce.cloud"));
		parameters.put("RetailForceApiKey", new Parameter("RetailForce API key", true));
		parameters.put("RetailForceApiSecret", new Parameter("RetailForce API secret", true));
		parameters.put("RetailForceDistributorId", new Parameter("RetailForce Distributor UUID", true));
		parameters.put("RetailForceConfigurationId", new Parameter("RetailForce Configuration UUID", true));
		parameters.put("RetailForceTest", new Parameter("RetailForce Test terminals", "true"));

	}

	//public Settings(ServletConfig context) {
	public Settings(ServletContext context) {

		// Determinate default values for TemplatesPath and InvoicesPath parameters
		String defaultTemplatesPath = new File((File) context.getAttribute(workFileAttribute),
				"templates").getPath();
		getParameter("TemplatesPath").defaultValue = defaultTemplatesPath;
		String defaultInvoicesPath = new File((File) context.getAttribute(workFileAttribute),
				"invoices").getPath();
		getParameter("InvoicesPath").defaultValue = defaultInvoicesPath;

		// Determinate build in templates path
		buildInTemplatesPath = context.getRealPath("/WEB-INF/templates");

		// Get settings from context
		for (String parameterName : parameters.keySet()) {
			parameters.get(parameterName).contextValue = context.getInitParameter(parameterName);
		}

		// Get preferences node
		Preferences prefs = Preferences.systemRoot().node(prefsNode);

		// Load preferences settings
		for (String parameterName : parameters.keySet()) {
			parameters.get(parameterName).preferencesValue = prefs.get(parameterName, null);
		}

		// Determine properties file name and store it
		File propsFile = new File((File) context.getAttribute(workFileAttribute), propsFileName);
		propsPath = propsFile.getPath();

		// Load settings from .properties file
		loadFromProps();
	}

	  ////////////
	 // Public //
	////////////

	public boolean verify() {
		this.errorMessage = null;

		for (String parameterName : parameters.keySet()) {
			Parameter parameter = parameters.get(parameterName);
			if (parameter.required
					&& parameter.value == null
					&& parameter.contextValue == null
					&& parameter.preferencesValue == null) {
				this.errorMessage = "Required parameter (" + parameterName + ") is not filled";
				this.errorParameter = parameterName;
				break;
			}
		}

		// TODO Verify settings and set error message

		// Check formulas
		for (String parameterName : parameters.keySet()) {
			if (parameterName.toLowerCase().contains("formula")) {
				Parameter parameter = parameters.get(parameterName);
				String formula = get(parameterName);
				if (formula != null && !formula.equals(parameter.defaultValue)) {
					try {
						ScriptEngineManager manager = new ScriptEngineManager();
						ScriptEngine engine = manager.getEngineByName("js");
						double prevPrice = 0;
						for (int quantity = 1; quantity < 100; ++quantity) {
							engine.put("quantity", quantity);
							double price = (double) engine.eval(formula);
							if (quantity > 1 && prevPrice < price) {
								this.errorMessage = String.format("Parameter '%s' is incorrect: price must decrease when"
										+ " quantity increases (now quantity changes from %d to %d, but price increases"
										+ " from %.2f to %.2f)", parameter.description, quantity - 1, quantity, prevPrice, price);
								this.errorParameter = parameterName;
								break;
							}
							if (price < 0) {
								this.errorMessage = String.format("Parameter '%s' is incorrect: price is negative (%.2f)"
										+ " for quantity = %d", parameter.description, price, quantity);
								this.errorParameter = parameterName;
								break;
							}
							prevPrice = price;
						}
					} catch (ScriptException | ClassCastException e) {
						this.errorMessage = String.format("Parameter '%s' is incorrect: %s", parameter.description,
								e.getMessage());
						this.errorParameter = parameterName;
					}
				}
			}
		}

		return this.errorMessage == null;
	}

	public String getErrorMessage() {
		return this.errorMessage;
	}

	public String getErrorParameter() {
		return this.errorParameter;
	}

	public String get(String parameterName) {
		Parameter parameter = this.getParameter(parameterName);
		return parameter.value != null ? parameter.value :
			parameter.contextValue != null ? parameter.contextValue :
				parameter.preferencesValue != null ? parameter.preferencesValue :
					parameter.defaultValue;
	}

	public Set<String> getParameterNames() {
		return parameters.keySet();
	}

	public String getUserDefinedValue(String parameterName) {
		Parameter parameter = this.getParameter(parameterName);
		return parameter.value;
	}

	public String getDescription(String parameterName) {
		Parameter parameter = this.getParameter(parameterName);
		return parameter.description;
	}

	public Boolean getRequiredFlag(String parameterName) {
		Parameter parameter = this.getParameter(parameterName);
		return parameter.required;
	}

	public void set(String parameterName, String parameterValue) {
		Parameter parameter = this.getParameter(parameterName);
		parameter.value = parameterValue;
	}

	public void save() {
		storeToProps();
	}

	  /////////////
	 // Private //
	/////////////

	/**
	 *  Load settings from .properties file
	 */
	private void loadFromProps() {
		FileInputStream propsFIS;
		try {
			propsFIS = new FileInputStream(propsPath);
			try {
				Properties props = new Properties();
				props.load(propsFIS);
				// Load properties settings
				for (String parameterName : parameters.keySet()) {
					parameters.get(parameterName).value = props.getProperty(parameterName);
				}
			} finally {
				propsFIS.close();
			}
		} catch (FileNotFoundException e) {
			// do nothing
		} catch (IOException e) {
			// TODO log this event
			throw new RuntimeException("Settings error: load properties file error", e);
		}
	}

	/**
	 *  Store settings to .properties file
	 */
	private void storeToProps() {
		FileOutputStream propsFIS;
		try {
			propsFIS = new FileOutputStream(propsPath);
			try {
				Properties props = new Properties();
				// Put settings
				for (String parameterName : parameters.keySet()) {
					Parameter parameter = parameters.get(parameterName);
					if (parameter.value != null)
						props.put(parameterName, parameter.value);
				}
				props.store(propsFIS, null);
			} finally {
				propsFIS.close();
			}
		} catch (FileNotFoundException e) {
			// TODO log this event
			throw new RuntimeException("Settings error: create properties file error", e);
		} catch (IOException e) {
			// TODO log this event
			throw new RuntimeException("Settings error: store properties file error", e);
		}
	}

	private Parameter getParameter(String parameterName) {
		Parameter parameter = parameters.get(parameterName);
		if (parameter == null) {
			// TODO log this event
			throw new RuntimeException("Settings error: parameter with name: \"" + parameterName + "\" is not valid!");
		}
		return parameter;
	}

	  /////////////
	 // Getters //
	/////////////

	public String getJDBCDriverClassName() {
		return this.get("JDBCDriverClassName");
	}

	public String getJDBCUrl() {
		return this.get("JDBCUrl");
	}

	public String getJDBCUsername() {
		return this.get("JDBCUsername");
	}

	public String getJDBCPassword() {
		return this.get("JDBCPassword");
	}

	public String getUntillPath() {
		return this.get("UntillPath");
	}

	public String getUntillDb() {
		return this.get("UntillDb");
	}

	public String getEmailHostName() {
		return this.get("EmailHostName");
	}

	public String getEmailSmtpPort() {
		return this.get("EmailSmtpPort");
	}

	public String getEmailSecurity() {
		return this.get("EmailSecurity");
	}

	public String getEmailFromEmail() {
		return this.get("EmailFromEmail");
	}

	public String getEmailFromName() {
		return this.get("EmailFromName");
	}

	public String getEmailAuthUserName() {
		return this.get("EmailAuthUserName");
	}

	public String getEmailAuthPassword() {
		return this.get("EmailAuthPassword");
	}

	public String getEmailToDealerSubjectTemplate() {
		return this.get("EmailToDealerSubjectTemplate");
	}

	public String getEmailToDealerTextMsgTemplate() {
		return this.get("EmailToDealerTextMsgTemplate");
	}

	public String getEmailToDealerHtmlMsgTemplate() {
		return this.get("EmailToDealerHtmlMsgTemplate");
	}

	public String getOwnerEmail() {
		return this.get("OwnerEmail");
	}

	public String getOwnerName() {
		return this.get("OwnerName");
	}

	public String getEmailToOwnerSubjectTemplate() {
		return this.get("EmailToOwnerSubjectTemplate");
	}

	public String getEmailToOwnerTextMsgTemplate() {
		return this.get("EmailToOwnerTextMsgTemplate");
	}

	public String getEmailToOwnerHtmlMsgTemplate() {
		return this.get("EmailToOwnerHtmlMsgTemplate");
	}

	public String getAdminPasswordHash() {
		return this.get("AdminPasswordHash");
	}

	public String getInvoicesPath() {
		return this.get("InvoicesPath");
	}

	public File getTemplate(String templateName) {
		File templateFile = new File(this.get("TemplatesPath"), templateName);
		if (templateFile.exists())
			return templateFile;
		templateFile = new File(buildInTemplatesPath, templateName);
		return templateFile;
	}

	public long getInvoiceGeneratorPeriod() {
		return Long.valueOf(this.get("InvoiceGeneratorPeriod"));
	}

	public long getNextInvoiceNumber() {
		return Long.valueOf(this.get("NextInvoiceNumber"));
	}

	public void setNextInvoiceNumber(long value) {
		this.set("NextInvoiceNumber", Long.toString(value));
	}

	public long getNextNegativeInvoiceNumber() {
		return Long.valueOf(this.get("NextNegativeInvoiceNumber"));
	}

	public void setNextNegativeInvoiceNumber(long value) {
		this.set("NextNegativeInvoiceNumber", Long.toString(value));
	}

	public String getManualArticlesDepartment() {
		return this.get("ManualArticlesDepartment");
	}

	public void setManualArticlesDepartment(String value) {
		this.set("ManualArticlesDepartment", value);
	}

	public String getEventsStorageJdbcDriver() {
		return this.get("EventsStorageJdbcDriver");
	}

	public String getEventsStorageJdbcUrl() {
		return this.get("EventsStorageJdbcUrl");
	}

	public String getEventsStorageJdbcUsername() {
		return this.get("EventsStorageJdbcUsername");
	}

	public String getEventsStorageJdbcPassword() {
		return this.get("EventsStorageJdbcPassword");
	}

	public double getEventsStorageSyncPeriod() {
		return Double.valueOf(this.get("EventsStorageSyncPeriod"));
	}

	public String getEventsStorageLastSyncData() {
		return this.get("EventsStorageLastSyncData");
	}

	public void setEventsStorageLastSyncData(String value) {
		this.set("EventsStorageLastSyncData", value);
	}

	public String getEnablePermanentJLoggingForCountries() {
		return this.get("EnablePermanentJLoggingForCountries");
	}

	public String getFrenchFdmForCountries() {
		return this.get("FrenchFdmForCountries");
	}

	public String getDefaultOnlineForDealers() {
		return this.get("DefaultOnlineForDealers");
	}

	public String getKeysDir() {
		return this.get("KeysDir");
	}

	public String getServletUrl() {
		return this.get("ServletUrl");
	}

	public int getMaxDefinitiveMaxVersion() {
		return Integer.valueOf(this.get("MaxDefinitiveMaxVersion"));
	}

	public String getHwmEnabledTasks() {
		return this.get("HwmEnabledTasks");
	}

	public String getHwmNextMonthForInvoiceGeneration() {
		return this.get("HwmNextMonthForInvoiceGeneration");
	}

	public void setHwmNextMonthForInvoiceGeneration(String value) {
		this.set("HwmNextMonthForInvoiceGeneration", value);
	}

	public String getHwmHqPriceFormula() {
		return this.get("HwmHqPriceFormula");
	}

	public String getHwmCentralBoPriceFormula() {
		return this.get("HwmCentralBoPriceFormula");
	}

	public String getSalesArea() {
		return this.get("SalesArea");
	}

	public String getSalesAreaForLA() {
		return this.get("SalesAreaForLA");
	}

	public String getRetailForceApiBasePath() {
		return this.get("RetailForceApiBasePath");
	}

	public String getRetailForceApiKey() {
		return this.get("RetailForceApiKey");
	}

	public String getRetailForceApiSecret() {
		return this.get("RetailForceApiSecret");
	}

	public String getRetailForceDistributorId() {
		return this.get("RetailForceDistributorId");
	}

	public String getRetailForceConfigurationId() {
		return this.get("RetailForceConfigurationId");
	}

	public boolean getRetailForceTest() {
		return Boolean.parseBoolean(this.get("RetailForceTest"));
	}

	private Integer tpapiSoapPort = null;

	/**
	 *
	 * @return TPAPI port number or null
	 * @throws IOException
	 */
	public Integer getTpapiSoapPort() throws IOException {
		if (tpapiSoapPort != null)
			return tpapiSoapPort;

		File untillDir = new File(getUntillPath()).getParentFile();
		if (untillDir == null)
			return null;
		File untillIniFile = new File(untillDir, "Untill.ini");
		if (!untillIniFile.exists())
			return null;

		Wini ini = new Wini(untillIniFile);

		Integer port = ini.get("common", "port", Integer.class);
		if (port == null)
			port = 3060;

		tpapiSoapPort = port + 3;

		return tpapiSoapPort;
	}

	public int getAutoProlongFromDay() {
		return DEFAULT_AUTO_PROLONG_FROM_DAY;
	}

	public int getAutoProlongDaysBeforeExpired() {
		return DEFAULT_AUTO_PROLONG_DAYS_BEFORE_EXPIRED;
	}

	public long getDefaultSalesAreaId() {
		// TODO make configurable (0 - any)
		return 0;
	}

}
