/**
 * 
 */
package eu.untill.license.client;

import com.google.gwt.i18n.client.Messages;

import eu.untill.license.client.ui.AddCreditDialog;
import eu.untill.license.client.ui.AddDiscountDialog;
import eu.untill.license.client.ui.AddHandlingFeeDialog;
import eu.untill.license.client.ui.AddManualArticleDialog;
import eu.untill.license.client.ui.AddNegativeArticleDialog;
import eu.untill.license.client.ui.ApostillHddCodesDialog;
import eu.untill.license.client.ui.ApproveLicenseDialog;
import eu.untill.license.client.ui.CreateManualInvoiceDialog;
import eu.untill.license.client.ui.ErrorDialog;
import eu.untill.license.client.ui.FiscalDialog;
import eu.untill.license.client.ui.LicenseDetailsDialog;
import eu.untill.license.client.ui.LicenseDialog;
import eu.untill.license.client.ui.LicenseListWidget;
import eu.untill.license.client.ui.LoginWidget;
import eu.untill.license.client.ui.ResendLicenseDialog;
import eu.untill.license.client.ui.SuTaskDialog;
import eu.untill.license.client.ui.WaitDialog;

/**
 * <AUTHOR>
 *
 */
public interface UntillLicenseServerMessages extends Messages,
		LoginWidget.UlsMessages,
		LicenseListWidget.UlsMessages,
		LicenseDialog.UlsMessages,
		ErrorDialog.UlsMessages,
		ApostillHddCodesDialog.UlsMessages,
		WaitDialog.UlsMessages,
		ApproveLicenseDialog.UlsMessages,
		AddDiscountDialog.UlsMessages,
		AddNegativeArticleDialog.UlsMessages,
		ResendLicenseDialog.UlsMessages,
		LicenseDetailsDialog.UlsMessages,
		CreateManualInvoiceDialog.UlsMessages,
		AddCreditDialog.UlsMessages,
		AddManualArticleDialog.UlsMessages,
		SuTaskDialog.UlsMessages,
		AddHandlingFeeDialog.UlsMessages,
		FiscalDialog.UlsMessages
{

}
