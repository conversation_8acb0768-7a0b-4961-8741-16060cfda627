package eu.untill.license.server;

import java.io.File;
import java.io.UnsupportedEncodingException;

public interface UntillLicensesPluginHelper {

	File generateLicenseFile(long licenseId, int licenseVersion, boolean temp) throws Exception;

	boolean voidOrder(int tableNo);

	boolean apostillGenerateHardCode(long apostillHddCodeId);

	boolean licensesPluginAddDiscount(int tableNo, double price, String text)
			throws UnsupportedEncodingException;
	boolean licensesPluginAddNegativeArticle(int tableNo, long articleId, int quantity, String text)
			throws UnsupportedEncodingException;
	boolean reopenBill(int pbillNumber, String pbillSuffix) throws UnsupportedEncodingException;

}
