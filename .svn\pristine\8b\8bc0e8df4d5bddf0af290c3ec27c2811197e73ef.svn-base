/**
 * 
 */
package eu.untill.license.client;

import com.google.gwt.resources.client.ClientBundle;
import com.google.gwt.resources.client.ImageResource;

//import eu.untill.license.client.ui.MessageDialog;
//import eu.untill.license.client.ui.ErrorDialog;

/**
 * <AUTHOR>
 *
 */
public interface UntillLicenseServerImages extends ClientBundle
//		,MessageDialog.UlsImages
//		,ErrorDialog.UlsImages
{
	// ...
	@Source("eu/untill/license/client/images/logo.png")
	ImageResource logo();

	// MessageDialog
	@Source("eu/untill/license/client/images/information.png")
	ImageResource information();
	@Source("eu/untill/license/client/images/question.png")
	ImageResource question();
	@Source("eu/untill/license/client/images/warning.png")
	ImageResource warning();
	@Source("eu/untill/license/client/images/exclamation.png")
	ImageResource exclamation();

	// ErrorDialog
	@Source("eu/untill/license/client/images/error.png")
	ImageResource error();

	// LicenseListPage
	@Source("eu/untill/license/client/images/locale.png")
	ImageResource locale();

	@Source("eu/untill/license/client/images/buttons/Create new license.png")
	ImageResource blank();
	@Source("eu/untill/license/client/images/buttons/Refresh.png")
	ImageResource refresh();
	@Source("eu/untill/license/client/images/buttons/Add Apostill codes.png")
	ImageResource add_apostill_hdd_codes();
	@Source("eu/untill/license/client/images/buttons/Create Invoice.png")
	ImageResource create_manual_invoice();
	@Source("eu/untill/license/client/images/buttons/Show name changes.png")
	ImageResource show_client_name_changes();
	@Source("eu/untill/license/client/images/buttons/Hosted WM report.png")
	ImageResource hwm_report();
	@Source("eu/untill/license/client/images/buttons/Actual prices.png")
	ImageResource actual_prices();

	// LicenseDetailsPage status: Apostill License, Apostill Trial License, SaaS License, Boost License, Demo License, Online License, Common License
    @Source("eu/untill/license/client/images/status/SaaS License.png")
    ImageResource status_saas();
    @Source("eu/untill/license/client/images/status/SaaS License (Expired).png")
    ImageResource status_saas_expired();
    @Source("eu/untill/license/client/images/status/SaaS License (Nearly expired).png")
    ImageResource status_saas_will_soon_expire();
    @Source("eu/untill/license/client/images/status/SaaS License (Waiting on Reseller).png")
    ImageResource status_saas_unconfirmed();
    @Source("eu/untill/license/client/images/status/SaaS License (Waiting on unTill).png")
    ImageResource status_saas_non_approved();
    @Source("eu/untill/license/client/images/status/Boost License.png")
	ImageResource status_boost();
	@Source("eu/untill/license/client/images/status/Boost License (Expired).png")
	ImageResource status_boost_expired();
	@Source("eu/untill/license/client/images/status/Boost License (Nearly expired).png")
	ImageResource status_boost_will_soon_expire();
	@Source("eu/untill/license/client/images/status/Boost License (Waiting on Reseller).png")
	ImageResource status_boost_unconfirmed();
	@Source("eu/untill/license/client/images/status/Boost License (Waiting on unTill).png")
	ImageResource status_boost_non_approved();
	@Source("eu/untill/license/client/images/status/Demo License.png")
	ImageResource status_demo();
	@Source("eu/untill/license/client/images/status/Demo License (Expired).png")
	ImageResource status_demo_expired();
	@Source("eu/untill/license/client/images/status/Demo License (Nearly expired).png")
	ImageResource status_demo_will_soon_expire();
	@Source("eu/untill/license/client/images/status/Demo License (Waiting on Reseller).png")
	ImageResource status_demo_unconfirmed();	
	@Source("eu/untill/license/client/images/status/Demo License (Waiting on unTill).png")
	ImageResource status_demo_non_approved();
	@Source("eu/untill/license/client/images/status/Online License.png")
	ImageResource status_online();
	@Source("eu/untill/license/client/images/status/Online License (Expired).png")
	ImageResource status_online_expired();
	@Source("eu/untill/license/client/images/status/Online License (Nearly expired).png")
	ImageResource status_online_will_soon_expire();
	@Source("eu/untill/license/client/images/status/Online License (Waiting on Reseller).png")
	ImageResource status_online_unconfirmed();
	@Source("eu/untill/license/client/images/status/Online License (Waiting on unTill).png")
	ImageResource status_online_non_approved();
	@Source("eu/untill/license/client/images/status/Common License.png")
	ImageResource status_common();
	@Source("eu/untill/license/client/images/status/Common License (Expired).png")
	ImageResource status_common_expired();
	@Source("eu/untill/license/client/images/status/Common License (Nearly expired).png")
	ImageResource status_common_will_soon_expire();
	@Source("eu/untill/license/client/images/status/Common License (Waiting on Reseller).png")
	ImageResource status_common_unconfirmed();
	@Source("eu/untill/license/client/images/status/Common License (Waiting on unTill).png")
	ImageResource status_common_non_approved();

	@Source("eu/untill/license/client/images/red.png")
	ImageResource red();
	@Source("eu/untill/license/client/images/yellow.png")
	ImageResource yellow();
	@Source("eu/untill/license/client/images/green.png")
	ImageResource green();

	@Source("eu/untill/license/client/images/arrow-left-blue.png")
	ImageResource arrowLeftBlue();
	@Source("eu/untill/license/client/images/arrow-left-gray.png")
	ImageResource arrowLeftGray();
	@Source("eu/untill/license/client/images/arrow-right-blue.png")
	ImageResource arrowRightBlue();
	@Source("eu/untill/license/client/images/arrow-right-gray.png")
	ImageResource arrowRightGray();
	@Source("eu/untill/license/client/images/check-mark-blue.png")
	ImageResource checkMarkBlue();
}
