/**
 *
 */
package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.ui.MessageDialog.Style;

/**
 * <AUTHOR>
 *
 */
public class AddDiscountDialog extends DialogBox implements ClickHandler, AsyncCallback<Void> {

//	  //////////////
//	 // Listener //
//	//////////////
//
//	public static interface HideDialogListener extends EventListener {
//		void onOk();
//		void onCancel();
//	}
//
//	private ArrayList<HideDialogListener> hideDialogListenerList = null;
//
//	public void addHideDialogListener(HideDialogListener listener) {
//		if (hideDialogListenerList == null)
//			hideDialogListenerList = new ArrayList<HideDialogListener> ();
//		hideDialogListenerList.add(listener);
//	}
//
//	public void removeHideDialogListener(HideDialogListener listener) {
//		if (hideDialogListenerList != null) {
//			hideDialogListenerList.remove(listener);
//			if (hideDialogListenerList.isEmpty())
//				hideDialogListenerList = null;
//		}
//	}

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String addDiscountHeader();
		String discountPriceLabel();
		String discountTextLabel();
		String okButton();
		String cancelButton();
	}

	public static interface UlsMessages extends Messages {
		String unknownNumberFormatOfPrice();
		String discountSuccessfullyAdded();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  /////////////
	 // Members //
	/////////////

	private long licenseId;
	private String currencyCode = null;
	private float currencyRate = 1;

	  //////////////////
	 // Constructors //
	//////////////////

	public AddDiscountDialog(long licenseId, String currencyCode, float currencyRate) {
		this.licenseId = licenseId;
		this.currencyCode = currencyCode;
		this.currencyRate = currencyRate;

		// Generate base interface
		generateUI();

		this.setText(constants.addDiscountHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private FlexTable tblParameters = new FlexTable();
	private Label lblPrice; // = new Label(constants.discountPriceLabel() + ":", false);
	private TextBox txtPrice = new TextBox();
	private Label lblText = new Label(constants.discountTextLabel() + ":", false);
	private TextBox txtText = new TextBox();

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		lblPrice = new Label(constants.discountPriceLabel() +
				(currencyCode != null ? " (" + currencyCode + ")" : "") + ":", false);

		// Assemble parameters table
		txtPrice.setVisibleLength(20);
		txtText.setMaxLength(50);
		txtText.setVisibleLength(60);
		tblParameters.setWidget(0, 0, lblPrice);
		tblParameters.setWidget(0, 1, txtPrice);
		tblParameters.setWidget(1, 0, lblText);
		tblParameters.setWidget(1, 1, txtText);

		// Assemble button panel
		btnOk.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.add(btnOk);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(tblParameters);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnOk){

			/// Fill and check entered data

			Double price;
//			NumberFormat numberFormat = NumberFormat.getInstance();
//			try {
//				price = numberFormat.parse(txtPrice.getText()).doubleValue();
//			} catch (ParseException e) {
//				txtPrice.setFocus(true);
//				new MessageDialog(messages.unknownNumberFormatOfPrice(), Style.WARNING).show();
//				return;
//			}
			try {
				price = Double.valueOf(txtPrice.getText()) * currencyRate;
			} catch (NumberFormatException e) {
				new MessageDialog(messages.unknownNumberFormatOfPrice(), Style.WARNING, txtPrice).show();
				return;
			}

			// Call RPC LicenseService.requestLicense
			UntillLicenseServer.getLicenseServiceAsync().addDiscount(UntillLicenseServer.getAuthScheme(),
					licenseId, price, txtText.getText(), this);

			WaitDialog.show();
		}

	}

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		if (caught instanceof AuthFailedException) {
			this.hide();
			LoginPage.get().show();
		} else {
			ErrorDialog.show(caught);
		}
	}

	@Override
	public void onSuccess(Void result) {
		WaitDialog.hide();
		new MessageDialog(messages.discountSuccessfullyAdded(), Style.INFORMATION).show();
		this.hide();
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtPrice.setFocus(true);
	}

}
