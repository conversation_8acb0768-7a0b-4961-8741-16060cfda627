<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="eu.untill.license.client.UntillLicenseServerTest" tests="2" skipped="0" failures="0" errors="0" timestamp="2025-08-06T13:08:50.923Z" hostname="UNP-WS2" time="7.955">
  <properties/>
  <testcase name="testValidLicenseHardCode" classname="eu.untill.license.client.UntillLicenseServerTest" time="7.944"/>
  <testcase name="testSimple" classname="eu.untill.license.client.UntillLicenseServerTest" time="0.011"/>
  <system-out><![CDATA[16:08:51,327 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/C:/PRODUCTS/UntillLicenseServer/build/resources/test/logback-test.xml]
16:08:51,327 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Resource [logback-test.xml] occurs multiple times on the classpath.
16:08:51,327 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Resource [logback-test.xml] occurs at [file:/C:/PRODUCTS/UntillLicenseServer/test/logback-test.xml]
16:08:51,327 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Resource [logback-test.xml] occurs at [file:/C:/PRODUCTS/UntillLicenseServer/build/resources/test/logback-test.xml]
16:08:51,370 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - debug attribute not set
16:08:51,370 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
16:08:51,375 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [STDOUT]
16:08:51,383 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
16:08:51,397 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
16:08:51,397 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [STDERR]
16:08:51,397 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
16:08:51,397 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to INFO
16:08:51,397 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [STDOUT] to Logger[ROOT]
16:08:51,398 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [STDERR] to Logger[ROOT]
16:08:51,398 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - End of configuration.
16:08:51,398 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@4331d187 - Registering current configuration as safe fallback point

16:08:51.406 [Test worker] INFO  org.eclipse.jetty.util.log initialized : Logging initialized @914ms
16:08:51.476 [Test worker] INFO  org.eclipse.jetty.server.Server doStart : jetty-9.2.14.v20151106
16:08:51.557 [Test worker] WARN  o.e.j.a.AnnotationConfiguration configure : ServletContainerInitializers: detected. Class hierarchy: empty
16:08:51.659 [Test worker] INFO  o.e.j.server.handler.ContextHandler doStart : Started c.g.g.j.@8692d67{/,file:/C:/PRODUCTS/UntillLicenseServer/www-test/,AVAILABLE}{C:\PRODUCTS\UntillLicenseServer\www-test}
16:08:51.664 [Test worker] INFO  o.e.jetty.server.ServerConnector doStart : Started ServerConnector@ab7e7ad{HTTP/1.1}{0.0.0.0:8683}
16:08:51.664 [Test worker] INFO  org.eclipse.jetty.server.Server doStart : Started @1174ms
16:08:58.523 [qtp250112971-33] DEBUG eu.untill.license.server.Common init : ENTRY
16:08:58.523 [qtp250112971-33] INFO  eu.untill.license.server.Common init : Starting unTill License Server: jetty/9.2.14.v20151106
16:08:58.824 [qtp250112971-33] DEBUG eu.untill.license.server.Common init : RETURN
16:08:58.839 [qtp250112971-33] DEBUG e.u.l.server.LicenseServiceImpl validLicenseHardCode : ENTRY (1234567890123456789012342)
16:08:58.840 [qtp250112971-33] DEBUG e.u.l.server.LicenseServiceImpl validLicenseHardCode : RETURN true
]]></system-out>
  <system-err><![CDATA[16:08:58.630 [qtp250112971-33] ERROR eu.untill.license.server.Common fixArticlesNames : Error fixing articles names
org.firebirdsql.jdbc.FBSQLException: GDS Exception. 335544344. I/O error during "CreateFile (open)" operation for file "C:\UNTILL\DB\LICENSES.FDB"
Error while trying to open file
null
	at org.firebirdsql.jdbc.FBDataSource.getConnection(FBDataSource.java:120)
	at org.firebirdsql.jdbc.AbstractDriver.connect(AbstractDriver.java:136)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.apache.commons.dbcp.DriverManagerConnectionFactory.createConnection(DriverManagerConnectionFactory.java:78)
	at org.apache.commons.dbcp.PoolableConnectionFactory.makeObject(PoolableConnectionFactory.java:582)
	at org.apache.commons.pool.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:1188)
	at org.apache.commons.dbcp.PoolingDataSource.getConnection(PoolingDataSource.java:106)
	at eu.untill.license.server.Common.getConnection(Common.java:281)
	at eu.untill.license.server.Common.fixArticlesNames(Common.java:201)
	at eu.untill.license.server.Common.init(Common.java:128)
	at eu.untill.license.server.LicenseServiceImpl.init(LicenseServiceImpl.java:56)
	at javax.servlet.GenericServlet.init(GenericServlet.java:244)
	at com.google.gwt.user.server.rpc.RemoteServiceServlet.init(RemoteServiceServlet.java:168)
	at org.eclipse.jetty.servlet.ServletHolder.initServlet(ServletHolder.java:616)
	at org.eclipse.jetty.servlet.ServletHolder.getServlet(ServletHolder.java:472)
	at org.eclipse.jetty.servlet.ServletHolder.ensureInstance(ServletHolder.java:767)
	at org.eclipse.jetty.servlet.ServletHolder.prepare(ServletHolder.java:752)
	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:582)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:577)
	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:223)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1127)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:515)
	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:185)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1061)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:97)
	at org.eclipse.jetty.server.handler.RequestLogHandler.handle(RequestLogHandler.java:95)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:97)
	at org.eclipse.jetty.server.Server.handle(Server.java:499)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:311)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:257)
	at org.eclipse.jetty.io.AbstractConnection$2.run(AbstractConnection.java:544)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:635)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$3.run(QueuedThreadPool.java:555)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.firebirdsql.gds.GDSException: I/O error during "CreateFile (open)" operation for file "C:\UNTILL\DB\LICENSES.FDB"
Error while trying to open file
null
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.readStatusVector(AbstractJavaGDSImpl.java:2103)
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.receiveResponse(AbstractJavaGDSImpl.java:2053)
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.internalAttachDatabase(AbstractJavaGDSImpl.java:460)
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.iscAttachDatabase(AbstractJavaGDSImpl.java:410)
	at org.firebirdsql.jca.FBManagedConnection.<init>(FBManagedConnection.java:105)
	at org.firebirdsql.jca.FBManagedConnectionFactory.createManagedConnection(FBManagedConnectionFactory.java:509)
	at org.firebirdsql.jca.FBStandAloneConnectionManager.allocateConnection(FBStandAloneConnectionManager.java:65)
	at org.firebirdsql.jdbc.FBDataSource.getConnection(FBDataSource.java:118)
	... 36 common frames omitted
]]></system-err>
</testsuite>
