package eu.untill.license.server;

import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;

public class LogBufferAppender extends AppenderBase<ILoggingEvent> {

	private final static int DEFAULT_SIZE = 10240;
	private int size;
	private ILoggingEvent buffer[];
	int start, count;

	PatternLayoutEncoder encoder;
	public PatternLayoutEncoder getEncoder() {
		return encoder;
	}
	public void setEncoder(PatternLayoutEncoder encoder) {
		this.encoder = encoder;
	}

	public LogBufferAppender() { this(DEFAULT_SIZE); }

	public LogBufferAppender(int size) {
		this.size = size <= 0 ? DEFAULT_SIZE : size;
		this.buffer = new ILoggingEvent[this.size];
		this.start = 0;
		this.count = 0;
	}

	@Override
	public void start() {
		if (encoder == null) {
			encoder = new PatternLayoutEncoder();
			//ch.qos.logback.classic.html.HTMLLayout
			encoder.setPattern("%date [%thread] %-5level %logger{36} %method %mdc: %message%n");
			encoder.setContext(context);
			encoder.start();
		}
		super.start();
	}

	@Override
	protected synchronized void append(ILoggingEvent eventObject) {
		encoder.encode(eventObject); // force to resolve formats like %method
		int ix = (start + count) % buffer.length;
		buffer[ix] = eventObject;
		if (count < buffer.length) {
			count++;
		} else {
			start++;
			start %= buffer.length;
		}
	}

	public synchronized String getLog(int maxCount) {
		StringBuffer sb = new StringBuffer();
		for (int i = (count > maxCount ? count - maxCount : 0); i < count; i++) {
			int ix = (start + i) % buffer.length;
			ILoggingEvent record = buffer[ix];
			sb.append(new String(encoder.encode(record)));
		}
		return sb.toString();
	}

}
