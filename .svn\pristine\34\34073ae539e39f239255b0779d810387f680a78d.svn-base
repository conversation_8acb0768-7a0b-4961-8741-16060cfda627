package eu.untill.license.server;

import static eu.untill.license.server.SuCommon.paramsFromJson;
import static eu.untill.license.server.SuCommon.paramsToJson;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import eu.untill.license.client.LicenseService.InvalidArgumentException;
import eu.untill.license.client.LicenseService.LicenseServiceException;
import eu.untill.license.shared.SuHost;
import eu.untill.license.shared.SuLicense;
import eu.untill.license.shared.SuSubTask;
import eu.untill.license.shared.SuTask;
import eu.untill.license.shared.SuTaskBundle;
import eu.untill.license.shared.SuTaskListOrder;

public class SuHandler {

	static final Logger LOG = LoggerFactory.getLogger(SuHandler.class);
	
	private CommonHelper commonHelper;

	public SuHandler(CommonHelper commonHelper) {
		this.commonHelper = commonHelper;
	}

	public int getSuTaskBundleCount(Connection conn, Dealer dealer, long dealerId)
			throws LicenseServiceException, SQLException {
		int result = 0;

		// Parameter "dealerId" only for super dealer
		if (!dealer.superDealer)
			dealerId = dealer.id;

		String sql = "SELECT COUNT(*)\n"
				+ "FROM SU_TASK_BUNDLE\n"
				+ (dealerId != 0 ? "WHERE ID_DEALERS = ?\n" : "");
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			int parIndex = 1;
			if (dealerId != 0)
				stmt.setLong(parIndex++, dealerId);

			try (ResultSet rs = stmt.executeQuery()) {
				if (rs.next())
					result = rs.getInt(1);
			}
		}

		return result;
	}

	public List<SuTaskBundle> getSuTaskBundleList(Connection conn, Dealer dealer, long dealerId,
			SuTaskListOrder order, int from, int to) throws LicenseServiceException, SQLException {
		List<SuTaskBundle> result = new ArrayList<>();

		// Parameter "dealerId" only for super dealer
		if (!dealer.superDealer)
			dealerId = dealer.id;

		String orderSqlPart = "ORDER BY CREATE_DATETIME DESC";
		if (order != null) {
			switch (order) {
			case BY_CREATE_DATETIME:      orderSqlPart = "ORDER BY CREATE_DATETIME";      break;
			case BY_CREATE_DATETIME_DESC: orderSqlPart = "ORDER BY CREATE_DATETIME DESC"; break;
			case BY_TITLE:                orderSqlPart = "ORDER BY TITLE";                break;
			case BY_TITLE_DESC:           orderSqlPart = "ORDER BY TITLE DESC";           break;
			case BY_START_DATETIME:       orderSqlPart = "ORDER BY START_DATETIME";       break;
			case BY_START_DATETIME_DESC:  orderSqlPart = "ORDER BY START_DATETIME DESC";  break;
			default:
				break;
			}
		}

		String sql = "SELECT ID, ID_DEALERS, CREATE_DATETIME, TITLE, TASK_ACTION, PARAMS, START_DATETIME\n"
				+ "FROM SU_TASK_BUNDLE\n"
				+ (dealerId != 0 ? "WHERE ID_DEALERS = ?\n" : "")
				+ orderSqlPart + "\n"
				+ "ROWS ? TO ?";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			int parIndex = 1;
			if (dealerId != 0)
				stmt.setLong(parIndex++, dealerId);
			stmt.setInt(parIndex++, from);
			stmt.setInt(parIndex++, to);

			try (ResultSet rs = stmt.executeQuery()) {
				result = new ArrayList<>();
				while (rs.next()) {
					SuTaskBundle suTaskBundle = new SuTaskBundle();
					suTaskBundle.taskBundleId = rs.getLong("ID");
					suTaskBundle.dealerId = rs.getLong("ID_DEALERS");
					suTaskBundle.createDatetime = rs.getTimestamp("CREATE_DATETIME");
					suTaskBundle.title = rs.getString("TITLE");
					suTaskBundle.action = rs.getString("TASK_ACTION");
					suTaskBundle.params = new HashMap<>(paramsFromJson(rs.getString("PARAMS")));
					Timestamp startDateTime = rs.getTimestamp("START_DATETIME");
					suTaskBundle.startDatetime = startDateTime == null ? null : startDateTime;
					suTaskBundle.tasks = getSuTaskListForSuTaskBundle(conn, dealer, suTaskBundle.taskBundleId);
					result.add(suTaskBundle);
				}
			}
		}

		return result;
	}

	private List<SuTask> getSuTaskListForSuTaskBundle(Connection conn, Dealer dealer, long taskBundleId)
			throws SQLException {
		List<SuTask> result = new ArrayList<>();

		String sql = "SELECT ST.ID, ST.ID_SU_TASK, ST.ID_SU_HOST, ST.STATUS, ST.STATUS_CHANGE_DT, ST.STATUS_SENT_DT, ST.RESULT\n"
				+ "FROM SU_TASK T\n"
				+ "INNER JOIN SU_SUBTASK ST ON ST.ID_SU_TASK = T.ID\n"
				+ "WHERE T.ID_SU_TASK_BUNDLE = ?\n"
				+ "ORDER BY ID_SU_TASK";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, taskBundleId);
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					long taskId = rs.getLong("ID_SU_TASK");
					if (result.isEmpty() || result.get(result.size() - 1).taskId != taskId) {
						SuTask suTask = new SuTask();
						suTask.taskId = taskId;
						suTask.taskBundleId = taskBundleId;
						suTask.subTasks = new ArrayList<>();
						result.add(suTask);
					}
					SuSubTask subTask = new SuSubTask();
					subTask.subTaskId = rs.getLong("ID");
					subTask.taskId = taskId;
					subTask.hostId = rs.getLong("ID_SU_HOST");
					try {
						subTask.status = SuSubTask.Status.values()[rs.getInt("STATUS")];
					} catch (ArrayIndexOutOfBoundsException e) {
						subTask.status = SuSubTask.Status.UNKNOWN;
					}
					subTask.statusChangeTime = rs.getTimestamp("STATUS_CHANGE_DT");
					subTask.statusSentTime = rs.getTimestamp("STATUS_SENT_DT");
					subTask.result = rs.getString("RESULT");
					subTask.host = getHostById(conn, subTask.hostId);
					result.get(result.size() - 1).subTasks.add(subTask);
				}
			}
		}

		return result;
	}

	// XXX temporary (obviously not an effective method)
	private SuHost getHostById(Connection conn, long hostId) throws SQLException {
		String sql = "SELECT ID, ID_SU_LICENSE, NAME, GUID, REGISTRATION_DT, HEARTBEAT, SHORT_SYS_INFO, DEPLOYED_PRODUCTS\n"
				+ "FROM SU_HOST\n"
				+ "WHERE ID = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, hostId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					throw new RuntimeException();
				SuHost host = new SuHost();
				host.hostId = rs.getLong("ID");
				host.licenseId = rs.getLong("ID_SU_LICENSE");
				host.name = rs.getString("NAME");
				host.guid = rs.getString("GUID");
				host.registrationTime = rs.getTimestamp("REGISTRATION_DT");
				host.heartbeat = rs.getTimestamp("HEARTBEAT");
				host.shortSysInfo = rs.getString("SHORT_SYS_INFO");
				host.deployedProducts = rs.getString("DEPLOYED_PRODUCTS");
				host.license = getLicenseById(conn, host.licenseId);
				return host;
			}
		}
	}

	private SuLicense getLicenseById(Connection conn, long licenseId) throws SQLException {
		String sql = "SELECT SL.ID, SL.HARD_CODE, SL.HOST_REG_PASS, L.CLIENT_NAME, L.CHAIN\n"
				+ "FROM SU_LICENSE SL\n"
				+ "LEFT OUTER JOIN LICENSES L ON L.HARD_CODE = SL.HARD_CODE AND L.IS_ACTIVE > 0\n"
				+ "WHERE SL.ID = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, licenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					throw new RuntimeException();
				SuLicense license = new SuLicense();
				license.licenseId = rs.getLong("ID");
				license.licenseUid = rs.getString("HARD_CODE");
				license.hostRegPass  = rs.getString("HOST_REG_PASS");
				license.clientName = rs.getString("CLIENT_NAME");
				license.chain = rs.getString("CHAIN");
				return license;
			}
		}
	}

	public List<SuHost> getSuHostsByLicenseIds(Connection conn, Dealer dealer, Set<Long> licenseIds)
			throws SQLException {
		List<SuHost> result = new ArrayList<>();

		long dealerId = dealer.superDealer ? 0 : dealer.id; // to filter foreign hosts

		String sql = "SELECT H.ID, H.ID_SU_LICENSE, H.NAME, H.GUID, H.REGISTRATION_DT, H.HEARTBEAT, H.SHORT_SYS_INFO,\n"
				+ " H.DEPLOYED_PRODUCTS, L.ID_DEALERS\n"
				+ "FROM SU_HOST H\n"
				+ "INNER JOIN SU_LICENSE SL ON SL.ID = H.ID_SU_LICENSE\n"
				+ "LEFT OUTER JOIN LICENSES L ON L.HARD_CODE = SL.HARD_CODE AND L.IS_ACTIVE > 0\n"
				+ "WHERE H.ID_SU_LICENSE = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			l: for (Long licenseId : licenseIds) {
				stmt.setLong(1, licenseId);

				try (ResultSet rs = stmt.executeQuery()) {
					while (rs.next()) {
						long hostDealerId = rs.getLong("ID_DEALERS");
						if (dealerId != 0 && hostDealerId != dealerId) {
							LOG.warn("SU License ({}) does not belong to Dealer ({})", licenseId, dealerId);
							continue l;
						}
						SuHost host = new SuHost();
						host.hostId = rs.getLong("ID");
						host.licenseId = rs.getLong("ID_SU_LICENSE");
						host.name = rs.getString("NAME");
						host.guid = rs.getString("GUID");
						host.registrationTime = rs.getTimestamp("REGISTRATION_DT");
						host.heartbeat = rs.getTimestamp("HEARTBEAT");
						host.shortSysInfo = rs.getString("SHORT_SYS_INFO");
						host.deployedProducts = rs.getString("DEPLOYED_PRODUCTS");
						host.license = getLicenseById(conn, host.licenseId);
						result.add(host);
					}
				}
			}
		}

		return result;
	}

	// XXX temporary
	public List<SuHost> getSuHostsByHardCodes(Connection conn, Dealer dealer, Set<String> licenseHardCodes) throws SQLException {
		List<SuHost> result = new ArrayList<>();

		long dealerId = dealer.superDealer ? 0 : dealer.id; // to filter foreign hosts

		String sql = "SELECT H.ID, H.ID_SU_LICENSE, H.NAME, H.GUID, H.REGISTRATION_DT, H.HEARTBEAT, H.SHORT_SYS_INFO,\n"
				+ " H.DEPLOYED_PRODUCTS, L.ID_DEALERS\n"
				+ "FROM SU_HOST H\n"
				+ "INNER JOIN SU_LICENSE SL ON SL.ID = H.ID_SU_LICENSE\n"
				+ "LEFT OUTER JOIN LICENSES L ON L.HARD_CODE = SL.HARD_CODE AND L.IS_ACTIVE > 0\n"
				+ "WHERE SL.HARD_CODE = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			l: for (String licenseHardCode : licenseHardCodes) {
				stmt.setString(1, licenseHardCode);

				try (ResultSet rs = stmt.executeQuery()) {
					while (rs.next()) {
						long hostDealerId = rs.getLong("ID_DEALERS");
						if (dealerId != 0 && hostDealerId != dealerId) {
							LOG.warn("SU License ({}) does not belong to Dealer ({})", licenseHardCode, dealerId);
							continue l;
						}
						SuHost host = new SuHost();
						host.hostId = rs.getLong("ID");
						host.licenseId = rs.getLong("ID_SU_LICENSE");
						host.name = rs.getString("NAME");
						host.guid = rs.getString("GUID");
						host.registrationTime = rs.getTimestamp("REGISTRATION_DT");
						host.heartbeat = rs.getTimestamp("HEARTBEAT");
						host.shortSysInfo = rs.getString("SHORT_SYS_INFO");
						host.deployedProducts = rs.getString("DEPLOYED_PRODUCTS");
						host.license = getLicenseById(conn, host.licenseId);
						result.add(host);
					}
				}
			}
		}

		return result;
	}

	public List<SuHost> getSuHostsByDealerId(Connection conn, Dealer dealer, long dealerId) throws SQLException {
		List<SuHost> result = new ArrayList<>();

		// Parameter "dealerId" only for super dealer
		if (!dealer.superDealer || dealerId == 0)
			dealerId = dealer.id;

		String sql = "SELECT H.ID ID_SU_HOST, H.ID_SU_LICENSE, H.NAME, H.GUID, H.REGISTRATION_DT, H.HEARTBEAT,\n"
				+ " H.SHORT_SYS_INFO, H.DEPLOYED_PRODUCTS,\n"
				+ " L.HARD_CODE, L.HOST_REG_PASS, LIC.CLIENT_NAME, LIC.CHAIN\n"
				+ "FROM SU_HOST H\n"
				+ "INNER JOIN SU_LICENSE L ON L.ID = H.ID_SU_LICENSE\n"
				+ "INNER JOIN LICENSES LIC ON LIC.HARD_CODE = L.HARD_CODE AND LIC.IS_ACTIVE > 0\n"
				+ "WHERE H.IS_ACTIVE > 0 AND LIC.ID_DEALERS = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, dealerId);
			try (ResultSet rs = stmt.executeQuery()) {
				Date now = commonHelper.getCurrentDate();
				while (rs.next()) {
					SuHost host = getHostFromResultSet(rs);
					host.license = getLicenseFromResultSet(rs);
					host.offlineDuration = host.heartbeat != null ? now.getTime() - host.heartbeat.getTime() : -1;
					result.add(host);
				}
			}
		}

		return result;
	}

	public void createSuTaskBundle(Connection conn, Dealer dealer, SuTaskBundle taskBundle)
			throws LicenseServiceException, SQLException {

		// Check parameters
		if (taskBundle == null) throw new InvalidArgumentException("taskBundle");
		if (taskBundle.tasks == null) throw new InvalidArgumentException("taskBundle.tasks");
		if (!dealer.superDealer && taskBundle.dealerId != dealer.id)  throw new InvalidArgumentException("taskBundle.dealerId");

		// check for hosts belongs to target dealer
		try (PreparedStatement stmt = conn.prepareStatement("SELECT L.ID_DEALERS \n"
				+ "FROM SU_HOST H\n"
				+ "INNER JOIN SU_LICENSE SL ON SL.ID = H.ID_SU_LICENSE\n"
				+ "LEFT OUTER JOIN LICENSES L ON L.HARD_CODE = SL.HARD_CODE AND L.IS_ACTIVE > 0\n"
				+ "WHERE H.ID = ?")) {
			for (SuTask task : taskBundle.tasks) {
				if (task == null) throw new InvalidArgumentException("taskBundle.task");
				if (task.subTasks == null) throw new InvalidArgumentException("taskBundle.task.subTasks");
				for (SuSubTask subTask: task.subTasks) {
					if (subTask == null) throw new InvalidArgumentException("taskBundle.task.subTask");
					stmt.setLong(1, subTask.hostId);
					try (ResultSet rs = stmt.executeQuery()) {
						if (!rs.next() || rs.getLong(1) != taskBundle.dealerId) {
							LOG.warn("Host does not exists or host license does not belongs to this dealer"
									+ " (subTask.subTaskId: {}, subTask.hostId: {}, taskBundle.dealerId: {})",
									subTask.subTaskId, subTask.hostId, taskBundle.dealerId);
							throw new InvalidArgumentException("taskBundle.task.subTask");
						}
					}
				}
			}
		}

		taskBundle.taskBundleId = Common.generateTableId(conn);
		taskBundle.createDatetime = commonHelper.getCurrentDate();
		//                                        1   2           3                4      5            6       7
		String sql = "INSERT INTO SU_TASK_BUNDLE (ID, ID_DEALERS, CREATE_DATETIME, TITLE, TASK_ACTION, PARAMS, START_DATETIME)\n"
				+ "VALUES (?, ?, ?, ?, ?, ?, ?)\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, taskBundle.taskBundleId);
			stmt.setLong(2, taskBundle.dealerId);
			stmt.setTimestamp(3, new Timestamp(taskBundle.createDatetime.getTime()));
			stmt.setString(4, taskBundle.title);
			stmt.setString(5, taskBundle.action);
			stmt.setString(6, paramsToJson(taskBundle.params));
			stmt.setTimestamp(7, taskBundle.startDatetime != null ? new Timestamp(taskBundle.startDatetime.getTime()) : null);
			if (stmt.executeUpdate() == 0) throw new RuntimeException();
		}

		//                                     1   2
		String sqlTask = "INSERT INTO SU_TASK (ID, ID_SU_TASK_BUNDLE)\n"
				+ "VALUES (?, ?)\n";
		//                                           1   2           3           4       5
		String sqlSubTask = "INSERT INTO SU_SUBTASK (ID, ID_SU_TASK, ID_SU_HOST, STATUS, STATUS_CHANGE_DT)\n"
				+ "VALUES (?, ?, ?, ?, ?)\n";
		try (
				PreparedStatement stmtTask = conn.prepareStatement(sqlTask);
				PreparedStatement stmtSubTask = conn.prepareStatement(sqlSubTask);
		) {
			for (SuTask task : taskBundle.tasks) {
				task.taskId = Common.generateTableId(conn);
				task.taskBundleId = taskBundle.taskBundleId;
				stmtTask.setLong(1, task.taskId);
				stmtTask.setLong(2, task.taskBundleId);
				if (stmtTask.executeUpdate() == 0) throw new RuntimeException();
				for (SuSubTask subTask: task.subTasks) {
					subTask.subTaskId = Common.generateTableId(conn);
					subTask.taskId = task.taskId;
					subTask.status =  SuSubTask.Status.CREATED;
					subTask.statusChangeTime = commonHelper.getCurrentDate();
					stmtSubTask.setLong(1, subTask.subTaskId);
					stmtSubTask.setLong(2, subTask.taskId);
					stmtSubTask.setLong(3, subTask.hostId);
					stmtSubTask.setInt(4, subTask.status.ordinal());
					stmtSubTask.setTimestamp(5, new Timestamp(subTask.statusChangeTime.getTime()));
					if (stmtSubTask.executeUpdate() == 0) throw new RuntimeException();
				}
			}
		}
	}

	public SuLicense getSuLicense(Connection conn, Dealer dealer, String hardCode) throws SQLException {
		String sql = "SELECT L.ID ID_SU_LICENSE, L.HARD_CODE, L.HOST_REG_PASS, LIC.CLIENT_NAME, LIC.CHAIN\n"
				+ "FROM SU_LICENSE L\n"
				+ "INNER JOIN LICENSES LIC ON LIC.HARD_CODE = L.HARD_CODE AND LIC.IS_ACTIVE > 0\n"
				+ "WHERE L.HARD_CODE = ?\n"
				+ (dealer.superDealer ? "" : " AND LIC.ID_DEALERS = ?\n");
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setString(1, hardCode);
			if (!dealer.superDealer)
				stmt.setLong(2, dealer.id);
			try (ResultSet rs = stmt.executeQuery()) {
				if (rs.next())
					return getLicenseFromResultSet(rs);
				else
					return null;
			}
		}
	}

	public List<SuHost> getSuHostsByHardCode(Connection conn, Dealer dealer, String hardCode) throws SQLException {
		List<SuHost> result = new ArrayList<>();

		long dealerId = dealer.superDealer ? 0 : dealer.id; // to filter foreign hosts

		String sql = "SELECT H.ID ID_SU_HOST, H.ID_SU_LICENSE, H.NAME, H.GUID, H.REGISTRATION_DT, H.HEARTBEAT,\n"
				+ " H.SHORT_SYS_INFO, H.DEPLOYED_PRODUCTS,\n"
				+ " L.HARD_CODE, L.HOST_REG_PASS, LIC.CLIENT_NAME, LIC.CHAIN, LIC.ID_DEALERS\n"
				+ "FROM SU_HOST H\n"
				+ "INNER JOIN SU_LICENSE L ON L.ID = H.ID_SU_LICENSE\n"
				+ "LEFT OUTER JOIN LICENSES LIC ON LIC.HARD_CODE = L.HARD_CODE AND LIC.IS_ACTIVE > 0\n"
				+ "WHERE H.IS_ACTIVE > 0 AND L.HARD_CODE = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setString(1, hardCode);
			try (ResultSet rs = stmt.executeQuery()) {
				Date now = commonHelper.getCurrentDate();
				while (rs.next()) {
					long hostDealerId = rs.getLong("ID_DEALERS");
					if (dealerId != 0 && hostDealerId != dealerId) {
						LOG.warn("SU License ({}) does not belong to Dealer ({})", hardCode, dealerId);
						return result;
					}
					SuHost host = getHostFromResultSet(rs);
					host.license = getLicenseFromResultSet(rs);
					host.offlineDuration = host.heartbeat != null ? now.getTime() - host.heartbeat.getTime() : -1;
					result.add(host);
				}
			}
		}

		return result;
	}

	public void unregisterSuHost(Connection conn, Dealer dealer, long hostId) throws SQLException {

		if (!dealer.superDealer) {
			// TODO check for host belongs to this dealer
		}

		String sql = "UPDATE SU_HOST SET IS_ACTIVE = 0 WHERE ID = ?";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, hostId);
			if (stmt.executeUpdate() == 0) throw new RuntimeException();
		}

	}

	public void setSuLicenseHostRegPass(Connection conn, Dealer dealer, String hardCode, String hostRegPass) throws SQLException {
		if (!dealer.superDealer) {
			// TODO check for license belongs to this dealer
		}

		String sql = "UPDATE OR INSERT INTO SU_LICENSE (HARD_CODE, HOST_REG_PASS)\n"
				+ "VALUES (?, ?)\n"
				+ "MATCHING (HARD_CODE)\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setString(1, hardCode);
			stmt.setString(2, hostRegPass);
			if (stmt.executeUpdate() == 0) throw new RuntimeException();
		}

	}

	private SuHost getHostFromResultSet(ResultSet rs) throws SQLException {
		SuHost host = new SuHost();
		host.hostId = rs.getLong("ID_SU_HOST");
		host.licenseId = rs.getLong("ID_SU_LICENSE");
		host.name = rs.getString("NAME");
		host.guid = rs.getString("GUID");
		host.registrationTime = rs.getTimestamp("REGISTRATION_DT");
		host.heartbeat = rs.getTimestamp("HEARTBEAT");
		host.shortSysInfo = rs.getString("SHORT_SYS_INFO");
		host.deployedProducts = rs.getString("DEPLOYED_PRODUCTS");
		return host;
	}

	private SuLicense getLicenseFromResultSet(ResultSet rs) throws SQLException {
		SuLicense license = new SuLicense();
		license.licenseId = rs.getLong("ID_SU_LICENSE");
		license.licenseUid = rs.getString("HARD_CODE");
		license.hostRegPass  = rs.getString("HOST_REG_PASS");
		license.clientName = rs.getString("CLIENT_NAME");
		license.chain = rs.getString("CHAIN");
		return license;
	}

}
