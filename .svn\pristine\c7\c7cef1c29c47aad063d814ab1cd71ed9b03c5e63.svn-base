package eu.untill.license.server;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import eu.untill.license.client.LicenseService.DbErrorException;
import eu.untill.license.client.LicenseService.InvalidArgumentException;
import eu.untill.license.online.SignedLicense;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.DbLicenseOnline;
import eu.untill.license.shared.License;

public class OnlineLicenseHandler {

	static final Logger LOG = LoggerFactory.getLogger(OnlineLicenseHandler.class);
	
	private CommonHelper commonHelper;

	private ConcurrentHashMap<String, RequestData> lastSuccessfulRequests = new ConcurrentHashMap<>();
	private static class RequestData {
		private Date time;
		private String previousSignature;
		public RequestData(Date time, String previousSignature) {
			this.time = time;
			this.previousSignature = previousSignature;
		}
	}

	public OnlineLicenseHandler(CommonHelper commonHelper) {
		this.commonHelper = commonHelper;
	}

	public static class OnlineLicenseHandlerException extends Exception {
		private static final long serialVersionUID = 8026120557025446931L;
		public OnlineLicenseHandlerException() { }
		public OnlineLicenseHandlerException(String message) { super(message); }
		public OnlineLicenseHandlerException(String message, Throwable cause) { super(message, cause); }
	}
	
	public static class BadRequest extends OnlineLicenseHandlerException {
		private static final long serialVersionUID = 3501947817446010720L;
		public BadRequest(String message) { super(message); }
		public BadRequest(String message, Throwable cause) { super(message, cause); }
	}

	public static class LicenseNotFound extends OnlineLicenseHandlerException {
		private static final long serialVersionUID = 704368756573436252L;
		public LicenseNotFound() { super("Active license is not found"); }
	}

	public static class LicenseAlreadyInstalled extends OnlineLicenseHandlerException {
		private static final long serialVersionUID = -2613082267583216852L;
		public LicenseAlreadyInstalled() { super("License is already installed"); }
	}

	public static class PreviousSignatureIsIncorrect extends OnlineLicenseHandlerException {
		private static final long serialVersionUID = 6802640892515305719L;
		public PreviousSignatureIsIncorrect() { super("Previous signature is incorrect"); }
	}

	public byte[] serviceOnlineLicenseRequest(Connection conn, String onlineLicenseId, String productName,
			String productVersionStr, String databaseInfo, String previousSignature) throws SQLException, IOException,
			BadRequest, LicenseNotFound, LicenseAlreadyInstalled, PreviousSignatureIsIncorrect {
		LOG.debug("ENTRY ({}, {}, {}, {}, {})", onlineLicenseId, productName, productVersionStr, Common.reduceString(databaseInfo), Common.reduceString(previousSignature));
		try {

			// Check parameters
			if (productName == null)
				throw new BadRequest("Required parameter 'productName' is missing");
			if (productVersionStr == null)
				throw new BadRequest("Required parameter 'productVersion' is missing");
			int productVersion;
			try {
				productVersion = Integer.parseInt(productVersionStr);
			} catch (NumberFormatException e) {
				throw new BadRequest("Parameter 'productVersion' is not integer", e);
			}

			String privateKey = commonHelper.getPrivateKey(productName, productVersion);
			if (privateKey == null)
				throw new BadRequest("Parameter 'productName' or 'productVersion' is incorrect");

			// calculate activationPeriodBegin and activationPeriodEnd in license
			// XXX hard-coded (now - 1d; now + 14d)
			Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
			calendar.setTime(commonHelper.getCurrentDate());
			Date activationTime = calendar.getTime();
			calendar.add(Calendar.DATE, -1);
			Date activationTimeMinusOneDay = calendar.getTime();
			calendar.add(Calendar.DATE, +1);
			calendar.add(Calendar.WEEK_OF_YEAR, +2);
			Date activationTimePlusTwoWeeks = calendar.getTime();

			SignedLicense license = new SignedLicense();
			OnlineLicenseExtraData extra = readOnlineLicenseWithBoostAndGetExtraData(conn, onlineLicenseId, license, activationTime);

			// check license type
			if (!license.isOnline()) {
				//throw new LicenseIsNotOnline();
				throw new BadRequest("Requested license is not Online");
			}

			// check productName
			String expectedProductName = license.isApostill() ? SignedLicense.PN_APOSTILL : SignedLicense.PN_UNTILL;
			if (!productName.equals(expectedProductName)) {
				updateFailure(conn, extra, "productName", productName);
				//throw new WrongProductName();
				throw new BadRequest("Parameter 'productName' does not match license");
			}

			// set license version
			int licVer = DbLicense.verToLicVer(productVersion);
			if (licVer < 6 && license.getFunction(License.FUNC_FRENCH_FDM) && productVersion != 138)
				throw new BadRequest(String.format(
						"This product version does not support license with French FDM (%s, %d)",
						productName, productVersion));
			if (licVer < 5 && license.getHeConnections() != 0)
				throw new BadRequest(String.format(
						"This product version does not support license with HHT-EFT (%s, %d)",
						productName, productVersion));
			if (licVer < 3 && license.getPsConnections() != 0)
				throw new BadRequest(String.format(
						"This product version does not support license with Production Screen Professional (%s, %d)",
						productName, productVersion));
			if (licVer < 2 && license.isModuled())
				throw new BadRequest(String.format(
						"This product version does not support moduled license (%s, %d)",
						productName, productVersion));
//			if (licVer < 2 && license.isDefinitive() && !license.isNeverExpired())
//				throw new BadRequest(String.format(
//						"This product version does not support definitive license with expiration (%s, %d)",
//						productName, productVersion));
			license.setVersion(licVer);

			// TODO check definitive... (?)

			// Temporary license...
			if (extra.tempExpiredDate != null) {
				if (license.isNeverExpired()) {
					license.setNeverExpired(false);
					license.setExpiredDate(extra.tempExpiredDate);
				} else {
					if (!extra.tempExpiredDate.after(license.getExpiredDate()))
						license.setExpiredDate(extra.tempExpiredDate);
				}
			}

			if (databaseInfo == null) {

				if (extra.installationTime != null && extra.activeSignature != null) {
					updateFailure(conn, extra, "databaseInfo", databaseInfo);
					throw new LicenseAlreadyInstalled();
				}

			} else {

				if (extra.installationTime != null && extra.activeSignature != null) {

					// check previousSignature
					if (previousSignature == null) {
						updateFailure(conn, extra, "previousSignature", previousSignature);
						throw new LicenseAlreadyInstalled();
					}

					if (!previousSignature.equals(extra.activeSignature)) {
						RequestData lastRequest = lastSuccessfulRequests.get(onlineLicenseId);
						if (lastRequest == null) {
							LOG.warn("Accepting incorrect previous signature (licenseUid: {}, previousSignature: {})",
									onlineLicenseId, previousSignature);
						} else if (lastRequest.previousSignature == null
								|| previousSignature.equals(lastRequest.previousSignature)
								|| Duration.between(lastRequest.time.toInstant(), commonHelper.getCurrentDate().toInstant()).toHours() > 12) {
							LOG.warn("Accepting incorrect previous signature (licenseUid: {}, previousSignature: {}, lastRequest.previousSignature: {})",
									onlineLicenseId, previousSignature, lastRequest.previousSignature);
						} else {
							updateFailure(conn, extra, "previousSignature", previousSignature);
							throw new PreviousSignatureIsIncorrect();
						}
					}

					// check productVersion
					if (productVersion < extra.activeProductVersion) {
						updateFailure(conn, extra, "productVersion", Integer.toString(productVersion));
						//throw new DowngradeNotAllowed();
						throw new BadRequest("'productVersion' downgrade not allowed");
					}

					// check databaseInfo
					if (!databaseInfo.equals(extra.activeDatabaseInfo) && productVersion == extra.activeProductVersion) {
						updateFailure(conn, extra, "databaseInfo", databaseInfo);
						//throw new ChangeDatabaseInfoNotAllowed();
						throw new BadRequest("Change 'databaseInfo' is allowed only when upgrading");
					}

				} else {

					// set INSTALLATION_TIME and reset ACTIVATION_COUNT and *FAILURE* fields
					try (
						PreparedStatement stmt = conn.prepareStatement("UPDATE LICENSES\n"
								+ "SET INSTALLATION_TIME = ?\n" // 1
								+ "WHERE ID = ?"); // 2
					) {
						stmt.setTimestamp(1, new Timestamp(commonHelper.getCurrentDate().getTime()));
						stmt.setLong(2, extra.id);
						if (stmt.executeUpdate() == 0) {
							// TODO kvn: write to log "Unknown mistake during update LICENSES in DB"
							// TODO kvn: send error and exit (+rollback)?
							// return or throw
						}
					}
					try (
						PreparedStatement stmt = conn.prepareStatement("DELETE FROM LICENSES_ONLINE\n"
								+ "WHERE ID_LICENSES = ?"); // 1
					) {
						stmt.setLong(1, extra.id);
						stmt.executeUpdate();
					}
	
				}

				// fill activationPeriodBegin and activationPeriodEnd in license
				license.setActivationPeriodBegin(activationTimeMinusOneDay);
				license.setActivationPeriodEnd(activationTimePlusTwoWeeks);

				// sign license
				license.sign(privateKey, productName, productVersion, databaseInfo);

				// activate license in DB
				activateOnlineLicenseInDb(conn, license, extra.id, activationTime, productName, productVersion, databaseInfo);

				lastSuccessfulRequests.put(onlineLicenseId, new RequestData(commonHelper.getCurrentDate(), previousSignature));

				LOG.info("Activated license (ID: {}, new signature: {})",
						onlineLicenseId, Common.reduceString(license.getSignature()));
			}

			// prepare and return license
			return license.saveToString().getBytes(StandardCharsets.UTF_8);

		} finally {
			LOG.debug("RETURN {}", onlineLicenseId);
		}
	}

	private static class OnlineLicenseExtraData {
		public long id;
		public Date tempExpiredDate;
		public Timestamp installationTime;
		public String activeSignature;
		//public String activeProductName;
		public int activeProductVersion;
		public String activeDatabaseInfo;
		public int failureCount;
		public String lastFailureField;
		public String lastFailureData;
	}

	private OnlineLicenseExtraData readOnlineLicenseWithBoostAndGetExtraData(Connection conn, String onlineLicenseId,
			SignedLicense license, Date activationTime) throws SQLException, LicenseNotFound {
		Date activationDate = Common.toUtcDateOnly(activationTime);
		OnlineLicenseExtraData extraData = new OnlineLicenseExtraData();
		// Find active license in DB (LICENSES) by License-UID (HARD_CODE)
		try (
			PreparedStatement stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "  LEFT OUTER JOIN LICENSES_ONLINE ON LICENSES_ONLINE.ID_LICENSES = LICENSES.ID\n"
					+ "WHERE IS_ACTIVE = 1 AND HARD_CODE = ?\n"
					+ "ORDER BY BIN_AND(STATUS, " + License.DESCRIPTION_BOOST_BIT + ")\n");
		) {
			stmt.setString(1, onlineLicenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					throw new LicenseNotFound();
				}
				Common.fillLicenseFromResultSet(rs, license);
				if (license.isBoost()) {
					LOG.warn("Found only boost for license (licenseUid: {})", onlineLicenseId);
					throw new LicenseNotFound();
				}
				license.setLicenseId(rs.getString("HARD_CODE"));
				extraData.id = rs.getLong("ID");
				extraData.tempExpiredDate = Common.toUtcDateOnly(rs.getDate("TEMP_EXPIRED_DATE"));
				extraData.installationTime = rs.getTimestamp("INSTALLATION_TIME");
				extraData.activeSignature = rs.getString("ACTIVE_SIGNATURE");
				//activeProductName = rs.getString("ACTIVE_PRODUCT_NAME");
				extraData.activeProductVersion = rs.getInt("ACTIVE_PRODUCT_VERSION");
				extraData.activeDatabaseInfo = rs.getString("ACTIVE_DATABASE_INFO");
				extraData.failureCount = rs.getInt("FAILURE_COUNT");
				extraData.lastFailureField = rs.getString("LAST_FAILURE_FIELD");
				extraData.lastFailureData = rs.getString("LAST_FAILURE_DATA");
				// Apply boosts
				while (rs.next()) {
					License boostLicense = new License();
					Common.fillLicenseFromResultSet(rs, boostLicense);
					if (!boostLicense.isBoost()) {
						LOG.warn("Found another license with the same number (licenseUid: {})", onlineLicenseId);
						throw new LicenseNotFound();
					}
					if (!boostLicense.getStartDate().after(activationDate)
							&& !boostLicense.getExpiredDate().before(activationDate)) {
						license.setLbConnections((short) (license.getLbConnections() + boostLicense.getLbConnections()));
						license.setRemConnections((short) (license.getRemConnections() + boostLicense.getRemConnections()));
						license.setOmConnections((short) (license.getOmConnections() + boostLicense.getOmConnections()));
						license.setPsConnections((short) (license.getPsConnections() + boostLicense.getPsConnections()));
						license.setHeConnections((short) (license.getHeConnections() + boostLicense.getHeConnections()));
						license.setFuncLimited(license.getFuncLimited() | boostLicense.getFuncLimited());
					}
				}
				
			}
		}
		return extraData;
	}
	
	private void updateFailure(Connection conn, OnlineLicenseExtraData extra,
			String newFailureField, String newFailureData) throws SQLException {
		if (extra.failureCount > 0 && newFailureField.equals(extra.lastFailureField) &&
				(newFailureData == null ? extra.lastFailureData == null : newFailureData.equals(extra.lastFailureData))) {
			try (
				PreparedStatement stmt = conn.prepareStatement("UPDATE LICENSES_ONLINE\n"
						+ "SET LAST_FAILURE_TIME = ?,\n" // 1
						+ "  LAST_FAILURE_COUNT = COALESCE(LAST_FAILURE_COUNT, 0) + 1\n"
						+ "WHERE ID_LICENSES = ?\n");    // 2
			) {
				stmt.setTimestamp(1, new Timestamp(commonHelper.getCurrentDate().getTime()));
				stmt.setLong(2, extra.id);
				if (stmt.executeUpdate() == 0) {
					// TODO kvn: write warning to log "Unknown mistake during update LICENSES_ONLINE in DB"
				}
			}
		} else {
			try (
				PreparedStatement stmtU = conn.prepareStatement("UPDATE LICENSES_ONLINE\n"
						+ "SET FAILURE_COUNT = COALESCE(FAILURE_COUNT, 0) + 1,\n"
						+ "  LAST_FAILURE_COUNT = 1,\n"
						+ "  LAST_FAILURE_TIME = ?,\n" // 1
						+ "  LAST_FAILURE_FIELD = ?,\n" // 2
						+ "  LAST_FAILURE_DATA = ?\n" // 3
						+ "WHERE ID_LICENSES = ?"); // 4
			) {
				stmtU.setTimestamp(1, new Timestamp(commonHelper.getCurrentDate().getTime()));
				stmtU.setString(2, newFailureField);
				stmtU.setString(3, newFailureData);
				stmtU.setLong(4, extra.id);
				if (stmtU.executeUpdate() == 0) {
					try (
						PreparedStatement stmtI = conn.prepareStatement("INSERT INTO LICENSES_ONLINE\n"
								//  1            -              -                   2
								+ "(ID_LICENSES, FAILURE_COUNT, LAST_FAILURE_COUNT, LAST_FAILURE_TIME,\n"
								//   3                   4
								+ "  LAST_FAILURE_FIELD, LAST_FAILURE_DATA)\n"
								+ "VALUES (?, 1, 1, ?, ?, ?)\n"
								+ "MATCHING (ID_LICENSES)");
					) {
						stmtI.setLong(1, extra.id);
						stmtI.setTimestamp(2, new Timestamp(commonHelper.getCurrentDate().getTime()));
						stmtI.setString(3, newFailureField);
						stmtI.setString(4, newFailureData);
						if (stmtI.executeUpdate() == 0) {
							// TODO kvn: write warning to log "Unknown mistake during update LICENSES_ONLINE in DB"
						}
					}
				}
			}
		}
		// Commit transaction
		conn.commit();
	}

	private void activateOnlineLicenseInDb(Connection conn, SignedLicense license, long idLicenses,
			Date activationTime, String productName, int productVersion, String databaseInfo) throws SQLException {
		try (
			PreparedStatement stmtU = conn.prepareStatement("UPDATE LICENSES_ONLINE\n"
					+ "SET ACTIVE_SIGNATURE = ?,\n"      // 1
					+ "  ACTIVE_PRODUCT_NAME = ?,\n"    // 2
					+ "  ACTIVE_PRODUCT_VERSION = ?,\n" // 3
					+ "  ACTIVE_DATABASE_INFO = ?,\n"    // 4
					+ "  ACTIVATION_TIME = ?,\n"         // 5
					+ "  ACTIVATION_COUNT = COALESCE(ACTIVATION_COUNT, 0) + 1,\n"
					+ "  ACTIVATION_PERIOD_BEGIN = ?,\n" // 6
					+ "  ACTIVATION_PERIOD_END = ?\n"    // 7
					+ "WHERE ID_LICENSES = ?\n");        // 8
		) {
			stmtU.setString(1, license.getSignature());
			stmtU.setString(2, productName);
			stmtU.setInt(3, productVersion);
			stmtU.setString(4, databaseInfo);
			stmtU.setTimestamp(5, new Timestamp(activationTime.getTime()));
			stmtU.setTimestamp(6, new Timestamp(license.getActivationPeriodBegin().getTime()));
			stmtU.setTimestamp(7, new Timestamp(license.getActivationPeriodEnd().getTime()));
			stmtU.setLong(8, idLicenses);
			if (stmtU.executeUpdate() == 0) {
				try (
					PreparedStatement stmtI = conn.prepareStatement("INSERT INTO LICENSES_ONLINE\n"
							//  1            2                 3                    4                       5
							+ "(ID_LICENSES, ACTIVE_SIGNATURE, ACTIVE_PRODUCT_NAME, ACTIVE_PRODUCT_VERSION, ACTIVE_DATABASE_INFO,\n"
							//   6                -                 7                        8
							+ "  ACTIVATION_TIME, ACTIVATION_COUNT, ACTIVATION_PERIOD_BEGIN, ACTIVATION_PERIOD_END)\n"
							+ "VALUES (?, ?, ?, ?, ?, ?, 1, ?, ?)\n");
				) {
					stmtI.setLong(1, idLicenses);
					stmtI.setString(2, license.getSignature());
					stmtI.setString(3, productName);
					stmtI.setInt(4, productVersion);
					stmtI.setString(5, databaseInfo);
					stmtI.setTimestamp(6, new Timestamp(activationTime.getTime()));
					stmtI.setTimestamp(7, new Timestamp(license.getActivationPeriodBegin().getTime()));
					stmtI.setTimestamp(8, new Timestamp(license.getActivationPeriodEnd().getTime()));
					if (stmtI.executeUpdate() == 0) {
						// TODO kvn: write to log "Unknown mistake during update LICENSES in DB"
						// TODO kvn: send error and exit (+rollback)?
						// return;
					}
				}
			}
		}
	}

	public void reinstallLicense(Connection conn, Dealer dealer, long licenseId) throws InvalidArgumentException,
			SQLException, DbErrorException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Begin transaction
		conn.setAutoCommit(false);

		try (
			PreparedStatement stmt = conn.prepareStatement(
					"SELECT AVAILABLE_REINSTALLATIONS, INSTALLATION_TIME\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
		) {
			stmt.setLong(1, licenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					LOG.warn("License is not found (licenseId: {})", licenseId);
					throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
				}
				// Check license state
				if (rs.getString("INSTALLATION_TIME") == null) {
					LOG.warn("This license is not installed (licenseId: {})", licenseId);
					throw new InvalidArgumentException("This license is not installed");
				}
				if (rs.getInt("AVAILABLE_REINSTALLATIONS") <= 0) {
					LOG.warn("Re-installation is not available (licenseId: {})", licenseId);
					throw new InvalidArgumentException("Re-installation is not available");
				}
			}
		}

		try (
			PreparedStatement stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET\n"
					+ "  AVAILABLE_REINSTALLATIONS = AVAILABLE_REINSTALLATIONS - 1,\n"
					+ "  INSTALLATION_TIME = NULL\n"
					+ "WHERE ID = ?\n");
		) {
			stmt.setLong(1, licenseId);
			if (stmt.executeUpdate() == 0) {
				LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
				throw new DbErrorException("Unknown mistake during update the license in DB");
			}
		}

		try (
			PreparedStatement stmt = conn.prepareStatement("DELETE FROM LICENSES_ONLINE\n"
					+ "WHERE ID_LICENSES = ?\n");
		) {
				stmt.setLong(1, licenseId);
				stmt.executeUpdate();
		}

		conn.commit();
	}

	public DbLicenseOnline getLicenseOnlineDetails(Connection conn, Dealer dealer, long licenseId)
			throws InvalidArgumentException, SQLException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		long dealerId = dealer.superDealer ? 0 : dealer.id;

		String sql = "SELECT LICENSES_ONLINE.* FROM LICENSES_ONLINE\n"
				+ (dealerId != 0 ? "INNER JOIN LICENSES ON LICENSES.ID = LICENSES_ONLINE.ID_LICENSES\n" : "")
				+ "WHERE ID_LICENSES = ?\n"
				+ (dealerId != 0 ? "AND ID_DEALERS = ?\n" : "");
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, licenseId);
			if (dealerId != 0)
				stmt.setLong(2, dealerId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					return null;
				return Common.getDbLicenseOnlineFromResultSet(rs);
			}
		}
	}

}
