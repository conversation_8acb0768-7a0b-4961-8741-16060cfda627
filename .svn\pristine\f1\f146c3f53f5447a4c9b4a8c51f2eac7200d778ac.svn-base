adjustProperty('untillMavenBaseUrl', 'https://dev.untill.com/artifactory')
adjustProperty('untillMavenUsername', null, true)
adjustProperty('untillMavenPassword', null, true)
adjustProperty('untillMavenResolveUrl', combineUrl(untillMavenBaseUrl, 'repo'))
adjustProperty('untillMavenPublishUrl', combineUrl(untillMavenBaseUrl, 'libs-release-local'))
adjustProperty('untillMavenPublishSnapshotUrl', combineUrl(untillMavenBaseUrl, 'libs-snapshot-local'))
adjustProperty('untillMavenPublishUsername', untillMavenUsername)
adjustProperty('untillMavenPublishPassword', untillMavenPassword)

  /////////////////////
 // Private methods //
/////////////////////

void adjustProperty(String propertyName, String defaultValue, boolean required = false) {
	if (!hasProperty(propertyName)) {
		def sysPropName = propertyName.replaceAll('([A-Z])([A-Z][a-z])|([a-z0-9])([A-Z])', '$1$3.$2$4').toLowerCase()
		def sysEnvName = propertyName.replaceAll('([A-Z])([A-Z][a-z])|([a-z0-9])([A-Z])', '$1$3_$2$4').toUpperCase()
		def value = System.properties[sysPropName]
		if (value == null) value = System.env[sysEnvName]
		if (value == null && required)
			println String.format('\tWARNING: Missing required property "%1$s"%n' +
				'Please define this property one of following means: in file "gradle.properties", ' +
				'as JVM system property "%2$s", as environment variable "%3$s" ' +
				'or as command line parameter "-P%1$s=<value>".',
				propertyName, sysPropName, sysEnvName)
		ext[propertyName] = value == null ? defaultValue : value
	}
}

String combineUrl(String baseUrl, String subUrl) {
	return baseUrl + (baseUrl.substring(baseUrl.length() - 1) == '/' ? '' : '/') + subUrl
}
