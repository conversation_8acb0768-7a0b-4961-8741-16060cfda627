package eu.untill.license.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import javax.xml.rpc.ServiceException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.tpapipos.ITPAPIPOS;

import TPAPIPosTypesU.TArticleInfo;
import TPAPIPosTypesU.TExtraInfo;
import TPAPIPosTypesU.TItemPrice;
import TPAPIPosTypesU.TOrderItem;
import TPAPIPosTypesU.TSalesAreaInfo;
import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.server.UntillTpapiWrapper.Credential;
import eu.untill.license.shared.ArticleWithPrices;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.License.DriverInfo;
import eu.untill.license.shared.Restrictions;

public class UntillTpapiHelperImpl implements UntillTpapiHelper {
	static final Logger LOG = LoggerFactory.getLogger(UntillTpapiHelper.class);

	public final static int ORDER_ITEM_TYPE_NORMAL_ARTICLE = 0;
	public final static int ORDER_ITEM_TYPE_ARTICLE_MESSAGE = 6;

	private UntillLicensesPluginHelper untillLicensesPluginHelper;
	private CommonHelper commonHelper;

	public UntillTpapiHelperImpl(CommonHelper commonHelper, UntillLicensesPluginHelper untillLicensesPluginHelper) {
		this.commonHelper = commonHelper;
		this.untillLicensesPluginHelper = untillLicensesPluginHelper;
	}

	@Override
	public int createOrderOnNewTable(Connection conn, List<TOrderItem> order, long dealerId) throws Exception {
		long clientId = getClientIdByDealerId(conn, dealerId);

		UntillTpapiWrapper tpapi = createUntillTpapiWrapper(conn);

		TSalesAreaInfo salesAreaInfo = getSalesArea(conn, tpapi, dealerId, false);

		int tableNo = getFreeTableNo(conn, salesAreaInfo);

		// Create order
		tpapi.createOrder(tableNo, order.toArray(new TOrderItem[0]), clientId);

		return tableNo;
	}

	public long getActiveBillIdByTableNo(Connection conn, int tableNo) throws Exception {
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID FROM BILL WHERE TABLENO = ? AND ISACTIVE = 1")) {
			stmt.setInt(1, tableNo);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					throw new Exception(String.format("Created active BILL with TEBLENO = %d is not found", tableNo));
				return rs.getLong("ID");
			}
		}
	}

	@Override
	public void closeOrder(Connection conn, int tableNo) throws Exception {
		long paymentId = getPaymentId(conn);

		UntillTpapiWrapper tpapi = createUntillTpapiWrapper(conn);

		// Close order (pay)
		tpapi.closeOrder(tableNo, paymentId);
	}

	@Override
	public long orderAndPay(Connection conn, List<TOrderItem> order, long dealerId) throws Exception {
		long paymentId = getPaymentId(conn);
		long clientId = getClientIdByDealerId(conn, dealerId);

		UntillTpapiWrapper tpapi = createUntillTpapiWrapper(conn);

		TSalesAreaInfo salesAreaInfo = getSalesArea(conn, tpapi, dealerId, false);

		int tableNo = getFreeTableNo(conn, salesAreaInfo);

		// Create order
		tpapi.createOrder(tableNo, order.toArray(new TOrderItem[0]), clientId);

		long billId = getActiveBillIdByTableNo(conn, tableNo);

		// Close order (payment)
		tpapi.closeOrder(tableNo, paymentId);

		return billId;
	}

	// XXX duplicate: LicenseHandler.getArticleByBarcode()
	@Override
	public long getArticleIdByBarcode(Connection conn, String barcode) throws Exception {
		long articleId = getArticleIdByBarcodeIfExist(conn, barcode);
		if (articleId == 0)
			throw new Exception(String.format("Article with barcode '%s' is not found", barcode));
		return articleId;
	}

	@Override
	public long getArticleIdByBarcodeIfExist(Connection conn, String barcode) throws Exception {
		// TODO use TPAPI???
		try (PreparedStatement stmt = conn.prepareStatement("SELECT A.ID ID FROM ARTICLES A\n" +
				"INNER JOIN ARTICLE_BARCODES AB ON AB.ID_ARTICLES = A.ID\n" +
				"WHERE UPPER(AB.BARCODE) = UPPER(?)\n" +
				"  AND A.IS_ACTIVE = 1 AND AB.IS_ACTIVE = 1\n")
		) {
			stmt.setString(1, barcode);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					return 0;
				return rs.getLong("ID");
			}
		}
	}

	private UntillTpapiWrapper createUntillTpapiWrapper(Connection conn) throws Exception {
		Credential credential = getCredential(conn);

		// Connect to unTill TP-API
		ITPAPIPOS pos;
		try {
			pos = Common.getTPTPIPOS();
			if (pos == null)
				throw new Exception("TP-API is not enabled or is not correctly configured");
		} catch (ServiceException e) {
			throw new Exception("Error connecting to TP-API", e);
		}

		return new UntillTpapiWrapper(pos, credential);
	}

	// Find first user with TP-API password
	private Credential getCredential(Connection conn) throws Exception {
		try (
			PreparedStatement stmt = conn.prepareStatement("SELECT NAME, TP_API_PWD\n" +
					"FROM UNTILL_USERS WHERE IS_ACTIVE = 1\n" +
					"  AND TP_API_PWD IS NOT NULL AND TP_API_PWD <> ''\n");
			ResultSet rs = stmt.executeQuery();
		) {
			if (!rs.next())
				throw new Exception("Did not match any TP-API User");
			return new Credential(rs.getString("NAME"), rs.getString("TP_API_PWD"));
		}
	}

	// Find paymentId by kind (PAYMENT_ACCOUNT == 2)
	private long getPaymentId(Connection conn) throws Exception {
		try (
			PreparedStatement stmt = conn.prepareStatement("SELECT ID FROM PAYMENTS\n" +
					"WHERE KIND = 2 AND IS_ACTIVE = 1\n");
			ResultSet rs = stmt.executeQuery();
		) {
			if (!rs.next())
				throw new Exception("Payment with king 'Account' is not found");
			return rs.getLong("ID");
		}
	}

	private long getClientIdByDealerId(Connection conn, long dealerId) throws Exception {
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID_CLIENTS FROM DEALERS\n" +
				"WHERE ID = ? AND IS_DEALER_ACTIVE = 1\n")) {
			stmt.setLong(1, dealerId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					throw new Exception("Dealer is not found");
				return rs.getLong("ID_CLIENTS");
			}
		}
	}

	private int getFreeTableNo(Connection conn, TSalesAreaInfo salesAreaInfo) throws Exception {
		if (salesAreaInfo.getTables() == null || salesAreaInfo.getTables().length == 0)
			throw new Exception(String.format("No table range found for sales area (%s)", salesAreaInfo.getSalesAreaName()));

		// XXX: use only first table range
		int from = salesAreaInfo.getTables()[0].getFromTable();
		int to = salesAreaInfo.getTables()[0].getToTable();

		// TODO: check all tables in order starting from random
		Random rnd = new Random();
		int result;
		for (int i = 0; i < 1000; i++) {
			result = rnd.nextInt(to - from + 1) + from;
			try (PreparedStatement stmt = conn.prepareStatement("SELECT ID FROM BILL\n" +
					"WHERE TABLENO = ? AND CLOSE_DATETIME IS NULL\n")) {
				stmt.setInt(1, result);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next())
						return result;
				}
			}
		}
		throw new Exception(String.format("No free table found in sales area (%s)", salesAreaInfo.getSalesAreaName()));
	}

	protected static class OrderItem {
		int quantity;
		long articleId;
		boolean sameRenewalPrice;
		int baseDuration; // in years (0, 1, 3)
	}

	protected static class MothtsAndDays {
		public int months;
		public int days;
	}

	private final static double YEAR_TO_MONTH_RATIO = 5.0 / 100;
	private final static double YEAR_TO_MONTH_RATIO_2 = 1.0 / 12; // for articles with same price for initial purchase and renewal
	private final static double RATIO_4_BOOST = 12.0 / 10;
	private final static double MONTH_TO_DAY_RATIO = 1.0 / 31;

	@Override
	public void orderLicense(Connection conn, RequestType requestType, DbLicense license, DbLicense prevLicense)
			throws Exception {
		LOG.trace("ENTRY ({})", requestType);

		// port from LicensesPluginU.OrderLicense()

		// requirements for license (and prevLicense if requestType != CREATE):
		// - id
		// - all connections
		// - funcLimited
		// - status
		// - startDate issueDate, expiredDate
		// - idDealers
		// - requestType ignored (uses requestType parameter)
		// - largeAccount
		// TODO: check for filled in calling methods

		Date startDate = license.getStartDate();
		Date actualStartDate = startDate;
		if (license.getIssueDate().after(actualStartDate))
			actualStartDate = license.getIssueDate();
		Date expiredDate = license.getExpiredDate();

		// Check license status
		if (license.isNeverExpired())
			throw new Exception("Order license with status \"NO_EXPIRED_DATE\" is not available");
		if (license.isUnlimited())
			throw new Exception("Order license with status \"UNLIMITED\" is not available");
		if (license.isCanceled())
			throw new Exception("Order license with status \"CANCELED\" is not available");
		if (license.isTrial())
			throw new Exception("Order license with status \"TRIAL/DEMO\" is not available");
		if (license.isModuled() && license.isApostill())
			throw new Exception("Order license with status \"MODULED\" and \"APOSTILL\" is not available");
		if (license.isSaas() && license.isApostill())
			throw new Exception("Order license with status \"SAAS\" and \"APOSTILL\" is not available");
		if (license.isBoost()) {
			if (license.isApostill())
				throw new Exception("Order license with status \"BOOST\" and \"APOSTILL\" is not available");
			if (license.isSaas())
				throw new Exception("Order license with status \"BOOST\" and \"SAAS\" is not available");
			if (requestType != RequestType.CREATE_BOOST)
				throw new Exception("Order license with status \"BOOST\" available only for \"CREATE_BOOST\"");
		}

		// Get client id by dealer id
		long clientId = getClientIdByDealerId(conn, license.idDealers);

		// Get dealer bank order layout
		long orderLayoutId;
		try (PreparedStatement stmt = conn.prepareStatement(
				"SELECT BANKS.ID_ORDER_LAYOUT orderLayoutId\n" +
				"FROM BANKS INNER JOIN DEALERS ON DEALERS.ID_BANKS = BANKS.ID\n" +
				"WHERE DEALERS.ID = ?\n" +
				"  AND BANKS.IS_ACTIVE = 1 AND DEALERS.IS_DEALER_ACTIVE = 1\n"
		)) {
			stmt.setLong(1, license.idDealers);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					orderLayoutId = 0;
				else
					orderLayoutId = rs.getLong("orderLayoutId");
			}
		}

		// Create order items
		LOG.trace("create order items");
		List<OrderItem> orderItems = new ArrayList<>();

		// Determinate added license options (and fill article lists)
		if (requestType == RequestType.CREATE && !license.isBoost()) { // XXX do need to check for BOOST?

			LAddLicenseArticles(conn, orderItems, license, false);

		} else { // requestType == UPGRADE || PROLONG || CREATE_BOOST

			if (requestType == RequestType.PROLONG) {
				Date nextDayAfterExpiration = Common.dateAddDays(prevLicense.getExpiredDate(), 1);
				if (nextDayAfterExpiration.after(actualStartDate))
					actualStartDate = nextDayAfterExpiration;
			}

			// Check previous license status
			if (requestType == RequestType.PROLONG && prevLicense.isDefinitive())
				throw new Exception("Prolong license with status \"DEFINITIVE\" is not available");
			if (prevLicense.isNeverExpired())
				throw new Exception("Prolong, upgrade or boost license with status \"NO_EXPIRED_DATE\" is not available");
			if (prevLicense.isUnlimited())
				throw new Exception("Prolong, upgrade or boost license with status \"UNLIMITED\" is not available");
			if (prevLicense.isCanceled())
				throw new Exception("Prolong, upgrade or boost license with status \"CANCELED\" is not available");
			if (prevLicense.isTrial())
				throw new Exception("Prolong, upgrade or boost license with status \"TRIAL/DEMO\" is not available");
			if (prevLicense.isApostill() != license.isApostill())
				throw new Exception("Prolong, upgrade or boost license with change status \"APOSTILL\" is not available");
			if (prevLicense.isModuled() != license.isModuled())
				throw new Exception("Prolong, upgrade or boost license with change status \"MODULED\" is not available");
			if (license.isBoost()) {
				if (prevLicense.isDefinitive() && Restrictions.DEFINITIVE_DISABLE_BOOST)
					throw new Exception("Boost license with status \"DEFINITIVE\" is not available");
				if (prevLicense.isBoost())
					throw new Exception("Boost license with status \"BOOST\" is not available");
				if (prevLicense.isSaas())
					throw new Exception("Boost license with status \"SAAS\" is not available");
			}
			if (prevLicense.isSaas() != license.isSaas())
				throw new Exception("Prolong or upgrade license with change status \"SAAS\" is not available");

			if (requestType == RequestType.UPGRADE) {

				if (!LAddUpgradeLicenseArticles(conn, orderItems, prevLicense, license))
					throw new Exception("Upgrades for the license could not be found");

			} else if (requestType == RequestType.PROLONG) {

				if (license.isSaas()) {
					LAddLicenseArticles(conn, orderItems, license, false);
				} else {
					LAddUpgradeLicenseArticles(conn, orderItems, prevLicense, license);
					LAddLicenseArticles(conn, orderItems, license, true);
				}

			} else { // DESCRIPTION_BOOST_BIT

				DbLicense combLicense = new DbLicense();
				combLicense.setLbConnections((short) (prevLicense.getLbConnections() + license.getLbConnections()));
				combLicense.setRemConnections((short) (prevLicense.getRemConnections() + license.getRemConnections()));
				combLicense.setOmConnections((short) (prevLicense.getOmConnections() + license.getOmConnections()));
				//combLicense.setHqConnections((short) (prevLicense.getHqConnections() + license.getHqConnections()));
				combLicense.setPsConnections((short) (prevLicense.getPsConnections() + license.getPsConnections()));
				combLicense.setHeConnections((short) (prevLicense.getHeConnections() + license.getHeConnections()));
				combLicense.setFuncLimited(prevLicense.getFuncLimited() | license.getFuncLimited());
				combLicense.setStatus(license.getStatus());
				Set<String> driversSet = prevLicense.getDrivers() == null ? new LinkedHashSet<>()
						: new LinkedHashSet<>(prevLicense.getDrivers());
				if (license.getDrivers() != null)
					driversSet.addAll(license.getDrivers());
				combLicense.setDrivers(new ArrayList<>(driversSet));

				if (!LAddUpgradeLicenseArticles(conn, orderItems, prevLicense, combLicense))
					throw new Exception("Boosts for the license could not be found");

			}

		}

		// SaaS or Boost: calculating quantity of full months, and days in partial month(s)
		int monthsQty = 0;
		int daysQty = 0;
		if (license.isBoost()) {
			MothtsAndDays monthsAndDaysQty = calcMonthsAndDaysBetween(startDate, expiredDate);
			monthsQty = monthsAndDaysQty.months;
			daysQty = monthsAndDaysQty.days;
		} else if (license.isSaas()) {
			MothtsAndDays monthsAndDaysQty = calcMonthsAndDaysBetween(actualStartDate, expiredDate);
			monthsQty = monthsAndDaysQty.months;
			daysQty = monthsAndDaysQty.days;
		} else if (requestType == RequestType.UPGRADE) { // and not SaaS or Boost
			MothtsAndDays monthsAndDaysQty = calcMonthsAndDaysBetween(actualStartDate, expiredDate);
			monthsQty = monthsAndDaysQty.months;
			daysQty = monthsAndDaysQty.days;
		}

		// Create order via TPAPI-POS
		LOG.trace("create order via TPAPI-POS");
		UntillTpapiWrapper tpapi = createUntillTpapiWrapper(conn);

		TSalesAreaInfo salesAreaInfo = getSalesArea(conn, tpapi, license.idDealers, license.largeAccount);

		// XXX: price specified for dealer/client is not used
		// XXX: price exceptions is not used
		long priceId = salesAreaInfo.getPriceId();

		// Get free tableno
		int tableNo = getFreeTableNo(conn, salesAreaInfo);

		// Assemble order
		LOG.trace("assemble order");
		int itemNumber = 0;
		List<TOrderItem> order = new ArrayList<>();
		for (OrderItem orderItem : orderItems) {
			double baseArticlePrice = articlesCache.getArticlePrice(tpapi, orderItem.articleId, priceId);
			if ((license.isSaas() || license.isBoost() || requestType == RequestType.UPGRADE && orderItem.sameRenewalPrice)
					&& orderItem.baseDuration == 1) {
				// TODO: "BOOST", "SAAS", "UPGRADE" ???
				// XXX hard coded:
				String monthsQtyStr = monthsQty == 1 ?  "one month" : Integer.toString(monthsQty) + " months";
				String daysQtyStr = Integer.toString(daysQty) + (daysQty == 1 ? " day" : " days");

				double monthlyArticlePrice = baseArticlePrice * (orderItem.sameRenewalPrice ? YEAR_TO_MONTH_RATIO_2 : YEAR_TO_MONTH_RATIO);
				if (license.isBoost())
					monthlyArticlePrice *= RATIO_4_BOOST;
				double dailyArticlePrice = monthlyArticlePrice * MONTH_TO_DAY_RATIO;

				if (monthsQty > 0) {
					order.add(new TOrderItem(itemNumber++, orderItem.articleId, ORDER_ITEM_TYPE_NORMAL_ARTICLE, "",
							monthsQty * monthlyArticlePrice, orderItem.quantity,
							new TExtraInfo[] { new TExtraInfo("override_price", "", new TExtraInfo[0]) }));
					order.add(new TOrderItem(itemNumber++, 0, ORDER_ITEM_TYPE_ARTICLE_MESSAGE, monthsQtyStr,
							0, 0, new TExtraInfo[0]));
				}
				if (daysQty > 0) {
					order.add(new TOrderItem(itemNumber++, orderItem.articleId, ORDER_ITEM_TYPE_NORMAL_ARTICLE, "",
							daysQty * dailyArticlePrice, orderItem.quantity,
							new TExtraInfo[] { new TExtraInfo("override_price", "", new TExtraInfo[0]) }));
					order.add(new TOrderItem(itemNumber++, 0, ORDER_ITEM_TYPE_ARTICLE_MESSAGE, daysQtyStr,
							0, 0, new TExtraInfo[0]));
				}
			} else {
				order.add(new TOrderItem(itemNumber++, orderItem.articleId, ORDER_ITEM_TYPE_NORMAL_ARTICLE, "",
						baseArticlePrice, orderItem.quantity, new TExtraInfo[0]));
				if (orderItem.baseDuration == 1) {
					order.add(new TOrderItem(itemNumber++, 0, ORDER_ITEM_TYPE_ARTICLE_MESSAGE,
							"one year", 0, 0, new TExtraInfo[0]));
				} else if (orderItem.baseDuration > 1) {
					order.add(new TOrderItem(itemNumber++, 0, ORDER_ITEM_TYPE_ARTICLE_MESSAGE,
							"for " + Integer.toString(orderItem.baseDuration) + " years", 0, 0, new TExtraInfo[0]));
				}
			}
		}

		// Create order
		LOG.trace("create order");
		tpapi.createOrder(tableNo, order.toArray(new TOrderItem[0]), clientId);

		try {

			// Get order report (proforma)
			LOG.trace("get order report (proforma)");
			String orderReport = tpapi.printProforma(tableNo, orderLayoutId);

			// Get bill id by table no
			long billId = getActiveBillIdByTableNo(conn, tableNo);

			// Save order, tableno and bill id to current license in DB
			try (PreparedStatement stmt = conn.prepareStatement(
					"UPDATE LICENSES\n" +
					"SET REQUEST_PHASE = '2ORD',\n" +
					"    REQUEST_ACTIVE = 1,\n" +
					"    REQUEST_TABLENO = ?,\n" +
					"    REQUEST_ORDER = ?,\n" +
					"    ID_BILL = ?\n" +
					"WHERE ID = ?\n"
			)) {
				stmt.setInt(1, tableNo);
				stmt.setString(2, orderReport);
				stmt.setLong(3, billId);
				stmt.setLong(4, license.id);
				if (stmt.executeUpdate() == 0) throw new RuntimeException();
			}

		} catch (Exception e) {
			conn.rollback();

			try {
				untillLicensesPluginHelper.voidOrder(tableNo);
			} catch (Exception se) {
				e.addSuppressed(se);
			};

			throw e;
		};

		conn.commit();
		LOG.trace("RETURN");
	}

	private TSalesAreaInfo getSalesArea(Connection conn, UntillTpapiWrapper tpapi, long idDealers,
			boolean largeAccount) throws Exception {

		// get dealer sales area id from DB
		long dealerSalesAreaId = 0;
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID_SALES_AREA, ID_SALES_AREA_LA FROM DEALERS\n" +
				"WHERE ID = ? AND IS_DEALER_ACTIVE = 1\n")) {
			stmt.setLong(1, idDealers);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					throw new Exception(String.format("Dealer (%d) is not found", idDealers));
				dealerSalesAreaId = rs.getLong(largeAccount ? "ID_SALES_AREA_LA" : "ID_SALES_AREA" );
			}
		}

		TSalesAreaInfo[] salesAreaInfo = tpapi.getSalesAreasInfo();

		if (salesAreaInfo == null || salesAreaInfo.length == 0)
			throw new Exception("TPAPI-POS GetSalesAreasInfo: no sales area found");

		// find dealer sales area by id if specified
		if (dealerSalesAreaId != 0) {
			for (TSalesAreaInfo salesArea : salesAreaInfo) {
				if (salesArea.getSalesAreaId() == dealerSalesAreaId) {
					return salesArea;
				}
			}
			throw new Exception(String.format("TPAPI-POS GetSalesAreasInfo: no Dealer (%d) sales area (%d) found",
					idDealers, dealerSalesAreaId));
		}

		String settingsSalesAreaName = largeAccount ? commonHelper.getSettings().getSalesAreaForLA()
				: commonHelper.getSettings().getSalesArea();

		// find settings sales area
		if (settingsSalesAreaName != null && !settingsSalesAreaName.trim().isEmpty()) {
			for (TSalesAreaInfo salesArea : salesAreaInfo) {
				if (salesArea.getSalesAreaName().trim().equalsIgnoreCase(settingsSalesAreaName.trim())) {
					return salesArea;
				}
			}
			// try find by number or id
			try {
				long settingsSalesAreaNumberOrId = Long.parseLong(settingsSalesAreaName.trim());
				for (TSalesAreaInfo salesArea : salesAreaInfo) {
					if (salesArea.getSalesAreaId() == settingsSalesAreaNumberOrId
							|| salesArea.getSalesAreaNumber() == settingsSalesAreaNumberOrId) {
						return salesArea;
					}
				}
			} catch (NumberFormatException e) {
				// do nothing
			}
			throw new Exception(String.format("TPAPI-POS GetSalesAreasInfo: not found sales area from settings (%s)",
					settingsSalesAreaName));
		}

		// TODO use sales area with a minimum number and next for large accounts
		if (largeAccount && salesAreaInfo.length > 1)
			return salesAreaInfo[1];
		return salesAreaInfo[0];
	}

	private ArticlesCache articlesCache = new ArticlesCache();

	private static class ArticlesCache {

		private volatile String currentBoDbSignatire = null;

		private volatile Map<Long, String> articles = new HashMap<>();
		private volatile Map<Long, Map<Long, Double>> articlesPrices = new HashMap<Long, Map<Long,Double>>();

		private Instant lastUpdate = null;
		private static long MINIMUM_UPDATE_PERIOD_MS = 5000L; // TODO: use special system option: TPAPI_ARTICLES_UPDATE_PERIOD_MS

		public String getArticleName(UntillTpapiWrapper tpapi, long articleId) throws Exception {
			checkUpdate(tpapi);
			Map<Long, String> articles = this.articles;
			if (!articles.containsKey(articleId))
				throw new Exception(String.format("Article id (%d) not found in TPAPI GetArticlesInfo result",
						articleId));
			return articles.get(articleId);
		}

		public double getArticlePrice(UntillTpapiWrapper tpapi, long articleId, long priceId) throws Exception {
			checkUpdate(tpapi);
			Map<Long, Double> prices = articlesPrices.get(articleId);
			if (prices == null)
				throw new Exception(String.format("Article id (%d) not found in TPAPI GetArticlesInfo result",
						articleId));
			return prices.containsKey(priceId) ? prices.get(priceId).doubleValue() : 0;
		}

		private void checkUpdate(UntillTpapiWrapper tpapi) throws Exception {
			if (lastUpdate == null || Duration.between(lastUpdate, Instant.now()).toMillis() > MINIMUM_UPDATE_PERIOD_MS) {
				String curSign = currentBoDbSignatire;
				if (curSign == null || !curSign.equals(tpapi.getBOStatus()))
					doUpdate(tpapi);
				lastUpdate = Instant.now();
			}
		}

		private synchronized void doUpdate(UntillTpapiWrapper tpapi) throws Exception {
			String newBoDbSignatire = tpapi.getBOStatus();
			if (currentBoDbSignatire != null && currentBoDbSignatire.equals(newBoDbSignatire))
				return;
			TArticleInfo[] articleInfos = tpapi.getArticlesInfo();
			Map<Long, String> articles = new HashMap<>();
			Map<Long, Map<Long, Double>> articlesPrices = new HashMap<Long, Map<Long,Double>>();
			if (articleInfos != null) {
				for (TArticleInfo articleInfo : articleInfos) {
					articles.put(articleInfo.getArticleId(), articleInfo.getArticleName());
					Map<Long, Double> prices = new HashMap<Long, Double>();
					if (articleInfo.getPrices() != null) {
						for (TItemPrice price : articleInfo.getPrices()) {
							prices.put(price.getPriceId(), price.getAmount());
						}
					}
					articlesPrices.put(articleInfo.getArticleId(), prices);
				}
			}
			this.articles = articles;
			this.articlesPrices = articlesPrices;
			this.currentBoDbSignatire = newBoDbSignatire;
		}
	}

	protected void LAddLicenseArticles(Connection conn, List<OrderItem> orderItems, DbLicense license, boolean prolong) throws Exception {
		boolean isApostill = license.isApostill();
		boolean isModuled = license.isModuled();
		boolean funcHostedWM = license.getFunction(DbLicense.FUNC_WM_HOSTED);

		if (isApostill && license.getLbConnections() > 0) // Include one LB_CONNECTIONS and FUNC_CHIPKNIP
			LAddArticle(conn, orderItems, prolong, isApostill, "POSTILL");

		if (isApostill) {
			if (license.getLbConnections() > 1)
				LAddArticle(conn, orderItems, prolong, isApostill, String.format("%dLOC", license.getLbConnections()));
		} else {
			if (license.getLbConnections() > 0)
				if (isModuled) {
					for (int i = 0; i < DbLicense.FUNC_MODULES.length; i++) {
						if (license.getFunction(DbLicense.FUNC_MODULES[i]))
							LAddArticle(conn, orderItems, prolong, isApostill, String.format("%dMOD%s",
									license.getLbConnections(), DbLicense.SHORT_MODULE_NAMES[i]));
					}
				} else {
					LAddArticle(conn, orderItems, prolong, isApostill, String.format("%dLOC",
							license.getLbConnections()));
				}
		}
		if (license.getRemConnections() > 0)
			LAddArticle(conn, orderItems, prolong, isApostill, "REM", license.getRemConnections());
		if (license.getOmConnections() > 0)
			LAddArticle(conn, orderItems, prolong, isApostill, String.format("%dHH", license.getOmConnections()));
//		if (license.getHqConnections() > 0)
//			LAddArticle1(conn, orderItems, prolong, isApostill, "HQ", false, license.getHqConnections());
		if (license.getPsConnections() > 0)
			LAddArticle(conn, orderItems, prolong, isApostill, "PS", license.getPsConnections());
		if (license.getHeConnections() > 0)
			LAddArticle(conn, orderItems, prolong, isApostill, String.format("%dHE", license.getHeConnections()), true);

		if ((license.getFuncLimited() & ~((1L << DbLicense.ENABLED_FUNCTIONS_NUMBER) - 1)) != 0)
			throw new Exception("Requested an unknown function");
		if (license.getFunction(DbLicense.FUNC_TOOL)) {
			// Do nothing
			//throw new Exception("Function "Tool" did not match any article");
		};
		if (license.getFunction(DbLicense.FUNC_GLOBEEXACT)) LAddArticle(conn, orderItems, prolong, isApostill, "AI");
		if (license.getFunction(DbLicense.FUNC_HOTELCONCEPTS)) LAddArticle(conn, orderItems, prolong, isApostill, "HCI");

		if (license.getFunction(DbLicense.FUNC_BEVERAGE_CONTROL)
				|| license.getFunction(DbLicense.FUNC_BERG)
				|| license.getFunction(DbLicense.FUNC_BECONET)
				|| license.getFunction(DbLicense.FUNC_FRIJADO))
			LAddArticle(conn, orderItems, prolong, isApostill, "BAI");

		if (license.getFunction(DbLicense.FUNC_STOCK)) LAddArticle(conn, orderItems, prolong, isApostill, "STOCK");
		if (license.getFunction(DbLicense.FUNC_KITCHENSCREEN)) LAddArticle(conn, orderItems, prolong, isApostill, "KS");
		if (!isApostill)
			if (license.getFunction(DbLicense.FUNC_EFT_INTERFACE)) LAddArticle(conn, orderItems, prolong, isApostill, "CPINI");
		if (license.getFunction(DbLicense.FUNC_RESERVATIONS)) LAddArticle(conn, orderItems, prolong, isApostill, "RESERV");
		if (license.getFunction(DbLicense.FUNC_MUSICPLAYER)) LAddArticle(conn, orderItems, prolong, isApostill, "MPLAYER");
		if (license.getFunction(DbLicense.FUNC_EMENU)) LAddArticle(conn, orderItems, prolong, isApostill, "EMENU");

		if (license.getFunction(DbLicense.FUNC_WM_REST)) LAddArticle(conn, orderItems, prolong, isApostill, "WMREST");
		if (license.getFunction(DbLicense.FUNC_WM_OFF)) LAddArticle(conn, orderItems, prolong, isApostill, "WMOFF");
		if (license.getFunction(DbLicense.FUNC_WM_HQ))
			if (!funcHostedWM) LAddArticle(conn, orderItems, prolong, isApostill, "WMHQ");
			else LAddArticleIfExist(conn, orderItems, prolong, isApostill, "WMHQWH");
		if (license.getFunction(DbLicense.FUNC_RESERVE1)) LAddArticle(conn, orderItems, prolong, isApostill, "RES1");
		if (license.getFunction(DbLicense.FUNC_RESERVE2)) LAddArticle(conn, orderItems, prolong, isApostill, "RES2");
		if (license.getFunction(DbLicense.FUNC_RESERVE3)) LAddArticle(conn, orderItems, prolong, isApostill, "RES3");

		if (license.getFunction(DbLicense.FUNC_RENTAL)) LAddArticle(conn, orderItems, prolong, isApostill, "RENTAL");
		if (license.getFunction(DbLicense.FUNC_TP_API_POS)) LAddArticle(conn, orderItems, prolong, isApostill, "TPAPI");
		if (license.getFunction(DbLicense.FUNC_TP_API_RES)) LAddArticle(conn, orderItems, prolong, isApostill, "TPAPIRES");

		if (license.getFunction(DbLicense.FUNC_PROTEL)) LAddArticle(conn, orderItems, prolong, isApostill, "PROTEL");

		// skip FUNC_PERMANENT_JLOG

		if (license.getFunction(DbLicense.FUNC_ANNONCER_KS)) LAddArticle(conn, orderItems, prolong, isApostill, "AKS");

		// already handled above:
		// FUNC_MODULE_BASIC,
		// FUNC_MODULE_A,
		// FUNC_MODULE_B,
		// FUNC_MODULE_C,
		// FUNC_MODULE_D,
		// FUNC_MODULE_E,
		// FUNC_MODULE_F

		if (license.getFunction(DbLicense.FUNC_WM_CENTRAL_BO))
			if (!funcHostedWM) LAddArticle(conn, orderItems, prolong, isApostill, "WMCBO");
			else LAddArticleIfExist(conn, orderItems, prolong, isApostill, "WMCBOWH");
		if (license.getFunction(DbLicense.FUNC_WM_HOSTED)) LAddArticle(conn, orderItems, prolong, isApostill, "HWM");

		// skip FUNC_FRENCH_FDM

//		if (license.getFunction(DbLicense.FUNC_TAKEAWAY_COM)) LAddArticle(conn, orderItems, prolong, isApostill, "TAKEAWAY", true);
//		if (license.getFunction(DbLicense.FUNC_SEVENROOMS)) LAddArticle(conn, orderItems, prolong, isApostill, "SEVENROOMS", true);
//		if (license.getFunction(DbLicense.FUNC_NETRESTO)) LAddArticle(conn, orderItems, prolong, isApostill, "NETRESTO", true);

		// skip FUNC_PIGGY

//		if (license.getFunction(DbLicense.FUNC_CLOCK_PMS)) LAddArticle(conn, orderItems, prolong, isApostill, "CLOCKPMS", true);
//		if (license.getFunction(DbLicense.FUNC_GUESTLINE)) LAddArticle(conn, orderItems, prolong, isApostill, "GUESTLINE", true);
//		if (license.getFunction(DbLicense.FUNC_DINETIME)) LAddArticle(conn, orderItems, prolong, isApostill, "DINETIME", true);
//		if (license.getFunction(DbLicense.FUNC_GLORY_MONEY_MACHINE)) LAddArticle(conn, orderItems, prolong, isApostill, "GLORYMM", true);

		if (license.getFunction(DbLicense.FUNC_TP_API_AM)) LAddArticle(conn, orderItems, prolong, isApostill, "TPAPIAM", true);
		if (license.getFunction(DbLicense.FUNC_TAPP)) LAddArticle(conn, orderItems, prolong, isApostill, "TAPP", true);

//		if (license.getFunction(DbLicense.FUNC_INTERSOLVE)) LAddArticle(conn, orderItems, prolong, isApostill, "INTERSOLVE", true);
//		if (license.getFunction(DbLicense.FUNC_ADORIA)) LAddArticle(conn, orderItems, prolong, isApostill, "ADORIA", true);
//		if (license.getFunction(DbLicense.FUNC_APALEO)) LAddArticle(conn, orderItems, prolong, isApostill, "APALEO", true);
//		if (license.getFunction(DbLicense.FUNC_XAFAX)) LAddArticle(conn, orderItems, prolong, isApostill, "XAFAX", true);

		if (license.getFunction(DbLicense.FUNC_FISCALIZATION)) LAddArticle(conn, orderItems, prolong, isApostill, "FISCALIZATION", true);

//		if (license.getFunction(DbLicense.FUNC_SHIJI)) LAddArticle(conn, orderItems, prolong, isApostill, "SHIJI", true);
//		if (license.getFunction(DbLicense.FUNC_TFLEX)) LAddArticle(conn, orderItems, prolong, isApostill, "TFLEX", true);
//		if (license.getFunction(DbLicense.FUNC_ASA)) LAddArticle(conn, orderItems, prolong, isApostill, "ASA", true);
//		if (license.getFunction(DbLicense.FUNC_LOYYO)) LAddArticle(conn, orderItems, prolong, isApostill, "LOYYO", true);
		if (license.getFunction(DbLicense.FUNC_DRGT)) LAddArticle(conn, orderItems, prolong, isApostill, "DRGT", true);

//		if (license.getFunction(DbLicense.FUNC_SENTOO)) LAddArticle(conn, orderItems, prolong, isApostill, "SENTOO", true);
//		if (license.getFunction(DbLicense.FUNC_COMO)) LAddArticle(conn, orderItems, prolong, isApostill, "COMO", true);
//		if (license.getFunction(DbLicense.FUNC_ZENCHEF)) LAddArticle(conn, orderItems, prolong, isApostill, "ZENCHEF", true);
//
//		if (license.getFunction(DbLicense.FUNC_OPERA_CLOUD)) LAddArticle(conn, orderItems, prolong, isApostill, "OPERACLOUD", true);
//		if (license.getFunction(DbLicense.FUNC_WORLDLINE_TIM)) LAddArticle(conn, orderItems, prolong, isApostill, "WORLDLINETIM", true);
//
//		if (license.getFunction(DbLicense.FUNC_KLEARLYCLOUD)) LAddArticle(conn, orderItems, prolong, isApostill, "KLEARLYCLOUD", true);
//		if (license.getFunction(DbLicense.FUNC_ACCOUNTILL)) LAddArticle(conn, orderItems, prolong, isApostill, "ACCOUNTILL", true);
		// todo: update MODULE_BARCODE_BASES

		// Add articles for drivers
		for (int f = 0; f < DbLicense.ENABLED_FUNCTIONS_NUMBER; f++) {
			DriverInfo[] driverInfos = DbLicense.getDriverInfosByFunc(f);
			if (driverInfos.length > 0 && license.getFunction(f)) {
				for (DriverInfo driverInfo : driverInfos) {
					if (driverInfo.getBarcodeBase() == null || driverInfo.getBarcodeBase().isEmpty())
						continue; // skip drivers without barcode base
					LAddArticle(conn, orderItems, prolong, isApostill, driverInfo.getBarcodeBase(), driverInfo.hasSameRenewalPrice());
				}
			}
		}
		if (license.getDrivers() != null) {
			for (String driverId : license.getDrivers()) {
				DriverInfo driverInfo = DbLicense.getDriverInfoByDriverId(driverId);
				if (driverInfo == null)
					throw new Exception("Unknown driver id: " + driverId);
				if (driverInfo.getFuncId() != -1)
					continue; // skip drivers with function id, they are already handled above
				if (driverInfo.getBarcodeBase() == null || driverInfo.getBarcodeBase().isEmpty())
					continue; // skip drivers without barcode base
				LAddArticle(conn, orderItems, prolong, isApostill, driverInfo.getBarcodeBase(), driverInfo.hasSameRenewalPrice());
			}
		}

		// Check status
		if (license.isNeverExpired())
			throw new Exception("Status \"NO_EXPIRED_DATE\" did not match any article");
		if (license.isUnlimited())
			throw new Exception("Status \"UNLIMITED\" did not match any article");

		if (license.isDefinitive()) LAddArticle(conn, orderItems, "NU", false, 1, 0);
	};

	private void LAddArticle(Connection conn, List<OrderItem> orderItems, boolean prolong, boolean apostill,
			String barCodeBase) throws Exception {
		LAddArticle(conn, orderItems, prolong, apostill, barCodeBase, false, 1);
	}

	private void LAddArticle(Connection conn, List<OrderItem> orderItems, boolean prolong, boolean apostill,
			String barCodeBase, boolean sameRenewalPrice) throws Exception {
		LAddArticle(conn, orderItems, prolong, apostill, barCodeBase, sameRenewalPrice, 1);
	}

	private void LAddArticle(Connection conn, List<OrderItem> orderItems, boolean prolong, boolean apostill,
			String barCodeBase, int quantity) throws Exception {
		LAddArticle(conn, orderItems, prolong, apostill, barCodeBase, false, quantity);
	}

	private void LAddArticle(Connection conn, List<OrderItem> orderItems, boolean prolong, boolean apostill,
			String barCodeBase, boolean sameRenewalPrice, int quantity) throws Exception {
		String prefix = (prolong ? "U" : "") + (apostill ? "A" : "");
		String postfix = (apostill ? "3Y" : "1Y");
		LAddArticle(conn, orderItems, prefix + barCodeBase + postfix, sameRenewalPrice, quantity, apostill ? 3 : 1);
	}

	private void LAddArticle(Connection conn, List<OrderItem> orderItems, String barCode, boolean sameRenewalPrice,
			int quantity, int baseDuration) throws Exception {
		OrderItem orderItem = new OrderItem();
		orderItem.quantity = quantity;
		orderItem.articleId = getArticleIdByBarcode(conn, barCode);
		orderItem.sameRenewalPrice = sameRenewalPrice;
		orderItem.baseDuration = baseDuration;
		orderItems.add(orderItem);
	}

	private void LAddArticleIfExist(Connection conn, List<OrderItem> orderItems, boolean prolong, boolean apostill,
			String barCodeBase) throws Exception {
		String prefix = (prolong ? "U" : "") + (apostill ? "A" : "");
		String postfix = (apostill ? "3Y" : "1Y");
		long articleId = getArticleIdByBarcodeIfExist(conn, prefix + barCodeBase + postfix);
		if (articleId == 0)
			return;
		OrderItem orderItem = new OrderItem();
		orderItem.quantity = 1;
		orderItem.articleId = articleId;
		orderItem.sameRenewalPrice = false;
		orderItem.baseDuration = apostill ? 3 : 1;
		orderItems.add(orderItem);
	}

	private boolean LAddUpgradeLicenseArticles(Connection conn, List<OrderItem> orderItems, DbLicense prevLicense, DbLicense license) throws Exception {
		boolean isApostill = license.isApostill();
		boolean isModuled = license.isModuled();
		DbLicense diffLicense = new DbLicense();

		// Get added license options
		if (license.getLbConnections() > prevLicense.getLbConnections()) {
			// if this is Apostill license, then first lbCon is not minus
			if (isApostill) {
				if (prevLicense.getLbConnections() > 1)
					LAddArticle(conn, orderItems, false, isApostill, String.format("%dLOC", prevLicense.getLbConnections()), -1);
			} else {
				if (prevLicense.getLbConnections() > 0) {
					if (isModuled) {
						for (int i = 0; i < DbLicense.FUNC_MODULES.length; i++) {
							if (prevLicense.getFunction(DbLicense.FUNC_MODULES[i])) {
								LAddArticle(conn, orderItems, false, isApostill, String.format("%dMOD%s",
										prevLicense.getLbConnections(), DbLicense.SHORT_MODULE_NAMES[i]), -1);
							};
						};
					} else {
						LAddArticle(conn, orderItems, false, isApostill, String.format("%dLOC", prevLicense.getLbConnections()), -1);
					};
				};
			};
			diffLicense.setLbConnections(license.getLbConnections());
		} else
			diffLicense.setLbConnections((short) 0);

		diffLicense.setRemConnections((short) (license.getRemConnections() - prevLicense.getRemConnections()));

		if ((license.getOmConnections() > prevLicense.getOmConnections())) {
			if (prevLicense.getOmConnections() > 0)
				LAddArticle(conn, orderItems, false, isApostill, String.format("%dHH", prevLicense.getOmConnections()), -1);
			diffLicense.setOmConnections(license.getOmConnections());
		} else
			diffLicense.setOmConnections((short) 0);

		//diffLicense.setHqConnections((short) (license.getHqConnections() - prevLicense.getHqConnections()));

		diffLicense.setPsConnections((short) (license.getPsConnections() - prevLicense.getPsConnections()));

		if ((license.getHeConnections() > prevLicense.getHeConnections())) {
			if (prevLicense.getHeConnections() > 0)
				LAddArticle(conn, orderItems, false, isApostill, String.format("%dHE", prevLicense.getHeConnections()), true, -1);
			diffLicense.setHeConnections(license.getHeConnections());
		} else
			diffLicense.setHeConnections((short) 0);

		long funcModuleMask = 0;
		for (int i = 0; i < DbLicense.FUNC_MODULES.length; i++)
			funcModuleMask |= 1L << DbLicense.FUNC_MODULES[i];

		diffLicense.setFuncLimited(license.getFuncLimited() & ~adjustFuncLim(prevLicense.getFuncLimited()));
		if (isApostill || !isModuled)
			diffLicense.setFuncLimited(diffLicense.getFuncLimited() & ~funcModuleMask);
		else if (diffLicense.getLbConnections() > 0)
			diffLicense.setFuncLimited(diffLicense.getFuncLimited() | license.getFuncLimited() & funcModuleMask);
		else if ((diffLicense.getFuncLimited() & funcModuleMask) != 0)
			diffLicense.setLbConnections(license.getLbConnections());

		if (license.getFunction(DbLicense.FUNC_WM_HOSTED) && !prevLicense.getFunction(DbLicense.FUNC_WM_HOSTED)) {
			if (prevLicense.getFunction(DbLicense.FUNC_WM_HQ)) {
				LAddArticle(conn, orderItems, false, isApostill, "WMHQ", -1);
				LAddArticleIfExist(conn, orderItems, false, isApostill, "WMHQWH");
			};
			if (prevLicense.getFunction(DbLicense.FUNC_WM_CENTRAL_BO)) {
				LAddArticle(conn, orderItems, false, isApostill, "WMCBO", -1);
				LAddArticleIfExist(conn, orderItems, false, isApostill, "WMCBOWH");
			};
		};

		// Simple copy status (DESCRIPTION_APOSTILL_BIT and DESCRIPTION_MODULED_BIT must be copied)
		diffLicense.setStatus(license.getStatus());

		// remove DESCRIPTION_DEFINITIVE_BIT if already exist
		if (prevLicense.isDefinitive())
			diffLicense.setDefinitive(false);

		long funcSkippedMask = (1L << DbLicense.FUNC_PERMANENT_JLOG) | (1L << DbLicense.FUNC_FRENCH_FDM)
				| (1L << DbLicense.FUNC_PIGGY);

		LAddLicenseArticles(conn, orderItems, diffLicense, false);

		return diffLicense.getLbConnections() > 0
				|| diffLicense.getRemConnections() > 0
				|| diffLicense.getOmConnections() > 0
//				|| diffLicense.getHqConnections() > 0
				|| diffLicense.getPsConnections() > 0
				|| diffLicense.getHeConnections() > 0
				|| (diffLicense.getFuncLimited() & ~funcSkippedMask) != 0
				|| diffLicense.isDefinitive();
	}

    public static MothtsAndDays calcMonthsAndDaysBetween(Date startDate, Date endDateIncl) {
		LocalDate locStartDate = Common.convDateToLocalDate(startDate);
		LocalDate locEndDateIncl = Common.convDateToLocalDate(endDateIncl).plusDays(1);

		Period period = Period.between(locStartDate, locEndDateIncl);
        int months = (int) period.toTotalMonths();
        int days = period.getDays();

		MothtsAndDays result = new MothtsAndDays();
		result.months = months;
		result.days = days;
		return result;
    }

	private long adjustFuncLim(long funcLim) {
		long result = funcLim;
		if ((funcLim & (1L << DbLicense.FUNC_BEVERAGE_CONTROL)) != 0
				|| (funcLim & (1L << DbLicense.FUNC_BERG)) != 0
				|| (funcLim & (1L << DbLicense.FUNC_BECONET)) != 0
				|| (funcLim & (1L << DbLicense.FUNC_FRIJADO)) != 0) {
			result |= (1L << DbLicense.FUNC_BEVERAGE_CONTROL)
					| (1L << DbLicense.FUNC_BERG)
					| (1L << DbLicense.FUNC_BECONET)
					| (1L << DbLicense.FUNC_FRIJADO);
		}
		result |= 1L << DbLicense.FUNC_MUSICPLAYER;
		return result;
	}

	@Override
	public void addArticleToTable(Connection conn, int tableNo, long dealerId, long articleId, int quantity,
			double manualPrice, String text) throws Exception {
		long clientId = getClientIdByDealerId(conn, dealerId);

		UntillTpapiWrapper tpapi = createUntillTpapiWrapper(conn);

		List<TOrderItem> items = new ArrayList<TOrderItem>();
		int itemNumber = 0;
		items.add(new TOrderItem(itemNumber++, articleId, ORDER_ITEM_TYPE_NORMAL_ARTICLE, "", manualPrice, quantity,
				new TExtraInfo[0]));
		if (text != null && !text.isEmpty())
			items.add(new TOrderItem(itemNumber++, 0, ORDER_ITEM_TYPE_ARTICLE_MESSAGE, text, 0, 0, new TExtraInfo[0]));

		tpapi.createOrder(tableNo, items.toArray(new TOrderItem[0]), clientId);
	}

	private static final String[] MODULE_BARCODE_BASES = { "AI", "HCI", "BAI", "STOCK", "KS", "CPINI", "RESERV",
			"MPLAYER", "EMENU", "WMREST", "WMOFF", "WMHQ", "WMHQWH", "RES1", "RES2", "RES3", "RENTAL", "TPAPI",
			"TPAPIRES", "PROTEL", "AKS", "WMCBO", "WMCBOWH", "HWM", "TAKEAWAY", "SEVENROOMS", "NETRESTO", "CLOCKPMS",
			"GUESTLINE", "DINETIME", "GLORYMM", "TPAPIAM", "TAPP", "INTERSOLVE", "ADORIA", "APALEO", "XAFAX",
			"FISCALIZATION", "SHIJI", "TFLEX", "ASA", "LOYYO", "DRGT", "SENTOO", "COMO", "ZENCHEF", "OPERACLOUD",
			"WORLDLINETIM", "KLEARLYCLOUD", "ACCOUNTILL" };

	private void lAddBarCodesWithProlong(List<String> allBarCodes, String barCodeBase) {
		String bcPostfix = "1Y";
		String bcPrefix = "";
		allBarCodes.add(bcPrefix + barCodeBase + bcPostfix);
		bcPrefix = "U";
		allBarCodes.add(bcPrefix + barCodeBase + bcPostfix);
	}

	@Override
	public List<ArticleWithPrices> getDealerPrices(Connection conn, long dealerId) throws Exception {

		UntillTpapiWrapper tpapi = createUntillTpapiWrapper(conn);

		TSalesAreaInfo salesAreaInfo = getSalesArea(conn, tpapi, dealerId, false);
		TSalesAreaInfo salesAreaInfoLA = getSalesArea(conn, tpapi, dealerId, true);

		long priceId = salesAreaInfo.getPriceId();
		long priceIdLA = salesAreaInfoLA.getPriceId();

		List<String> allBarCodes = new ArrayList<>();

		for (int i = 1; i <= 25; i++)
			lAddBarCodesWithProlong(allBarCodes, String.format("%dLOC", i));
		lAddBarCodesWithProlong(allBarCodes, "REM");
		for (int i = 1; i <= 23; i++)
			lAddBarCodesWithProlong(allBarCodes, String.format("%dHH", i));
		lAddBarCodesWithProlong(allBarCodes, "PS");
		for (int i = 1; i <= 23; i++)
			lAddBarCodesWithProlong(allBarCodes, String.format("%dHE", i));

		for (String barcodeBase : MODULE_BARCODE_BASES)
			lAddBarCodesWithProlong(allBarCodes, barcodeBase);

		for (DriverInfo driverInfo : DbLicense.DRIVER_INFOS) {
			if (driverInfo.getFuncId() != -1)
				continue; // skip drivers with function id, they are already handled above
			if (driverInfo.getBarcodeBase() != null && !driverInfo.getBarcodeBase().isEmpty())
				lAddBarCodesWithProlong(allBarCodes, driverInfo.getBarcodeBase());
		}

		allBarCodes.add("NU");

		for (int m = 0; m < DbLicense.FUNC_MODULES.length; m++)
			for (int i = 1; i <= 25; i++)
				lAddBarCodesWithProlong(allBarCodes, String.format("%dMOD%s", i, DbLicense.SHORT_MODULE_NAMES[m]));

		List<ArticleWithPrices> articlesPrices = new ArrayList<>();

		for (String barCode : allBarCodes) {
			long articleId = getArticleIdByBarcodeIfExist(conn, barCode);
			if (articleId != 0) {
				ArticleWithPrices articlePrices = new ArticleWithPrices();
				articlePrices.articleName = articlesCache.getArticleName(tpapi, articleId);
				articlePrices.price = articlesCache.getArticlePrice(tpapi, articleId, priceId);
				articlePrices.priceLA = articlesCache.getArticlePrice(tpapi, articleId, priceIdLA);
				articlesPrices.add(articlePrices);
			}
		}

		return articlesPrices;
	}

}
