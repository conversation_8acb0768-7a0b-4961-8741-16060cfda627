package eu.untill.license.client.ui;

import java.util.List;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PasswordTextBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.Common;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.shared.ArticleWithPrices;

public class ActualPricesDialog extends DialogBox implements ClickHandler {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Actual prices")
		String actualPricesDialogHeader();

//		@DefaultStringValue("Show Large Account prices")
//		String showLargeAccountPricesLabel();

		@DefaultStringValue("Export")
		String exportButton();
		String closeButton();

		@DefaultStringValue("Article")
		String articleColumnHeader();
		@DefaultStringValue("Price")
		String priceColumnHeader();
		@DefaultStringValue("Large Account Price")
		String laPriceColumnHeader();

		@DefaultStringValue("Dealer")
		String dealerColumnHeader();
		String clientNameColumnHeader();
		String hardCodeColumnHeader();
		String issueDateColumnHeader();
		@DefaultStringValue("Before")
		String beforeChangeColumnHeader();
		@DefaultStringValue("After")
		String afterChangeColumnHeader();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	  /////////////
	 // Members //
	/////////////

	private long dealerId;
	private boolean passwordRequired;
	private String csv;

	  //////////////////
	 // Constructors //
	//////////////////

	public ActualPricesDialog(long dealerId, boolean passwordRequired) {
		this.dealerId = dealerId;
		this.passwordRequired = passwordRequired;

		// Generate base interface
		generateUI();

		this.setText(constants.actualPricesDialogHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnPassword = new EntitledDecoratorPanel();
	private FlexTable tblPassword = new FlexTable();
	private Label lblPassword = new Label("Password" + ":", false); // TODO: to constants
	private PasswordTextBox txtPassword = new PasswordTextBox();
	private CheckBox chkShowHostRegPass = new CheckBox("Show");  // TODO to constants
	private Button btnRequest = new Button("Request", this); // TODO: to constants

//	private EntitledDecoratorPanel dpnParameters = new EntitledDecoratorPanel();
//	private FlexTable tblParameters = new FlexTable();
//	private CheckBox chkShowLargeAccountPrices = new CheckBox(constants.showLargeAccountPricesLabel() + ":");

	private EntitledDecoratorPanel dpnData = new EntitledDecoratorPanel(/*constants.clientNameChangesLabel()*/);
	private FlexTable tblData = new FlexTable();
	private ScrollPanel scrData = new ScrollPanel(tblData);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnExport = new Button(constants.exportButton(), this);
	private Button btnClose = new Button(constants.closeButton(), this);

	private void generateUI() {

//		// Assemble parameters panel
//		chkShowLargeAccountPrices.addValueChangeHandler(event -> {
//			requestChanges();
//		});
//		int row = 0;
//		tblParameters.setWidget(row++, 0, chkShowLargeAccountPrices);
//		dpnParameters.setWidget(tblParameters);

		// Assemble password panel
		txtPassword.setWidth("20em");
		txtPassword.getElement().setAttribute("autocomplete", "off");
		txtPassword.getElement().setId("prices-password");
		btnRequest.setWidth("80px");
		chkShowHostRegPass.addClickHandler(event -> txtPassword.getElement().setAttribute("type",
				chkShowHostRegPass.getValue() ? "text" : "password"));
		tblPassword.setWidget(0, 0, lblPassword);
		tblPassword.setWidget(0, 1, txtPassword);		
		tblPassword.setWidget(0, 2, chkShowHostRegPass);
		tblPassword.setWidget(0, 3, btnRequest);
		dpnPassword.add(tblPassword);
		
		// Create data table
		tblData.addStyleName("al-Table"); 
		tblData.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblData.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "400px");
		tblData.setText(0, col++, constants.articleColumnHeader());
		tableCF.setWidth(0, col, "100px");
		tblData.setText(0, col++, constants.priceColumnHeader());
		tableCF.setWidth(0, col, "100px");
		tblData.setText(0, col++, constants.laPriceColumnHeader());

		// Assemble order panel with scroll
		scrData.setSize("600", "200px");
		dpnData.setWidget(scrData);

		// Assemble button panel
		btnExport.setWidth("80px");
		btnClose.setWidth("80px");
		pnlButtons.setWidth("100%");
		pnlButtons.add(btnExport);
		pnlButtons.setCellHorizontalAlignment(btnExport, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.setCellWidth(btnExport, "100%");
		pnlButtons.add(btnClose);
		pnlButtons.setCellHorizontalAlignment(btnClose, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		// Assemble main panel
//		mainPanel.add(dpnParameters);
		if (passwordRequired)
			mainPanel.add(dpnPassword);
		mainPanel.add(dpnData);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnRequest) {

			requestData();
			
		} else if (sender == btnExport) {

			Common.export(csv, "export.csv", "text/csv");

		} else if (sender == btnClose) {

			this.hide();
			
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void requestData() {
		WaitDialog.show();
		UntillLicenseServer.getLicenseServiceAsync().getDealerPrices(
				UntillLicenseServer.getAuthScheme(), dealerId, txtPassword.getValue(),
				new AsyncCallback<List<ArticleWithPrices>>() {
					
					@Override
					public void onSuccess(List<ArticleWithPrices> result) {
						WaitDialog.hide();
						refreshData(result);
					}
					
					@Override
					public void onFailure(Throwable caught) {
						WaitDialog.hideAll();
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void refreshData(List<ArticleWithPrices> data) {
		csv = Common.convertToCSV(constants.articleColumnHeader(), constants.priceColumnHeader(),
				constants.laPriceColumnHeader()) + "\r\n";
		scrData.remove(tblData);
		try {
			for (int r = tblData.getRowCount() - 1; r > 0; --r)
				tblData.removeRow(r);
			fillData(data);
		} finally {
			scrData.add(tblData);
		}
	}

	private void fillData(List<ArticleWithPrices> data) {
		RowFormatter tblChangesRF = tblData.getRowFormatter();
		FlexCellFormatter tblChangesCF = tblData.getFlexCellFormatter();

		int row = 1;

		if (data == null)
			return;

		StringBuilder scvB = new StringBuilder(csv);
		
		for (ArticleWithPrices art : data) {
			scvB.append(Common.convertToCSV(art.articleName, Double.toString(art.price), Double.toString(art.priceLA)) + "\r\n");

			// set row style classes
			tblChangesRF.addStyleName(row, "ll-Row");
	
			// fill row
			int col = 0;
			tblData.setText(row, col++, art.articleName);
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_RIGHT);
			tblData.setText(row, col++, Double.toString(art.price));
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_RIGHT);
			tblData.setText(row, col++, Double.toString(art.priceLA));
			++row;
		}

		csv = scvB.toString();
	}
	
	
	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		//btnClose.setFocus(true);
		if (passwordRequired)
			txtPassword.setFocus(true);
		else
			requestData();
	}

}
