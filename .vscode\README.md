# VS Code Configuration for UntillLicenseServer

This directory contains VS Code configuration files converted from Eclipse launch configuration.

## Files

### launch.json
Contains debug/run configurations:
- **UntillLicenseServer - GWT DevMode**: Direct launch of GWT DevMode
- **UntillLicenseServer - Gradle Run**: Launch with <PERSON>radle build pre-task

### tasks.json
Contains Gradle tasks:
- `gradle: build` - Build the project
- `gradle: clean` - Clean build artifacts
- `gradle: compileJava` - Compile Java sources
- `gradle: test` - Run tests
- `gradle: gwtDev` - Run GWT development mode

### settings.json
Project-specific VS Code settings:
- Java configuration
- Source paths and output directories
- File exclusions for build artifacts
- Code formatting settings

## Usage

1. **Install Required Extensions**:
   - Extension Pack for Java (Microsoft)
   - Gradle for Java (Microsoft)

2. **Open Project**:
   - Open the project folder in VS Code
   - VS Code should automatically detect the Java project structure

3. **Run/Debug**:
   - Press `F5` or go to Run and Debug view
   - Select "UntillLicenseServer - GWT DevMode" configuration
   - Click the play button

4. **Build Tasks**:
   - Press `Ctrl+Shift+P` and type "Tasks: Run Task"
   - Select any of the available Gradle tasks

## Original Eclipse Configuration

The original Eclipse launch file `UntillLicenseServer.launch` contained:
- Main class: `com.google.gwt.dev.DevMode`
- Program arguments: `-war war -startupUrl UntillLicenseServer.html eu.untill.license.UntillLicenseServer`
- VM arguments: `-Xmx1g`
- Custom classpath with GWT dependencies

## Notes

- The VS Code configuration assumes you're using Gradle for dependency management
- Make sure to run `./gradlew build` at least once before debugging
- GWT DevMode will start a local server, typically on http://localhost:8888
- The startup URL will be automatically opened in your default browser
