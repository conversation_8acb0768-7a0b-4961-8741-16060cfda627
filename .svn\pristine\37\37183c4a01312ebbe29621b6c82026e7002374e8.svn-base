/** Common ********************************************************************/

body, table td, select {
	font-size: 12px;
}	

button, input, div {
	font-family: Arial Unicode MS, Arial, sans-serif;
	font-size: 12px;
}

body, html {
	height: 100%;
}

pre {
	font-size: 12px;
}

body {
	background: #eeeeee;
	/*overflow:hidden;*/
}

.NoWordWrap {
	white-space: nowrap;
}


/* - Anchor ----------------------------------------------------------------- */

a {
	color: #0000aa;
}
a:link {
	color: #0000cc;
}
a:visited {
	color: #551a8b;
}
a:active {
	color: #ff0000;
}


/* - Button ----------------------------------------------------------------- */

.gwt-<PERSON><PERSON> {
	margin: 0;
	padding: 3px 5px 3px 5px;
	text-decoration: none;
	cursor: pointer;
	cursor: hand;
	background: url("images/hborder.png") repeat-x 0px -27px;
	border: 1px solid #cccccc;
/*	border: 1px outset #ccc;*/
}
.gwt-Button:active {
	padding: 4px 4px 2px 6px;
/*	border: 1px inset #ccc;*/
}
.gwt-Button:hover {
/* 	border-color: #9cf #69e #69e #7af;*/
}
.gwt-Button[disabled] {
	cursor: default;
	color: #888;
}
.gwt-Button[disabled]:hover {
	border: 1px outset #ccc;
}


/** TitleWidget ***************************************************************/

.TitleWidget {
	margin-bottom: 10px;
}
.TitleWidget-Logo {
	height: 32px;
	width: 32px;
	font-size: 0;
}
.TitleWidget-Top {
	color: #FFFFFF;
	font-size: 20px;
	background-color: #000000;
	height: 32px;
}
.TitleWidget-Bottom {
	color: #DDDDDD;
	background-color: #808080;
	height: 32px;
	padding: 0;
	padding-right: 5px;
}
.TitleWidget-DealerNameLabel {
	margin-right: 10px;
}
.TitleWidget-LogoutButton {
	white-space: nowrap;
}


/** ToolBar *******************************************************************/

.ToolBar-Button {
	width: 120px;
	white-space: nowrap;
}

.ToolBar-Object, .ToolBar-Button {
	margin: 2px;
}


/* - DecoratorPanel --------------------------------------------------------- */

.EntitledDecoratorPanel-Caption {
	background-color: #e0e0e0;
	border-bottom: 1px solid #e0e0e0;
	padding-left: 10px;
	padding-right: 5px;
}

.gwt-DecoratorPanel {
/*	margin: 1px; */
	height: 100%;
}
.gwt-DecoratorPanel .topCenter,
.gwt-DecoratorPanel .bottomCenter,
.gwt-DecoratorPanel .middleLeft,
.gwt-DecoratorPanel .middleRight,
.gwt-DecoratorPanel .topLeft,
.gwt-DecoratorPanel .topRight,
.gwt-DecoratorPanel .bottomLeft,
.gwt-DecoratorPanel .bottomRight {
	background: none;  /* disable standard value */
/*	zoom: 1;*/
/*	width: 1px;*/
}
.gwt-DecoratorPanel .topLeftInner,
.gwt-DecoratorPanel .topRightInner,
.gwt-DecoratorPanel .bottomLeftInner,
.gwt-DecoratorPanel .bottomRightInner {
	width: 3px;
	height: 3px;
	zoom: 1;
}
.gwt-DecoratorPanel .middleCenterInner {
	border: 1px solid #aaaaaa;
	background-color: #f8f8f8;
	padding: 2px;
	height: 100%;
}
.gwt-DecoratorPanel .topLeft,
.gwt-DecoratorPanel .topRight,
.gwt-DecoratorPanel .bottomLeft,
.gwt-DecoratorPanel .bottomRight {
	width: 3px;
	height: 3px;
	zoom: 1;
}


/* - DialogBox -------------------------------------------------------------- */

.gwt-DialogBox .Caption {
	padding: 6px 4px 4px 8px;
	border-bottom: 1px solid #666666;
	border-top: none; /* disable standard value */
	background: none; /* disable standard value */
	font-weight: bold;
}
.gwt-DialogBox .dialogContent {
}
.gwt-DialogBox .dialogMiddleCenter {
	background: #eeeeee; /* Dialog background */
}
.gwt-DialogBox .dialogTopLeft {
	background: url(images/dialog/tl.png) no-repeat;
}
.gwt-DialogBox .dialogTopCenter {
	background: url(images/dialog/tc.png) repeat-x;
}
.gwt-DialogBox .dialogTopRight {
	background: url(images/dialog/tr.png) no-repeat;
}
.gwt-DialogBox .dialogMiddleLeft {
	background: url(images/dialog/ml.png) repeat-y;
}
.gwt-DialogBox .dialogMiddleRight {
	background: url(images/dialog/mr.png) repeat-y;
}
.gwt-DialogBox .dialogBottomLeft {
	background: url(images/dialog/bl.png) no-repeat;
}
.gwt-DialogBox .dialogBottomCenter {
	background: url(images/dialog/bc.png) repeat-x;
}
.gwt-DialogBox .dialogBottomRight {
	background: url(images/dialog/br.png) no-repeat;
}

.gwt-DialogBox .dialogTopLeftInner {
	width: 3px;
}
.gwt-DialogBox .dialogTopRightInner {
	width: 3px;
}
.gwt-DialogBox .dialogBottomLeftInner {
	width: 3px;
	height: 3px;
}
.gwt-DialogBox .dialogBottomRightInner {
	width: 3px;
	height: 3px;
}

* html .gwt-DialogBox .dialogTopLeftInner {
	width: 3px;
}
* html .gwt-DialogBox .dialogTopRightInner {
	width: 3px;
}
* html .gwt-DialogBox .dialogBottomLeftInner {
	width: 3px;
	height: 3px;
}
* html .gwt-DialogBox .dialogBottomRightInner {
	width: 3px;
	height: 3px;
}


/* - CheckBox --------------------------------------------------------------- */

.gwt-CheckBox {
}
.gwt-CheckBox * {
	vertical-align: middle;
}
.gwt-CheckBox-disabled {
  color: #888;
}


/* - LicenseList ------------------------------------------------------------ */

.ll-Table {
	margin: 2px;
	border: 1px solid #cccccc;
	border-collapse: collapse;
	background-color: #ffffff;
	width: 100%;
}

.ll-HeaderButton {
	color: #444444;
	background: transparent;
	border: none;
/*	padding: 0;*/
/*	margin: 0;*/
	width: 100%;
	height: 100%;
	font-weight: bold;
	vertical-align: middle;
	cursor: pointer;
	cursor: hand;
}

.ll-Header td {
	color: #444444;
	text-align: center;
	background: url(images/hborder.png) repeat-x scroll 0 -27px;
	font-weight: bold;
	border: 1px solid #cccccc;

	vertical-align: middle;

/*	cursor: hand;
	border: 1px outset #ccc;*/
}
/*.ll-Header > td:active {
	border: 10px inset #ccc;
}
.ll-Header > td:hover {
	border-color: #9cf #69e #69e #7af;
}*/
.ll-Row td {
	border: 1px solid #cccccc;
	padding-left: 2px;
	padding-right: 2px;
}
/* Mark row under mouse pointer */
.ll-Row:hover {
	background-color: #f0f0f0;
}

.ll-CanceledRow * {
	text-decoration: line-through;
}
.ll-ExpiredRow * {
	color: #FF0000;
}
.ll-WillSoonExpireRow * {
	color: #7F7F7F;
}
.ll-RowButton {
	/*background: ;*/
	/*background-color: #7f7f7f;*/
	padding: 0px 1px 1px 0px;
	color: #000000;
	width: 100%;
}
.ll-RowButton:active {
	padding: 1px 0px 0px 1px;
}
.ll-CommonCell {
	cursor: pointer;
	cursor: hand;
}
.ll-CommandCell {
}

.ll-RowSelected * {
	background-color: #7f7fff;
}

/* - ApproveLicenseDialog ------------------------------------------------------------ */

.al-Table {
	margin: 2px;
/*	border: 1px solid #cccccc;*/
	border-collapse: collapse;
/*	background-color: #ffffff;*/
}
/*
.al-HeaderButton {
	color: #444444;
	background: transparent;
	border: none;
	width: 100%;
	height: 100%;
	font-weight: bold;
	vertical-align: middle;
	cursor: pointer;
	cursor: hand;
}
*/
.al-Header td {
	color: #444444;
	text-align: center;
	background: url(images/hborder.png) repeat-x scroll 0 -27px;
/*	font-weight: bold;*/
	border: 1px solid #cccccc;

	vertical-align: middle;
}
.al-Row td {
	border: 1px solid #cccccc;
	padding-left: 2px;
	padding-right: 2px;
	background-color: #ffffff;
}
.al-TextRow td {
	border: 1px solid #cccccc;
	padding-left: 60px;
	padding-right: 2px;
	background-color: #ffffff;
}
.al-Footer td {
	font-size: 8pt;
	padding-left: 2px;
	padding-right: 2px;
}

.al-RowButton {
	white-space: nowrap;
	padding: 0px 1px 1px 0px;
	color: #000000;
}
.al-RowButton:active {
	padding: 1px 0px 0px 1px;
}

.strike {
	text-decoration: line-through;
	color: #ff0000;
}

/* - LicenseDetailsDialog ------------------------------------------------------------ */

.ld-HeaderLabel {
	font-weight: bold;
}
.ld-Table {
/*	width: max-content;*/
	border-collapse: collapse;
}
.ld-Table td:first-child {
    min-width: 180px;
}
.ld-BoostPeriodCell {
	border-left-style: groove;
    border-right-style: groove;
}
.ld-ThisBoostCell {
	font-weight: bold;
}

/* - AgentCommandDialog -------------------------------------------------------------- */

.HardCodeLabel {
	white-space: nowrap;
	font-family: monospace;
	font-size: 8pt;
}
