<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class eu.untill.license.server.HwmInvoiceHandlerTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class eu.untill.license.server.HwmInvoiceHandlerTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/eu.untill.license.server.html">eu.untill.license.server</a> &gt; HwmInvoiceHandlerTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">11</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.549s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">generateAndSend_error1</td>
<td class="success">0.040s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateAndSend_noWork1</td>
<td class="success">0.073s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateAndSend_noWork2</td>
<td class="success">0.015s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">generateAndSend_test1</td>
<td class="success">0.025s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">order_noWork</td>
<td class="success">0.009s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">order_test1</td>
<td class="success">0.262s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">prepare_concurrency</td>
<td class="success">0.049s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">prepare_noWork</td>
<td class="success">0s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">prepare_test1</td>
<td class="success">0.035s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">prepare_test2</td>
<td class="success">0.038s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">prepare_wrongDb</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>16:09:00.665 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.673 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.674 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.676 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.678 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.679 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.679 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.681 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.685 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.692 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.693 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.694 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.696 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.785 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-06, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.787 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-07, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.789 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-08, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.790 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-09, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.791 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.793 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.794 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [1003]}}
16:09:00.794 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.821 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler getNextMonthForInvoiceGeneration : Invalid format for HwmNextMonthForInvoiceGeneration parameter: '2016-HZ'
16:09:00.842 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.843 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 1, emails: null}
16:09:00.843 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.843 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 2, emails: null}
16:09:00.844 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.844 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 3, emails: null}
16:09:00.845 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.845 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 4, emails: null}
16:09:00.846 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.846 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 5, emails: null}
16:09:00.847 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.848 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 6, emails: null}
16:09:00.848 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.849 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 7, emails: null}
16:09:00.849 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.850 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 8, emails: null}
16:09:00.878 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-06, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.879 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-07, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.880 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-08, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.881 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-09, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.882 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.883 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.884 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [1003]}}
16:09:00.885 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.915 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-08, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102]}}
16:09:00.917 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-09, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102, 3201]}}
16:09:00.918 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102, 3201, 3301]}}
16:09:00.919 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: Client4, licenseIds: [4101, 4201]}}
16:09:00.919 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 2, chain: Client4, licenseIds: [4801]}}
16:09:00.920 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102, 3201, 3301]}}
16:09:00.920 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: Client4, licenseIds: [4101, 4201]}}
16:09:00.920 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 2, chain: Client4, licenseIds: [4801, 4901]}}
16:09:00.921 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: CLIENT3, licenseIds: [3201, 3301]}}
16:09:00.921 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: Client4, licenseIds: [4101, 4201]}}
16:09:00.922 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 2, chain: Client4, licenseIds: [4801]}}
16:09:01.137 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.145 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.152 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.161 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.168 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.174 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.179 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.186 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at Aug 6, 2025, 4:09:02 PM</p>
</div>
</div>
</body>
</html>
