/**
 * 
 */
package eu.untill.license.server;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.UUID;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 *
 */
public class CommonHelperImpl implements CommonHelper {

	static final Logger LOG = LoggerFactory.getLogger(CommonHelperImpl.class);

	/* (non-Javadoc)
	 * @see eu.untill.license.server.CommonHelper#generateConfirmationUid()
	 */
	@Override
	public String generateGuid() {
		return UUID.randomUUID().toString();
	}

	/* (non-Javadoc)
	 * @see eu.untill.license.server.CommonHelper#getCurrentDate()
	 */
	@Override
	public Date getCurrentDate() {
		return new Date();
	}

	@Override
	public String getPrivateKey(String productName, int productVersion) {
		Settings settings = Common.getSettings();
		String privateKeysDir = settings.getKeysDir();
		if (settings.getKeysDir() == null || !(new File(privateKeysDir).exists()))
			return null;

		// TODO automatic select file from PrivateKeysDir
		try {
			if (productName.equals("unTill")) {
				if (productVersion >= 113) {
					return Base64.encodeBase64String(Files.readAllBytes(Paths.get(
							privateKeysDir, "unTill.113.private.key")));
				} else {
					return null;
				}
			} else {
				return null;
			}
		} catch (IOException e) {
			LOG.warn("read private key error", e);
			return null;
		}
	}

	@Override
	public String getServletUrl() {
		String servletUrl = Common.getSettings().getServletUrl();
		if (servletUrl == null)
			servletUrl = Common.getServletRequestUrl();
		return servletUrl;
	}

	@Override
	public Object getSettingsLock() {
		return Common.getSettingsLock();
	}

	@Override
	public Settings getSettings() {
		return Common.getSettings();
	}

	@Override
	public void setSettings(Settings settings) {
		Common.setSettings(settings);
	}

}
