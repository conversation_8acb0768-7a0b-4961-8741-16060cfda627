function eu_untill_license_UntillLicenseServerJUnit_JUnit(){var O='bootstrap',P='begin',Q='gwt.codesvr.eu.untill.license.UntillLicenseServerJUnit.JUnit=',R='gwt.codesvr=',S='eu.untill.license.UntillLicenseServerJUnit.JUnit',T='startup',U='DUMMY',V=0,W=1,X='iframe',Y='position:absolute; width:0; height:0; border:none; left: -1000px;',Z=' top: -1000px;',$='CSS1Compat',_='<!doctype html>',ab='',bb='<html><head><\/head><body><\/body><\/html>',cb='undefined',db='readystatechange',eb=10,fb='Chrome',gb='eval("',hb='");',ib='script',jb='javascript',kb='moduleStartup',lb='moduleRequested',mb='eu_untill_license_UntillLicenseServerJUnit_JUnit',nb='Failed to load ',ob='head',pb='meta',qb='name',rb='eu.untill.license.UntillLicenseServerJUnit.JUnit::',sb='::',tb='gwt:property',ub='content',vb='=',wb='gwt:onPropertyErrorFn',xb='Bad handler "',yb='" for "gwt:onPropertyErrorFn"',zb='gwt:onLoadErrorFn',Ab='" for "gwt:onLoadErrorFn"',Bb='#',Cb='?',Db='/',Eb='img',Fb='clear.cache.gif',Gb='baseUrl',Hb='eu.untill.license.UntillLicenseServerJUnit.JUnit.nocache.js',Ib='base',Jb='//',Kb='selectingPermutation',Lb='eu.untill.license.UntillLicenseServerJUnit.JUnit.devmode.js',Mb='5A344854173DD552596B1400688AAE23',Nb=':',Ob='.cache.js',Pb='link',Qb='rel',Rb='stylesheet',Sb='href',Tb='loadExternalRefs',Ub='gwt/chrome/chrome.css',Vb='end',Wb='http:',Xb='file:',Yb='_gwt_dummy_',Zb='__gwtDevModeHook:eu.untill.license.UntillLicenseServerJUnit.JUnit',$b='Ignoring non-whitelisted Dev Mode URL: ',_b=':moduleBase';var o=window;var p=document;r(O,P);function q(){var a=o.location.search;return a.indexOf(Q)!=-1||a.indexOf(R)!=-1}
function r(a,b){if(o.__gwtStatsEvent){o.__gwtStatsEvent({moduleName:S,sessionId:o.__gwtStatsSessionId,subSystem:T,evtGroup:a,millis:(new Date).getTime(),type:b})}}
eu_untill_license_UntillLicenseServerJUnit_JUnit.__sendStats=r;eu_untill_license_UntillLicenseServerJUnit_JUnit.__moduleName=S;eu_untill_license_UntillLicenseServerJUnit_JUnit.__errFn=null;eu_untill_license_UntillLicenseServerJUnit_JUnit.__moduleBase=U;eu_untill_license_UntillLicenseServerJUnit_JUnit.__softPermutationId=V;eu_untill_license_UntillLicenseServerJUnit_JUnit.__computePropValue=null;eu_untill_license_UntillLicenseServerJUnit_JUnit.__getPropMap=null;eu_untill_license_UntillLicenseServerJUnit_JUnit.__installRunAsyncCode=function(){};eu_untill_license_UntillLicenseServerJUnit_JUnit.__gwtStartLoadingFragment=function(){return null};eu_untill_license_UntillLicenseServerJUnit_JUnit.__gwt_isKnownPropertyValue=function(){return false};eu_untill_license_UntillLicenseServerJUnit_JUnit.__gwt_getMetaProperty=function(){return null};var s=null;var t=o.__gwt_activeModules=o.__gwt_activeModules||{};t[S]={moduleName:S};eu_untill_license_UntillLicenseServerJUnit_JUnit.__moduleStartupDone=function(e){var f=t[S].bindings;t[S].bindings=function(){var a=f?f():{};var b=e[eu_untill_license_UntillLicenseServerJUnit_JUnit.__softPermutationId];for(var c=V;c<b.length;c++){var d=b[c];a[d[V]]=d[W]}return a}};var u;function v(){w();return u}
function w(){if(u){return}var a=p.createElement(X);a.id=S;a.style.cssText=Y+Z;a.tabIndex=-1;p.body.appendChild(a);u=a.contentWindow.document;u.open();var b=document.compatMode==$?_:ab;u.write(b+bb);u.close()}
function A(k){function l(a){function b(){if(typeof p.readyState==cb){return typeof p.body!=cb&&p.body!=null}return /loaded|complete/.test(p.readyState)}
var c=b();if(c){a();return}function d(){if(!c){if(!b()){return}c=true;a();if(p.removeEventListener){p.removeEventListener(db,d,false)}if(e){clearInterval(e)}}}
if(p.addEventListener){p.addEventListener(db,d,false)}var e=setInterval(function(){d()},eb)}
function m(c){function d(a,b){a.removeChild(b)}
var e=v();var f=e.body;var g;if(navigator.userAgent.indexOf(fb)>-1&&window.JSON){var h=e.createDocumentFragment();h.appendChild(e.createTextNode(gb));for(var i=V;i<c.length;i++){var j=window.JSON.stringify(c[i]);h.appendChild(e.createTextNode(j.substring(W,j.length-W)))}h.appendChild(e.createTextNode(hb));g=e.createElement(ib);g.language=jb;g.appendChild(h);f.appendChild(g);d(f,g)}else{for(var i=V;i<c.length;i++){g=e.createElement(ib);g.language=jb;g.text=c[i];f.appendChild(g);d(f,g)}}}
eu_untill_license_UntillLicenseServerJUnit_JUnit.onScriptDownloaded=function(a){l(function(){m(a)})};r(kb,lb);var n=p.createElement(ib);n.src=k;if(eu_untill_license_UntillLicenseServerJUnit_JUnit.__errFn){n.onerror=function(){eu_untill_license_UntillLicenseServerJUnit_JUnit.__errFn(mb,new Error(nb+code))}}p.getElementsByTagName(ob)[V].appendChild(n)}
eu_untill_license_UntillLicenseServerJUnit_JUnit.__startLoadingFragment=function(a){return D(a)};eu_untill_license_UntillLicenseServerJUnit_JUnit.__installRunAsyncCode=function(a){var b=v();var c=b.body;var d=b.createElement(ib);d.language=jb;d.text=a;c.appendChild(d);c.removeChild(d)};function B(){var c={};var d;var e;var f=p.getElementsByTagName(pb);for(var g=V,h=f.length;g<h;++g){var i=f[g],j=i.getAttribute(qb),k;if(j){j=j.replace(rb,ab);if(j.indexOf(sb)>=V){continue}if(j==tb){k=i.getAttribute(ub);if(k){var l,m=k.indexOf(vb);if(m>=V){j=k.substring(V,m);l=k.substring(m+W)}else{j=k;l=ab}c[j]=l}}else if(j==wb){k=i.getAttribute(ub);if(k){try{d=eval(k)}catch(a){alert(xb+k+yb)}}}else if(j==zb){k=i.getAttribute(ub);if(k){try{e=eval(k)}catch(a){alert(xb+k+Ab)}}}}}__gwt_getMetaProperty=function(a){var b=c[a];return b==null?null:b};s=d;eu_untill_license_UntillLicenseServerJUnit_JUnit.__errFn=e}
function C(){function e(a){var b=a.lastIndexOf(Bb);if(b==-1){b=a.length}var c=a.indexOf(Cb);if(c==-1){c=a.length}var d=a.lastIndexOf(Db,Math.min(c,b));return d>=V?a.substring(V,d+W):ab}
function f(a){if(a.match(/^\w+:\/\//)){}else{var b=p.createElement(Eb);b.src=a+Fb;a=e(b.src)}return a}
function g(){var a=__gwt_getMetaProperty(Gb);if(a!=null){return a}return ab}
function h(){var a=p.getElementsByTagName(ib);for(var b=V;b<a.length;++b){if(a[b].src.indexOf(Hb)!=-1){return e(a[b].src)}}return ab}
function i(){var a=p.getElementsByTagName(Ib);if(a.length>V){return a[a.length-W].href}return ab}
function j(){var a=p.location;return a.href==a.protocol+Jb+a.host+a.pathname+a.search+a.hash}
var k=g();if(k==ab){k=h()}if(k==ab){k=i()}if(k==ab&&j()){k=e(p.location.href)}k=f(k);return k}
function D(a){if(a.match(/^\//)){return a}if(a.match(/^[a-zA-Z]+:\/\//)){return a}return eu_untill_license_UntillLicenseServerJUnit_JUnit.__moduleBase+a}
function F(){var f=[];var g=V;var h=[];var i=[];function j(a){var b=i[a](),c=h[a];if(b in c){return b}var d=[];for(var e in c){d[c[e]]=e}if(s){s(a,d,b)}throw null}
__gwt_isKnownPropertyValue=function(a,b){return b in h[a]};eu_untill_license_UntillLicenseServerJUnit_JUnit.__getPropMap=function(){var a={};for(var b in h){if(h.hasOwnProperty(b)){a[b]=j(b)}}return a};eu_untill_license_UntillLicenseServerJUnit_JUnit.__computePropValue=j;o.__gwt_activeModules[S].bindings=eu_untill_license_UntillLicenseServerJUnit_JUnit.__getPropMap;r(O,Kb);if(q()){return D(Lb)}var k;try{k=Mb;var l=k.indexOf(Nb);if(l!=-1){g=parseInt(k.substring(l+W),eb);k=k.substring(V,l)}}catch(a){}eu_untill_license_UntillLicenseServerJUnit_JUnit.__softPermutationId=g;return D(k+Ob)}
function G(){if(!o.__gwt_stylesLoaded){o.__gwt_stylesLoaded={}}function c(a){if(!__gwt_stylesLoaded[a]){var b=p.createElement(Pb);b.setAttribute(Qb,Rb);b.setAttribute(Sb,D(a));p.getElementsByTagName(ob)[V].appendChild(b);__gwt_stylesLoaded[a]=true}}
r(Tb,P);c(Ub);r(Tb,Vb)}
B();eu_untill_license_UntillLicenseServerJUnit_JUnit.__moduleBase=C();t[S].moduleBase=eu_untill_license_UntillLicenseServerJUnit_JUnit.__moduleBase;var H=F();if(o){var I=!!(o.location.protocol==Wb||o.location.protocol==Xb);o.__gwt_activeModules[S].canRedirect=I;function J(){var b=Yb;try{o.sessionStorage.setItem(b,b);o.sessionStorage.removeItem(b);return true}catch(a){return false}}
if(I&&J()){var K=Zb;var L=o.sessionStorage[K];if(!/^http:\/\/(localhost|127\.0\.0\.1)(:\d+)?\/.*$/.test(L)){if(L&&(window.console&&console.log)){console.log($b+L)}L=ab}if(L&&!o[K]){o[K]=true;o[K+_b]=C();var M=p.createElement(ib);M.src=L;var N=p.getElementsByTagName(ob)[V];N.insertBefore(M,N.firstElementChild||N.children[V]);return false}}}G();r(O,Vb);A(H);return true}
eu_untill_license_UntillLicenseServerJUnit_JUnit.succeeded=eu_untill_license_UntillLicenseServerJUnit_JUnit();