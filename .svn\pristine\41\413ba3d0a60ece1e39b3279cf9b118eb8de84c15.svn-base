package eu.untill.license.shared;

import static org.junit.Assert.assertEquals;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

import org.junit.Test;

public class VersionTest {

	@Test
	public void versionCompare() {
		List<String> list = new ArrayList<>(
				Arrays.asList("0.9", "0.10", "1.1", "0.1", "8", "10.2", null, "2.1", "9.1-SR2", "9", "9.1", "0.8"));
		List<String> expectedList =
				Arrays.asList("0.1", "0.8", "0.9", "0.10", "1.1", "2.1", "8", "9", "9.1", "9.1-SR2", "10.2", null);

		list.sort((ver1, ver2) -> {
			return Comparator.nullsLast(Version::compareTo).compare(Version.create(ver1), Version.create(ver2));
		});

		assertEquals(expectedList, list);
	}

}
