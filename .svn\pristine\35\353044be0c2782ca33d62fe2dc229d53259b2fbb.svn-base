package eu.untill.license.client.ui;

import java.util.List;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.shared.ClientNameChange;
import eu.untill.license.shared.HardCodeHelper;

public class ClientNameChangesDialog extends DialogBox implements ClickHandler {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Client name changes")
		String clientNameChangesHeader();

		@DefaultStringValue("Filter")
		String filterLabel();
//		@DefaultStringValue("Changes")
//		String clientNameChangesLabel();

		String refreshButton();
		String closeButton();

		@DefaultStringValue("Dealer")
		String dealerColumnHeader();
		String clientNameColumnHeader();
		String hardCodeColumnHeader();
		String issueDateColumnHeader();
		@DefaultStringValue("Before")
		String beforeChangeColumnHeader();
		@DefaultStringValue("After")
		String afterChangeColumnHeader();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	  /////////////
	 // Members //
	/////////////

	private long dealerId;

	  //////////////////
	 // Constructors //
	//////////////////

	public ClientNameChangesDialog(long dealerId) {
		this.dealerId = dealerId;

		// Generate base interface
		generateUI();

		this.setText(constants.clientNameChangesHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnFilter = new EntitledDecoratorPanel();
	private FlexTable tblFilter = new FlexTable();
	private Label lblFilter = new Label(constants.filterLabel() + ":", false);
	private TextBox txtFilter = new TextBox();

	private EntitledDecoratorPanel dpnChanges = new EntitledDecoratorPanel(/*constants.clientNameChangesLabel()*/);
	private FlexTable tblChanges = new FlexTable();
	private ScrollPanel scrChanges = new ScrollPanel(tblChanges);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnRefresh = new Button(constants.refreshButton(), this);
	private Button btnClose = new Button(constants.closeButton(), this);

	private void generateUI() {

		// Assemble filter panel
		txtFilter.setVisibleLength(124);
		txtFilter.addKeyUpHandler((event) -> {
			if (event.getNativeKeyCode() == KeyCodes.KEY_ENTER)
				requestChanges();
		});
		int row = 0;
		tblFilter.setWidget(row, 0, lblFilter);
		tblFilter.setWidget(row++, 1, txtFilter);
		dpnFilter.setWidget(tblFilter);

		// Create data table
		tblChanges.addStyleName("al-Table"); 
		tblChanges.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblChanges.getFlexCellFormatter();
		int col = 0;
		if (dealerId == 0) {
			tableCF.setWidth(0, col, "100px");
			tblChanges.setText(0, col++, constants.dealerColumnHeader());
		}
		tableCF.setWidth(0, col, "150px");
		tblChanges.setText(0, col++, constants.clientNameColumnHeader());
		tableCF.setWidth(0, col, "170px");
		tblChanges.setText(0, col++, constants.hardCodeColumnHeader());
		tableCF.setWidth(0, col, "80px");
		tblChanges.setText(0, col++, constants.issueDateColumnHeader());
		tableCF.setWidth(0, col, dealerId == 0 ? "150px" : "200px");
		tblChanges.setText(0, col++, constants.beforeChangeColumnHeader());
		tableCF.setWidth(0, col, dealerId == 0 ? "150px" : "200px");
		tblChanges.setText(0, col++, constants.afterChangeColumnHeader());

		// Assemble data panel with scroll
		scrChanges.setSize("520", "200px");
		dpnChanges.setWidget(scrChanges);

		// Assemble button panel
		btnRefresh.setWidth("80px");
		btnClose.setWidth("80px");
		pnlButtons.setWidth("100%");
		pnlButtons.add(btnRefresh);
		pnlButtons.setCellHorizontalAlignment(btnRefresh, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.setCellWidth(btnRefresh, "100%");
		pnlButtons.add(btnClose);
		pnlButtons.setCellHorizontalAlignment(btnClose, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnFilter);
		mainPanel.add(dpnChanges);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnRefresh) {

			requestChanges();

		} else if (sender == btnClose) {

			this.hide();
			
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void requestChanges() {
		UntillLicenseServer.getLicenseServiceAsync().getClientNameChanges(
				UntillLicenseServer.getAuthScheme(), dealerId, txtFilter.getText().trim(),
				new AsyncCallback<List<ClientNameChange>>() {
					@Override
					public void onSuccess(List<ClientNameChange> result) {
						WaitDialog.hide();
						refreshChanges(result);
					}
					@Override
					public void onFailure(Throwable caught) {
						WaitDialog.hideAll();
						ErrorDialog.show("Error", caught);
					}
				});
		WaitDialog.show();
	}

	private void refreshChanges(List<ClientNameChange> changes) {
		scrChanges.remove(tblChanges);
		try {
			for (int r = tblChanges.getRowCount() - 1; r > 0; --r)
				tblChanges.removeRow(r);
			fillChanges(changes);
		} finally {
			scrChanges.add(tblChanges);
		}
	}

	private void fillChanges(List<ClientNameChange> changes) {
		RowFormatter tblChangesRF = tblChanges.getRowFormatter();
		FlexCellFormatter tblChangesCF = tblChanges.getFlexCellFormatter();

		int row = 1;

		if (changes == null)
			return;

		for (ClientNameChange change : changes) {
			// set row style classes
			tblChangesRF.addStyleName(row, "ll-Row");
	
			// fill row
			int col = 0;
			if (dealerId == 0)
				tblChanges.setText(row, col++, change.dealerName);
			tblChanges.setText(row, col++, change.clientName);
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			Label hardCodeLabel = new Label(HardCodeHelper.formatHardCode(change.hardCode), false);
			hardCodeLabel.addStyleName("HardCodeLabel");
			tblChanges.setWidget(row, col++, hardCodeLabel);
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			tblChanges.setText(row, col++, DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT).format(change.issueDate));
			tblChanges.setText(row, col++, change.clientNameBefore);
			tblChanges.setText(row, col++, change.clientNameAfter);
			++row;
		}

		if (txtFilter.getText().trim().isEmpty() && changes.size() == 100) {
			tblChangesRF.addStyleName(row, "ll-Row");
			int col = 0;
			if (dealerId == 0)
				tblChanges.setText(row, col++, "...");
			tblChanges.setText(row, col++, "...");
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			tblChanges.setText(row, col++, "...");
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			tblChanges.setText(row, col++, "...");
			tblChanges.setText(row, col++, "...");
			tblChanges.setText(row, col++, "...");
			++row;
		}
	}
	
	
	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtFilter.setFocus(true);
		requestChanges();
	}

}
