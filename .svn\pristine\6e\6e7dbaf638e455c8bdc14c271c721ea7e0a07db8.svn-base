/**
 *
 */
package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.core.client.Scheduler.ScheduledCommand;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.MultiWordSuggestOracle;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.ValueBoxBase.TextAlignment;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.datepicker.client.CalendarUtil;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.user.datepicker.client.DatePicker;

import eu.untill.license.client.Common;
import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.LicenseService.LicensePeriod;
import eu.untill.license.client.LicenseService.LicenseType;
import eu.untill.license.client.LicenseService.RequestLicenseResult;
import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.client.LicenseService.RrDealer;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.License.FuncProp.FuncType;
import eu.untill.license.shared.ProlongParams;
import eu.untill.license.shared.Restrictions;

/**
 * <AUTHOR>
 *
 */
public class LicenseDialog extends DialogBox implements ValueChangeHandler<Boolean>, ClickHandler, 
		AsyncCallback<RequestLicenseResult> {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String addNewLicenseHeader();
		String prolongLicenseHeader();
		String upgradeLicenseHeader();
		String getEmergencyLicenseHeader();
		String licenseBoostHeader();

		String overallDataLabel();

		String startDateLabel();
		String expiredDateLabel();

		String clientPanel();
		String clientNameLabel();
		String clientDisplayNameLabel();
		String clientAddressLabel();
		String clientEmailLabel();
		String clientPhoneLabel();
		String hardCodeLabel();
		String testHardCodeButton();
		String licenseUidLabel();
		String chainLabel();

		String quantitativeRestrictionsPanel();
		String numberOfLocalDatabasesLabel();
		String numberOfRemoteConnectionsLabel();
		String numberOfHandTerminalsLabel();
		//String numberOfHeadQuartersLabel();
		String numberOfProductionScreensLabel();
		String numberOfHhtEftLabel();
		String unlimitedCheckBox();

		String modulesLabel();
		String extraModulesLabel();
		String interfacesLabel();

		String[] funcNames();

		String statusLabel();
		String definitiveLicenseCheckBox();
		String trialCheckBox();
		String onlineCheckBox();
		String moduledCheckBox();
		@DefaultStringValue("SaaS")
		String saasCheckBox();
		@DefaultStringValue("Auto renew")
		String autoProlongCheckBox();
		@DefaultStringValue("Version lower than 136")
		String versionLowerThan136CheckBox();
		@DefaultStringValue("Large Account")
		String largeAccountCheckBox();

		String dealerCommentsLabel();

		String requestButton();
		String cancelButton();

		String licenseUnlimited();

		String temporaryEndDateLabel();
		//String generateTemporaryLicenseLabel();
	}

	public static interface UlsMessages extends Messages {
		String pleaseEnterClientNameMessage();
		String pleaseEnterClientDisplayNameMessage();
		String pleaseEnterAnotherHardCode();
		String unknownNumberFormatOfConnectionsMessage();
		String negativeValueOfConnectionsMessage();
		String downgradeConnectionsMessage();
		String sumOfConnectionsIsZeroMessage();
		String unknownNumberFormatOfDefinitiveVersion();
		String wrongValueOfDefinitiveMaxVersionMessage();

		String unexpectedFailureMessage();
		String illegalRequestMessage();
		String downgradeFuncLimitedMessage();

		String hardCodeIsValidMessage();
		String hardCodeIsEmptyMessage();
		String hardCodeIllegalFormatMessage();
		String hardCodeBadCheckSumMessage();
		String hardCodeIsNoValidMessage();

		String periodIsIncorrectMessage();
		String hardCodeAlreadyExistsMessage();
		String clientAlreadyExistsMessage();
		String emergencyLicensesAreNotAvailableMessage();
		String failSendEmergencyLicenseMessage();

		String licenseSuccessfullySentMessage();
		String licenseSuccessfullyRequestedMessage();
		String requestCreatedButNotSendMessage();

		String incorrectTemporaryEndDate();
		String incorrectStartDate();
		String incorrectEndDate();

		@DefaultMessage("No improvement found for Boost license")
		String noImprovementFoundForBoostLicense();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	private static final boolean ALLOW_UNLIMETED_LICENSE = false;
	
	  /////////////
	 // Members //
	/////////////

	private RequestType requestType;
	private DbLicense prevLicense; // TODO May be not need to keep full license (need: id, StatusApostill, startDate, expiredDate)
	private RrDealer dealer;
	private boolean apostill;
	private boolean allowFiscalization; // TODO .. fill from dealer
	private FiscalDialog fiscalDialog;

	public RrDealer getDealer() {
		return dealer;
	}

	  //////////////////
	 // Constructors //
	//////////////////

	// Create new license constructors (unTill/Apostill (not used yet))
	public LicenseDialog(RrDealer dealer) {
		this(dealer, false);
	}
	public LicenseDialog(RrDealer dealer, boolean apostill) {
		this.requestType = RequestType.CREATE;
		this.dealer = dealer;
		this.apostill = apostill;
		allowFiscalization = "Denmark".equalsIgnoreCase(dealer.country); // XXX hardcoded

		// Generate base interface
		generateBaseUI();

		this.setText(constants.addNewLicenseHeader());
		if (chkaEnabledFunctions[DbLicense.FUNC_PERMANENT_JLOG] != null)
			chkaEnabledFunctions[DbLicense.FUNC_PERMANENT_JLOG].setValue(dealer.permanentJLoggingDefault);
		chkOnline.setValue(dealer.onlineDefault);
		txtClientName.addChangeHandler((event) -> {
			if (txtClientDisplayName.getText().isEmpty())
				txtClientDisplayName.setText(txtClientName.getText());
		});
		chkAutoProlong_storedValue = dealer.autoProlongDef;
		//chkAutoProlong.setValue(autoProlongDef);

		fiscalDialog = new FiscalDialog(this, null);
		setupFiscalizationFuncDeps();

		setupWmFuncDeps();

		refreshOnlineStatus(); // -> refreshSaasStatus() -> requestLicensePeriod()
		refreshModuledStatus();

		requestChains();
	}

	// Prolong/upgrade/emergency/boost existing license constructor
	public LicenseDialog(DbLicense license, RrDealer dealer, RequestType requestType) {
		this.requestType = requestType;
		this.prevLicense = license;
		this.dealer = dealer;
		this.apostill = license.isApostill();
		allowFiscalization = "Denmark".equalsIgnoreCase(dealer.country); // XXX hardcoded

		// Generate base interface
		generateBaseUI();

		// Fill fields
		// TODO Do not fill invisible fields
		txtClientName.setText(license.getClientName());
		if (license.getClientDisplayName() != null && !license.getClientDisplayName().trim().isEmpty()) {
			txtClientDisplayName.setText(license.getClientDisplayName());
			txtClientDisplayName.setReadOnly(true);
		}
		txtClientAddress.setText(license.getClientAddress());
		txtClientEmail.setText(license.getClientEmail());
		txtClientPhone.setText(license.getClientPhone());
		txtChain.setText(license.chain == null ? "" : license.chain);
		txtHardCode.setText(HardCodeHelper.formatHardCode(license.hardCode));

		if (requestType == RequestType.CREATE_BOOST) {
			txtLbConnections.setText("0");
			txtRemConnections.setText("0");
			txtOmConnections.setText("0");
//			txtHqConnections.setText("0");
			txtPsConnections.setText("0");
			txtHeConnections.setText("0");
		} else {
			short lbConn = license.prolongParams != null && license.prolongParams.getLbConnections() != null ?
					license.prolongParams.getLbConnections() : license.getLbConnections();
			txtLbConnections.setText(Short.toString(lbConn));
			short remConn = license.prolongParams != null && license.prolongParams.getRemConnections() != null ?
					license.prolongParams.getRemConnections() : license.getRemConnections();
			txtRemConnections.setText(Short.toString(remConn));
			short omConn = license.prolongParams != null && license.prolongParams.getOmConnections() != null ?
					license.prolongParams.getOmConnections() : license.getOmConnections();
			txtOmConnections.setText(Short.toString(omConn));
//			short hqConn = license.prolongParams != null && license.prolongParams.getHqConnections() != null ?
//					license.prolongParams.getHqConnections() : license.getHqConnections();
//			txtHqConnections.setText(Short.toString(hqConn));
			short psConn = license.prolongParams != null && license.prolongParams.getPsConnections() != null ?
					license.prolongParams.getPsConnections() : license.getPsConnections();
			txtPsConnections.setText(Short.toString(psConn));
			short heConn = license.prolongParams != null && license.prolongParams.getHeConnections() != null ?
					license.prolongParams.getHeConnections() : license.getHeConnections();
			txtHeConnections.setText(Short.toString(heConn));
		}

		if (license.isUnlimited()) {
			chkUnlimitedConnections.setValue(new Boolean(true), true);
		}
		for (int i = 0; i < chkaEnabledFunctions.length; i++) {
			if (chkaEnabledFunctions[i] == null) continue;
			boolean funcEnabled = ( (1l << i) & license.getFuncLimited() ) != 0;
			chkaEnabledFunctions[i].setValue(new Boolean(funcEnabled));
			if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE && !license.isModuled())
				chkaEnabledFunctions[i].setEnabled(false);
			if ((requestType == RequestType.UPGRADE || requestType == RequestType.CREATE_BOOST)
					&& funcEnabled && !DbLicense.isHiddenFunc(i))
				chkaEnabledFunctions[i].setEnabled(false);
			if (requestType == RequestType.EMERGENCY || requestType == RequestType.PROLONG
					/*|| (requestType == RequestType.PROLONG && prevLicense.isTrial())*/)
				chkaEnabledFunctions[i].setEnabled(false);
		}
		if (chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED] != null
				&& !chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].isEnabled())
			chkHwmYearly.setEnabled(false);
		if (chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION] != null && requestType == RequestType.CREATE_BOOST) {
			chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].setEnabled(false);
			chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].setValue(false);
		}
		chkDefinitiveLicense.setValue(license.isDefinitive());
		if (chkDefinitiveLicense.getValue()) {
			txtDefMjVersion.setText(Long.toString(license.getDefinitiveMaxVersion()));
			refreshDefinitiveStatus();
		}
		chkTrial.setValue(license.isTrial());
		chkOnline.setValue(license.isOnline());
		chkModuled.setValue(license.isModuled());
		chkSaas.setValue(license.isSaas());
		chkAutoProlong.setValue(license.autoProlongable);
		chkAutoProlong_storedValue = license.autoProlongable;
		chkVersionLowerThan136.setValue(license.minVersion < 136);
		chkLargeAccount.setValue(license.largeAccount);
		chkHwmYearly.setValue(license.hwmYearly);

		txtDealerComments.setText(license.dealerComments);

		switch (requestType) {
		case PROLONG:   this.setText(constants.prolongLicenseHeader());      break;
		case UPGRADE:   this.setText(constants.upgradeLicenseHeader());      break;
		case EMERGENCY:	this.setText(constants.getEmergencyLicenseHeader()); break;
		case CREATE_BOOST:     this.setText(constants.licenseBoostHeader()); break;
		case CREATE: break;
		}

		fiscalDialog = new FiscalDialog(this, license.getFiscal());
		setupFiscalizationFuncDeps();

		setupWmFuncDeps();

		refreshOnlineStatus(); // -> refreshSaasStatus() -> requestLicensePeriod()
		refreshModuledStatus();

		requestChains();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnLicense = new EntitledDecoratorPanel(constants.overallDataLabel());
	private FlexTable tblLicense = new FlexTable();
	private Label lblStartDate = new Label(constants.startDateLabel() + ":", false);
	private DateBox txtStartDate = new DateBox(new DatePicker(), null,
			new DateBox.DefaultFormat(DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT)));
	private Label lblExpiredDate = new Label(constants.expiredDateLabel() + ":", false);
	private DateBox txtExpiredDate = new DateBox(new DatePicker(), null,
			new DateBox.DefaultFormat(DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT)));
	private CheckBox chkCreateTempLicense = new CheckBox(constants.temporaryEndDateLabel() + ":");
			//constants.generateTemporaryLicenseLabel());
//	private Label lblTempExpiredDate = new Label(constants.temporaryEndDateLabel() + ":", false);
	private DateBox txtTempExpiredDate = new DateBox(new DatePicker(), null,
			new DateBox.DefaultFormat(DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT)));

	private EntitledDecoratorPanel dpnClient = new EntitledDecoratorPanel(constants.clientPanel());
	private FlexTable tblClient = new FlexTable();
	private Label lblClientName = new Label(constants.clientNameLabel() + ":", false);
	protected TextBox txtClientName = new TextBox();
	private Label lblClientDisplayName = new Label(constants.clientDisplayNameLabel() + ":", false); private TextBox txtClientDisplayName = new TextBox();
	private Label lblClientAddress = new Label(constants.clientAddressLabel() + ":", false); private TextBox txtClientAddress = new TextBox();
	private Label lblClientEmail = new Label(constants.clientEmailLabel() + ":", false); private TextBox txtClientEmail = new TextBox();
	private Label lblClientPhone = new Label(constants.clientPhoneLabel() + ":", false); private TextBox txtClientPhone = new TextBox();
	private Label lblHardCode = new Label(constants.hardCodeLabel() + ":", false);
		private HorizontalPanel pnlHardCode = new HorizontalPanel();
		private TextBox txtHardCode = new TextBox();
		private Button btnTestHardCode = new Button(constants.testHardCodeButton());
	private Label lblChain = new Label(constants.chainLabel() + ":", false);
	private MultiWordSuggestOracle chainOracle = new MultiWordSuggestOracle();
	private SuggestBox txtChain = new SuggestBox(chainOracle);

	private HorizontalPanel pnlLicenseDetails = new HorizontalPanel();

	private EntitledDecoratorPanel dpnQuantRest = new EntitledDecoratorPanel(constants.quantitativeRestrictionsPanel());
	private FlexTable tblQuantRest = new FlexTable();
	private Label lblLbConnections = new Label(constants.numberOfLocalDatabasesLabel() + ":", false);
	protected TextBox txtLbConnections = new TextBox();
	private Label lblRemConnections = new Label(constants.numberOfRemoteConnectionsLabel() + ":", false); private TextBox txtRemConnections = new TextBox();
	private Label lblOmConnections = new Label(constants.numberOfHandTerminalsLabel() + ":", false); private TextBox txtOmConnections = new TextBox();
//	private Label lblHqConnections = new Label(constants.numberOfHeadQuartersLabel() + ":", false); private TextBox txtHqConnections = new TextBox();
	private Label lblPsConnections = new Label(constants.numberOfProductionScreensLabel() + ":", false); private TextBox txtPsConnections = new TextBox();
	private Label lblHeConnections = new Label(constants.numberOfHhtEftLabel() + ":", false); private TextBox txtHeConnections = new TextBox();
	private CheckBox chkUnlimitedConnections = new CheckBox(constants.unlimitedCheckBox());

	private EntitledDecoratorPanel dpnModules = new EntitledDecoratorPanel(constants.modulesLabel());
	private VerticalPanel pnlModules = new VerticalPanel();
	private EntitledDecoratorPanel dpnExtraModules = new EntitledDecoratorPanel(constants.extraModulesLabel());
	private VerticalPanel pnlExtraModules = new VerticalPanel();
	private EntitledDecoratorPanel dpnInterfaces = new EntitledDecoratorPanel(constants.interfacesLabel());
	private HorizontalPanel pnlInterfacesH = new HorizontalPanel();
	private VerticalPanel pnlInterfacesV1 = new VerticalPanel();
	private VerticalPanel pnlInterfacesV2 = new VerticalPanel();
	private CheckBox[] chkaEnabledFunctions = new CheckBox[DbLicense.ENABLED_FUNCTIONS_NUMBER];

	private CheckBox chkDefinitiveLicense = new CheckBox(constants.definitiveLicenseCheckBox() + ":");
	private CheckBox chkTrial = new CheckBox(constants.trialCheckBox());
	private CheckBox chkOnline = new CheckBox(constants.onlineCheckBox());
	private TextBox txtDefMjVersion = new TextBox();
	private CheckBox chkModuled = new CheckBox(constants.moduledCheckBox());
	private CheckBox chkSaas = new CheckBox(constants.saasCheckBox());
	private boolean chkAutoProlong_storedValue = true;
	private CheckBox chkAutoProlong = new CheckBox(constants.autoProlongCheckBox());
	private CheckBox chkVersionLowerThan136 = new CheckBox(constants.versionLowerThan136CheckBox());
	private CheckBox chkLargeAccount = new CheckBox(constants.largeAccountCheckBox());
	private CheckBox chkHwmYearly = new CheckBox("Yearly payments"); // TODO!: label
	private Button btnFiscal = new Button("Fiscal parameters...");  // TODO!: label

	private EntitledDecoratorPanel dpnDealerComments = new EntitledDecoratorPanel(constants.dealerCommentsLabel() + ":");
	private TextArea txtDealerComments = new TextArea();

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnRequest = new Button(constants.requestButton());
	private Button btnCancel = new Button(constants.cancelButton());

	private void generateBaseUI() {
		txtClientName.setMaxLength(80); txtClientName.setVisibleLength(70);
		txtClientDisplayName.setMaxLength(80); txtClientDisplayName.setVisibleLength(70);
		txtClientAddress.setMaxLength(80); txtClientAddress.setVisibleLength(70);
		txtClientEmail.setMaxLength(50); txtClientEmail.setVisibleLength(25);
		txtClientPhone.setMaxLength(50); txtClientPhone.setVisibleLength(25);
		txtHardCode.setMaxLength(35); txtHardCode.setVisibleLength(60);
		if (txtChain.getValueBox() instanceof TextBox) {
			TextBox txtChainTextBox = (TextBox) txtChain.getValueBox();
			txtChainTextBox.setMaxLength(50);
			txtChainTextBox.setVisibleLength(70);
		}

//		txtStartDate.getTextBox().setMaxLength(5);
//		txtExpiredDate.getTextBox().setMaxLength(5);
//		txtTempExpiredDate.getTextBox().setMaxLength(5);
		
		txtLbConnections.setVisibleLength(7);
		txtLbConnections.setAlignment(TextAlignment.RIGHT);
		txtRemConnections.setVisibleLength(7);
		txtRemConnections.setAlignment(TextAlignment.RIGHT);
		txtOmConnections.setVisibleLength(7);
		txtOmConnections.setAlignment(TextAlignment.RIGHT);
//		txtHqConnections.setVisibleLength(7);
//		txtHqConnections.setAlignment((TextAlignment.RIGHT);
		txtPsConnections.setVisibleLength(7);
		txtPsConnections.setAlignment(TextAlignment.RIGHT);
		txtHeConnections.setVisibleLength(7);
		txtHeConnections.setAlignment(TextAlignment.RIGHT);

		int row;

		HorizontalPanel pnlClientAndOverall = new HorizontalPanel();
		pnlClientAndOverall.setWidth("100%");
		
		// License (Overall data)
		if (requestType != RequestType.CREATE_BOOST) {
			txtStartDate.setEnabled(false);
			txtExpiredDate.setEnabled(false);
		} else {
			txtStartDate.addValueChangeHandler((handler) -> { onStartDateChangeForBoost(); });
			txtExpiredDate.addValueChangeHandler((handler) -> { onExpiredDateChangeForBoost(); });
		}
		row = 0;
		//FlexCellFormatter tblLicenseCF = tblLicense.getFlexCellFormatter();
		//tblLicenseCF.setWidth(row, 2, "100%");
		tblLicense.setWidget(row, 0, lblStartDate); tblLicense.setWidget(row, 1, txtStartDate);
		row++;
		tblLicense.setWidget(row, 0, lblExpiredDate); tblLicense.setWidget(row, 1, txtExpiredDate);
		row++;
		if ((requestType == RequestType.CREATE || requestType == RequestType.PROLONG) && !apostill) {
			chkCreateTempLicense.addValueChangeHandler(this);
			chkCreateTempLicense.setEnabled(false);
			txtTempExpiredDate.setEnabled(false);
			chkCreateTempLicense.setWordWrap(false);
			tblLicense.setWidget(row, 0, chkCreateTempLicense);
			tblLicense.setWidget(row, 1, txtTempExpiredDate);
			row++;
		}

		if (!apostill && (requestType == RequestType.CREATE || !prevLicense.isTrial())) {
			if (!UntillLicenseServer.getLogonDealer().isSuperDealer
					|| Restrictions.DISABLE_CREATE_DEFINITIVE && requestType == RequestType.CREATE
					|| requestType == RequestType.EMERGENCY
					|| requestType == RequestType.CREATE_BOOST
					|| requestType == RequestType.PROLONG
					|| requestType == RequestType.UPGRADE && (
						prevLicense.isDefinitive()
						|| Restrictions.DISABLE_UPGRADE_TO_DEFINITIVE
						|| Restrictions.ONLY_SUPER_DEALER_CAN_UPGRADE_TO_DEFINITIVE
						&& !UntillLicenseServer.getLogonDealer().isSuperDealer
					)
			) {
				chkDefinitiveLicense.setEnabled(false);
				txtDefMjVersion.setEnabled(false);
			}
			chkDefinitiveLicense.addValueChangeHandler(this);
			chkDefinitiveLicense.setWordWrap(false);
			txtDefMjVersion.setVisibleLength(3);
			txtDefMjVersion.setAlignment(TextAlignment.RIGHT);
			tblLicense.setWidget(row, 0, chkDefinitiveLicense);
			//tblLicense.getFlexCellFormatter().setWidth(row, 5, "10px");
			tblLicense.setWidget(row, 1, txtDefMjVersion);
			row++;
		}

		chkVersionLowerThan136.setWordWrap(false);
		tblLicense.setWidget(row, 0, chkVersionLowerThan136);
		row++;

		chkLargeAccount.setWordWrap(false);
		tblLicense.setWidget(row, 0, chkLargeAccount);
		row++;

		row = 0;
		if (!apostill) {
			if (requestType == RequestType.EMERGENCY || requestType == RequestType.PROLONG) {
				chkTrial.setEnabled(false);
			}
			if (requestType != RequestType.CREATE && prevLicense.isTrial()) {
				tblLicense.setWidget(row, 4, chkTrial);
				row++;
			}
			if (requestType == RequestType.CREATE
					|| requestType == RequestType.PROLONG && !prevLicense.isOnline()
					|| requestType == RequestType.UPGRADE && !prevLicense.isOnline())
				chkOnline.setEnabled(true);
			else
				chkOnline.setEnabled(false);
			tblLicense.setWidget(row, 4, chkOnline);
			row++;
			if (requestType == RequestType.CREATE)
				chkModuled.setEnabled(true);
			else
				chkModuled.setEnabled(false);
			tblLicense.setWidget(row, 4, chkModuled);
			row++;
			chkSaas.setEnabled(false);
			tblLicense.setWidget(row, 4, chkSaas);
			row++;
			chkAutoProlong.setEnabled(false);
			tblLicense.setWidget(row, 4, chkAutoProlong);
			row++;
		}

		chkOnline.addValueChangeHandler(this);
		chkModuled.addValueChangeHandler(this);
		chkSaas.addValueChangeHandler(this);

		txtDefMjVersion.setReadOnly(true);

		tblLicense.setWidth("100%");
		dpnLicense.setWidget(tblLicense);

		// Client details
		pnlHardCode.setWidth("100%");
		// Client
		if ((requestType == RequestType.PROLONG || requestType == RequestType.UPGRADE || requestType == RequestType.CREATE_BOOST)
			&& !(Restrictions.SUPER_DEALER_CAN_CHANGE_HARD_CODE && UntillLicenseServer.getLogonDealer().isSuperDealer)) {
			txtHardCode.setReadOnly(true);
		} else {
			txtHardCode.addChangeHandler(new ChangeHandler() {
				@Override
				public void onChange(ChangeEvent event) {
					//TextBox txtHardCode = (TextBox) event.getSource();
					txtHardCode.setText(HardCodeHelper.formatHardCode(
							HardCodeHelper.unformatHardCode(txtHardCode.getText())));
				}
			});
		}
		pnlHardCode.add(txtHardCode);
		if (requestType != RequestType.PROLONG && requestType != RequestType.UPGRADE && requestType != RequestType.CREATE_BOOST) {
			btnTestHardCode.addClickHandler(this);
			pnlHardCode.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
			pnlHardCode.add(btnTestHardCode);
		}
		if (Restrictions.ONLY_SUPER_DEALER_CAN_CHANGE_CLIENT_NAME) {
			if (requestType != RequestType.CREATE
					&& !UntillLicenseServer.getLogonDealer().isSuperDealer) {
				txtClientName.setReadOnly(true);
			}
		}
		if (requestType == RequestType.EMERGENCY
				|| (requestType == RequestType.PROLONG && prevLicense.isTrial())
				|| requestType == RequestType.CREATE_BOOST
		) {
			txtClientName.setReadOnly(true);
			txtClientAddress.setReadOnly(true);
			txtClientEmail.setReadOnly(true);
			txtClientPhone.setReadOnly(true);
			txtChain.getValueBox().setReadOnly(true);
		}
		if (requestType == RequestType.EMERGENCY || requestType == RequestType.CREATE_BOOST) {
			txtClientDisplayName.setReadOnly(true);
			chkVersionLowerThan136.setEnabled(false);
			chkLargeAccount.setEnabled(false);
			chkHwmYearly.setEnabled(false);
			btnFiscal.setEnabled(false);
		}
		if (requestType != RequestType.CREATE && !UntillLicenseServer.getLogonDealer().isSuperDealer) {
			chkLargeAccount.setEnabled(false);
		}
		row = 0;
		FlexCellFormatter tblClientCF = tblClient.getFlexCellFormatter();
		tblClient.setWidget(row, 0, lblClientName); tblClient.setWidget(row, 1, txtClientName); tblClientCF.setColSpan(row, 1, 3); row++;
		tblClient.setWidget(row, 0, lblClientDisplayName); tblClient.setWidget(row, 1, txtClientDisplayName); tblClientCF.setColSpan(row, 1, 3); row++;
		tblClient.setWidget(row, 0, lblClientAddress); tblClient.setWidget(row, 1, txtClientAddress); tblClientCF.setColSpan(row, 1, 3); row++;
		tblClient.setWidget(row, 0, lblClientEmail); tblClient.setWidget(row, 1, txtClientEmail);
		tblClient.setWidget(row, 2, lblClientPhone); tblClient.setWidget(row, 3, txtClientPhone); row++;
		tblClient.setWidget(row, 0, lblHardCode); tblClient.setWidget(row, 1, pnlHardCode); tblClientCF.setColSpan(row, 1, 3); row++;
		tblClient.setWidget(row, 0, lblChain); tblClient.setWidget(row, 1, txtChain); tblClientCF.setColSpan(row, 1, 3); row++;
		dpnClient.setWidget(tblClient);

		pnlClientAndOverall.add(dpnClient);
		pnlClientAndOverall.add(dpnLicense);

		// License details

		pnlLicenseDetails.setWidth("100%");

		// Quantitative restrictions
		chkUnlimitedConnections.addValueChangeHandler(this);
		if (apostill) {
			txtLbConnections.setText("1");
			txtLbConnections.setEnabled(false);
		} else {
			txtLbConnections.setText("0");
		}
		txtRemConnections.setText("0");
		txtOmConnections.setText("0");
//		txtHqConnections.setText("0");
		txtPsConnections.setText("0");
		txtHeConnections.setText("0");
		if (requestType == RequestType.EMERGENCY || requestType == RequestType.PROLONG
				/*|| (requestType == RequestType.PROLONG && prevLicense.isTrial())*/)
		{
			txtLbConnections.setReadOnly(true);
			txtRemConnections.setReadOnly(true);
			txtOmConnections.setReadOnly(true);
//			txtHqConnections.setReadOnly(true);
			txtPsConnections.setReadOnly(true);
			txtHeConnections.setReadOnly(true);
			chkUnlimitedConnections.setEnabled(false);
		}
		row = 0;
		tblQuantRest.setWidget(row, 0, lblLbConnections); tblQuantRest.setWidget(row, 1, txtLbConnections);
		++row;
		if (!apostill) {
			tblQuantRest.setWidget(row, 0, lblRemConnections); tblQuantRest.setWidget(row, 1, txtRemConnections);
			++row;
			tblQuantRest.setWidget(row, 0, lblOmConnections); tblQuantRest.setWidget(row, 1, txtOmConnections);
			++row;
//			if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
//				tblQuantRest.setWidget(row, 0, lblHqConnections); tblQuantRest.setWidget(row, 1, txtHqConnections);
//				++row;
//			}
			tblQuantRest.setWidget(row, 0, lblPsConnections); tblQuantRest.setWidget(row, 1, txtPsConnections);
			++row;
			tblQuantRest.setWidget(row, 0, lblHeConnections); tblQuantRest.setWidget(row, 1, txtHeConnections);
			++row;
		} else {
			tblQuantRest.setWidget(row, 0, lblOmConnections); tblQuantRest.setWidget(row, 1, txtOmConnections);
			++row;
		}
		if (ALLOW_UNLIMETED_LICENSE) {
			tblQuantRest.setWidget(row, 0, chkUnlimitedConnections);
			tblQuantRest.getFlexCellFormatter().setColSpan(row, 0, 2);
			row++;
		}
		dpnQuantRest.setWidget(tblQuantRest);

		pnlLicenseDetails.add(dpnQuantRest);

		// Functions
		ArrayList<CheckBox> extraModulesList = new ArrayList<CheckBox>();
		ArrayList<CheckBox> interfacesList = new ArrayList<CheckBox>();
		ArrayList<CheckBox> modulesList = new ArrayList<CheckBox>();
		for (int i = 0; i < DbLicense.ENABLED_FUNCTIONS_NUMBER; ++i) {
			if (apostill ? !DbLicense.FUNC_PROPS[i].isAvailableForApostill()
					: !DbLicense.FUNC_PROPS[i].isAvailableForUntill()) {
				continue;
			}

			String functionName = (i < constants.funcNames().length) ?
					constants.funcNames()[i] : DbLicense.FUNC_NAMES[i];
			chkaEnabledFunctions[i] = new CheckBox(functionName);
			if (DbLicense.isHiddenFunc(i)) {
				chkaEnabledFunctions[i].setVisible(false);
			}
			if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE)
				modulesList.add(chkaEnabledFunctions[i]);
			else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_EXTRA_MODULE)
				extraModulesList.add(chkaEnabledFunctions[i]);
			else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_INTERFACE)
				interfacesList.add(chkaEnabledFunctions[i]);
		}

		if (apostill)
			chkaEnabledFunctions[DbLicense.FUNC_EFT_INTERFACE].setEnabled(false);

		if (!modulesList.isEmpty()) {
			for (CheckBox checkBox : modulesList) {
				pnlModules.add(checkBox);
			}
			dpnModules.setWidget(pnlModules);
			pnlLicenseDetails.add(dpnModules);
		}

		if (!extraModulesList.isEmpty()) {
			Collections.sort(extraModulesList, new Comparator<CheckBox>(){
				@Override
				public int compare(CheckBox checkBox1, CheckBox checkBox2) {
					if (chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED] != null) {
						if (checkBox1 == chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED]) return 1;
						if (checkBox2 == chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED]) return -1;
					}
					return checkBox1.getText().compareToIgnoreCase(checkBox2.getText());
				}
			});
			for (CheckBox checkBox : extraModulesList) {
				pnlExtraModules.add(checkBox);
				if (chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED] != null
						&& checkBox == chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED]) {
					pnlExtraModules.add(chkHwmYearly);
					chkHwmYearly.getElement().getStyle().setMarginLeft(20, Unit.PX);
				}
				if (chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION] != null
						&& checkBox == chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION]) {
					pnlExtraModules.add(btnFiscal);
					btnFiscal.getElement().getStyle().setMarginLeft(20, Unit.PX);
				}
			}
			dpnExtraModules.setWidget(pnlExtraModules);
			pnlLicenseDetails.add(dpnExtraModules);
		}


		if (!interfacesList.isEmpty()) {
			Collections.sort(interfacesList, new Comparator<CheckBox>(){
				@Override
				public int compare(CheckBox checkBox1, CheckBox checkBox2) {
					return checkBox1.getText().compareToIgnoreCase(checkBox2.getText());
				}
			});
			for (CheckBox checkBox : interfacesList) {
				if (pnlInterfacesV1.getWidgetCount() > pnlInterfacesV2.getWidgetCount())
					pnlInterfacesV2.add(checkBox);
				else 
					pnlInterfacesV1.add(checkBox);
			}
			pnlInterfacesH.add(pnlInterfacesV1);
			pnlInterfacesH.add(pnlInterfacesV2);
			dpnInterfaces.setWidget(pnlInterfacesH);
			pnlLicenseDetails.add(dpnInterfaces);
		}

		//txtDealerComments.setCharacterWidth(59);
		txtDealerComments.setWidth("99%");
		txtDealerComments.setVisibleLines(4);

		//txtDealerComments.setSize("100%", "30pt");
		dpnDealerComments.setWidget(txtDealerComments);

		btnRequest.addClickHandler(this);
		btnRequest.setWidth("80px");
		btnCancel.addClickHandler(this);
		btnCancel.setWidth("80px");

		pnlButtons.add(btnRequest);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		//mainPanel.add(dpnLicense);
		//mainPanel.add(dpnClient);
		mainPanel.add(pnlClientAndOverall);
//		mainPanel.add(dpnQuantRest);
		mainPanel.add(pnlLicenseDetails);
//		if (!apostill)
//			mainPanel.add(dpnStatus);
		mainPanel.add(dpnDealerComments);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);

		Scheduler.get().scheduleDeferred(new ScheduledCommand() {
			@Override
			public void execute() {
				int maxHeight;
				maxHeight = Math.max(dpnClient.getContainerHeight(), dpnLicense.getContainerHeight());
				dpnClient.setContainerHeight(maxHeight);
				dpnLicense.setContainerHeight(maxHeight);

				maxHeight = Math.max(Math.max(dpnQuantRest.getContainerHeight(), dpnModules.getContainerHeight()),
						Math.max(dpnExtraModules.getContainerHeight(), dpnInterfaces.getContainerHeight()));
				dpnQuantRest.setContainerHeight(maxHeight);
				dpnModules.setContainerHeight(maxHeight);
				dpnExtraModules.setContainerHeight(maxHeight);
				dpnInterfaces.setContainerHeight(maxHeight);

//				maxHeight = Math.max(tblClient.getOffsetHeight(), tblLicense.getOffsetHeight());
//				tblClient.setHeight(Integer.toString(maxHeight) + "px");
//				tblLicense.setHeight(Integer.toString(maxHeight) + "px");
//
//				maxHeight = Math.max(Math.max(tblQuantRest.getOffsetHeight(), pnlModules.getOffsetHeight()),
//						Math.max(pnlExtraModules.getOffsetHeight(), pnlInterfaces.getOffsetHeight()));
//				tblQuantRest.setHeight(Integer.toString(maxHeight) + "px");
//				pnlModules.setHeight(Integer.toString(maxHeight) + "px");
//				pnlExtraModules.setHeight(Integer.toString(maxHeight) + "px");
//				pnlInterfaces.setHeight(Integer.toString(maxHeight) + "px");
			}
		});

	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	private void onStartDateChangeForBoost() {
		Date startDate = txtStartDate.getValue();
		Date prevLicStartDate = Common.toLocalDateOnly(this.prevLicense.getStartDate());
		Date prevLicExpiredDate = Common.toLocalDateOnly(this.prevLicense.getExpiredDate());
		Date minStartDate = new Date();
		CalendarUtil.resetTime(minStartDate);
		if (minStartDate.before(prevLicStartDate))
			minStartDate = prevLicStartDate;
		if (startDate.before(minStartDate)) {
			txtStartDate.setValue(minStartDate);
		} else if (startDate.after(prevLicExpiredDate)) {
			txtStartDate.setValue(prevLicExpiredDate);
		}
		startDate = txtStartDate.getValue();
		if ((Common.getYear(startDate) != Common.getYear(minStartDate)
				|| Common.getMonth(startDate) != Common.getMonth(minStartDate))
				&& Common.getDayOfMonth(startDate) != 1) {
			Common.setDayOfMonth(startDate, 1);
			txtStartDate.setValue(startDate);
		}
		onExpiredDateChangeForBoost();
	}
	
	private void onExpiredDateChangeForBoost() {
		Date startDate = txtStartDate.getValue();
		Date expiredDate = txtExpiredDate.getValue();
		Date prevLicExpiredDate = Common.toLocalDateOnly(this.prevLicense.getExpiredDate());
		Date minExpiredDate = CalendarUtil.copyDate(startDate);
		if (Common.getDayOfMonth(minExpiredDate) < 15) {
			Common.setDayOfMonth(minExpiredDate, 1);
			CalendarUtil.addMonthsToDate(minExpiredDate, 1);
		} else {
			Common.setDayOfMonth(minExpiredDate, 1);
			CalendarUtil.addMonthsToDate(minExpiredDate, 2);
		}
		CalendarUtil.addDaysToDate(minExpiredDate, -1);
		Date expiredDateLastDay = CalendarUtil.copyDate(expiredDate);
		Common.setDayOfMonth(expiredDateLastDay, 1);
		CalendarUtil.addMonthsToDate(expiredDateLastDay, 1);
		CalendarUtil.addDaysToDate(expiredDateLastDay, -1);
		if (expiredDateLastDay.after(prevLicExpiredDate) || minExpiredDate.after(prevLicExpiredDate)) {
			txtExpiredDate.setValue(prevLicExpiredDate);
		} else if (expiredDate.before(minExpiredDate)) {
			txtExpiredDate.setValue(minExpiredDate);
		} else if (!CalendarUtil.isSameDate(expiredDate, expiredDateLastDay)) {
			txtExpiredDate.setValue(expiredDateLastDay);
		}
	}

	@Override
	public void onValueChange(ValueChangeEvent<Boolean> event) {
		if (event.getSource() == chkUnlimitedConnections) {

			boolean unlimited = chkUnlimitedConnections.getValue();
			txtLbConnections.setEnabled(!unlimited);
			txtLbConnections.setText(unlimited ? constants.licenseUnlimited()
					: prevLicense == null ? "0" : Integer.toString(prevLicense.getLbConnections()));
			txtRemConnections.setEnabled(!unlimited);
			txtRemConnections.setText(unlimited ? constants.licenseUnlimited()
					: prevLicense == null ? "0" : Integer.toString(prevLicense.getRemConnections()));
			txtOmConnections.setEnabled(!unlimited);
			txtOmConnections.setText(unlimited ? constants.licenseUnlimited()
					: prevLicense == null ? "0" : Integer.toString(prevLicense.getOmConnections()));
//			txtHqConnections.setEnabled(!unlimited);
//			txtHqConnections.setText(unlimited ? constants.licenseUnlimited()
//					: prevLicense == null ? "0" : Integer.toString(prevLicense.getHqConnection()s));
			txtPsConnections.setEnabled(!unlimited);
			txtPsConnections.setText(unlimited ? constants.licenseUnlimited()
					: prevLicense == null ? "0" : Integer.toString(prevLicense.getPsConnections()));
			txtHeConnections.setEnabled(!unlimited);
			txtHeConnections.setText(unlimited ? constants.licenseUnlimited()
					: prevLicense == null ? "0" : Integer.toString(prevLicense.getHeConnections()));

		} else if (event.getSource() == chkCreateTempLicense) {

			refreshTemporaryLicenseFields();

		} else if (event.getSource() == chkDefinitiveLicense) {

			refreshDefinitiveStatus();

		} else if (event.getSource() == chkOnline) {
			
			refreshOnlineStatus();

		} else if (event.getSource() == chkModuled) {
			
			refreshModuledStatus();

		} else if (event.getSource() == chkSaas) {
			
			refreshSaasStatus();
			refreshWmHosted();

		}
	}

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			// TODO Ask to close if changed

			this.hide();

		} else if (sender == btnTestHardCode) {

			String hardCode = HardCodeHelper.unformatHardCode(txtHardCode.getText());

			if (hardCode.isEmpty()) {
				new MessageDialog(messages.hardCodeIsEmptyMessage(), Style.WARNING).show();
			} else if (hardCode.replaceAll("\\s", "").length() != DbLicense.HARD_INFO_STRING_LENGTH) {
				new MessageDialog(messages.hardCodeIllegalFormatMessage(), Style.WARNING).show();
			} else {
				UntillLicenseServer.getLicenseServiceAsync().validLicenseHardCode(hardCode, new AsyncCallback<Boolean>() {
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show(caught);
					}
					@Override
					public void onSuccess(Boolean result) {
						if (result) {
							new MessageDialog(messages.hardCodeIsValidMessage(), Style.INFORMATION).show();
						} else {
							new MessageDialog(messages.hardCodeBadCheckSumMessage(), Style.WARNING).show();
						}
					}
				});
			}

		} else if (sender == btnRequest) {

			// Create license object
			DbLicense license = new DbLicense();

			/// Fill and check entered data

			// license
			if (requestType == RequestType.CREATE_BOOST) {
				if (txtStartDate.getValue() == null
						|| Common.toUtcDateOnly(txtStartDate.getValue()).before(prevLicense.getStartDate())) {
					txtStartDate.setFocus(true);
					new MessageDialog(messages.incorrectStartDate(), Style.WARNING).show();
					return;
				}
				if (txtExpiredDate.getValue() == null
						|| Common.toUtcDateOnly(txtExpiredDate.getValue()).before(Common.toUtcDateOnly(txtStartDate.getValue()))
						|| !prevLicense.isNeverExpired()
						&& Common.toUtcDateOnly(txtExpiredDate.getValue()).after(prevLicense.getExpiredDate())) {
					txtExpiredDate.setFocus(true);
					new MessageDialog(messages.incorrectEndDate(), Style.WARNING).show();
					return;
				}
			}
			license.setStartDate(Common.toUtcDateOnly(txtStartDate.getValue()));
			license.setExpiredDate(Common.toUtcDateOnly(txtExpiredDate.getValue()));
			if (requestType == RequestType.EMERGENCY) {
				license.tempExpiredDate = null;
			} else {
				if (txtTempExpiredDate.isAttached() && chkCreateTempLicense.getValue()) {
					Date tempExpiredDate = Common.toUtcDateOnly(txtTempExpiredDate.getValue());
					if (tempExpiredDate == null
							|| tempExpiredDate.before(license.getStartDate())
							|| tempExpiredDate.after(license.getExpiredDate()))
					{
						txtTempExpiredDate.setFocus(true);
						new MessageDialog(messages.incorrectTemporaryEndDate(), Style.WARNING).show();
						return;
					}
					license.tempExpiredDate = tempExpiredDate;
				} else {
					license.tempExpiredDate = null;
				}
			}

			// client
			if (requestType == RequestType.EMERGENCY) {
				license.setClientName(prevLicense.getClientName());
				license.setClientDisplayName(prevLicense.getClientDisplayName());
				license.setClientAddress(prevLicense.getClientAddress());
				license.setClientEmail(prevLicense.getClientEmail());
				license.setClientPhone(prevLicense.getClientPhone());
				license.chain = prevLicense.chain;
				license.hardCode = HardCodeHelper.unformatHardCode(txtHardCode.getText());
				if (license.hardCode.equals(prevLicense.hardCode)) {
					txtHardCode.selectAll();
					new MessageDialog(messages.pleaseEnterAnotherHardCode(), Style.WARNING,
							txtHardCode).show();
					return;
				}
			} else if (requestType == RequestType.CREATE_BOOST) {
				license.setClientName(prevLicense.getClientName());
				license.setClientDisplayName(prevLicense.getClientDisplayName());
				license.setClientAddress(prevLicense.getClientAddress());
				license.setClientEmail(prevLicense.getClientEmail());
				license.setClientPhone(prevLicense.getClientPhone());
				license.chain = prevLicense.chain;
				license.hardCode = prevLicense.hardCode;
			} else {
				if (txtClientName.getText().trim().isEmpty()) {
					txtClientName.selectAll();
					new MessageDialog(messages.pleaseEnterClientNameMessage(), Style.WARNING,
							txtClientName).show();
					return;
				}
				license.setClientName(txtClientName.getText().trim());
				if (txtClientDisplayName.getText().trim().isEmpty()) {
					txtClientDisplayName.selectAll();
					new MessageDialog(messages.pleaseEnterClientDisplayNameMessage(), Style.WARNING,
							txtClientDisplayName).show();
					return;
				}
				license.setClientDisplayName(txtClientDisplayName.getText().trim());
				license.setClientAddress(txtClientAddress.getText().trim());
				license.setClientEmail(txtClientEmail.getText().trim());
				license.setClientPhone(txtClientPhone.getText().trim());
				license.chain = txtChain.getText().trim().isEmpty() ? null : txtChain.getText().trim();
				if (chkOnline.getValue())
					license.hardCode = requestType != RequestType.CREATE ? prevLicense.hardCode : null ;
				else
					license.hardCode = HardCodeHelper.unformatHardCode(txtHardCode.getText());
			}

			// Quantitative restrictions
			if (requestType == RequestType.EMERGENCY) {
				license.setLbConnections(prevLicense.getLbConnections());
				license.setRemConnections(prevLicense.getRemConnections());
				license.setOmConnections(prevLicense.getOmConnections());
//				license.setHqConnections(prevLicense.getHqConnections());
				license.setPsConnections(prevLicense.getPsConnections());
				license.setHeConnections(prevLicense.getHeConnections());
			} else {
				if (chkUnlimitedConnections.getValue()) {
					license.setLbConnections((short) 0);
					license.setRemConnections((short) 0);
					license.setOmConnections((short) 0);
//					license.setHqConnections((short) 0);
					license.setPsConnections((short) 0);
					license.setHeConnections((short) 0);
					if (requestType != RequestType.CREATE_BOOST)
						license.setUnlimited(true);
				} else {
					TextBox lastWorkTextBox = null;
					try {
						lastWorkTextBox = txtLbConnections;
						license.setLbConnections(Short.parseShort(txtLbConnections.getText().trim()));
						if (license.getLbConnections() < 0) throw new IllegalArgumentException();
						lastWorkTextBox = txtRemConnections;
						license.setRemConnections(Short.parseShort(txtRemConnections.getText().trim()));
						if (license.getRemConnections() < 0) throw new IllegalArgumentException();
						lastWorkTextBox = txtOmConnections;
						license.setOmConnections(Short.parseShort(txtOmConnections.getText().trim()));
						if (license.getOmConnections() < 0) throw new IllegalArgumentException();
//						lastWorkTextBox = txtHqConnections;
//						license.setHqConnections(Short.parseShort(txtHqConnections.getText().trim()));
//						if (license.getHqConnections() < 0) throw new IllegalArgumentException();
						lastWorkTextBox = txtPsConnections;
						license.setPsConnections(Short.parseShort(txtPsConnections.getText().trim()));
						if (license.getPsConnections() < 0) throw new IllegalArgumentException();
						lastWorkTextBox = txtHeConnections;
						license.setHeConnections(Short.parseShort(txtHeConnections.getText().trim()));
						if (license.getHeConnections() < 0) throw new IllegalArgumentException();
					} catch (NumberFormatException e) {
						lastWorkTextBox.selectAll();
						new MessageDialog(messages.unknownNumberFormatOfConnectionsMessage(),
								Style.WARNING, lastWorkTextBox).show();
						return;
					} catch (IllegalArgumentException e) {
						lastWorkTextBox.selectAll();
						new MessageDialog(messages.negativeValueOfConnectionsMessage(),
								Style.WARNING, lastWorkTextBox).show();
						return;
					}
					if (requestType != RequestType.CREATE_BOOST && license.getLbConnections() == 0) {
						txtLbConnections.selectAll();
						new MessageDialog(messages.sumOfConnectionsIsZeroMessage(), Style.WARNING,
								txtLbConnections).show();
						return;
					}
				}
			}

			// Extra unTill modules and Interfaces
			if (requestType == RequestType.EMERGENCY) {
				license.setFuncLimited(prevLicense.getFuncLimited());
			} else {
				if (requestType == RequestType.UPGRADE)
					license.setFuncLimited(prevLicense.getFuncLimited());
				else
					license.setFuncLimited(0L);
				for (int i = 0; i < chkaEnabledFunctions.length; i++) {
					if (chkaEnabledFunctions[i] == null) continue;
					if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE && !chkModuled.getValue())
						continue;
					if (chkaEnabledFunctions[i].getValue()) {
						license.setFuncLimited(license.getFuncLimited() | (1l << i));
						// Special behavior for BEVERAGE_CONTROL (+ BERG, BECONET, FRIJADO)
						if (i == DbLicense.FUNC_BEVERAGE_CONTROL) {
							license.setFuncLimited(license.getFuncLimited() | 1l << DbLicense.FUNC_BEVERAGE_CONTROL);
							license.setFuncLimited(license.getFuncLimited() | 1l << DbLicense.FUNC_BERG);
							license.setFuncLimited(license.getFuncLimited() | 1l << DbLicense.FUNC_BECONET);
							license.setFuncLimited(license.getFuncLimited() | 1l << DbLicense.FUNC_FRIJADO);
						}
					}
				}
				if (requestType == RequestType.CREATE_BOOST) {
					license.setFuncLimited(license.getFuncLimited() & ~prevLicense.getFuncLimited());
				}
			}

			if (requestType == RequestType.CREATE_BOOST) {
				if (license.getLbConnections() + license.getRemConnections() + license.getOmConnections()
						/*+ license.getHqConnections() */+ license.getPsConnections() + license.getHeConnections() <= 0
						&& (license.getFuncLimited() & ~(DbLicense.FUNC_NON_CHARGED_MASK)) == 0) {
					new MessageDialog(messages.noImprovementFoundForBoostLicense(), Style.WARNING, txtLbConnections).show();
					return;
				}
			}

			// Status
			if (requestType == RequestType.EMERGENCY) {
				license.setStatus(prevLicense.getStatus());
				license.setDefinitiveMaxVersion(prevLicense.getDefinitiveMaxVersion());
				license.setNeverExpired(false);
				license.setOnline(false);
				license.autoProlongable = false;
				license.minVersion = prevLicense.minVersion;
				license.requestLargeAccount = false;
				license.largeAccount = prevLicense.largeAccount;
				license.hwmYearly = prevLicense.hwmYearly;
				license.hwmFirst = false;
			} else if (requestType == RequestType.CREATE_BOOST) {
				license.setStatus(prevLicense.getStatus());
				license.setDefinitiveMaxVersion(prevLicense.getDefinitiveMaxVersion());
				license.setNeverExpired(false);
				license.setBoost(true);
				license.autoProlongable = false;
				license.minVersion = prevLicense.minVersion;
				license.requestLargeAccount = false;
				license.largeAccount = prevLicense.largeAccount;
				license.hwmYearly = false;
				license.hwmFirst = false;
			} else {
				license.setStatusActive(true);
				license.setDefinitive(chkDefinitiveLicense.getValue());
				if (license.isDefinitive()) {
					try {
						license.setDefinitiveMaxVersion(Integer.parseInt(txtDefMjVersion.getText()));
					} catch (NumberFormatException e) {
						txtDefMjVersion.selectAll();
						new MessageDialog(messages.unknownNumberFormatOfDefinitiveVersion(), 
								Style.WARNING, txtDefMjVersion).show();
						return;
					}
				}
				//license.setNeverExpired(chkDefinitiveLicense.getValue());
				license.setTrial(chkTrial.getValue());
				license.setOnline(chkOnline.getValue());
				license.setApostill(this.apostill);
				license.setModuled(chkModuled.getValue());
				license.setSaas(chkSaas.getValue());
				// AUTO_PROLONGABLE
				license.autoProlongable = chkAutoProlong.getValue();
				license.minVersion = chkVersionLowerThan136.getValue()
						? (requestType != RequestType.CREATE && prevLicense.minVersion < 136 ? prevLicense.minVersion : 0)
						: (requestType != RequestType.CREATE && prevLicense.minVersion >= 136 ? prevLicense.minVersion : 136);
				if (requestType == RequestType.CREATE) {
					license.largeAccount = false;
					license.requestLargeAccount = chkLargeAccount.getValue();
					license.hwmYearly = chkHwmYearly.getValue();
					license.hwmFirst = license.getFunction(DbLicense.FUNC_WM_HOSTED) && license.hwmYearly;
				} else { // PROLONG, UPGRADE
					license.largeAccount = chkLargeAccount.getValue();
					license.requestLargeAccount = false; // !prevLicense.largeAccount && license.largeAccount;
					license.hwmYearly = chkHwmYearly.getValue();
					license.hwmFirst = license.getFunction(DbLicense.FUNC_WM_HOSTED)
							&& !prevLicense.getFunction(DbLicense.FUNC_WM_HOSTED) && license.hwmYearly;
				}
				if (chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION] != null
						&& chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].getValue()) {
					if (!fiscalDialog.checkParameters())
						return;
					license.setFiscal(fiscalDialog.getFiscal());
				}
			}

			// PROLONG_PARAMS
			if (requestType == RequestType.UPGRADE) {
				if (license.getLbConnections() < prevLicense.getLbConnections()) {
					if (license.prolongParams == null) license.prolongParams = new ProlongParams();
					license.prolongParams.setLbConnections(license.getLbConnections());
					license.setLbConnections(prevLicense.getLbConnections());
				}
				if (license.getRemConnections() < prevLicense.getRemConnections()) {
					if (license.prolongParams == null) license.prolongParams = new ProlongParams();
					license.prolongParams.setRemConnections(license.getRemConnections());
					license.setRemConnections(prevLicense.getRemConnections());
				}
				if (license.getOmConnections() < prevLicense.getOmConnections()) {
					if (license.prolongParams == null) license.prolongParams = new ProlongParams();
					license.prolongParams.setOmConnections(license.getOmConnections());
					license.setOmConnections(prevLicense.getOmConnections());
				}
//				if (license.getHqConnections() < prevLicense.getHqConnections()) {
//					if (license.prolongParams == null) license.prolongParams = new ProlongParams();
//					license.prolongParams.setHqConnections(license.getHqConnections());
//					license.setHqConnections(prevLicense.getHqConnections());
//				}
				if (license.getPsConnections() < prevLicense.getPsConnections()) {
					if (license.prolongParams == null) license.prolongParams = new ProlongParams();
					license.prolongParams.setPsConnections(license.getPsConnections());
					license.setPsConnections(prevLicense.getPsConnections());
				}
				if (license.getHeConnections() < prevLicense.getHeConnections()) {
					if (license.prolongParams == null) license.prolongParams = new ProlongParams();
					license.prolongParams.setHeConnections(license.getHeConnections());
					license.setHeConnections(prevLicense.getHeConnections());
				}
				
			}

			license.dealerComments = txtDealerComments.getText();

			if (requestType == RequestType.UPGRADE && license.getFunction(DbLicense.FUNC_WM_HOSTED)
					&& !prevLicense.getFunction(DbLicense.FUNC_WM_HOSTED)) {
				if (!license.dealerComments.isEmpty() && !license.dealerComments.endsWith("\n"))
					license.dealerComments += "\r\n";
				license.dealerComments += (license.getFunction(DbLicense.FUNC_WM_HQ) ?
						DbLicense.FUNC_NAMES[DbLicense.FUNC_WM_HQ] : DbLicense.FUNC_NAMES[DbLicense.FUNC_WM_CENTRAL_BO])
						+ " became free because of " + DbLicense.FUNC_NAMES[DbLicense.FUNC_WM_HOSTED] + "\r\n";
			}

			if (requestType == RequestType.CREATE) {
				license.idDealers = dealer.id;
				license.id = 0;
			} else {
				license.idDealers = prevLicense.idDealers;
				license.id = prevLicense.id;
			}

			// additional confirmation for boost
			if (requestType == RequestType.CREATE_BOOST) {

				new LicenseDetailsDialog(prevLicense, license, ok -> {
					if (ok) {

						// Call RPC LicenseService.requestLicense
						UntillLicenseServer.getLicenseServiceAsync().requestLicense(UntillLicenseServer.getAuthScheme(),
								license, requestType, this);
						WaitDialog.show();
				
					}
				}).show();
				
			} else {
			
				// Call RPC LicenseService.requestLicense
				UntillLicenseServer.getLicenseServiceAsync().requestLicense(UntillLicenseServer.getAuthScheme(),
						license, requestType, this);
				WaitDialog.show();

			}
		}
	}

	/// requestLicense callback handling

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		if (caught instanceof AuthFailedException) {
			this.hide();
			LoginPage.get().show();
		} else {
			ErrorDialog.show(caught);
		}
	}

	@Override
	public void onSuccess(RequestLicenseResult result) {
		WaitDialog.hide();

		String message = null;
		boolean closeForm = false;
		MessageDialog.Style messageStyle = Style.WARNING;
		Focusable setFocusTo = null;

		switch (result.getResult()) {
		case FAIL_UNEXPECTED:
			messageStyle = Style.EXCLAMATION;
			message = messages.unexpectedFailureMessage() + " ("
					+ result.getUnexpectedMessage() + ")";
			break;
		case FAIL_ILLEGAL_REQUEST:
			messageStyle = Style.EXCLAMATION;
			message = messages.illegalRequestMessage();
			break;
		case FAIL_FUNC_LIMITED_DOWNGRADE:
			messageStyle = Style.EXCLAMATION;
			message = messages.downgradeFuncLimitedMessage();
			break;
		case FAIL_CLIENT_NAME_IS_EMPTY:
			setFocusTo = txtClientName;
			message = messages.pleaseEnterClientNameMessage();
			break;
		case FAIL_CLIENT_DISPLAY_NAME_IS_EMPTY:
			setFocusTo = txtClientDisplayName;
			message = messages.pleaseEnterClientDisplayNameMessage();
			break;
		case FAIL_HARD_CODE_IS_NO_VALID:
			setFocusTo = txtHardCode;
			message = messages.hardCodeIsNoValidMessage();
			break;
		case FAIL_LB_CONNECTIONS_IS_NEGATIVE:
			setFocusTo = txtLbConnections;
			message = messages.negativeValueOfConnectionsMessage();
			break;
		case FAIL_LB_CONNECTIONS_IS_DOWNGRADE:
			setFocusTo = txtLbConnections;
			message = messages.downgradeConnectionsMessage();
			break;
		case FAIL_REM_CONNECTIONS_IS_NEGATIVE:
			setFocusTo = txtRemConnections;
			message = messages.negativeValueOfConnectionsMessage();
			break;
		case FAIL_REM_CONNECTIONS_IS_DOWNGRADE:
			setFocusTo = txtRemConnections;
			message = messages.downgradeConnectionsMessage();
			break;
		case FAIL_OM_CONNECTIONS_IS_NEGATIVE:
			setFocusTo = txtOmConnections;
			message = messages.negativeValueOfConnectionsMessage();
			break;
		case FAIL_OM_CONNECTIONS_IS_DOWNGRADE:
			setFocusTo = txtOmConnections;
			message = messages.downgradeConnectionsMessage();
			break;
//		case FAIL_HQ_CONNECTIONS_IS_NEGATIVE:
//			setFocusTo = (txtHqConnections);
//			message = messages.negativeValueOfConnectionsMessage();
//			break;
//		case FAIL_HQ_CONNECTIONS_IS_DOWNGRADE:
//			setFocusTo = (txtHqConnections);
//			message = messages.downgradeConnectionsMessage();
//			break;
		case FAIL_PS_CONNECTIONS_IS_NEGATIVE:
			setFocusTo = txtPsConnections;
			message = messages.negativeValueOfConnectionsMessage();
			break;
		case FAIL_PS_CONNECTIONS_IS_DOWNGRADE:
			setFocusTo = txtPsConnections;
			message = messages.downgradeConnectionsMessage();
			break;
		case FAIL_HE_CONNECTIONS_IS_NEGATIVE:
			setFocusTo = txtHeConnections;
			message = messages.negativeValueOfConnectionsMessage();
			break;
		case FAIL_HE_CONNECTIONS_IS_DOWNGRADE:
			setFocusTo = txtHeConnections;
			message = messages.downgradeConnectionsMessage();
			break;
		case FAIL_LB_CONNECTIONS_IS_ZERO:
			setFocusTo = txtLbConnections;
			message = messages.sumOfConnectionsIsZeroMessage();
			break;
		case FAIL_PERIOD_IS_INCORRECT:
			// Request new START_DATE and EXPIRED_DATE
			this.requestLicensePeriod();
			message = messages.periodIsIncorrectMessage();
			break;
		case FAIL_HARD_CODE_ALREADY_EXISTS:
			setFocusTo = txtHardCode;
			message = messages.hardCodeAlreadyExistsMessage();
			break;
		case FAIL_CLIENT_ALREADY_EXISTS:
			setFocusTo = txtClientName;
			message = messages.clientAlreadyExistsMessage();
			break;
		case FAIL_EMERGENCY_IS_NOT_AVAILABLE:
			closeForm = true;
			message = messages.emergencyLicensesAreNotAvailableMessage();
			break;
		case FAIL_SEND_EMERGENCY_LICENSE:
			message = messages.failSendEmergencyLicenseMessage();
			messageStyle = Style.EXCLAMATION;
			break;
		case FAIL_DEFINITIVE_MAX_VERSION_IS_WRONG:
			setFocusTo = txtDefMjVersion;
			message = messages.wrongValueOfDefinitiveMaxVersionMessage();
			break;
		case FAIL_CHAIN_USE_FOR_ANOTHER_HWM_PM:
			setFocusTo = txtChain;
			message = "Chain already in use for another Hosted WM payment method"; // TODO: to messages
			break;
		case SUCCESS_BUT_FAIL_SEND_REQUEST_MESSAGE:
			closeForm = true;
			message = messages.requestCreatedButNotSendMessage();
			messageStyle = Style.WARNING;
			break;
		case SUCCESS:
			closeForm = true;
			if (requestType == RequestType.EMERGENCY)
				message = messages.licenseSuccessfullySentMessage();
			else
				message = messages.licenseSuccessfullyRequestedMessage();
			messageStyle = Style.INFORMATION;
			break;
		case SUCCESS_NO_UPGRADE:
			closeForm = true;
			break;
		default:
			message = messages.unexpectedFailureMessage();
			messageStyle = Style.EXCLAMATION;
		}

		if (setFocusTo != null && setFocusTo instanceof TextBox)
			((TextBox) setFocusTo).selectAll();
		if (message != null)
			new MessageDialog(message, messageStyle, setFocusTo).show();

		if (closeForm) {
			this.hide();
			MainPage.get().refresh();
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void requestLicensePeriod() {
		if (requestType == RequestType.UPGRADE) {
			// Simple fill with current dates
			txtStartDate.setValue(Common.toLocalDateOnly(this.prevLicense.getStartDate()));
			txtExpiredDate.setValue(Common.toLocalDateOnly(this.prevLicense.getExpiredDate()));
			refreshTemporaryLicenseFields();
		} else if (requestType == RequestType.CREATE_BOOST) {
			Date nowLocalDate = new Date();
			CalendarUtil.resetTime(nowLocalDate);
			txtStartDate.setValue(nowLocalDate);
			txtExpiredDate.setValue(new Date(0));
			onStartDateChangeForBoost();
		} else {
			// Disable request button
			btnRequest.setEnabled(false);
			if (requestType == RequestType.EMERGENCY) {
				Date prevExpiredDate = this.prevLicense.getExpiredDate();
				if (this.prevLicense.isNeverExpired())
					prevExpiredDate = null;
				UntillLicenseServer.getLicenseServiceAsync().getEmergencyLicensePeriod(prevExpiredDate,
						new GetNewLicensePeriodAsyncCallback());
			} else {
				// Get license type
				LicenseType licenseType = this.apostill
						? (chkTrial.getValue() ? LicenseType.APOSTILL_TRIAL : LicenseType.APOSTILL)
						: (chkTrial.getValue() ? LicenseType.DEMO : (chkSaas.getValue() ? LicenseType.SAAS : LicenseType.GENERAL));
				if (requestType == RequestType.CREATE) {
					UntillLicenseServer.getLicenseServiceAsync().getNewLicensePeriod(licenseType,
							new GetNewLicensePeriodAsyncCallback());
				} else if (requestType == RequestType.PROLONG) {
					UntillLicenseServer.getLicenseServiceAsync().getProlongLicensePeriod(licenseType,
							new LicensePeriod(this.prevLicense.getStartDate(), this.prevLicense.getExpiredDate()),
							new GetNewLicensePeriodAsyncCallback());
				}
			}
		}
	}

	private class GetNewLicensePeriodAsyncCallback implements AsyncCallback<LicensePeriod> {

		@Override
		public void onFailure(Throwable caught) {
			// Unlikely
			LicenseDialog.this.onFailure(caught);
		}

		@Override
		public void onSuccess(LicensePeriod result) {
			txtStartDate.setValue(Common.toLocalDateOnly(result.getStartDate()));
			txtExpiredDate.setValue(Common.toLocalDateOnly(result.getExpiredDate()));

			refreshTemporaryLicenseFields();

			btnRequest.setEnabled(true);
		}
	}

	private void requestChains() {
		// Call RPC
		UntillLicenseServer.getLicenseServiceAsync().getChains(UntillLicenseServer.getAuthScheme(),
				requestType == RequestType.CREATE ? dealer.id : prevLicense.idDealers, false,
				new AsyncCallback<List<String>>() {
					@Override
					public void onSuccess(List<String> result) {
						chainOracle.addAll(result);
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error get chains", caught);
					}
				});
	}

	private boolean funcFiscalizationEnabled;
	
	private void setupFiscalizationFuncDeps() {
		if (chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION] == null)
			return;

		funcFiscalizationEnabled = chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].isEnabled();
		if (funcFiscalizationEnabled) {
			chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].addValueChangeHandler(event -> { refreshFiscalizationFuncDeps(); });
			chkOnline.addValueChangeHandler(event -> { refreshFiscalizationFuncDeps(); });
		}

		btnFiscal.addClickHandler(event -> { fiscalDialog.show(); });

		refreshFiscalizationFuncDeps();
	}

	private boolean lastStateOfFuncFiscalization = false;

	private void refreshFiscalizationFuncDeps() {
		if (funcFiscalizationEnabled) {
			if (chkOnline.getValue() && allowFiscalization) {
				if (!chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].isEnabled()) {
					chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].setValue(lastStateOfFuncFiscalization);
					chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].setEnabled(true);
				}
			} else {
				if (chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].isEnabled()) {
					lastStateOfFuncFiscalization = chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].getValue();
					chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].setValue(false);
					chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].setEnabled(false);
				}
			}
		}

		btnFiscal.setEnabled(chkaEnabledFunctions[DbLicense.FUNC_FISCALIZATION].getValue());
	}


	private boolean funcWmHqEnabled;
	private boolean funcWmCentralBoEnabled;
	private boolean funcWmHostedEnabled;
	private boolean chkHwmYearlyEnabled;

	private void setupWmFuncDeps() {
		if (chkaEnabledFunctions[DbLicense.FUNC_WM_HQ] == null
				|| chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO] == null
				|| chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED] == null)
			return;

		funcWmHqEnabled = chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].isEnabled();
		funcWmCentralBoEnabled = chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].isEnabled();
		funcWmHostedEnabled = chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].isEnabled();
		chkHwmYearlyEnabled = chkHwmYearly.isEnabled();

		if (funcWmHqEnabled && (funcWmCentralBoEnabled || funcWmHostedEnabled))
			chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].addValueChangeHandler(event -> { refreshWmFuncDeps(); });
		if (funcWmCentralBoEnabled && (funcWmHqEnabled || funcWmHostedEnabled))
			chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].addValueChangeHandler(event -> { refreshWmFuncDeps(); });

		if (funcWmHostedEnabled && chkHwmYearlyEnabled)
			chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].addValueChangeHandler(event -> { refreshWmHosted(); });

		refreshWmFuncDeps(); // -> refreshWmHosted()
	}

	private boolean lastStateOfFuncWmHosted = false;

	private void refreshWmFuncDeps() {
		if (funcWmHqEnabled) {
			if (chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].isEnabled()
					&& chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].getValue())
				chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].setEnabled(false);
			if (!chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].isEnabled()
					&& !chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].getValue())
				chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].setEnabled(true);
		}
			
		if (funcWmCentralBoEnabled) {
			if (chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].isEnabled()
					&& chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].getValue())
				chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].setEnabled(false);
			if (!chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].isEnabled()
					&& !chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].getValue())
				chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].setEnabled(true);
		}

		if (funcWmHostedEnabled) {
			boolean anyWmFuncChecked = chkaEnabledFunctions[DbLicense.FUNC_WM_HQ].getValue() ||
					chkaEnabledFunctions[DbLicense.FUNC_WM_CENTRAL_BO].getValue();
			if (chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].isEnabled() && !anyWmFuncChecked) {
				lastStateOfFuncWmHosted = chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].getValue();
				chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].setValue(false);
				chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].setEnabled(false);
			} else if (!chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].isEnabled() && anyWmFuncChecked) {
				chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].setValue(lastStateOfFuncWmHosted);
				chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].setEnabled(true);
			}
		}

		refreshWmHosted();
	}

	private boolean lastStateOfChkHwmYearly = false;

	private void refreshWmHosted() {
		if (chkHwmYearlyEnabled) {
			if (chkaEnabledFunctions[DbLicense.FUNC_WM_HOSTED].getValue() && !chkSaas.getValue()) {
				if (!chkHwmYearly.isEnabled()) {
					chkHwmYearly.setValue(lastStateOfChkHwmYearly);
					chkHwmYearly.setEnabled(true);
				}
			} else {
				if (chkHwmYearly.isEnabled()) {
					lastStateOfChkHwmYearly = chkHwmYearly.getValue();
					chkHwmYearly.setValue(false);
					chkHwmYearly.setEnabled(false);
				}
			}	
		}
	}

	private void refreshDefinitiveStatus() {
		txtDefMjVersion.setReadOnly(!chkDefinitiveLicense.getValue());
		if (chkDefinitiveLicense.getValue() && txtDefMjVersion.getText().isEmpty()) {
			UntillLicenseServer.getLicenseServiceAsync().getMaxDefinitiveMaxVersion(
					UntillLicenseServer.getAuthScheme(), new AsyncCallback<Integer>() {
				@Override
				public void onFailure(Throwable caught) {
					ErrorDialog.show(caught);
				}
				@Override
				public void onSuccess(Integer result) {
					if (result != 0 && txtDefMjVersion.getText().isEmpty())
						txtDefMjVersion.setText(Integer.toString(result));
				}
			});

		}
	}

	private String txtHardCode_storedValue = null;
	private boolean chkSaas_storedValue = false;

	private void refreshOnlineStatus() {
		if (chkOnline.getValue()) {
			lblHardCode.setText(constants.licenseUidLabel() + ":");
			if (requestType == RequestType.CREATE) {
				if (txtHardCode.isEnabled()) {
					txtHardCode_storedValue = txtHardCode.getText();
					txtHardCode.setText("");
					txtHardCode.setEnabled(false);
					btnTestHardCode.setEnabled(false);
				}
				if (!chkSaas.isEnabled()) {
					chkSaas.setValue(chkSaas_storedValue);
					chkSaas.setEnabled(true);
				}
			}
		} else {
			lblHardCode.setText(constants.hardCodeLabel() + ":");
			if (requestType == RequestType.CREATE || requestType == RequestType.EMERGENCY) {
				if (!txtHardCode.isEnabled()) {
					if (txtHardCode_storedValue != null) {
						txtHardCode.setValue(txtHardCode_storedValue);
					}
					txtHardCode.setEnabled(true);
					btnTestHardCode.setEnabled(true);
				}
				if (chkSaas.isEnabled()) {
					chkSaas_storedValue = chkSaas.getValue();
					chkSaas.setValue(false);
					chkSaas.setEnabled(false);
				}
			}
		}
		if (Restrictions.AUTO_PROLONG_ONLY_FOR_ONLINE) {
			if (chkOnline.getValue()) {
				if (!chkAutoProlong.isEnabled())
					chkAutoProlong.setValue(chkAutoProlong_storedValue);
			} else {
				if (chkAutoProlong.isEnabled()) {
					chkAutoProlong_storedValue = chkAutoProlong.getValue();
					chkAutoProlong.setValue(false);
				}
			}
			chkAutoProlong.setEnabled(chkOnline.getValue() && requestType != RequestType.EMERGENCY
					&& requestType != RequestType.CREATE_BOOST);
		}
		refreshSaasStatus();
	}

	private void refreshModuledStatus() {
		if (requestType != RequestType.CREATE)
			return;
		for (int i = 0 ; i < chkaEnabledFunctions.length; i++) {
			if (chkaEnabledFunctions[i] == null) continue;
			if (DbLicense.FUNC_PROPS[i].getType() != FuncType.FT_MODULE) continue;
			if (i == DbLicense.FUNC_MODULE_BASIC) {
				chkaEnabledFunctions[i].setEnabled(false);
				chkaEnabledFunctions[i].setValue(chkModuled.getValue());
			} else {
				chkaEnabledFunctions[i].setEnabled(chkModuled.getValue());
			}
		}
	}

	private void refreshSaasStatus() {
		requestLicensePeriod();
		if (Restrictions.AUTO_PROLONG_ONLY_FOR_SAAS) {
			if (chkSaas.getValue()) {
				if (!chkAutoProlong.isEnabled())
					chkAutoProlong.setValue(chkAutoProlong_storedValue);
			} else {
				if (chkAutoProlong.isEnabled()) {
					chkAutoProlong_storedValue = chkAutoProlong.getValue();
					chkAutoProlong.setValue(false);
				}
			}
			chkAutoProlong.setEnabled(chkSaas.getValue());
		}
	}
	
	private Boolean chkCreateTempLicense_storedValue = false;
	private Date txtTempExpiredDate_storedValue = null;

	private void refreshTemporaryLicenseFields() {
		if (!txtTempExpiredDate.isAttached())
			return;
		if (!chkCreateTempLicense.isEnabled()) {
			if (txtTempExpiredDate_storedValue == null && txtStartDate.getValue() != null 
					&& txtExpiredDate.getValue() != null) {
				// generate default value
				txtTempExpiredDate_storedValue = new Date();
				CalendarUtil.resetTime(txtTempExpiredDate_storedValue);
				if (txtTempExpiredDate_storedValue.before(txtStartDate.getValue()))
					txtTempExpiredDate_storedValue = CalendarUtil.copyDate(txtStartDate.getValue());
				CalendarUtil.addMonthsToDate(txtTempExpiredDate_storedValue, 1);
				if (txtTempExpiredDate_storedValue.after(txtExpiredDate.getValue()))
					txtTempExpiredDate_storedValue = txtExpiredDate.getValue();
			}
			if (txtTempExpiredDate_storedValue != null) {
				chkCreateTempLicense.setEnabled(true);
				chkCreateTempLicense.setValue(chkCreateTempLicense_storedValue);
				txtTempExpiredDate.setValue(txtTempExpiredDate_storedValue);
			}
		}
		if (chkCreateTempLicense.isEnabled())
			txtTempExpiredDate.setEnabled(chkCreateTempLicense.getValue());
	}
	
	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		switch (this.requestType) {
		case PROLONG: txtClientDisplayName.setFocus(true); break;
		case UPGRADE: txtClientDisplayName.setFocus(true); break;
		case EMERGENCY: txtHardCode.setFocus(true);   break;
		case CREATE_BOOST: txtLbConnections.setFocus(true); break;
		case CREATE: txtClientName.setFocus(true); break;
		}
	}

}
