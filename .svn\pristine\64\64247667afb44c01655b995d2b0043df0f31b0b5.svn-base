package eu.untill.license.client.ui;

import java.util.Date;
import java.util.List;

import com.google.gwt.core.client.JsDate;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.user.datepicker.client.DatePicker;

import eu.untill.license.client.Common;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.shared.DbHwmInvoice;

public class HwmReportDialog extends DialogBox implements ClickHandler {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Hosted WM report")
		String hwmReportDialogHeader();

		@DefaultStringValue("Month")
		String monthLabel();

		@DefaultStringValue("Export")
		String exportButton();
		String closeButton();

		@DefaultStringValue("Chain")
		String chainColumnHeader();
		@DefaultStringValue("Number")
		String numberColumnHeader();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	  /////////////
	 // Members //
	/////////////

	private String csv;

	  //////////////////
	 // Constructors //
	//////////////////

	public HwmReportDialog() {
		// Generate base interface
		generateUI();

		JsDate jsDate = JsDate.create();
		jsDate.setMonth(jsDate.getMonth() - 1);
		txtMonth.setValue(new Date((long) jsDate.getTime()));
		
		this.setText(constants.hwmReportDialogHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnParameters = new EntitledDecoratorPanel();
	private FlexTable tblParameters = new FlexTable();
	private Label lblMonth = new Label(constants.monthLabel() + ":", false);
	private DateBox txtMonth = new DateBox(new DatePicker(), null,
			new DateBox.DefaultFormat(DateTimeFormat.getFormat("LLLL yyyy")));

	private EntitledDecoratorPanel dpnData = new EntitledDecoratorPanel();
	private FlexTable tblData = new FlexTable();
	private ScrollPanel scrData = new ScrollPanel(tblData);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnExport = new Button(constants.exportButton(), this);
	private Button btnClose = new Button(constants.closeButton(), this);

	private void generateUI() {

		// Assemble parameters panel
		txtMonth.addValueChangeHandler(event -> {
			requestData();
		});
		int row = 0;
		tblParameters.setWidget(row, 0, lblMonth);
		tblParameters.setWidget(row++, 1, txtMonth);
		dpnParameters.setWidget(tblParameters);

		// Create date table
		tblData.addStyleName("al-Table"); 
		tblData.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblData.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "150px");
		tblData.setText(0, col++, constants.chainColumnHeader());
		tableCF.setWidth(0, col, "170px");
		tblData.setText(0, col++, constants.numberColumnHeader());

		// Assemble data panel with scroll
		scrData.setSize("400", "200px");
		dpnData.setWidget(scrData);

		// Assemble button panel
		btnExport.setWidth("80px");
		btnClose.setWidth("80px");
		pnlButtons.setWidth("100%");
		pnlButtons.add(btnExport);
		pnlButtons.setCellHorizontalAlignment(btnExport, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.setCellWidth(btnExport, "100%");
		pnlButtons.add(btnClose);
		pnlButtons.setCellHorizontalAlignment(btnClose, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnParameters);
		mainPanel.add(dpnData);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnExport) {

			Common.export(csv, "export.csv", "text/csv");

		} else if (sender == btnClose) {

			this.hide();
			
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

    private void requestData() {
		UntillLicenseServer.getLicenseServiceAsync().getHwmInvoiceReport(
				UntillLicenseServer.getAuthScheme(), txtMonth.getValue(),
				new AsyncCallback<List<DbHwmInvoice>>() {
					
					@Override
					public void onSuccess(List<DbHwmInvoice> result) {
						WaitDialog.hide();
						refreshData(result);
					}
					
					@Override
					public void onFailure(Throwable caught) {
						WaitDialog.hideAll();
						ErrorDialog.show("Error", caught);
					}
				});
		WaitDialog.show();
	}

	private void refreshData(List<DbHwmInvoice> data) {
		csv = Common.convertToCSV(constants.chainColumnHeader(), constants.numberColumnHeader()) + "\r\n";
		scrData.remove(tblData);
		try {
			for (int r = tblData.getRowCount() - 1; r > 0; --r)
				tblData.removeRow(r);
			fillData(data);
		} finally {
			scrData.add(tblData);
		}
	}

	private void fillData(List<DbHwmInvoice> data) {
		RowFormatter tblChangesRF = tblData.getRowFormatter();
		FlexCellFormatter tblChangesCF = tblData.getFlexCellFormatter();

		int row = 1;

		if (data == null)
			return;

		StringBuilder scvB = new StringBuilder(csv);
		
		for (DbHwmInvoice invoice : data) {
			scvB.append(Common.convertToCSV(invoice.chain != null ? invoice.chain : invoice.firstClientName,
					Integer.toString(invoice.count)) + "\r\n");

			// set row style classes
			tblChangesRF.addStyleName(row, "ll-Row");
	
			// fill row
			int col = 0;
			tblData.setText(row, col++, invoice.chain != null ? invoice.chain : invoice.firstClientName);
			tblChangesCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			tblData.setText(row, col++, Integer.toString(invoice.count));
			++row;
		}

		csv = scvB.toString();
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		//txtMonth.setFocus(true);
		requestData();
	}

}
