/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.CloseEvent;
import com.google.gwt.event.logical.shared.CloseHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.i18n.client.NumberFormat;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.LicenseService.CommonRpcResult;
import eu.untill.license.client.LicenseService.RrArticle;
import eu.untill.license.client.LicenseService.RrOrder;
import eu.untill.license.client.LicenseService.RrOrderItem;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.AddHandlingFeeDialog.AddHandlingFeeDialogListener;
import eu.untill.license.client.ui.MessageDialog.Buttons;
import eu.untill.license.client.ui.MessageDialog.MessageDialogListener;
import eu.untill.license.client.ui.MessageDialog.SelectedButton;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;

/**
 * <AUTHOR>
 *
 */
public class ApproveLicenseDialog extends DialogBox
		implements ClickHandler, AsyncCallback<Void>, AddHandlingFeeDialogListener {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String licensePanel();
		String clientNameLabel();
		String hardCodeLabel();
		String licenseUidLabel();

		String approveLicenseHeader();
		String quantityColumnHeader();
		String articleNumberColumnHeader();
		String articleNameColumnHeader();
		String priceColumnHeader();
		String addNegativeColumnHeader();
		String addNegativeForThisArticleButton();
		String orderIsEmpty();
		String orderDoesNotExist();
		String orderLabel();

		String addDiscountButton();
		String reorderButton();
		@DefaultStringValue("Add handling fee")
		String addHandlingFeeArticleButton();
		String approveButton();
		String approveNoInvoiceButton();
		String approveLaterButton();

		String totalPriceWOVatLabel();
		String totalVatLabel();
		String totalPriceLabel();
		String dealerLabel();
		String dealerCommentsLabel();
	}

	public static interface UlsMessages extends Messages {
		String licenseSuccessfullyApproved();

		String reorderLicenseQuestion();
		String reorderSuccessfullyCompletedMessage();
		String reorderFailedMessage(String errorMessage);
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  /////////////
	 // Members //
	/////////////

	private DbLicense license;
	//private RrOrder order = null;
	private String currencyCode = null;
	private float currencyRate = 1;
	private RrArticle handlingFeeArticle = null;

	  //////////////////
	 // Constructors //
	//////////////////

	public ApproveLicenseDialog(DbLicense license, String dealerName) {
		this.license = license;
		// Generate base interface
		generateUI();

		txtDealerName.setText(dealerName);
		txtClientName.setText(license.getClientName());
		txtHardCode.setText(HardCodeHelper.formatHardCode(license.hardCode));
		txtDealerComments.setText(license.dealerComments);

		this.setText(constants.approveLicenseHeader());
		requestHandlingFeeArticle();
		//refresh(); // after requestHandlingFeeArticle()
	}

	public void refresh() {
		this.requestOrder();
	}
	
	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnLicense = new EntitledDecoratorPanel(constants.licensePanel());
	private FlexTable tblClient = new FlexTable();
	private Label lblDealerName = new Label(constants.dealerLabel() + ":", false);
	private TextBox txtDealerName = new TextBox();
	private Label lblClientName = new Label(constants.clientNameLabel() + ":", false);
	private TextBox txtClientName = new TextBox();
	private Label lblHardCode = null;
	private TextBox txtHardCode = new TextBox();
	private Label lblDealerComments = new Label(constants.dealerCommentsLabel() + ":", false);
	private TextArea txtDealerComments = new TextArea();
		
	private EntitledDecoratorPanel dpnOrder = new EntitledDecoratorPanel(constants.orderLabel());
	//private TextArea txtOrder = new TextArea();
	private FlexTable tblOrder = new FlexTable();
	private ScrollPanel scrOrder = new ScrollPanel(tblOrder);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnAddDiscount = new Button(constants.addDiscountButton(), this);
	private Button btnReorder = new Button(constants.reorderButton(), this);
	private Button btnAddHandlingFee = new Button(constants.addHandlingFeeArticleButton(), this);
	private Button btnApprove = new Button(constants.approveButton(), this);
	private Button btnApproveNoInvoice = new Button(constants.approveNoInvoiceButton(), this);
	private Button btnCancel = new Button(constants.approveLaterButton(), this);

	private void generateUI() {

		lblHardCode = new Label((license.isOnline() ? constants.licenseUidLabel() : constants.hardCodeLabel())
				+ ":", false);

		// Assemble client panel
		txtDealerName.setVisibleLength(90);
		txtClientName.setVisibleLength(90);
		txtHardCode.setVisibleLength(90);
		txtDealerComments.setWidth("98%");
		txtDealerComments.setVisibleLines(4);
		txtDealerName.setReadOnly(true);
		txtClientName.setReadOnly(true);
		txtHardCode.setReadOnly(true);
		txtDealerComments.setReadOnly(true);
		int row = 0;
		tblClient.setWidget(row, 0, lblDealerName);
		tblClient.setWidget(row++, 1, txtDealerName);
		tblClient.setWidget(row, 0, lblClientName);
		tblClient.setWidget(row++, 1, txtClientName);
		tblClient.setWidget(row, 0, lblHardCode);
		tblClient.setWidget(row++, 1, txtHardCode);
		tblClient.setWidget(row, 0, lblDealerComments);
		tblClient.setWidget(row++, 1, txtDealerComments);
		dpnLicense.setWidget(tblClient);

		// Create order table
		tblOrder.addStyleName("al-Table"); 
		tblOrder.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblOrder.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "24px");
		tblOrder.setText(0, col++, constants.quantityColumnHeader());
		tableCF.setWidth(0, col, "24px");
		tblOrder.setText(0, col++, constants.articleNumberColumnHeader());
		tableCF.setWidth(0, col, "400px");
		tblOrder.setText(0, col++, constants.articleNameColumnHeader());
		tableCF.setWidth(0, col, "60px");
		tblOrder.setText(0, col++, constants.priceColumnHeader());
		tableCF.setWidth(0, col, "80px");
		tblOrder.setText(0, col++, constants.addNegativeColumnHeader());

		// Assemble order panel with scroll
		scrOrder.setSize("600px", "200px");
		dpnOrder.setWidget(scrOrder);

		// Assemble button panel
		btnAddDiscount.setWidth("120px");
		btnReorder.setWidth("120px");
		btnAddHandlingFee.setEnabled(false);
		btnAddHandlingFee.setWidth("120px");
		btnApprove.setWidth("80px");
		btnApproveNoInvoice.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.setWidth("100%");
		pnlButtons.add(btnAddDiscount);
		pnlButtons.add(btnReorder);
		pnlButtons.add(btnAddHandlingFee);
		pnlButtons.setCellHorizontalAlignment(btnAddHandlingFee, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.setCellWidth(btnAddHandlingFee, "100%");
		pnlButtons.add(btnApprove);
		pnlButtons.setCellHorizontalAlignment(btnApprove, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.add(btnApproveNoInvoice);
		pnlButtons.setCellHorizontalAlignment(btnApproveNoInvoice, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.add(btnCancel);
		pnlButtons.setCellHorizontalAlignment(btnCancel, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnLicense);
		mainPanel.add(dpnOrder);
		//mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnAddDiscount) {

			AddDiscountDialog addDiscountDialog = new AddDiscountDialog(license.id,
					currencyCode, currencyRate);
			addDiscountDialog.addCloseHandler(new CloseHandler<PopupPanel> () {
				@Override public void onClose(CloseEvent<PopupPanel> event) {
					refresh();
				}
			});
			addDiscountDialog.show();

		} else if (sender == btnReorder) {

			new MessageDialog(messages.reorderLicenseQuestion(), Style.QUESTION, 
					Buttons.YESNO, new reorderHandler()).show();

		} else if (sender == btnAddHandlingFee) {

			new AddHandlingFeeDialog(this).show();

		} else if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnApprove){
	
			// Call RPC LicenseService.requestLicense
			UntillLicenseServer.getLicenseServiceAsync().approveLicense(
					UntillLicenseServer.getAuthScheme(), license.id, false, this);

			WaitDialog.show();

		} else if (sender == btnApproveNoInvoice){
			
			// Call RPC LicenseService.requestLicense
			UntillLicenseServer.getLicenseServiceAsync().approveLicense(
					UntillLicenseServer.getAuthScheme(), license.id, true, this);

			WaitDialog.show();

		}
	}

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		if (caught instanceof AuthFailedException) {
			this.hide();
			LoginPage.get().show();
		} else {
			ErrorDialog.show(caught);
		}
	}

	@Override
	public void onSuccess(Void result) {
		WaitDialog.hide();
		new MessageDialog(messages.licenseSuccessfullyApproved(), Style.INFORMATION).show();
		this.hide();
		MainPage.get().refresh();
	}

	private class reorderHandler implements MessageDialogListener, AsyncCallback<CommonRpcResult> {
		@Override
		public void onSelectButton(SelectedButton selectedButton) {
			if (selectedButton == SelectedButton.YES) {
	
				// Call RPC resendLicense
				UntillLicenseServer.getLicenseServiceAsync().reorderLicense(
						UntillLicenseServer.getAuthScheme(), license.id, this);
	
				WaitDialog.show();
				
			}
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(CommonRpcResult result) {
			WaitDialog.hideAll();
			if (result.isSuccess()) {
				new MessageDialog(messages.reorderSuccessfullyCompletedMessage(), 
						Style.INFORMATION).show();
			} else {
				new MessageDialog(messages.reorderFailedMessage(result.getErrorMessage()), 
						Style.WARNING).show();
			}
			refresh();
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void requestHandlingFeeArticle() {
		// Call RPC
		UntillLicenseServer.getLicenseServiceAsync().getHandlingFeeArticle(UntillLicenseServer.getAuthScheme(),
				new AsyncCallback<RrArticle>() {
					@Override
					public void onSuccess(RrArticle article) {
						if (article != null) {
							handlingFeeArticle = article;
							btnAddHandlingFee.setEnabled(true);
						} else {
							btnAddHandlingFee.setTitle("Handling Fee article is not found");
						}
						refresh();
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error getting Handling Fee article", caught);
						refresh();
					}
				});
	}

	private void requestOrder() {
		btnAddDiscount.setEnabled(false);
		btnApprove.setEnabled(false);
		btnApproveNoInvoice.setEnabled(false);
		UntillLicenseServer.getLicenseServiceAsync().getLicenseOrder(UntillLicenseServer.getAuthScheme(),
				license.id, new AsyncCallback<RrOrder>() {
					@Override
					public void onFailure(Throwable caught) {
						//WaitDialog.hideAll();
						ErrorDialog.show(caught);
					}
					@Override
					public void onSuccess(RrOrder result) {
						currencyCode = result == null ? null : result.currencyCode;
						currencyRate = result == null ? 1 : result.currencyRate;
						refreshOrderTable(result);
						if (result != null) {
							btnAddDiscount.setEnabled(true);
							btnApprove.setEnabled(true);
							btnApproveNoInvoice.setEnabled(true);
						}
						//WaitDialog.hide();
					}
		});
	}

	private void refreshOrderTable(RrOrder order) {
		scrOrder.remove(tblOrder);
		try {
			clearOrderTable();
			fillOrderTable(order);
		} finally {
			scrOrder.setWidget(tblOrder);
		}
	}

	private void clearOrderTable() {
		for (int r = tblOrder.getRowCount() - 1; r > 0; --r)
			tblOrder.removeRow(r);
	}

	private void fillOrderTable(RrOrder order) {
		RowFormatter tblOrderRF = tblOrder.getRowFormatter();
		FlexCellFormatter tblOrderCF = tblOrder.getFlexCellFormatter();

		// Add licenses into table
		if (order == null || order.orderItemList == null || order.orderItemList.isEmpty()) {
			tblOrderCF.setColSpan(1, 0, tblOrder.getCellCount(0));
			tblOrder.setText(1, 0, order == null ? constants.orderDoesNotExist() : constants.orderIsEmpty());
			tblOrderCF.setAlignment(1, 0, HasHorizontalAlignment.ALIGN_CENTER,
					HasVerticalAlignment.ALIGN_MIDDLE);
		} else {
			int row = 1;

			// create numberFormat
			StringBuilder formatSB = new StringBuilder("######0");
			if (order.currencyRound > 0) {
				formatSB.append(".");
				for (int i = 0; i < order.currencyRound; i++)
					formatSB.append("0");
			}
			NumberFormat numberFormat = NumberFormat.getFormat(formatSB.toString());

			String pPref = "", pSuff = "";
			if (order.currencySymbol != null)
				if (order.currencySymbolAlignment == 0)
					pPref = order.currencySymbol;
				else
					pSuff = order.currencySymbol;

			int orderItemIndex = 0;
			int endOrderItemIndex = order.orderItemList.size();
			for (; orderItemIndex < endOrderItemIndex; ++orderItemIndex) {
				RrOrderItem orderItem = order.orderItemList.get(orderItemIndex);

				// set row style classes
				tblOrderRF.addStyleName(row, "al-Row");

				// fill row
				int col = 0;
				tblOrderCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblOrder.setText(row, col++, Integer.toString(orderItem.quantity));
				tblOrderCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblOrder.setText(row, col++, Integer.toString(orderItem.articleNumber));
				tblOrder.setText(row, col++, orderItem.articleName);
				tblOrderCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_RIGHT);
				tblOrder.setText(row, col++, pPref + 
						numberFormat.format(orderItem.price * (1/order.currencyRate)) + pSuff);

				if (!orderItem.negative && orderItem.price > 0.0 &&
						(handlingFeeArticle == null || orderItem.articleId != handlingFeeArticle.id)) {
					tblOrderCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tblOrder.setWidget(row, col++, new AddNegativeButton(orderItem.articleId, 
							orderItem.articleName, orderItem.quantity));
				} else {
					tblOrder.setText(row, col++, " ");
				}

				if (orderItem.text != null && !orderItem.text.isEmpty()) {
					++row;
					tblOrderRF.addStyleName(row, "al-TextRow");
					tblOrderCF.setColSpan(row, 0, tblOrder.getCellCount(0));
					tblOrder.setText(row, 0, orderItem.text);
				}

				++row;
			}

			// fill total rows

			tblOrderRF.addStyleName(row, "al-Footer");
			tblOrderCF.setColSpan(row, 0, 3);
			tblOrderCF.setHorizontalAlignment(row, 0, HasHorizontalAlignment.ALIGN_RIGHT);
			tblOrder.setText(row, 0, constants.totalPriceWOVatLabel() + ":");
			tblOrderCF.setHorizontalAlignment(row, 1, HasHorizontalAlignment.ALIGN_RIGHT);
			tblOrder.setText(row, 1, pPref + numberFormat.format(order.totalWoVat * (1/order.currencyRate)) + pSuff);
			++row;
			tblOrderRF.addStyleName(row, "al-Footer");
			tblOrderCF.setColSpan(row, 0, 3);
			tblOrderCF.setHorizontalAlignment(row, 0, HasHorizontalAlignment.ALIGN_RIGHT);
			tblOrder.setText(row, 0, constants.totalVatLabel() + ":");
			tblOrderCF.setHorizontalAlignment(row, 1, HasHorizontalAlignment.ALIGN_RIGHT);
			tblOrder.setText(row, 1, pPref + numberFormat.format(order.totalVat * (1/order.currencyRate)) + pSuff);
			++row;
			tblOrderRF.addStyleName(row, "al-Footer");
			tblOrderCF.setColSpan(row, 0, 3);
			tblOrderCF.setHorizontalAlignment(row, 0, HasHorizontalAlignment.ALIGN_RIGHT);
			tblOrder.setText(row, 0, constants.totalPriceLabel() + ":");
			tblOrderCF.setHorizontalAlignment(row, 1, HasHorizontalAlignment.ALIGN_RIGHT);
			tblOrder.setText(row, 1, pPref +numberFormat.format(order.total * (1/order.currencyRate)) + pSuff);
			++row;
		}
	}

	  /////////////////////
	 // Private classes //
	/////////////////////

	private class AddNegativeButton extends Button implements ClickHandler {
		long articleId;
		String articleName;
		int quantity;

		public AddNegativeButton(long articleId, String articleName, int quantity) {
			super(constants.addNegativeForThisArticleButton());
			this.articleId = articleId;
			this.articleName = articleName;
			this.quantity = quantity;
			addClickHandler(this);
			addStyleName("al-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			AddNegativeArticleDialog addNegativeArticleDialog =
				new AddNegativeArticleDialog(license.id, articleId, articleName, quantity);
			addNegativeArticleDialog.addCloseHandler(new CloseHandler<PopupPanel> () {
				@Override public void onClose(CloseEvent<PopupPanel> event) {
					refresh();
				}
			});
			addNegativeArticleDialog.show();

		}

	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtDealerName.setFocus(true);
	}

	@Override
	public void onAddHandlingFee(double price, String description) {
		UntillLicenseServer.getLicenseServiceAsync().addArticle(UntillLicenseServer.getAuthScheme(), license.id,
				handlingFeeArticle.id, 1, price * currencyRate, description, new AsyncCallback<Void>() {
					@Override
					public void onSuccess(Void result) {
						WaitDialog.hide();
						refresh();
					}

					@Override
					public void onFailure(Throwable caught) {
						WaitDialog.hideAll();
						ErrorDialog.show(caught);
					}
				});
		WaitDialog.show();
	}

}
