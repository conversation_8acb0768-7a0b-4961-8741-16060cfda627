/**
 * The file contains styles for GWT widgets in the chrome theme.
 */

body, table td, select {
  font-family: Arial Unicode MS, Arial, sans-serif;
  font-size: small;
}
pre {
  font-family: "courier new", courier;
  font-size: small;
}
body {
  color: black;
  margin: 0px;
  border: 0px;
  padding: 0px;
  background: #fff;
  direction: ltr;
}
a, a:visited, a:hover {
  color: #0000AA;
}

/**
 * The reference theme can be used to determine when this style sheet has
 * loaded.  Create a hidden div element with absolute position, assign the style
 * name below, and attach it to the DOM.  Use a timer to detect when the
 * element's height and width are set to 5px.
 */
.gwt-Reference-chrome {
  height: 5px;
  width: 5px;
  zoom: 1;
}

.gwt-Button {
  margin: 0;
  padding: 3px 5px;
  text-decoration: none;
  font-size: small;
  cursor: pointer;
  cursor: hand;
  background: url("images/hborder.png") repeat-x 0px -27px;
  border: 1px outset #ccc;
}
.gwt-Button:active {
  border: 1px inset #ccc;
}
.gwt-Button:hover {
  border-color: #9cf #69e #69e #7af;
}
.gwt-<PERSON><PERSON>[disabled] {
  cursor: default;
  color: #888;
}
.gwt-Button[disabled]:hover {
  border: 1px outset #ccc;
}

.gwt-CheckBox {
}
.gwt-CheckBox-disabled {
  color: #888;
}

.gwt-DecoratorPanel {
}
.gwt-DecoratorPanel .topCenter,
.gwt-DecoratorPanel .bottomCenter {
  background: url(images/hborder.png) repeat-x;
}
.gwt-DecoratorPanel .middleLeft,
.gwt-DecoratorPanel .middleRight {
  background: url(images/vborder.png) repeat-y;
}
.gwt-DecoratorPanel .topLeftInner,
.gwt-DecoratorPanel .topRightInner,
.gwt-DecoratorPanel .bottomLeftInner,
.gwt-DecoratorPanel .bottomRightInner {
  width: 5px;
  height: 5px;
  zoom: 1;
}
.gwt-DecoratorPanel .topLeft {
  background: url(images/corner.png) no-repeat 0px 0px;
}
.gwt-DecoratorPanel .topRight {
  background: url(images/corner.png) no-repeat -5px 0px;
}
.gwt-DecoratorPanel .bottomLeft {
  background: url(images/corner.png) no-repeat 0px -5px;
}
.gwt-DecoratorPanel .bottomRight {
  background: url(images/corner.png) no-repeat -5px -5px;
}

.gwt-DialogBox .Caption {
  background: #ebebeb url(images/hborder.png) repeat-x 0px -2003px;
  padding: 4px 4px 4px 8px;
  cursor: default;
  border-bottom: 1px solid #bbbbbb;
  border-top: 5px solid #e3e3e3;
}
.gwt-DialogBox .dialogContent {
}
.gwt-DialogBox .dialogMiddleCenter {
  padding: 3px;
  background: white;
}
.gwt-DialogBox .dialogBottomCenter {
  background: url(images/hborder.png) repeat-x 0px -4px;
}
.gwt-DialogBox .dialogMiddleLeft {
  background: url(images/vborder.png) repeat-y;
}
.gwt-DialogBox .dialogMiddleRight {
  background: url(images/vborder.png) repeat-y -4px 0px;
}
.gwt-DialogBox .dialogTopLeftInner {
  width: 5px;
  zoom: 1;
}
.gwt-DialogBox .dialogTopRightInner {
  width: 8px;
  zoom: 1;
}
.gwt-DialogBox .dialogBottomLeftInner {
  width: 5px;
  height: 8px;
  zoom: 1;
}
.gwt-DialogBox .dialogBottomRightInner {
  width: 5px;
  height: 8px;
  zoom: 1;
}
.gwt-DialogBox .dialogTopLeft {
  background: url(images/corner.png) no-repeat -13px 0px;
}
.gwt-DialogBox .dialogTopRight {
  background: url(images/corner.png) no-repeat -18px 0px;
}
.gwt-DialogBox .dialogBottomLeft {
  background: url(images/corner.png) no-repeat 0px -15px;
}
.gwt-DialogBox .dialogBottomRight {
  background: url(images/corner.png) no-repeat -5px -15px;
}

.gwt-DisclosurePanel {
}
.gwt-DisclosurePanel-open {
}
.gwt-DisclosurePanel-closed {
}
.gwt-DisclosurePanel .header,
.gwt-DisclosurePanel .header a,
.gwt-DisclosurePanel .header td {
  text-decoration: none;  /* Remove underline from header */
  color: black;
  cursor: pointer;
  cursor: hand;
}
.gwt-DisclosurePanel .content {
  border-left: 3px solid #e3e3e3;
  padding: 4px 0px 4px 8px;
  margin-left: 6px;
}

.gwt-FileUpload {
}

.gwt-Frame {
  border-top: 2px solid #666;
  border-left: 2px solid #666;
  border-right: 2px solid #bbb;
  border-bottom: 2px solid #bbb;
}

.gwt-HorizontalSplitPanel {
}
.gwt-HorizontalSplitPanel .hsplitter {
  cursor: move;
  border: 0px;
  background: #91c0ef url(images/vborder.png) repeat-y;
  line-height: 0px;
}
.gwt-VerticalSplitPanel {
}
.gwt-VerticalSplitPanel .vsplitter {
  cursor: move;
  border: 0px;
  background: #91c0ef url(images/hborder.png) repeat-x;
  line-height: 0px;
}

.gwt-HTML {
}

.gwt-Hyperlink {
  cursor: pointer;
}

.gwt-Image {
}

.gwt-Label {
}

.gwt-ListBox {
}

.gwt-MenuBar {
  cursor: default;
}
.gwt-MenuBar .gwt-MenuItem {
  cursor: default;
}
.gwt-MenuBar .gwt-MenuItem-selected {
  background: #cdcdcd;
}
.gwt-MenuBar .gwt-MenuItem-disabled {
  color: #cdcdcd;
}
.gwt-MenuBar-horizontal {
  background: #ebebeb url(images/hborder.png) repeat-x 0px -2003px;
  border: 1px solid #BBBBBB;
}
.gwt-MenuBar-horizontal .gwt-MenuItem {
  padding: 0px 10px;
  vertical-align: bottom;
  color: #666666;
  font-weight: bold;
}
.gwt-MenuBar-horizontal .gwt-MenuItemSeparator {
  width: 1px;
  padding: 0px;
  margin: 0px;
  border: 0px;
  border-left: 1px solid #888888;
  background: white;
}
.gwt-MenuBar-horizontal .gwt-MenuItemSeparator .menuSeparatorInner {
  width: 1px;
  height: 1px;
  background: white;
}
.gwt-MenuBar-vertical {
  margin-top: 0px;
  margin-left: 0px;
  background: white;
}
.gwt-MenuBar-vertical table {
  border-collapse: collapse;
}
.gwt-MenuBar-vertical .gwt-MenuItem {
  padding: 4px 14px 4px 1px;
}
.gwt-MenuBar-vertical .gwt-MenuItemSeparator {
  padding: 2px 0px;
}
.gwt-MenuBar-vertical .gwt-MenuItemSeparator .menuSeparatorInner {
  height: 1px;
  padding: 0px;
  border: 0px;
  border-top: 1px solid #777777;
  background: #ddddee;
  overflow: hidden;
}
.gwt-MenuBar-vertical .subMenuIcon {
  padding-right: 4px;
}
.gwt-MenuBar-vertical .subMenuIcon-selected {
  background: #cdcdcd;
}
.gwt-MenuBarPopup .menuPopupTopCenter {
  background: url(images/hborder.png) 0px -12px repeat-x;
}
.gwt-MenuBarPopup .menuPopupBottomCenter {
  background: url(images/hborder.png) 0px -13px repeat-x;
}
.gwt-MenuBarPopup .menuPopupMiddleLeft {
  background: url(images/vborder.png) -12px 0px repeat-y;
}
.gwt-MenuBarPopup .menuPopupMiddleRight {
  background: url(images/vborder.png) -13px 0px repeat-y;
}
.gwt-MenuBarPopup .menuPopupTopLeftInner {
  width: 5px;
  height: 5px;
  zoom: 1;
}
.gwt-MenuBarPopup .menuPopupTopRightInner {
  width: 8px;
  height: 5px;
  zoom: 1;
}
.gwt-MenuBarPopup .menuPopupBottomLeftInner {
  width: 5px;
  height: 8px;
  zoom: 1;
}
.gwt-MenuBarPopup .menuPopupBottomRightInner {
  width: 8px;
  height: 8px;
  zoom: 1;
}
.gwt-MenuBarPopup .menuPopupTopLeft {
  background: url(images/corner.png) no-repeat 0px -36px;
}
.gwt-MenuBarPopup .menuPopupTopRight {
  background: url(images/corner.png) no-repeat -5px -36px;
}
.gwt-MenuBarPopup .menuPopupBottomLeft {
  background: url(images/corner.png) no-repeat 0px -41px;
}
.gwt-MenuBarPopup .menuPopupBottomRight {
  background: url(images/corner.png) no-repeat -5px -41px;
}

.gwt-PasswordTextBox {
  padding: 2px;
}
.gwt-PasswordTextBox-readonly {
  color: #888;
}

.gwt-PopupPanel {
  border: 3px solid #e3e3e3;
  padding: 3px;
  background: white;
}

.gwt-DecoratedPopupPanel .popupContent {
}
.gwt-DecoratedPopupPanel .popupMiddleCenter {
  padding: 3px;
  background: #e3e3e3;
}
.gwt-DecoratedPopupPanel .popupTopCenter {
  background: url(images/hborder.png) repeat-x;
}
.gwt-DecoratedPopupPanel .popupBottomCenter {
  background: url(images/hborder.png) repeat-x 0px -4px;
}
.gwt-DecoratedPopupPanel .popupMiddleLeft {
  background: url(images/vborder.png) repeat-y;
}
.gwt-DecoratedPopupPanel .popupMiddleRight {
  background: url(images/vborder.png) repeat-y -4px 0px;
}
.gwt-DecoratedPopupPanel .popupTopLeftInner {
  width: 5px;
  height: 5px;
  zoom: 1;
}
.gwt-DecoratedPopupPanel .popupTopRightInner {
  width: 8px;
  height: 5px;
  zoom: 1;
}
.gwt-DecoratedPopupPanel .popupBottomLeftInner {
  width: 5px;
  height: 8px;
  zoom: 1;
}
.gwt-DecoratedPopupPanel .popupBottomRightInner {
  width: 8px;
  height: 8px;
  zoom: 1;
}
.gwt-DecoratedPopupPanel .popupTopLeft {
  background: url(images/corner.png) no-repeat 0px -10px;
}
.gwt-DecoratedPopupPanel .popupTopRight {
  background: url(images/corner.png) no-repeat -5px -10px;
}
.gwt-DecoratedPopupPanel .popupBottomLeft {
  background: url(images/corner.png) no-repeat 0px -15px;
}
.gwt-DecoratedPopupPanel .popupBottomRight {
  background: url(images/corner.png) no-repeat -5px -15px;
}

.gwt-PopupPanelGlass {
  background-color: #000;
  opacity: 0.3;
  filter: alpha(opacity=30);
}

.gwt-PushButton-up,
.gwt-PushButton-up-hovering,
.gwt-PushButton-up-disabled,
.gwt-PushButton-down,
.gwt-PushButton-down-hovering,
.gwt-PushButton-down-disabled {
  margin: 0;
  text-decoration: none;
  background: url("images/hborder.png") repeat-x 0px -27px;
}
.gwt-PushButton-up,
.gwt-PushButton-up-hovering,
.gwt-PushButton-up-disabled {
  padding: 3px 5px 3px 5px;
}
.gwt-PushButton-up {
  border: 1px outset #ccc;
  cursor: pointer;
  cursor: hand;
}
.gwt-PushButton-up-hovering {
  border: 1px outset;
  border-color: #9cf #69e #69e #7af;
  cursor: pointer;
  cursor: hand;
}
.gwt-PushButton-up-disabled {
  border: 1px outset #ccc;
  cursor: default;
  opacity: .5;
  filter: alpha(opacity=40);
  zoom: 1;
}
.gwt-PushButton-down,
.gwt-PushButton-down-hovering,
.gwt-PushButton-down-disabled {
  padding: 4px 4px 2px 6px;
}
.gwt-PushButton-down {
  border: 1px inset #666;
  cursor: pointer;
  cursor: hand;
}
.gwt-PushButton-down-hovering {
  border: 1px inset;
  border-color: #9cf #69e #69e #7af;
  cursor: pointer;
  cursor: hand;
}
.gwt-PushButton-down-disabled {
  border: 1px outset #ccc;
  cursor: default;
  opacity: 0.5;
  filter: alpha(opacity=40);
  zoom: 1;
}

.gwt-RadioButton {
}
.gwt-RadioButton-disabled {
  color: #888;
}

.gwt-RichTextArea {
}
.hasRichTextToolbar {
  border: 0px;
}
.gwt-RichTextToolbar {
  background: #ebebeb url(images/hborder.png) repeat-x 0px -2003px;
  border-bottom: 1px solid #BBBBBB;
  padding: 3px;
  margin: 0px;
}
.gwt-RichTextToolbar .gwt-PushButton-up {
  padding: 0px 1px 0px 0px;
  margin-right: 4px;
  margin-bottom: 4px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-PushButton-up-hovering {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 1px 0px 0px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-PushButton-down {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 0px 0px 1px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-PushButton-down-hovering {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 0px 0px 1px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-ToggleButton-up {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 1px 0px 0px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-ToggleButton-up-hovering {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 1px 0px 0px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-ToggleButton-down {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 0px 0px 1px;
  border-width: 1px;
}
.gwt-RichTextToolbar .gwt-ToggleButton-down-hovering {
  margin-right: 4px;
  margin-bottom: 4px;
  padding: 0px 0px 0px 1px;
  border-width: 1px;
}

.gwt-StackPanel {
  border-bottom: 1px solid #bbbbbb;
}
.gwt-StackPanel .gwt-StackPanelItem {
  cursor: pointer;
  cursor: hand;
  font-weight: bold;
  font-size: 1.3em;
  padding: 3px;
  border: 1px solid #bbbbbb;
  border-bottom: 0px;
  background: #d3def6 url(images/hborder.png) repeat-x 0px -989px;
}
.gwt-StackPanel .gwt-StackPanelContent {
  border: 1px solid #bbbbbb;
  border-bottom: 0px;
  background: white;
  padding: 2px 2px 10px 5px;
}

.gwt-DecoratedStackPanel {
  border-bottom: 1px solid #bbbbbb;
}
.gwt-DecoratedStackPanel .gwt-StackPanelContent {
  border: 1px solid #bbbbbb;
  border-bottom: 0px;
  background: white;
  padding: 2px 2px 10px 5px;
}
.gwt-DecoratedStackPanel .gwt-StackPanelItem {
  cursor: pointer;
  cursor: hand;
}
.gwt-DecoratedStackPanel .stackItemTopLeft,
.gwt-DecoratedStackPanel .stackItemTopRight {
  height: 6px;
  width: 6px;
  zoom: 1;
}
.gwt-DecoratedStackPanel .stackItemTopLeft {
  border-left: 1px solid #bbbbbb;
  background: #e4e4e4 url(images/corner.png) no-repeat 0px -49px;
}
.gwt-DecoratedStackPanel .stackItemTopRight {
  border-right: 1px solid #bbbbbb;
  background: #e4e4e4 url(images/corner.png) no-repeat -6px -49px;
}
.gwt-DecoratedStackPanel .stackItemTopLeftInner,
.gwt-DecoratedStackPanel .stackItemTopRightInner {
  width: 1px;
  height: 1px;
}

.gwt-DecoratedStackPanel .stackItemTopCenter {
  background: url(images/hborder.png) 0px -21px repeat-x;
}
.gwt-DecoratedStackPanel .stackItemMiddleLeft {
  background: #d3def6 url(images/hborder.png) repeat-x 0px -989px;
  border-left: 1px solid #bbbbbb;
}
.gwt-DecoratedStackPanel .stackItemMiddleLeftInner,
.gwt-DecoratedStackPanel .stackItemMiddleRightInner {
  width: 1px;
  height: 1px;
}
.gwt-DecoratedStackPanel .stackItemMiddleRight {
  background: #d3def6 url(images/hborder.png) repeat-x 0px -989px;
  border-right: 1px solid #bbbbbb;
}
.gwt-DecoratedStackPanel .stackItemMiddleCenter {
  font-weight: bold;
  font-size: 1.3em;
  background: #d3def6 url(images/hborder.png) repeat-x 0px -989px;
}
.gwt-DecoratedStackPanel .gwt-StackPanelItem-first .stackItemTopRight,
.gwt-DecoratedStackPanel .gwt-StackPanelItem-first .stackItemTopLeft {
  border: 0px;
  background-color: white;
}
.gwt-DecoratedStackPanel .gwt-StackPanelItem-below-selected .stackItemTopLeft,
.gwt-DecoratedStackPanel .gwt-StackPanelItem-below-selected .stackItemTopRight {
  background-color: white;
}

.gwt-SuggestBox {
  padding: 2px;
}
.gwt-SuggestBoxPopup {
  margin-left: 3px;
}
.gwt-SuggestBoxPopup .item {
  padding: 2px 6px;
  color: #424242;
  cursor: default;
}
.gwt-SuggestBoxPopup .item-selected {
  background: #cdcdcd;
}
.gwt-SuggestBoxPopup .suggestPopupContent {
  background: white;
}
.gwt-SuggestBoxPopup .suggestPopupTopCenter {
  background: url(images/hborder.png) repeat-x;
}
.gwt-SuggestBoxPopup .suggestPopupBottomCenter {
  background: url(images/hborder.png) repeat-x 0px -4px;
}
.gwt-SuggestBoxPopup .suggestPopupMiddleLeft {
  background: url(images/vborder.png) repeat-y;
}
.gwt-SuggestBoxPopup .suggestPopupMiddleRight {
  background: url(images/vborder.png) repeat-y -4px 0px;
}
.gwt-SuggestBoxPopup .suggestPopupTopLeftInner {
  width: 5px;
  height: 5px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupTopRightInner {
  width: 8px;
  height: 5px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupBottomLeftInner {
  width: 5px;
  height: 8px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupBottomRightInner {
  width: 8px;
  height: 8px;
  zoom: 1;
}
.gwt-SuggestBoxPopup .suggestPopupTopLeft {
  background: url(images/corner.png) no-repeat 0px -23px;
}
.gwt-SuggestBoxPopup .suggestPopupTopRight {
  background: url(images/corner.png) no-repeat -5px -23px;
}
.gwt-SuggestBoxPopup .suggestPopupBottomLeft {
  background: url(images/corner.png) no-repeat 0px -28px;
}
.gwt-SuggestBoxPopup .suggestPopupBottomRight {
  background: url(images/corner.png) no-repeat -5px -28px;
}

.gwt-TabBar {
}
.gwt-TabBar .gwt-TabBarFirst {
  width: 5px;  /* first tab distance from the left */
}
.gwt-TabBar .gwt-TabBarRest {
}
.gwt-TabBar .gwt-TabBarItem {
  margin-left: 6px;
  padding: 3px 6px 3px 6px;
  cursor: pointer;
  cursor: hand;
  color: black;
  font-weight: bold;
  text-align: center;
  background: #e3e3e3;
}
.gwt-TabBar .gwt-TabBarItem-selected {
  cursor: default;
  background: #bcbcbc;
}
.gwt-TabBar .gwt-TabBarItem-disabled {
  cursor: default;
  color: #999999;
}
.gwt-TabPanel {
}
.gwt-TabPanelBottom {
  border-color: #bcbcbc;
  border-style: solid;
  border-width: 3px 2px 2px;
  overflow: hidden;
  padding: 6px;
}

.gwt-DecoratedTabBar {
}
.gwt-DecoratedTabBar .gwt-TabBarFirst {
  width: 5px;  /* first tab distance from the left */
}
.gwt-DecoratedTabBar .gwt-TabBarRest {
}
.gwt-DecoratedTabBar .gwt-TabBarItem {
  border-collapse: collapse;
  margin-left: 6px;
}
.gwt-DecoratedTabBar .tabTopCenter {
  padding: 0px;
  background: #e3e3e3;
}
.gwt-DecoratedTabBar .tabTopLeft,
.gwt-DecoratedTabBar .tabTopRight {
  padding: 0px;
  zoom: 1;
}
.gwt-DecoratedTabBar .tabTopLeftInner,
.gwt-DecoratedTabBar .tabTopRightInner {
  width: 6px;
  height: 6px;
}
.gwt-DecoratedTabBar .tabTopLeft {
  background: url(images/corner.png) no-repeat 0px -55px;
}
.gwt-DecoratedTabBar .tabTopRight {
  background: url(images/corner.png) no-repeat -6px -55px;
}

.gwt-DecoratedTabBar .tabMiddleLeft,
.gwt-DecoratedTabBar .tabMiddleRight {
  width: 6px;
  padding: 0px;
  background: #e3e3e3 url(images/hborder.png) repeat-x 0px -1463px;
}
.gwt-DecoratedTabBar .tabMiddleLeftInner,
.gwt-DecoratedTabBar .tabMiddleRightInner {
  width: 1px;
  height: 1px;
}
.gwt-DecoratedTabBar .tabMiddleCenter {
  padding: 0px 4px 2px 4px;
  cursor: pointer;
  cursor: hand;
  color: black;
  font-weight: bold;
  text-align: center;
  background: #e3e3e3 url(images/hborder.png) repeat-x 0px -1463px;
}
.gwt-DecoratedTabBar .gwt-TabBarItem-selected .tabTopCenter {
  background: #747474;
}
.gwt-DecoratedTabBar .gwt-TabBarItem-selected .tabTopLeft {
  background-position: 0px -61px;
}
.gwt-DecoratedTabBar .gwt-TabBarItem-selected .tabTopRight {
  background-position: -6px -61px;
}
.gwt-DecoratedTabBar .gwt-TabBarItem-selected .tabMiddleLeft,
.gwt-DecoratedTabBar .gwt-TabBarItem-selected .tabMiddleRight {
  background: #bcbcbc url(images/hborder.png) repeat-x 0px -2511px;
}
.gwt-DecoratedTabBar .gwt-TabBarItem-selected .tabMiddleCenter {
  cursor: default;
  background: #bcbcbc url(images/hborder.png) repeat-x 0px -2511px;
  color: white;
}
.gwt-DecoratedTabBar .gwt-TabBarItem-disabled .tabMiddleCenter {
  cursor: default;
  color: #999999;
}

.gwt-TextArea {
  padding: 2px;
}
.gwt-TextArea-readonly {
  color: #888;
}

.gwt-TextBox {
  padding: 2px;
}
.gwt-TextBox-readonly {
  color: #888;
}

.gwt-ToggleButton-up,
.gwt-ToggleButton-up-hovering,
.gwt-ToggleButton-up-disabled,
.gwt-ToggleButton-down,
.gwt-ToggleButton-down-hovering,
.gwt-ToggleButton-down-disabled {
  margin: 0;
  text-decoration: none;
  background: url("images/hborder.png") repeat-x 0px -27px;
}
.gwt-ToggleButton-up,
.gwt-ToggleButton-up-hovering,
.gwt-ToggleButton-up-disabled {
  padding: 3px 5px 3px 5px;
}
.gwt-ToggleButton-up {
  border: 1px outset #ccc;
  cursor: pointer;
  cursor: hand;
}
.gwt-ToggleButton-up-hovering {
  border: 1px outset;
  border-color: #9cf #69e #69e #7af;
  cursor: pointer;
  cursor: hand;
}
.gwt-ToggleButton-up-disabled {
  border: 1px outset #ccc;
  cursor: default;
  opacity: .5;
  zoom: 1;
  filter: alpha(opacity=40);
}
.gwt-ToggleButton-down,
.gwt-ToggleButton-down-hovering,
.gwt-ToggleButton-down-disabled {
  padding: 4px 4px 2px 6px;
}
.gwt-ToggleButton-down {
  background-position: 0 -513px;
  border: 1px inset #ccc;
  cursor: pointer;
  cursor: hand;
}
.gwt-ToggleButton-down-hovering {
  background-position: 0 -513px;
  border: 1px inset;
  border-color: #9cf #69e #69e #7af;
  cursor: pointer;
  cursor: hand;
}
.gwt-ToggleButton-down-disabled {
  background-position: 0 -513px;
  border: 1px inset #ccc;
  cursor: default;
  opacity: .5;
  zoom: 1;
  filter: alpha(opacity=40);
}

.gwt-Tree .gwt-TreeItem {
  padding: 1px 0px;
  margin: 0px;
  white-space: nowrap;
  cursor: hand;
  cursor: pointer;
}
.gwt-Tree .gwt-TreeItem-selected {
  background: #93c2f1 url(images/hborder.png) repeat-x 0px -1463px;
}
.gwt-TreeItem .gwt-RadioButton input,
.gwt-TreeItem .gwt-CheckBox input {
  margin-left: 0px;
}

.gwt-DateBox input {
  width: 8em;
}
.dateBoxFormatError {
  background: #eed6d6;
}
.dateBoxPopup {
}

.gwt-DatePicker {
  border: 1px solid #888;
  cursor: default;
}
.gwt-DatePicker td,
.datePickerMonthSelector td:focus {
  outline: none
}
.datePickerDays {
  width: 100%;
  background: white;
}
.datePickerDay,
.datePickerWeekdayLabel,
.datePickerWeekendLabel {
  font-size: 75%;
  text-align: center;
  padding: 4px;
  outline: none;
}
.datePickerWeekdayLabel,
.datePickerWeekendLabel {
  background: #c1c1c1;
  padding: 0px 4px 2px;
  cursor: default;
}
.datePickerDay {
  padding: 4px;
  cursor: hand;
  cursor: pointer;
}
.datePickerDayIsToday {
  border: 1px solid black;
  padding: 3px;
}
.datePickerDayIsWeekend {
  background: #EEEEEE;
}
.datePickerDayIsFiller {
  color: #888888;
}
.datePickerDayIsValue {
  background: #abf;
}
.datePickerDayIsDisabled {
  color: #AAAAAA;
  font-style: italic;
}
.datePickerDayIsHighlighted {
  background: #dde;
}
.datePickerDayIsValueAndHighlighted {
  background: #ccf;
}
.datePickerMonthSelector {
  background: #c1c1c1;
  width: 100%;
}
td.datePickerMonth,
td.datePickerYear {
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
  font-size: 70%;
  font-weight: bold;
}
.datePickerPreviousButton,
.datePickerNextButton,
.datePickerPreviousYearButton,
.datePickerNextYearButton {
  font-size: 120%;
  line-height: 1em;
  cursor: hand;
  cursor: pointer;
  padding: 0px 4px;
}

.gwt-StackLayoutPanel {
  border-bottom: 1px solid #bbbbbb;
}
.gwt-StackLayoutPanel .gwt-StackLayoutPanelHeader {
  cursor: pointer;
  cursor: hand;
  border: 1px solid #bbbbbb;
  border-bottom: 0px;
  background: #d3def6 url(images/hborder.png) repeat-x 0px -989px;

  font-weight: bold;
  font-size: 1.3em;
  padding: 3px;
  text-align: center;
}
.gwt-StackLayoutPanel .gwt-StackLayoutPanelHeader-hovering {
  background: #d3def6 url(images/hborder.png) repeat-x 0px -1464px;
}
.gwt-StackLayoutPanel .gwt-StackLayoutPanelContent {
  border: 1px solid #bbbbbb;
  border-bottom: 0px;
  background: white;
  padding: 2px 2px 10px 5px;
}

.gwt-TabLayoutPanel {
}
.gwt-TabLayoutPanel .gwt-TabLayoutPanelTabs {
}
.gwt-TabLayoutPanelContentContainer {
  border-color: #bcbcbc;
  border-style: solid;
  border-width: 2px 1px 1px;
}
.gwt-TabLayoutPanel .gwt-TabLayoutPanelContent {
  border-color: #bcbcbc;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  padding: 6px;
}
.gwt-TabLayoutPanel .gwt-TabLayoutPanelTab {
  margin-left: 6px;
  padding: 3px 6px 3px 6px;
  cursor: pointer;
  cursor: hand;
  color: black;
  font-weight: bold;
  text-align: center;
  background: #e3e3e3;
}
.gwt-TabLayoutPanel .gwt-TabLayoutPanelTab-selected {
  cursor: default;
  background: #bcbcbc;
}

.gwt-SplitLayoutPanel-HDragger {
  background: white url(images/splitPanelThumb.png) center center no-repeat;
  cursor: col-resize;
}

.gwt-SplitLayoutPanel-VDragger {
  background: white url(images/splitPanelThumb.png) center center no-repeat;
  cursor: row-resize;
}
