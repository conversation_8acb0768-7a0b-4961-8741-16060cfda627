package eu.untill.license.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import eu.untill.license.server.InvoiceHelper.Invoice;
import eu.untill.license.shared.DbLicense;

public class InvoiceHandler {

	static final Logger LOG = LoggerFactory.getLogger(InvoiceHandler.class);

	private CommonHelper commonHelper;
	private EmailHelper emailHelper;
	private InvoiceHelper invoiceHelper;

	public InvoiceHandler(CommonHelper commonHelper, EmailHelper emailHelper, InvoiceHelper invoiceHelper) {
		this.commonHelper = commonHelper;
		this.emailHelper = emailHelper;
		this.invoiceHelper = invoiceHelper;
	}

	/**
	 * Generate pdf-file and send invoice
	 * @param conn
	 * @throws SQLException
	 */
	public void generateAndSend(Connection conn) throws SQLException {
		while (true) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				// Get one licenses with INVOICE_REQUIRED from DB
				DbLicense license = null;
				long licenseBillId = 0;
				boolean nothingFound = true;
				String sql = "SELECT FIRST 1 *\n"
						+ "FROM LICENSES\n"
						+ "WHERE COALESCE(INVOICE_REQUIRED, 0) <> 0 AND COALESCE(ID_BILL, 0) <> 0\n"
						+ "ORDER BY ID"; // DESC
				try (
					PreparedStatement stmt = conn.prepareStatement(sql);
					ResultSet rs = stmt.executeQuery();
				) {
					if (rs.next()) {
						license = Common.getDbLicenseFromResultSet(rs);
						licenseBillId = rs.getLong("ID_BILL");
						nothingFound = false;
					}
				}
				if (nothingFound) {
					conn.commit(); // or rollback()
					break;
				}

				// TODO log: start generate invoice

				synchronized (commonHelper.getSettingsLock()) {
					Settings settings = commonHelper.getSettings();
	
					// Generate invoice pdf
					Invoice invoice = invoiceHelper.generateInvoiceForLicense(conn, settings,
							commonHelper.getCurrentDate(), licenseBillId, license);
					if (invoice == null) // TODO rewrite error handling
						throw new RuntimeException("invoiceHelper.generateInvoiceForDealer() error");
					try {
	
						try (PreparedStatement stmt = conn.prepareStatement(
								"UPDATE LICENSES\n"
								+ "SET INVOICE_REQUIRED = 0,\n"
								+ "INVOICE_DATE = ?,\n"
								+ "INVOICE_NUMBER = ?\n"
								+ "WHERE ID = ?\n")) {
							stmt.setDate(1, new java.sql.Date(new Date().getTime()));
							stmt.setLong(2, (invoice.isPositive() ? +1 : -1) * invoice.getNumber());
							stmt.setLong(3, license.id);
							if (stmt.executeUpdate() == 0) throw new RuntimeException();
						}
	
						if (invoice.getEmails() != null) {
							List<String> emailList = Common.parseEmailList(invoice.getEmails());
							if (!emailList.isEmpty()) {
								if (!emailHelper.emailInvoice(invoice.getFile(), invoice.getDate(),
										invoice.getFullNumber(), emailList, license, license.getClientName()))
									throw new RuntimeException("emailHelper.emailInvoice() error"); // TODO rewrite error handling
							} else {
								LOG.warn("Dealer has no emails for invoices: {}", invoice.getFile().getName());
							}
						} else {
							LOG.warn("Dealer has no invoice emails: {}", invoice.getFile().getName());
						}
		
						// Set next invoice number and save
						if (invoice.isPositive()) {
							settings.setNextInvoiceNumber(invoice.getNumber() + 1);
						} else {
							settings.setNextNegativeInvoiceNumber(invoice.getNumber() + 1);
						}
						settings.save();
					} catch (Exception e) {
						invoice.getFile().delete();
						throw e;
					}
				}
				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}

			// TODO log: finish generate invoice
		} // while (true)
	}

}
