package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.json.client.JSONNumber;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.Cell;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.datepicker.client.DateBox;

import eu.untill.license.client.Common;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.Restrictions;
import eu.untill.license.shared.ShProductInfo;
import eu.untill.license.shared.SuHost;
import eu.untill.license.shared.SuSubTask;
import eu.untill.license.shared.SuTask;
import eu.untill.license.shared.SuTaskBundle;

public class SuTaskDialog extends DialogBox implements ClickHandler, AsyncCallback<Void> {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {

		@DefaultStringValue("Create new task")
		String createTaskHeader();
		@DefaultStringValue("Task Details")
		String viewTaskHeader();

		@DefaultStringValue("Task")
		String taskPanel();

		@DefaultStringValue("Title")
		String taskTitleLabel();
		@DefaultStringValue("Type")
		String taskActionLabel();
		@DefaultStringValue("Start date")
		String startDateLabel();
		@DefaultStringValue("Start time")
		String startTimeLabel();

		@DefaultStringValue("Recipients")
		String subTasksLabel();

		@DefaultStringValue("Add recipient(s) ...")
		String addSubTasksButton();
		@DefaultStringValue("Clear recipients")
		String clearSubTasksButton();

		@DefaultStringValue("Create")
		String createButton();
		String cancelButton();
		@DefaultStringValue("Close")
		String closeButton();

		String clientNameColumnHeader();
		@DefaultStringValue("License UID")
		String licenseUidColumnHeader();
		@DefaultStringValue("Сhain")
		String chainColumnHeader();
		@DefaultStringValue("Computer")
		String hostColumnHeader();
		@DefaultStringValue("Status")
		String statusColumnHeader();
		@DefaultStringValue("Result")
		String resultColumnHeader();

		@DefaultStringValue("Created")
		String suSubTaskCreatedStatus();
		@DefaultStringValue("Preparing")
		String suSubTaskPreparingStatus();
		@DefaultStringValue("Prepared")
		String suSubTaskPreparedStatus();
		@DefaultStringValue("WaitForExecuting")
		String suSubTaskWaitForExecutingStatus();
		@DefaultStringValue("Executing")
		String suSubTaskExecutingStatus();
		@DefaultStringValue("Success")
		String suSubTaskSuccessStatus();
		@DefaultStringValue("Failure")
		String suSubTaskFailureStatus();

		@DefaultStringValue("Install")
		String actionDeployTitle();
		@DefaultStringValue("Uninstall")
		String actionUndeployTitle();
		@DefaultStringValue("System States")
		String actionInfoTitle();
		@DefaultStringValue("Manual")
		String actionManualTitle();

	}

	public static interface UlsMessages extends Messages {
		@DefaultMessage("Please enter action")
		String pleaseEnterAction();
		@DefaultMessage("Please enter start date")
		String pleaseEnterStartDate();
		@DefaultMessage("Please enter start time")
		String pleaseEnterStartTime();
		@DefaultMessage("Please add at least one recipient")
		String pleaseAddSubTasks();
		@DefaultMessage("Unexpected characters used")
		String unexpectedCharactersUsed();
		@DefaultMessage("Please select product")
		String pleaseSelectProduct();
		@DefaultMessage("Please select version")
		String pleaseSelectVersion();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();


	  /////////////
	 // Members //
	/////////////

	enum Action {
		DEPLOY("deploy", true, true),
		UNDEPLOU("undeploy", true, false),
		INFO("info", false, false),
		MANUAL(null, false, false);
		private String action;
		boolean paramProduct, paramVersion;
		private Action(String action, boolean paramProduct, boolean paramVersion) {
			this.action = action;
			this.paramProduct = paramProduct;
			this.paramVersion = paramVersion;
		}
	}

	private boolean viewMode;
	private SuTaskBundle taskBundle;

	private Map<String, ShProductInfo> products = null;

	private boolean manualActionAreShown = false;
	private boolean showAllProducts = Restrictions.SU_SHOW_ALL_PRODUCTS;
	private boolean showAllProductVersions = Restrictions.SU_SHOW_ALL_PRODUCT_VERSIONS;

	  //////////////////
	 // Constructors //
	//////////////////

	public SuTaskDialog(SuTaskBundle taskBundle) {
		viewMode = true;
		this.taskBundle = taskBundle;

		generateUI();

		txtTaskTitle.setValue(taskBundle.title);

		Action action = Action.MANUAL;
		for (Action actionI : Action.values())
			if (actionI.action != null && actionI.action.equals(taskBundle.action))
				action = actionI;
		if (action == Action.MANUAL) {
			showManualAction();
			txtTaskAction.setValue(taskBundle.action);
		}
		lstTaskAction.setSelectedIndex(action.ordinal());
		HashMap<String, String> params = new HashMap<>(taskBundle.params);
		if (action.paramProduct) {
			String str = params.remove("product");
			if (str != null)
				txtParamProduct.setValue(str);
		}
		if (action.paramVersion) {
			String str = params.remove("version");
			if (str != null)
				txtParamVersion.setValue(str);
		}
		txtTaskParams.setValue(params.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining(" "))); // XXX
		if (taskBundle.startDatetime != null) {
			chkStartDate.setValue(true);
			txtStartDate.setValue(taskBundle.startDatetime);
			txtStartTime.setValue(timeFormat.format(taskBundle.startDatetime));
		}

		refreshParameters();

		this.setText(constants.viewTaskHeader());

		refreshSubTasks();
	}

	public SuTaskDialog(long dealerId) {
		viewMode = false;
		taskBundle = new SuTaskBundle();
		taskBundle.dealerId = dealerId;

		generateUI();

		refreshParameters();
		if (!Restrictions.DISABLE_SCM4J_DEPLOYER_ENGINE)
			requestLstParamProduct(); 

		this.setText(constants.createTaskHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnTask = new EntitledDecoratorPanel(constants.taskPanel());
	private FlexTable tblTask = new FlexTable();
	private Label lblTaskTitle = new Label(constants.taskTitleLabel() + ":", false);
	private TextBox txtTaskTitle = new TextBox();
	private Label lblTaskAction = new Label(constants.taskActionLabel() + ":", false);
	private ListBox lstTaskAction = new ListBox();
	private TextBox txtTaskAction = new TextBox();
	private TextBox txtTaskParams = new TextBox();
	private CheckBox chkStartDate = new CheckBox(constants.startDateLabel() + ":", false);
	private DateBox txtStartDate = new DateBox();
	private Label lblStartTime = new Label(constants.startTimeLabel() + ":");
	private TextBox txtStartTime = new TextBox();
	private DateTimeFormat timeFormat = DateTimeFormat.getFormat(PredefinedFormat.TIME_MEDIUM);

	private Label lblParamProduct = new Label("Product" + ":", false);
	private TextBox txtParamProduct = new TextBox();
	private ListBox lstParamProduct = new ListBox();
	private Label lblParamVersion = new Label("Version" + ":", false);
	private TextBox txtParamVersion = new TextBox();
	private ListBox lstParamVersion = new ListBox();

	private EntitledDecoratorPanel dpnSubTasks = new EntitledDecoratorPanel(constants.subTasksLabel());
	private FlexTable tblSubTasks = new FlexTable();
	private ScrollPanel scrSubTaks = new ScrollPanel(tblSubTasks);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnAddSubTasks = new Button(constants.addSubTasksButton(), this);
	private Button btnClearSubTasks = new Button(constants.clearSubTasksButton(), this);
	private Button btnCreate = new Button(constants.createButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);
	private Button btnClose = new Button(constants.closeButton(), this);

	private static final String NBSP = "\u00A0";

	private void generateUI() {

		// fill action list
		lstTaskAction.addItem(constants.actionDeployTitle(), Action.DEPLOY.name());
		lstTaskAction.addItem(constants.actionUndeployTitle(), Action.UNDEPLOU.name());
		lstTaskAction.addItem(constants.actionInfoTitle(), Action.INFO.name());
		lstTaskAction.addChangeHandler((event) -> {
			refreshParameters();
		});

		if (!Restrictions.DISABLE_SCM4J_DEPLOYER_ENGINE) {
			lstParamProduct.addChangeHandler(event -> {
				refreshLstParamVersion();
			});
			if (!Restrictions.SU_SHOW_ALL_PRODUCTS) {
				lstParamProduct.addMouseDownHandler((event) -> {
					if (!showAllProducts && /*event.isControlKeyDown() && event.isAltKeyDown() && */event.isShiftKeyDown()) {
						showAllProducts = true;
						refreshLstParamProduct();
					}
				});
			}
			if (!Restrictions.SU_SHOW_ALL_PRODUCT_VERSIONS) {
				lstParamProduct.addMouseDownHandler((event) -> {
					if (!showAllProductVersions && event.isControlKeyDown() && event.isAltKeyDown() && event.isShiftKeyDown()) {
						showAllProductVersions = true;
						refreshLstParamVersion();
					}
				});
			}
		}

		if (Restrictions.SU_HIDE_MANUAL_ACTION) {
			txtTaskAction.setVisible(false);
			txtTaskParams.setVisible(false);
			lstTaskAction.addMouseDownHandler((event) -> {
				if (!manualActionAreShown && event.isControlKeyDown() && event.isAltKeyDown() && event.isShiftKeyDown()) {
					showManualAction();
				}
			});
		} else {
			showManualAction();
		}

		// Assemble client panel
		txtTaskTitle.setWidth("900px");
		lstTaskAction.setWidth("77pt");
		txtTaskAction.setWidth("90pt");
		txtTaskParams.setWidth("200pt");
		lstParamProduct.setWidth("120pt");
		lstParamVersion.setWidth("120pt");
		chkStartDate.setWordWrap(false);
		txtStartDate.setFormat(new DateBox.DefaultFormat(DateTimeFormat.getFormat(PredefinedFormat.DATE_MEDIUM)));
		txtStartDate.addValueChangeHandler(event -> {
			if (txtStartDate.getValue() != null && txtStartTime.getText().trim().isEmpty())
				txtStartTime.setText("00:00:00");
		});
		txtStartDate.setEnabled(false);
		txtStartTime.setVisibleLength(5);
		txtStartTime.addValueChangeHandler(event -> {
			String timeText = txtStartTime.getText().trim();
			Date time = new Date(0);
			if (timeFormat.parse(timeText, 0, time) == 0)
				if (timeFormat.parse(timeText + ":00", 0, time) == 0)
					if (timeFormat.parse(timeText + ":00:00", 0, time) == 0)
						time = null;
			if (time != null)
				txtStartTime.setValue(timeFormat.format(time));
		});
		txtStartTime.setEnabled(false);
		if (viewMode) {
			txtTaskTitle.setReadOnly(true);
			lstTaskAction.setEnabled(false);
			txtParamProduct.setReadOnly(true);
			txtParamVersion.setReadOnly(true);
			txtTaskAction.setReadOnly(true);
			txtTaskParams.setReadOnly(true);
			chkStartDate.setEnabled(false);
		} else {
			chkStartDate.addValueChangeHandler(event -> {
				txtStartDate.setEnabled(chkStartDate.getValue());
				txtStartTime.setEnabled(chkStartDate.getValue());
			});
		}
		tblTask.setWidth("100%");
		int row = 0;
		FlexCellFormatter tblTaskCF = tblTask.getFlexCellFormatter();
		tblTask.setWidget(row, 0, lblTaskTitle);
		tblTaskCF.setColSpan(row, 1, 3);
		tblTask.setWidget(row++, 1, txtTaskTitle);
		tblTask.setWidget(row, 0, lblTaskAction);
		HorizontalPanel pnlAction = new HorizontalPanel();
		pnlAction.setVerticalAlignment(HasVerticalAlignment.ALIGN_MIDDLE);
		pnlAction.add(lstTaskAction);
		pnlAction.add(new Label(NBSP));
		pnlAction.add(txtTaskAction);
		pnlAction.add(new Label(NBSP));
		pnlAction.add(txtTaskParams);
		tblTask.setWidget(row++, 1, pnlAction);
		tblTask.setWidget(row, 0, lblParamProduct);
		if (viewMode || Restrictions.DISABLE_SCM4J_DEPLOYER_ENGINE) {
			tblTask.setWidget(row++, 1, txtParamProduct);
		} else {
			tblTask.setWidget(row++, 1, lstParamProduct);
		}
		tblTask.setWidget(row, 0, lblParamVersion);
		if (viewMode || Restrictions.DISABLE_SCM4J_DEPLOYER_ENGINE) {
			tblTask.setWidget(row++, 1, txtParamVersion);
		} else {
			tblTask.setWidget(row++, 1, lstParamVersion);
		}
		tblTask.setWidget(row, 0, chkStartDate);
		HorizontalPanel pnlStartDatetime = new HorizontalPanel();
		pnlStartDatetime.setVerticalAlignment(HasVerticalAlignment.ALIGN_MIDDLE);
		pnlStartDatetime.add(txtStartDate);
		pnlStartDatetime.add(new Label(NBSP));
		pnlStartDatetime.add(lblStartTime);
		pnlStartDatetime.add(new Label(NBSP));
		pnlStartDatetime.add(txtStartTime);
		tblTask.setWidget(row++, 1, pnlStartDatetime);
		dpnTask.setWidget(tblTask);

		// Create sub tasks table
		tblSubTasks.addClickHandler(this);
		tblSubTasks.addStyleName("al-Table"); 
		tblSubTasks.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblSubTasks.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "2500");
		tblSubTasks.setText(0, col++, constants.clientNameColumnHeader());
		tableCF.setWidth(0, col, "1900");
		tblSubTasks.setText(0, col++, constants.licenseUidColumnHeader());
		tableCF.setWidth(0, col, "900");
		tblSubTasks.setText(0, col++, constants.hostColumnHeader());
		tableCF.setWidth(0, col, "1000");
		tblSubTasks.setText(0, col++, constants.statusColumnHeader());
		tableCF.setWidth(0, col, "600");
		tblSubTasks.setText(0, col++, constants.resultColumnHeader());

		// Assemble sub tasks panel with scroll
		scrSubTaks.setSize("1000px", "300px");
		dpnSubTasks.setWidget(scrSubTaks);

		// Assemble button panel
		pnlButtons.setWidth("100%");
		if (viewMode) {
			btnClose.setWidth("80px");
			Label lblSplitter = new Label(" ");
			pnlButtons.add(lblSplitter);
			pnlButtons.setCellWidth(lblSplitter, "100%");
			pnlButtons.add(btnClose);
			pnlButtons.setCellHorizontalAlignment(btnClose, HasHorizontalAlignment.ALIGN_RIGHT);
		} else {
			btnAddSubTasks.setWidth("120px");
			btnClearSubTasks.setWidth("120px");
			btnCreate.setWidth("80px");
			btnCancel.setWidth("80px");
			pnlButtons.add(btnAddSubTasks);
			pnlButtons.setCellHorizontalAlignment(btnAddSubTasks, HasHorizontalAlignment.ALIGN_LEFT);
			pnlButtons.add(btnClearSubTasks);
			pnlButtons.setCellHorizontalAlignment(btnClearSubTasks, HasHorizontalAlignment.ALIGN_LEFT);
			pnlButtons.setCellWidth(btnClearSubTasks, "100%");
			pnlButtons.add(btnCreate);
			pnlButtons.setCellHorizontalAlignment(btnCreate, HasHorizontalAlignment.ALIGN_RIGHT);
			pnlButtons.add(btnCancel);
			pnlButtons.setCellHorizontalAlignment(btnCancel, HasHorizontalAlignment.ALIGN_RIGHT);
		}
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnTask);
		mainPanel.add(dpnSubTasks);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnAddSubTasks) {

			new SuAddHostsDialog(taskBundle.dealerId, (hosts) -> addHosts(hosts));

		} else if (sender == btnClearSubTasks) {

			taskBundle.tasks = null;
			refreshSubTasks();

		} else if (sender == btnCreate) {

			btnCreateClick();

		} else if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnClose) {

			this.hide();

		} else if (sender == tblSubTasks) {

			Cell cell = tblSubTasks.getCellForEvent(event);
			if (cell != null && cell.getRowIndex() > 0) {
				int i = 0;
				SuSubTask subTask = null;
				for (SuTask task : taskBundle.tasks) {
					for (SuSubTask subTaskI : task.subTasks) {
						if (i++ == cell.getRowIndex() - 1)
							subTask = subTaskI;
					}
				}
				if (subTask != null)
					new SuSubtaskDialog(subTask).show();
			}

		}

	}

	private void btnCreateClick() {
		Date startTime = new Date(0);

		// check task parameters
		if (lstTaskAction.getSelectedIndex() == Action.MANUAL.ordinal()
				&& txtTaskAction.getValue().trim().isEmpty()) {
			new MessageDialog(messages.pleaseEnterAction(), Style.WARNING, txtTaskAction).show();
			return;
		}
		if (chkStartDate.getValue()) {
			if (txtStartDate.getValue() == null) {
				new MessageDialog(messages.pleaseEnterStartDate(), Style.WARNING, txtStartDate.getTextBox()).show();
				return;
			}
			if (timeFormat.parse(txtStartTime.getValue(), 0, startTime) == 0) {
				new MessageDialog(messages.pleaseEnterStartTime(), Style.WARNING, txtStartTime).show();
				return;
			}
		}
		if (taskBundle.tasks == null || taskBundle.tasks.isEmpty() || taskBundle.tasks.get(0).subTasks == null ||
				taskBundle.tasks.get(0).subTasks.isEmpty()) {
			new MessageDialog(messages.pleaseAddSubTasks(), Style.WARNING, btnAddSubTasks).show();
			return;
		}

		Action action = Action.values()[lstTaskAction.getSelectedIndex()];
		HashMap<String, String> params = new HashMap<>();
		String defaultTitle = lstTaskAction.getSelectedItemText();

		if (action.paramProduct) {
			String paramProduct;
			if (Restrictions.DISABLE_SCM4J_DEPLOYER_ENGINE) {
				paramProduct = txtParamProduct.getValue();
				if (!paramProduct.matches("[\\w.:-]*")) {
					new MessageDialog(messages.unexpectedCharactersUsed(), Style.WARNING, txtParamProduct).show();
					return;
				}
			} else {
				paramProduct = lstParamProduct.getSelectedValue();
				if (paramProduct == null) {
					new MessageDialog(messages.pleaseSelectProduct() , Style.WARNING, lstParamProduct).show();
					return;
				}
			}
			params.put("product", paramProduct);
			defaultTitle = defaultTitle + " " + paramProduct;
		}

		if (action.paramVersion) {
			String paramVersion;
			if (Restrictions.DISABLE_SCM4J_DEPLOYER_ENGINE) {
				paramVersion = txtParamVersion.getValue();
				if (!paramVersion.matches("[\\w.:-]*")) {
					new MessageDialog(messages.unexpectedCharactersUsed(), Style.WARNING, txtParamVersion).show();
					return;
				}
			} else {
				paramVersion = lstParamVersion.getSelectedItemText();
				if (paramVersion == null) {
					new MessageDialog(messages.pleaseSelectVersion() , Style.WARNING, lstParamVersion).show();
					return;
				}
			}
			params.put("version", paramVersion);
			defaultTitle = defaultTitle + " " + paramVersion;
		}

		if (!txtTaskParams.getValue().isEmpty())
			params.putAll(Arrays.asList(txtTaskParams.getValue().split(" ")).stream().map(s -> s.split("=")).collect(Collectors.toMap(e -> e[0], e -> e[1])));

		// fill taskBundle
		taskBundle.title = txtTaskTitle.getValue().trim().isEmpty() ? defaultTitle : txtTaskTitle.getValue();
		taskBundle.action = action.action == null ? txtTaskAction.getValue() : action.action;
		taskBundle.params = params;
		taskBundle.startDatetime = chkStartDate.getValue() && txtStartDate.getValue() != null ?
				Common.combineDateAndTime(txtStartDate.getValue(), startTime) : null;
		UntillLicenseServer.getLicenseServiceAsync().createSuTaskBundle(UntillLicenseServer.getAuthScheme(), 
				taskBundle, this);
		WaitDialog.show();
	}

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		ErrorDialog.show(caught);
	}

	@Override
	public void onSuccess(Void result) {
		WaitDialog.hide();
		this.hide();
		MainPage.get().refresh();
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void showManualAction() {
		if (!manualActionAreShown) {
			lstTaskAction.addItem(constants.actionManualTitle() + ":", Action.MANUAL.name());
			txtTaskAction.setVisible(true);
			txtTaskParams.setVisible(true);
			manualActionAreShown = true;
		}
	}

	private void addHosts(List<SuHost> hosts) {
		if (taskBundle.tasks == null)
			taskBundle.tasks = new ArrayList<>();
		h: for (SuHost host : hosts) {
			SuTask targetTask = null;
			for (SuTask task : taskBundle.tasks) {
				if (task.subTasks.get(0).host.license.licenseUid.equals(host.license.licenseUid)) {
					for (SuSubTask subtask : task.subTasks)
						if (subtask.hostId == host.hostId)
							continue h;
					targetTask = task;
				}
			}
			if (targetTask == null) {
				targetTask = new SuTask();
				targetTask.subTasks = new ArrayList<>();
				taskBundle.tasks.add(targetTask);
			}
			SuSubTask subtask = new SuSubTask();
			subtask.hostId = host.hostId;
			subtask.status = SuSubTask.Status.CREATED;
			subtask.host = host;
			targetTask.subTasks.add(subtask);
		}
		refreshSubTasks();
	}

	private void refreshSubTasks() {
		scrSubTaks.remove(tblSubTasks);
		try {
			for (int r = tblSubTasks.getRowCount() - 1; r > 0; --r)
				tblSubTasks.removeRow(r);
			fillSubTasks();
		} finally {
			scrSubTaks.add(tblSubTasks);
		}
	}

	private void fillSubTasks() {

		RowFormatter tblTargerRF = tblSubTasks.getRowFormatter();
		FlexCellFormatter tblTargerCF = tblSubTasks.getFlexCellFormatter();

		int row = 1;

		if (taskBundle.tasks == null)
			return;

		for (SuTask task : taskBundle.tasks) {
			String curLicenseUid = null;
			for (SuSubTask subtask : task.subTasks) {
	
				String status = subtask.status == SuSubTask.Status.CREATED ? constants.suSubTaskCreatedStatus()
						: subtask.status == SuSubTask.Status.PREPARING ? constants.suSubTaskPreparingStatus()
						: subtask.status == SuSubTask.Status.PREPARED ? constants.suSubTaskPreparedStatus()
						: subtask.status == SuSubTask.Status.WAIT_FOR_EXECUTING ? constants.suSubTaskWaitForExecutingStatus()
						: subtask.status == SuSubTask.Status.EXECUTING ? constants.suSubTaskExecutingStatus()
						: subtask.status == SuSubTask.Status.SUCCESS ? constants.suSubTaskSuccessStatus()
						: subtask.status == SuSubTask.Status.FAILURE ? constants.suSubTaskFailureStatus()
						: subtask.status != null ? subtask.status.name() : "";
	
				// set row style classes
				tblTargerRF.addStyleName(row, "ll-Row");
	
				// fill row
				int col = 0;
				if (curLicenseUid == null || !curLicenseUid.equals(subtask.host.license.licenseUid)) {
					curLicenseUid = subtask.host.license.licenseUid;
					tblTargerCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tblSubTasks.setText(row, col++, subtask.host.license.clientName);
					tblTargerCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					Label licenseUidLabel = new Label(HardCodeHelper.formatHardCode(subtask.host.license.licenseUid), false);
					licenseUidLabel.addStyleName("HardCodeLabel");
					tblSubTasks.setWidget(row, col++, licenseUidLabel);
				} else {
					tblSubTasks.setText(row, col++, ""); // clientName
					tblSubTasks.setText(row, col++, ""); // licenseUid
				}
				tblTargerCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblSubTasks.setText(row, col++, subtask.host.name);
				tblTargerCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblSubTasks.setText(row, col++, status);
				tblTargerCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblSubTasks.setHTML(row, col++, parseJsonResult(subtask.result));
	
				++row;
			}
		}

	}

	private String parseJsonResult(String jsonResult) {

		if (jsonResult == null || jsonResult.isEmpty())
			return "";

		JSONValue jsonValue = JSONParser.parseStrict(jsonResult);
		JSONObject jsonObjectResult = jsonValue.isObject();
		if (jsonObjectResult == null) return "parsing error #1";
		JSONNumber jsonNumberExitCode = jsonObjectResult.get("exitCode").isNumber();
		if (jsonNumberExitCode == null) return "parsing error #2";
		if ((int) jsonNumberExitCode.doubleValue() == 0)
			return "Ok"; //"<span style=\"color:green;\">Ok</span>";
		else
			return "Error"; //"<span style=\"color:red;\">Error (" + jsonNumberExitCode.doubleValue() + ")</span>";
	}

	private void refreshParameters() {
		Action action = Action.values()[lstTaskAction.getSelectedIndex()];
		txtTaskAction.setEnabled(action == Action.MANUAL);
		txtParamProduct.setEnabled(action.paramProduct);
		lstParamProduct.setEnabled(action.paramProduct);
		if (!action.paramProduct) {
			txtParamProduct.setValue("");
			lstParamProduct.setSelectedIndex(-1);
		}
		txtParamVersion.setEnabled(action.paramVersion);
		lstParamVersion.setEnabled(action.paramVersion);
		if (!action.paramVersion) {
			txtParamVersion.setValue("");
			lstParamVersion.setSelectedIndex(-1);
		}
	}

	private void requestLstParamProduct() {
		UntillLicenseServer.getLicenseServiceAsync().getProductList(new AsyncCallback<Map<String, ShProductInfo>>() {
			@Override
			public void onSuccess(Map<String, ShProductInfo> result) {
				products = result;
				refreshLstParamProduct();
			}
			@Override
			public void onFailure(Throwable caught) {
				ErrorDialog.show(caught);
			}
		});
	}

	private void refreshLstParamProduct() {
		String lastSelectedValue = lstParamProduct.getSelectedValue();
		lstParamProduct.clear();
		if (products != null) {
			for (String product : products.keySet()) {
				ShProductInfo productInfo = products.get(product);
				if (productInfo.hidden && !showAllProducts)
					continue;
				lstParamProduct.addItem(Common.capitalizeProductName(product), product);
				if (product != null && product.equals(lastSelectedValue))
					lstParamProduct.setSelectedIndex(lstParamProduct.getItemCount() - 1);
			}
			if (!lstParamProduct.isEnabled())
				lstParamProduct.setSelectedIndex(-1);
		}
		refreshLstParamVersion();
	}

	private void refreshLstParamVersion() {
		lstParamVersion.clear();
		String product = lstParamProduct.getSelectedValue();
		if (product == null)
			return;
		UntillLicenseServer.getLicenseServiceAsync().getProductVersionList(product, new AsyncCallback<Map<String, Boolean>>() {
			@Override
			public void onSuccess(Map<String, Boolean> result) {
				for (String version : result.keySet()) {
					if (result.get(version) || showAllProductVersions)
						lstParamVersion.addItem(version);
				}
				if (!lstParamVersion.isEnabled())
					lstParamVersion.setSelectedIndex(-1);
			}
			@Override
			public void onFailure(Throwable caught) {
				ErrorDialog.show(caught);
			}
		});
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		lstTaskAction.setFocus(true);
	}

}
