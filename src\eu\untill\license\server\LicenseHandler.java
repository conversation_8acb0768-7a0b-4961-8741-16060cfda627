/**
 *
 */
package eu.untill.license.server;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.validator.routines.EmailValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;

import TPAPIPosTypesU.TExtraInfo;
import TPAPIPosTypesU.TOrderItem;
import eu.untill.license.client.LicenseService.AddApostillHddCodesResult;
import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.LicenseService.CommonRpcResult;
import eu.untill.license.client.LicenseService.DbErrorException;
import eu.untill.license.client.LicenseService.InvalidArgumentException;
import eu.untill.license.client.LicenseService.LicensePeriod;
import eu.untill.license.client.LicenseService.LicenseServiceException;
import eu.untill.license.client.LicenseService.LicenseType;
import eu.untill.license.client.LicenseService.RequestLicenseResult;
import eu.untill.license.client.LicenseService.RequestLicenseResult.Results;
import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.client.LicenseService.RrArticle;
import eu.untill.license.client.LicenseService.RrDealer;
import eu.untill.license.client.LicenseService.RrOrder;
import eu.untill.license.client.LicenseService.RrOrderItem;
import eu.untill.license.client.LicenseService.ServerErrorException;
import eu.untill.license.client.LicenseService.SortingType;
import eu.untill.license.server.Common.ExtraField;
import eu.untill.license.server.Dealer.RequiredDealerIsNotFoundException;
import eu.untill.license.server.EmailHelper.ReportType;
import eu.untill.license.server.EmailHelper.SendLicenseReason;
import eu.untill.license.server.InvoiceHelper.Invoice;
import eu.untill.license.shared.ArticleWithPrices;
import eu.untill.license.shared.ClientNameChange;
import eu.untill.license.shared.DbHwmInvoice;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.License;
import eu.untill.license.shared.License.FuncProp.FuncType;
import eu.untill.license.shared.Restrictions;

/**
 * <AUTHOR>
 *
 */
public class LicenseHandler {
	static final Logger LOG = LoggerFactory.getLogger(LicenseHandler.class);

//	private Connection conn;
	private CommonHelper commonHelper;
	private EmailHelper emailHelper;
	private UntillLicensesPluginHelper untillLicensesPluginHelper;
	private InvoiceHelper invoiceHelper;
	private UntillTpapiHelper untillTpapiHelper;
	private FiscalHelper fiscalHelper;
//	private Dealer dealer;

	public LicenseHandler(CommonHelper commonHelper, EmailHelper emailHelper,
			UntillLicensesPluginHelper untillLicensesPluginHelper, InvoiceHelper invoiceHelper,
			UntillTpapiHelper untillTpapiHelper, FiscalHelper fiscalHelper) {
//		this.conn = conn;
		this.commonHelper = commonHelper;
		this.emailHelper = emailHelper;
		this.untillLicensesPluginHelper = untillLicensesPluginHelper;
		this.invoiceHelper = invoiceHelper;
		this.untillTpapiHelper = untillTpapiHelper;
		this.fiscalHelper = fiscalHelper;
//		this.dealer = dealer;
	}

	public List<String> getClientNames(Connection conn, Dealer dealer, long dealerId, boolean onlyOnline) throws SQLException {

		// Parameters "dealerId" only for super dealer
		if (!dealer.superDealer) {
			dealerId = dealer.id;
		}

		TreeSet<String> result = new TreeSet<>(String.CASE_INSENSITIVE_ORDER);

		// Get active client names from DB
		String sql = "SELECT CLIENT_NAME, CLIENT_DISPLAY_NAME FROM LICENSES\n"
				+ "WHERE (IS_ACTIVE > 0 OR REQUEST_ACTIVE > 0)\n"
				+ (dealerId != 0 ? " AND ID_DEALERS = ?\n" : "")
				+ (onlyOnline ? " AND BIN_AND(STATUS, ?) <> 0\n" : "");
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			int parIndex = 1;
			if (dealerId != 0)
				stmt.setLong(parIndex++, dealerId);
			if (onlyOnline)
				stmt.setShort(parIndex++, DbLicense.ONLINE_BIT);
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					result.add(rs.getString("CLIENT_NAME"));
					if (rs.getString("CLIENT_DISPLAY_NAME") != null)
						result.add(rs.getString("CLIENT_DISPLAY_NAME"));
				}
			}
		}

		return new ArrayList<String>(result);
	}

	public List<String> getHardCodes(Connection conn, Dealer dealer, long dealerId, boolean onlyOnline) throws SQLException {

		// Parameters "dealerId" only for super dealer
		if (!dealer.superDealer) {
			dealerId = dealer.id;
		}

		ArrayList<String> result = new ArrayList<>();

		// Get active client names from DB
		String sql = "SELECT DISTINCT HARD_CODE FROM LICENSES\n"
				+ "WHERE (IS_ACTIVE > 0 OR REQUEST_ACTIVE > 0)\n"
				+ (dealerId != 0 ? " AND ID_DEALERS = ?\n" : "")
				+ (onlyOnline ? " AND BIN_AND(STATUS, ?) <> 0\n" : "");
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			int parIndex = 1;
			if (dealerId != 0)
				stmt.setLong(parIndex++, dealerId);
			if (onlyOnline)
				stmt.setShort(parIndex++, DbLicense.ONLINE_BIT);
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					result.add(rs.getString(1));
				}
			}
		}

		return result;
	}

	public List<RrDealer> getAllDealers(Connection conn, Dealer dealer) throws SQLException, LicenseServiceException {
		ArrayList<RrDealer> result;

		if (!dealer.superDealer)
			throw new AuthFailedException();

		String permJLogForCountries = commonHelper.getSettings().getEnablePermanentJLoggingForCountries();
		String defaultOnlineForDealers = commonHelper.getSettings().getDefaultOnlineForDealers();

		List<Dealer> allDealers = Dealer.getAllDealers(conn, false);
		result = new ArrayList<RrDealer>();
		for (Dealer dealerItem : allDealers) {
			result.add(new RrDealer(dealerItem.id, dealerItem.name, dealerItem.country, dealerItem.superDealer,
					EmailValidator.getInstance(true).isValid(dealerItem.email),
					dealerItem.onlyProlong, dealerItem.country.matches("(?i)" + permJLogForCountries),
					dealerItem.name.matches("(?i)" + defaultOnlineForDealers), dealerItem.autoProlongDef,
					!dealerItem.pricesPass.isEmpty()));
		}

		return result;
	}

	public List<RrDealer> getApostillDealers(Connection conn, Dealer dealer) throws LicenseServiceException, SQLException {
		ArrayList<RrDealer> result;

		if (!dealer.superDealer)
			throw new AuthFailedException();

		String permJLogForCountries = commonHelper.getSettings().getEnablePermanentJLoggingForCountries();
		String defaultOnlineForDealers = commonHelper.getSettings().getDefaultOnlineForDealers();

		List<Dealer> apostillDealers = Dealer.getAllDealers(conn, true);
		result = new ArrayList<RrDealer>();
		for (Dealer dealerItem : apostillDealers) {
			result.add(new RrDealer(dealerItem.id, dealerItem.name, dealerItem.country, dealerItem.superDealer,
					EmailValidator.getInstance(true).isValid(dealerItem.email),
					dealerItem.onlyProlong, dealerItem.country.matches("(?i)" + permJLogForCountries),
					dealerItem.name.matches("(?i)" + defaultOnlineForDealers), dealerItem.autoProlongDef,
					!dealerItem.pricesPass.isEmpty()));
		}

		return result;
	}

	@SuppressWarnings("fallthrough")
	public List<DbLicense> getLicenseList(Connection conn, Dealer dealer, long dealerId,
			SortingType sortingType, String clientNameFilter, String hardCodeFilter,
			boolean showOnlyTrial, boolean showCanceled, boolean showOnlyNonApproved,
			String chainFilter, boolean showOnlyOnline, boolean disableWildcards)
			throws SQLException
	{
		List<DbLicense> result;

		// Parameters "dealerId", "showCanceled" and "showOnlyNonApproved" only for super dealer
		if (!dealer.superDealer) {
			dealerId = dealer.id;
			showCanceled = false;
			showOnlyNonApproved = false;
		}

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			String filterSqlPart = "";
			if (dealerId != 0)
				filterSqlPart += "    AND L.ID_DEALERS = ?\n";
			if (clientNameFilter != null)
				filterSqlPart += "    AND (UPPER(L.CLIENT_NAME) " + (disableWildcards ? "=" : "LIKE") + " UPPER(?)\n"
						+ "        OR UPPER(L.CLIENT_DISPLAY_NAME) " + (disableWildcards ? "=" : "LIKE") + " UPPER(?))\n";
			if (hardCodeFilter != null)
				filterSqlPart += "    AND UPPER(L.HARD_CODE) " + (disableWildcards ? "=" : "LIKE") + " UPPER(?)\n";
			if (chainFilter != null)
				filterSqlPart += "    AND UPPER(L.CHAIN) " + (disableWildcards ? "=" : "LIKE") + " UPPER(?)\n";

			String orderSqlPart = "ORDER BY L.ID";
			if (sortingType != null) {
				orderSqlPart = "ORDER BY ";
				String orderSuffix = "";
				switch (sortingType) {
				case BY_LB_CONNECTIONS_DESC:
				case BY_REM_CONNECTIONS_DESC:
				case BY_OM_CONNECTIONS_DESC:
//				case BY_HQ_CONNECTIONS_DESC:
				case BY_PS_CONNECTIONS_DESC:
				case BY_HE_CONNECTIONS_DESC:
					orderSuffix = " DESC";
				case BY_LB_CONNECTIONS:
				case BY_REM_CONNECTIONS:
				case BY_OM_CONNECTIONS:
//				case BY_HQ_CONNECTIONS:
				case BY_PS_CONNECTIONS:
				case BY_HE_CONNECTIONS:
					orderSqlPart += "MOD(STATUS/"
							+ Short.toString(DbLicense.DESCRIPTION_UNLIMITED_BIT) + ", 2)"
							+ orderSuffix + ", ";
					break;
				case BY_EXPIRED_DATE_DESC:
					orderSuffix = " DESC";
				case BY_EXPIRED_DATE:
					orderSqlPart += "MOD(STATUS/"
						+ Short.toString(DbLicense.NO_EXPIRED_DATE_BIT) + ", 2)"
						+ orderSuffix + ", ";
				default:
					break;
				}

				switch (sortingType) {
				case BY_CLIENT_NAME: orderSqlPart += "UPPER(L.CLIENT_NAME)"; break;
				case BY_CLIENT_NAME_DESC: orderSqlPart += "UPPER(L.CLIENT_NAME) DESC"; break;
				case BY_CLIENT_DISPLAY_NAME: orderSqlPart += "UPPER(L.CLIENT_DISPLAY_NAME)"; break;
				case BY_CLIENT_DISPLAY_DESC: orderSqlPart += "UPPER(L.CLIENT_DISPLAY_NAME) DESC"; break;
				case BY_CHAIN: orderSqlPart += "UPPER(L.CHAIN)"; break;
				case BY_CHAIN_DESC: orderSqlPart += "UPPER(L.CHAIN) DESC"; break;
				case BY_LB_CONNECTIONS: orderSqlPart += "COALESCE(L.LB_CONNECTIONS, 0)"; break;
				case BY_LB_CONNECTIONS_DESC: orderSqlPart += "COALESCE(L.LB_CONNECTIONS, 0) DESC"; break;
				case BY_REM_CONNECTIONS: orderSqlPart += "COALESCE(L.REM_CONNECTIONS, 0) + "
						+ "COALESCE(L.BO_CONNECTIONS, 0) + COALESCE(L.POS_CONNECTIONS, 0)"; break;
				case BY_REM_CONNECTIONS_DESC: orderSqlPart += "COALESCE(L.REM_CONNECTIONS, 0) + "
						+ "COALESCE(L.BO_CONNECTIONS, 0) + COALESCE(L.POS_CONNECTIONS, 0) DESC"; break;
				case BY_OM_CONNECTIONS: orderSqlPart += "COALESCE(L.OM_CONNECTIONS, 0)"; break;
				case BY_OM_CONNECTIONS_DESC: orderSqlPart += "COALESCE(L.OM_CONNECTIONS, 0) DESC"; break;
//				case BY_HQ_CONNECTIONS: orderSqlPart += "COALESCE(L.HQ_CONNECTIONS, 0)"; break;
//				case BY_HQ_CONNECTIONS_DESC: orderSqlPart += "COALESCE(L.HQ_CONNECTIONS, 0) DESC"; break;
				case BY_PS_CONNECTIONS: orderSqlPart += "COALESCE(L.PS_CONNECTIONS, 0)"; break;
				case BY_PS_CONNECTIONS_DESC: orderSqlPart += "COALESCE(L.PS_CONNECTIONS, 0) DESC"; break;
				case BY_HE_CONNECTIONS: orderSqlPart += "COALESCE(L.HE_CONNECTIONS, 0)"; break;
				case BY_HE_CONNECTIONS_DESC: orderSqlPart += "COALESCE(L.HE_CONNECTIONS, 0) DESC"; break;
				case BY_START_DATE: orderSqlPart += "L.START_DATE"; break;
				case BY_START_DATE_DESC: orderSqlPart += "L.START_DATE DESC"; break;
				case BY_ISSUE_DATE: orderSqlPart += "L.ISSUE_DATE"; break;
				case BY_ISSUE_DATE_DESC: orderSqlPart += "L.ISSUE_DATE DESC"; break;
				case BY_EXPIRED_DATE: orderSqlPart += "L.EXPIRED_DATE"; break;
				case BY_EXPIRED_DATE_DESC: orderSqlPart += "L.EXPIRED_DATE DESC"; break;
				case BY_TEMP_EXPIRED_DATE: orderSqlPart += "L.TEMP_EXPIRED_DATE"; break;
				case BY_TEMP_EXPIRED_DATE_DESC: orderSqlPart += "L.TEMP_EXPIRED_DATE DESC"; break;
				default: orderSqlPart += "1";
				}
			}

			// Get licenses from DB
			stmt = conn.prepareStatement("SELECT * \n"
					+ "FROM LICENSES L\n"
					+ "WHERE (L.REQUEST_ACTIVE = 1 OR L.IS_ACTIVE = 1\n"
					+ "    AND NOT EXISTS (SELECT LP.ID FROM LICENSES LP\n"
					+ "        WHERE LP.ID_LICENSES_PREV = L.ID AND LP.REQUEST_ACTIVE = 1\n"
					+ "        AND BIN_AND(STATUS, " + License.DESCRIPTION_BOOST_BIT + ") = 0)"
					+ "    )\n"
					+ filterSqlPart
					+ orderSqlPart + "\n");
			int parIndex = 1;
			if (dealerId != 0)
				stmt.setLong(parIndex++, dealerId);
			if (clientNameFilter != null)
				if (disableWildcards) {
					stmt.setString(parIndex++, clientNameFilter);
					stmt.setString(parIndex++, clientNameFilter);
				} else {
					stmt.setString(parIndex++, clientNameFilter.trim().replace('*', '%').replace('?', '_'));
					stmt.setString(parIndex++, clientNameFilter.trim().replace('*', '%').replace('?', '_'));
				}
			if (hardCodeFilter != null)
				if (disableWildcards)
					stmt.setString(parIndex++, hardCodeFilter.replaceAll("-", ""));
				else
					stmt.setString(parIndex++, hardCodeFilter.replaceAll("-", "").trim()
							.replace('*', '%').replace('?', '_'));
			if (chainFilter != null)
				if (disableWildcards)
					stmt.setString(parIndex++, chainFilter);
				else
					stmt.setString(parIndex++, chainFilter.trim().replace('*', '%').replace('?', '_'));
			rs = stmt.executeQuery();

			Date nowDate = Common.toUtcDateOnly(commonHelper.getCurrentDate());

			// Get results
			result = new ArrayList<DbLicense>();
			while (rs.next()) {
				DbLicense license = Common.getDbLicenseFromResultSet(rs);
				if (showOnlyTrial != license.isTrial())
					continue;
				if (!showCanceled && license.isCanceled())
					continue;
				if (showOnlyNonApproved && !license.needApprove())
					continue;
				if (showOnlyOnline && !license.isOnline())
					continue;
				if (license.isBoost() && !license.isNeverExpired() && nowDate.compareTo(license.getExpiredDate()) > 0)
					continue;
				result.add(license);
			}

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		return result;
	}

	public LicensePeriod getNewLicensePeriod(LicenseType licenseType) {
		Date startDate = Common.toUtcDateOnly(commonHelper.getCurrentDate());
		Calendar expiredDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		expiredDate.setTime(startDate);

		switch (licenseType) {
		case GENERAL:        expiredDate.add(Calendar.YEAR, 1);  break;
		case APOSTILL:       expiredDate.add(Calendar.YEAR, 3);  break;
		case APOSTILL_TRIAL: expiredDate.add(Calendar.DATE, 10); break; // TODO make configurable
		case SAAS:
			if (expiredDate.get(Calendar.DAY_OF_MONTH) < 15) {
				expiredDate.set(Calendar.DAY_OF_MONTH, 1);
				expiredDate.add(Calendar.MONTH, 1);
			} else {
				expiredDate.set(Calendar.DAY_OF_MONTH, 1);
				expiredDate.add(Calendar.MONTH, 2);
			}
			break;
		default: assert false; break;
		}

		expiredDate.add(Calendar.DATE, -1);
		LicensePeriod result = new LicensePeriod(startDate, expiredDate.getTime());

		return result;
	}

	public LicensePeriod getProlongLicensePeriod(LicenseType licenseType, LicensePeriod prolongedLicensePeriod) {
		Calendar expiredDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		expiredDate.setTime(prolongedLicensePeriod.getExpiredDate());
		expiredDate.add(Calendar.DATE, 1);

		// If prolong was after break, then continue from current day (only for Saas)
		if (licenseType == LicenseType.SAAS) {
			Calendar currentDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
			currentDate.setTime(Common.toUtcDateOnly(commonHelper.getCurrentDate()));
			if (currentDate.after(expiredDate))
				expiredDate = currentDate;
		}

		switch (licenseType) {
		case GENERAL:        expiredDate.add(Calendar.YEAR, 1);  break;
		case DEMO:           expiredDate.add(Calendar.MONTH, 3); break;
		case APOSTILL:       expiredDate.add(Calendar.YEAR, 3);  break;
		case APOSTILL_TRIAL: ;                                   break;
		case SAAS:
			if (expiredDate.get(Calendar.DAY_OF_MONTH) < 15) {
				expiredDate.set(Calendar.DAY_OF_MONTH, 1);
				expiredDate.add(Calendar.MONTH, 1);
			} else {
				expiredDate.set(Calendar.DAY_OF_MONTH, 1);
				expiredDate.add(Calendar.MONTH, 2);
			}
			break;
		default: assert false; break;
		}

		expiredDate.add(Calendar.DATE, -1);
		LicensePeriod result = new LicensePeriod(prolongedLicensePeriod.getStartDate(),
				expiredDate.getTime());

		return result;
	}

	public LicensePeriod getEmergencyLicensePeriod(Date baseExpiredDate) {
		Date startDate = Common.toUtcDateOnly(commonHelper.getCurrentDate());
		Calendar expiredDateCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		expiredDateCal.setTime(startDate);
		expiredDateCal.add(Calendar.DATE, 10); // TODO make configurable

		expiredDateCal.add(Calendar.DATE, -1);

		Date expiredDate = expiredDateCal.getTime();

		if (baseExpiredDate != null && baseExpiredDate.before(expiredDate))
			expiredDate = baseExpiredDate;

		LicensePeriod result = new LicensePeriod(startDate, expiredDate);

		return result;
	}

	public boolean validLicenseHardCode(String hardCode) {
		// TODO move to client
		return HardCodeHelper.validLicenseHardCode(hardCode);
	}

	private static final byte[] HARD_CODE_SYMBOLS = "0123456789ABCDEFGHJKLMNPQRSTUVWXYZ".getBytes();

	public static String generateRandomHardCode() {
		Random random = new Random();
		byte[] hardCodeBytes = new byte[License.HARD_INFO_STRING_LENGTH];
		int chkSum = 0;
		for (int i = 0; i < License.HARD_INFO_STRING_LENGTH - 1; ++i) {
			hardCodeBytes[i] = HARD_CODE_SYMBOLS[random.nextInt(HARD_CODE_SYMBOLS.length)];
			chkSum += (hardCodeBytes[i] + 0x100) & 0xff;
		}
		chkSum = chkSum % 10;
		byte codeCheckSum = (byte) ('0' + chkSum % 10);
		hardCodeBytes[License.HARD_INFO_STRING_LENGTH - 1] = codeCheckSum;

		return new String(hardCodeBytes);
	}

	public RequestLicenseResult requestLicense(Connection conn, Dealer dealer, DbLicense license,
			RequestType requestType) throws LicenseServiceException, SQLException {
		return requestLicense(conn, dealer, license, requestType, false);
	}

	public RequestLicenseResult requestLicense(Connection conn, Dealer dealer, DbLicense license,
			RequestType requestType, boolean automaticNoEmails) throws LicenseServiceException, SQLException
	{
		// Check parameters
		if (license == null)
			throw new InvalidArgumentException("parameter 'license' is null");
		if (requestType == null)
			throw new InvalidArgumentException("parameter 'requestType' is null");
		if (requestType != RequestType.CREATE && requestType != RequestType.PROLONG
				&& requestType != RequestType.UPGRADE && requestType != RequestType.EMERGENCY
				&& requestType != RequestType.CREATE_BOOST)
			throw new InvalidArgumentException("parameter 'requestType' is invalid");
		if (automaticNoEmails && requestType == RequestType.EMERGENCY)
			throw new InvalidArgumentException("parameter 'automaticNoEmails' is invalid");

		// Check authentication
		Dealer superDealer = null;
		Dealer targetDealer = dealer;

		// Check permissions
		if (targetDealer.onlyProlong && !targetDealer.superDealer &&
				!(requestType == RequestType.PROLONG || requestType == RequestType.EMERGENCY)) {
			LOG.warn("Attempt to create/upgrade a license by dealer with 'Only prolong' flag");
			return new RequestLicenseResult("You can not create or upgrade license");
		}

		// Generate License UID (HARD_CODE) for new online license
		if (requestType == RequestType.CREATE && license.isOnline())
			license.hardCode = generateRandomHardCode();

		// Precheck license data: Client part (common)
		if (license.hardCode == null) license.hardCode = "";
		license.hardCode = license.hardCode.trim();
		if (license.dealerComments == null) license.dealerComments = "";
		license.dealerComments = license.dealerComments.trim();

		// Preheck license data: Period
		if (license.getStartDate() == null ||
				(!license.isNeverExpired() &&
						(license.getExpiredDate() == null || license.getStartDate().compareTo(license.getExpiredDate()) > 0)
				)
		) {
			LOG.warn("Attempt to request the license with wrong period\n\tlicense: {}", license);
			return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
		}

		// Precheck license data: Temp expired date
		if (license.tempExpiredDate != null) {
			if (!(requestType == RequestType.CREATE || requestType == RequestType.PROLONG)
					|| license.isApostill()
					|| license.tempExpiredDate.before(license.getStartDate())
					|| license.tempExpiredDate.after(license.getExpiredDate())) {
				LOG.warn("Illegal request a temporary license\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
		}

		// (for emergency license, client data must be exactly equal original license
		// client data, and therefore can not be _any_ change)
		if (requestType != RequestType.EMERGENCY) {

			// Precheck license data: Client part (rest)
			if (license.getClientName() == null || license.getClientName().trim().isEmpty())
				return new RequestLicenseResult(Results.FAIL_CLIENT_NAME_IS_EMPTY);
			license.setClientName(license.getClientName().trim());
			if (requestType != RequestType.CREATE_BOOST && !automaticNoEmails)
				if (license.getClientDisplayName() == null || license.getClientDisplayName().trim().isEmpty())
					return new RequestLicenseResult(Results.FAIL_CLIENT_DISPLAY_NAME_IS_EMPTY);
			if (license.getClientAddress() == null) license.setClientAddress("");
			license.setClientAddress(license.getClientAddress().trim());
			if (license.getClientEmail() == null) license.setClientEmail("");
			license.setClientEmail(license.getClientEmail().trim());
			if (license.getClientPhone() == null) license.setClientPhone("");
			license.setClientPhone(license.getClientPhone().trim());

			// Precheck license data: Quantitative restrictions part
			if (!license.isUnlimited()) {
				if (license.getLbConnections() < 0)
					return new RequestLicenseResult(Results.FAIL_LB_CONNECTIONS_IS_NEGATIVE);
				if (license.getRemConnections() < 0)
					return new RequestLicenseResult(Results.FAIL_REM_CONNECTIONS_IS_NEGATIVE);
				if (license.getOmConnections() < 0)
					return new RequestLicenseResult(Results.FAIL_OM_CONNECTIONS_IS_NEGATIVE);
//				if (license.hqConnections < 0)
//					return new RequestLicenseResult(Results.FAIL_HQ_CONNECTIONS_IS_NEGATIVE);
				if (license.getPsConnections() < 0)
					return new RequestLicenseResult(Results.FAIL_PS_CONNECTIONS_IS_NEGATIVE);
				if (license.getHeConnections() < 0)
					return new RequestLicenseResult(Results.FAIL_HE_CONNECTIONS_IS_NEGATIVE);
				if (license.getLbConnections() == 0 && requestType != RequestType.CREATE_BOOST)
					return new RequestLicenseResult(Results.FAIL_LB_CONNECTIONS_IS_ZERO);
			}

		} // if (requestType != RequestType.EMERGENCY)

		// Precheck license data: Status part
		if ((license.getStatus() & ~DbLicense.STATUS_VALID_MASK) != 0
				|| !license.isStatusActive()
				|| license.isCanceled()
				|| license.isNeverExpired())
		{
			LOG.warn("Attempt to request the license with the wrong status\n\tlicense: {}", license);
			return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
		}
		if (license.isDefinitive()) {
			if (Restrictions.DEFINITIVE_DISABLE_PROLONG && requestType == RequestType.PROLONG) {
				LOG.warn("Activated restriction DEFINITIVE_DISABLE_PROLONG\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
			if (Restrictions.DEFINITIVE_DISABLE_UPGRADE && requestType == RequestType.UPGRADE) {
				LOG.warn("Activated restriction DEFINITIVE_DISABLE_UPGRADE\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
			if (Restrictions.DISABLE_CREATE_DEFINITIVE && requestType == RequestType.CREATE) {
				LOG.warn("Activated restriction DISABLE_CREATE_DEFINITIVE\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
			if (Restrictions.DEFINITIVE_DISABLE_BOOST && requestType == RequestType.CREATE_BOOST) {
				LOG.warn("Activated restriction DEFINITIVE_DISABLE_BOOST\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
			if (license.getDefinitiveMaxVersion() < 0
					|| license.getDefinitiveMaxVersion() > getMaxDefinitiveMaxVersion())
				return new RequestLicenseResult(Results.FAIL_DEFINITIVE_MAX_VERSION_IS_WRONG);
		}
		if (license.isSaas()) {
			if (license.isApostill() || license.isTrial() || !license.isOnline()) {
				LOG.warn("Attempt to request the license with the wrong status combination\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
		}
		if (license.isBoost()) {
			if (license.isApostill() || license.isTrial() || !license.isOnline() || license.isSaas()) {
				LOG.warn("Attempt to request the license with the wrong status combination\n\tlicense: {}", license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
		}

		// Get license type
		LicenseType licenseType = license.isApostill() ?
				(license.isTrial() ? LicenseType.APOSTILL_TRIAL : LicenseType.APOSTILL)
				: (license.isTrial() ? LicenseType.DEMO : (license.isSaas() ? LicenseType.SAAS
				: (license.isBoost() ? LicenseType.BOOST : LicenseType.GENERAL)));

		// Check acceptability request type and license type
		if (requestType == RequestType.CREATE && !(
						licenseType == LicenseType.GENERAL ||
						licenseType == LicenseType.SAAS)
				|| requestType == RequestType.PROLONG && !(
						licenseType == LicenseType.GENERAL ||
						licenseType == LicenseType.SAAS ||
						licenseType == LicenseType.APOSTILL ||
						licenseType == LicenseType.DEMO)
				|| requestType == RequestType.UPGRADE && !(
						licenseType == LicenseType.GENERAL ||
						licenseType == LicenseType.SAAS ||
						licenseType == LicenseType.APOSTILL)
				|| requestType == RequestType.EMERGENCY && !(
						licenseType == LicenseType.GENERAL)
				|| requestType == RequestType.CREATE_BOOST && !(
						licenseType == LicenseType.BOOST)) {
			LOG.warn("Attempt to request the license with unacceptability request type ({}) and license type ({})", requestType, licenseType);
			return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
		}

		// Precheck Large Account
		if (license.requestLargeAccount && license.largeAccount
				|| license.requestLargeAccount && requestType != RequestType.CREATE
				|| license.largeAccount && requestType == RequestType.CREATE
		) {
			LOG.warn("Attempt to wrong request (Large Account part) (requestType: {}, licenseType: {}, requestLargeAccount: {}, largeAccount: {})",
					requestType, licenseType, license.requestLargeAccount, license.largeAccount);
			return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
		}

		// Precheck HWM
		if (license.hwmFirst && !license.hwmYearly
				|| license.hwmYearly && !license.getFunction(License.FUNC_WM_HOSTED)
				|| license.hwmYearly && licenseType != LicenseType.GENERAL
				|| license.hwmYearly && requestType == RequestType.CREATE_BOOST
		) {
			LOG.warn("Attempt to wrong request (HWM part) (requestType: {}, licenseType: {}, funcLimited: {}, hwmYearly: {}, hwmFirst: {})",
					requestType, licenseType, license.getFuncLimited(), license.hwmYearly, license.hwmFirst);
			return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
		}

		// Precheck Fiscal
		if (license.getFunction(License.FUNC_FISCALIZATION)) {
			if (license.getFiscal() == null) {
				LOG.warn("Attempt to wrong request (no extra fiscal part) (requestType: {}, license: {})",
						requestType, license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
		} else {
			if (license.getFiscal() != null) {
				LOG.warn("Attempt to wrong request (extra fiscal part present) (requestType: {}, license: {})",
						requestType, license);
				return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
			}
		}

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			boolean upgradesFound = false;

			Date currentUtcDate = Common.toUtcDateOnly(commonHelper.getCurrentDate());

			// Get original license (for "PROLONG" or "UPGRADE" request) and
			// get expected period of new license
			DbLicense originalLicense = null;
			String licenseComment = "";
			LicensePeriod expectedLicensePeriod;

			if (requestType == RequestType.CREATE) {

				// Update target dealer and super dealer (for "CREATE" request)
				if (targetDealer.superDealer && license.idDealers != 0
						&& license.idDealers != targetDealer.id) {
					superDealer = targetDealer;
					try {
						targetDealer = new Dealer(conn, license.idDealers);
					} catch (RequiredDealerIsNotFoundException e) {
						LOG.warn("Dealer of the license is not found", e);
						throw new ServerErrorException("Dealer of the license is not found");
					}
				}

				expectedLicensePeriod = this.getNewLicensePeriod(licenseType);

			} else { // requestType != RequestType.CREATE

				// Check for no license-request for this license
				if (isExistsLicenseRequestForLicense(conn, license.id)) {
					LOG.warn("Attempt to request license for licenses with already license-request (license.id: {})", license.id);
					return new RequestLicenseResult("License-request for this license already exists");
				}

				// Check license id
				stmt = conn.prepareStatement("SELECT *\n"
						+ "FROM LICENSES\n"
						+ "WHERE ID = ?\n");
				stmt.setLong(1, license.id);
				rs = stmt.executeQuery();
				// TODO correct error messages (+ EMERGENCY)
				if (!rs.next()) {
					LOG.warn("Attempt to prolong/upgrade/boost the license which is missing (license.id: {})", license.id);
					return new RequestLicenseResult("You can not prolong, upgrade or boost this license, because is missing");
				}
				long originalLicenseDealerId = rs.getLong("ID_DEALERS");
				if (originalLicenseDealerId != targetDealer.id && !targetDealer.superDealer) {
					LOG.warn("Attempt to prolong/upgrade/boost the license which does not belong to this dealers (license.id: {}, targetDealer.id: {})",
							license.id, targetDealer.id);
					return new RequestLicenseResult("You can not prolong, upgrade  or boost this license, because you are not an owner");
				}
				// Update target dealer and super dealer (for the not "CREATE" request)
				if (originalLicenseDealerId != 0 && originalLicenseDealerId != targetDealer.id) {
					superDealer = targetDealer;
					try {
						targetDealer = new Dealer(conn, originalLicenseDealerId);
					} catch (RequiredDealerIsNotFoundException e) {
						LOG.warn("Dealer of the license is not found", e);
						throw new ServerErrorException("Dealer of the license is not found");
					}
				}
				// Check license state
				if (rs.getLong("IS_ACTIVE") == 0) {
					LOG.warn("Attempt to prolong/upgrade/boost the inactive license (license.id: {})", license.id);
					return new RequestLicenseResult("You can not prolong or upgrade this license, because license is inactive");
				}
				originalLicense = Common.getDbLicenseFromResultSet(rs);
				licenseComment = rs.getString("COMMENTS");
				rs.close(); rs = null; stmt.close(); stmt = null;

				// Check dealer id
				if (originalLicense.idDealers != license.idDealers) {
					LOG.warn("Attempt to change dealer of license (license.id: {}, targetDealer.id: {})", originalLicense.id, targetDealer.id);
					return new RequestLicenseResult("You can not prolong, upgrade or boost license with change dealer");
				}

				if (originalLicense.isCanceled()) {
					LOG.warn("Attempt to prolong/upgrade/boost the cancelled license\n\tlicense: {}", license);
					return new RequestLicenseResult("You can not prolong, upgrade or boost this license, because license is canceled");
				}
				if (requestType == RequestType.PROLONG && originalLicense.nonProlongable) {
					LOG.warn("Attempt to prolong the non-prolongable license\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
				if (requestType != RequestType.EMERGENCY && originalLicense.isNeverExpired()) {
					LOG.warn("Attempt to prolong/upgrade/boost the license without expired date\n\tlicense: {}", license);
					return new RequestLicenseResult("You can not prolong, upgrade or boost this license, because license has no expired date");
				}
				if (requestType == RequestType.PROLONG && TimeUnit.MILLISECONDS
						.toDays(originalLicense.getExpiredDate().getTime() - currentUtcDate.getTime()) > 60) {
					LOG.warn("Attempt to prolong the license premature\n\toriginalLicense: {}", originalLicense);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
				// TODO Check that original license is not expired (except if prolong)
				//if (requestType != RequestType.PROLONG
				//		&& !originalLicense.isStatusNeverExpired()
				//		&& originalLicense.expiredDate.compareTo(nowDate) < 0) ...
				if (requestType == RequestType.PROLONG) {
					expectedLicensePeriod = this.getProlongLicensePeriod(licenseType,
							new LicensePeriod(originalLicense.getStartDate(), originalLicense.getExpiredDate()));
				} else if (requestType == RequestType.UPGRADE) {
					expectedLicensePeriod = new LicensePeriod(originalLicense.getStartDate(), originalLicense.getExpiredDate());
				} else if (requestType == RequestType.CREATE_BOOST) {
					expectedLicensePeriod = new LicensePeriod(originalLicense.getStartDate(), originalLicense.getExpiredDate());
				} else { //requestType == RequestType.EMERGENCY
					expectedLicensePeriod = this.getEmergencyLicensePeriod(
							originalLicense.isNeverExpired() ? null : originalLicense.getExpiredDate());
				}

			} // if (requestType == RequestType.CREATE) ... else ...

			// Complete check license data

			// Check license data: Period
			if (requestType == RequestType.CREATE_BOOST) {
				if (license.getStartDate().before(expectedLicensePeriod.getStartDate())
						|| license.getExpiredDate().after(expectedLicensePeriod.getExpiredDate()))
				{
					LOG.warn("Attempt to request new boost license for a period not within the base license period\n\tlicense: {}\n\texpectedLicensePeriod: {}", license, expectedLicensePeriod);
					return new RequestLicenseResult(Results.FAIL_PERIOD_IS_INCORRECT);
				}
			} else {
				if (expectedLicensePeriod.getStartDate().compareTo(license.getStartDate()) != 0 ||
						(!license.isNeverExpired() &&
								expectedLicensePeriod.getExpiredDate().compareTo(license.getExpiredDate()) != 0))
				{
					LOG.warn("Attempt to request the license for the period not appointed by server\n\tlicense: {}\n\texpectedLicensePeriod: {}", license, expectedLicensePeriod);
					return new RequestLicenseResult(Results.FAIL_PERIOD_IS_INCORRECT);
				}
			}

			// Check license data: Client part
			if (requestType == RequestType.CREATE || requestType == RequestType.EMERGENCY
					|| Restrictions.SUPER_DEALER_CAN_CHANGE_HARD_CODE && (targetDealer.superDealer || superDealer != null)) {
				if (!this.validLicenseHardCode(license.hardCode))
					return new RequestLicenseResult(Results.FAIL_HARD_CODE_IS_NO_VALID);
			} else {
				if (!originalLicense.hardCode.equals(license.hardCode)) {
					LOG.warn("Attempt to prolong/upgrade/boost the license with change hardcode\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
			}

			if (Restrictions.ONLY_SUPER_DEALER_CAN_CHANGE_CLIENT_NAME) {
				if (requestType != RequestType.CREATE && !targetDealer.superDealer && superDealer == null
						&& !isEquals(license.getClientName(), originalLicense.getClientName())) {
					LOG.warn("Attempt to change client name by not super dealer\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
			}

			if (requestType == RequestType.CREATE_BOOST) {
				if (license.isApostill()
						|| !isEquals(license.getClientName(), originalLicense.getClientName())
						|| !isEquals(license.getClientDisplayName(), originalLicense.getClientDisplayName())
						|| !isEquals(license.getClientAddress(), originalLicense.getClientAddress())
						|| !isEquals(license.getClientEmail(), originalLicense.getClientEmail())
						|| !isEquals(license.getClientPhone(), originalLicense.getClientPhone())) {
					LOG.warn("Attempt to boost license with change parameters\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
			}

			if (requestType == RequestType.EMERGENCY) {

				if (originalLicense.emergencyAvailable <= 0) {
					return new RequestLicenseResult(Results.FAIL_EMERGENCY_IS_NOT_AVAILABLE);
				}

				short expectedStatus = originalLicense.getStatus();
				expectedStatus &= ~DbLicense.NO_EXPIRED_DATE_BIT;

				if (license.hardCode.equals(originalLicense.hardCode)) {
					LOG.warn("Attempt to get emergency license with same hard code\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}

				if (license.isApostill()
						|| !isEquals(license.getClientName(), originalLicense.getClientName())
						|| !isEquals(license.getClientDisplayName(), originalLicense.getClientDisplayName())
						|| !isEquals(license.getClientAddress(), originalLicense.getClientAddress())
						|| !isEquals(license.getClientEmail(), originalLicense.getClientEmail())
						|| !isEquals(license.getClientPhone(), originalLicense.getClientPhone())
						|| !license.isUnlimited() && (
								license.getLbConnections() != originalLicense.getLbConnections()
								|| license.getRemConnections() != originalLicense.getRemConnections()
								|| license.getOmConnections() != originalLicense.getOmConnections()
//								|| license.getHqConnections() != originalLicense.getHqConnections()
								|| license.getPsConnections() != originalLicense.getPsConnections()
								|| license.getHeConnections() != originalLicense.getHeConnections()
						)
						|| license.getFuncLimited() != originalLicense.getFuncLimited()
						|| DbLicense.getDriversDifference(originalLicense.getDrivers(), license.getDrivers()) != null
						|| license.getStatus() != expectedStatus || license.isDefinitive()
								&& license.getDefinitiveMaxVersion() != originalLicense.getDefinitiveMaxVersion()
						|| license.largeAccount != originalLicense.largeAccount
						|| license.hwmYearly != originalLicense.hwmYearly
				) {
					LOG.warn("Attempt to get emergency license with change parameters\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}

			} else { // requestType != RequestType.EMERGENCY

				// Check license data: Quantitative restrictions part
				// Check license data: Status part
				if (license.isUnlimited()) {
					if (requestType == RequestType.UPGRADE) {
						if (!originalLicense.isUnlimited()) {
							LOG.warn("Attempt to upgrade license having established unlimited status\n\tlicense: {}", license);
							return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
						}
					} else {
						LOG.warn("Attempt to create/prolong/boost license with unlimited status\n\tlicense: {}", license);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
				} else {
					if (requestType == RequestType.UPGRADE) {
						if (license.getLbConnections() < originalLicense.getLbConnections())
							return new RequestLicenseResult(Results.FAIL_LB_CONNECTIONS_IS_DOWNGRADE);
						if (license.getRemConnections() < originalLicense.getRemConnections())
							return new RequestLicenseResult(Results.FAIL_REM_CONNECTIONS_IS_DOWNGRADE);
						if (license.getOmConnections() < originalLicense.getOmConnections())
							return new RequestLicenseResult(Results.FAIL_OM_CONNECTIONS_IS_DOWNGRADE);
//						if (license.getHqConnections() < originalLicense.getHqConnections())
//							return new RequestLicenseResult(Results.FAIL_HQ_CONNECTIONS_IS_DOWNGRADE);
						if (license.getPsConnections() < originalLicense.getPsConnections())
							return new RequestLicenseResult(Results.FAIL_PS_CONNECTIONS_IS_DOWNGRADE);
						if (license.getHeConnections() < originalLicense.getHeConnections())
							return new RequestLicenseResult(Results.FAIL_HE_CONNECTIONS_IS_DOWNGRADE);
						if (license.getLbConnections() > originalLicense.getLbConnections()
								|| license.getRemConnections() > originalLicense.getRemConnections()
								|| license.getOmConnections() > originalLicense.getOmConnections()
//								|| license.getHqConnections() > originalLicense.getHqConnections()
								|| license.getPsConnections() > originalLicense.getPsConnections()
								|| license.getHeConnections() > originalLicense.getHeConnections()
						) {
							upgradesFound = true;
						}
					} else if (requestType == RequestType.CREATE_BOOST) {
						upgradesFound |= license.getLbConnections() > 0
								|| license.getRemConnections() > 0
								|| license.getOmConnections() > 0
//								|| license.getHqConnections() > 0
								|| license.getPsConnections() > 0
								|| license.getHeConnections() > 0;
					} else if (requestType == RequestType.PROLONG) {
						// . check for prolong with modifications
						if (license.getLbConnections() > originalLicense.getLbConnections()
								|| license.getRemConnections() > originalLicense.getRemConnections()
								|| license.getOmConnections() > originalLicense.getOmConnections()
//								|| license.getHqConnections() > originalLicense.getHqConnections()
								|| license.getPsConnections() > originalLicense.getPsConnections()
								|| license.getHeConnections() > originalLicense.getHeConnections()
						) {
							LOG.warn("Attempt to prolong license with modifications\n\tlicense: {}\n\toriginalLicense: {}",
									license, originalLicense);
							return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
						}
					} else { // requestType == RequestType.CREATE
//							if (license.hqConnections != 0) {
//								if (logger.isLoggable(Level.WARNING))
//									LOG.warn("Attempt to create license with number of head quarters\n\tlicense: {}", license);
//								return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
//							}
					}

				}

				// Check license data: funcLimited part
				long accessibleFunctionsMask = 0l;
				for (int i = 0; i < DbLicense.ENABLED_FUNCTIONS_NUMBER; ++i) {
					if (license.isApostill() ? !DbLicense.FUNC_PROPS[i].isAvailableForApostill()
							: !DbLicense.FUNC_PROPS[i].isAvailableForUntill()) {
						continue;
					}
					if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE && !license.isModuled())
						continue;
					accessibleFunctionsMask |= 1l << i;
					// Special behavior for BEVERAGE_CONTROL (+ BERG, BECONET, FRIJADO)
					if (i == DbLicense.FUNC_BEVERAGE_CONTROL) {
						accessibleFunctionsMask |= 1l << DbLicense.FUNC_BERG;
						accessibleFunctionsMask |= 1l << DbLicense.FUNC_BECONET;
						accessibleFunctionsMask |= 1l << DbLicense.FUNC_FRIJADO;
					}
				}

				// Special behavior for BEVERAGE_CONTROL (== BERG, BECONET, FRIJADO)
				boolean bc = (1l << DbLicense.FUNC_BEVERAGE_CONTROL & license.getFuncLimited()) != 0;
				if (((1l << DbLicense.FUNC_BERG & license.getFuncLimited()) != 0) != bc
						|| ((1l << DbLicense.FUNC_BECONET & license.getFuncLimited()) != 0) != bc
						|| ((1l << DbLicense.FUNC_FRIJADO & license.getFuncLimited()) != 0) != bc) {
					LOG.warn("Attempt to request the license with FUNC_BEVERAGE_CONTROL <> FUNC_BERG or FUNC_BECONET or FUNC_FRIJADO\n\tlicense: {}", license);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}

				if (requestType == RequestType.UPGRADE) {
					// . check for downgrade (exclude Tool extra module)
					if ((originalLicense.getFuncLimited() & ~license.getFuncLimited()) != 0) {
						return new RequestLicenseResult(Results.FAIL_FUNC_LIMITED_DOWNGRADE);
					}
					long newFunc = ~originalLicense.getFuncLimited() & license.getFuncLimited();
					if ((newFunc & accessibleFunctionsMask) != newFunc) {
						LOG.warn("Attempt to upgrade the license with the inaccessible function(s)\n\tlicense: {}", license);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
					if ((newFunc & ~DbLicense.FUNC_NON_CHARGED_MASK) != 0) {
						upgradesFound = true;
					}
				} else {
					if (requestType == RequestType.PROLONG) {
						if (license.getFuncLimited() != (originalLicense.getFuncLimited() & accessibleFunctionsMask)) {
							LOG.warn("Attempt to prolong license with modifications (func)\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
							return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
						}
					} else if (requestType == RequestType.CREATE_BOOST) {
						upgradesFound = upgradesFound || (license.getFuncLimited() & ~DbLicense.FUNC_NON_CHARGED_MASK) != 0;
					}
					if ((license.getFuncLimited() & accessibleFunctionsMask) != license.getFuncLimited()) {
						LOG.warn("Attempt to create/prolong/boost the license with the inaccessible function(s)\n\tlicense: {}", license);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
				}

				// Force set "French FDM" flag if dealer from specified country
				String frenchFdmForCountries = commonHelper.getSettings().getFrenchFdmForCountries();
				if (frenchFdmForCountries != null && !frenchFdmForCountries.isEmpty() && targetDealer.country != null
						&& targetDealer.country.matches("(?i)" + frenchFdmForCountries)) {
					license.setFunction(DbLicense.FUNC_FRENCH_FDM, true);
				}

//					// . check dependencies WM functions
//					if ((license.funcLimited & (1l << License.FUNC_WM_OFF)) != 0
//							&& (license.funcLimited & (1l << License.FUNC_WM_REST)) == 0 ) {
//						LOG.warn("Attempt to request the license with FUNC_WM_OFF, but without FUNC_WM_REST\n\tlicense: {}", license);
//						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
//					}
//					if ((license.funcLimited & (1l << License.FUNC_WM_HQ)) != 0
//							&& (license.funcLimited & (1l << License.FUNC_WM_OFF)) == 0 ) {
//						LOG.warn("Attempt to request the license with FUNC_WM_HQ, but without FUNC_WM_OFF\n\tlicense: {}", license);
//						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
//					}

				// Check license data: drivers part
				if (requestType == RequestType.UPGRADE) {
					// TODO check drivers for downgrade
					if (DbLicense.getDriversDifference(originalLicense.getDrivers(), license.getDrivers()) != null) {
						upgradesFound = true;
					}
				} else {
					if (requestType == RequestType.PROLONG) {
						if (!DbLicense.driversEqual(originalLicense.getDrivers(), license.getDrivers())) {
							LOG.warn("Attempt to prolong license with modifications (drivers)\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
							return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
						}
					} else if (requestType == RequestType.CREATE_BOOST) {
						upgradesFound = upgradesFound || DbLicense.getDriversDifference(originalLicense.getDrivers(), license.getDrivers()) != null;
					}
				}

				if (requestType == RequestType.CREATE_BOOST && !upgradesFound) {
					LOG.warn("No upgrades found for boost license\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}

				// Check license data: status part
				if (requestType != RequestType.CREATE) {
					if (license.isApostill() != originalLicense.isApostill()) {
						LOG.warn("Attempt to request the license with another type than that of the previous license\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
					if (license.isTrial() != originalLicense.isTrial()) {
						LOG.warn("Attempt to request the license with another trial status than that of the previous license\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
					if (license.isOnline() != originalLicense.isOnline()) {
						if (!license.isOnline()) {
							LOG.warn("Attempt to request the license with change online to offline status\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
							return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
						}
					}
					if (license.isModuled() != originalLicense.isModuled()) {
						LOG.warn("Attempt to request the license with change moduled status\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
					if (originalLicense.isDefinitive()
							&& license.getDefinitiveMaxVersion() != originalLicense.getDefinitiveMaxVersion()) {
						LOG.warn("Attempt to request the license with change definitiveMaxVersion\n\tlicense: {}\n\toriginalLicense: {}", license, originalLicense);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
				}

				if (requestType == RequestType.UPGRADE && !originalLicense.isDefinitive() && license.isDefinitive()) {
					if (Restrictions.DISABLE_UPGRADE_TO_DEFINITIVE) {
						LOG.warn("Activated restriction DISABLE_UPGRADE_TO_DEFINITIVE\n\tlicense: {}", license);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
					if (Restrictions.ONLY_SUPER_DEALER_CAN_UPGRADE_TO_DEFINITIVE
							&& !(targetDealer.superDealer || superDealer != null)) {
						LOG.warn("Activated restriction ONLY_SUPER_DEALER_CAN_UPGRADE_TO_DEFINITIVE\n\tlicense: {}", license);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
					upgradesFound = true;
				}

				if (requestType != RequestType.CREATE && license.largeAccount != originalLicense.largeAccount) {
					if (!(targetDealer.superDealer || superDealer != null)) {
						LOG.warn("Attempt to change LARGE_ACCOUNT state\n\tlicense: {}", license);
						return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
					}
				}

				if (requestType != RequestType.CREATE_BOOST) {
					// Check if record with the same hard code is already exists
					if (isLicenseDuplicateHardCode(conn, license,
							requestType != RequestType.CREATE ? new Long(license.id) : null))
						return new RequestLicenseResult(Results.FAIL_HARD_CODE_ALREADY_EXISTS);

					// Check if license with same client name, address, email and phone already exists
					if (isLicenseDuplicateClient(conn, license,
							requestType != RequestType.CREATE ? new Long(license.id) : null))
						return new RequestLicenseResult(Results.FAIL_CLIENT_ALREADY_EXISTS);

					// TODO check for already exists license-request with same hard-code or client
				}


			} // if (requestType == RequestType.EMERGENCY) ... else ...

			// Check HWM
			if (requestType != RequestType.CREATE) {
				if (originalLicense.getFunction(License.FUNC_WM_HOSTED) && license.hwmFirst
						|| !originalLicense.getFunction(License.FUNC_WM_HOSTED) && license.hwmYearly && !license.hwmFirst) {
					LOG.warn("Attempt to wrong request (HWM part) (requestType: {}, licenseType: {}, hwmYearly: {}, hwmFirst: {})\n\tlicense: {}\n\toriginalLicense: {}",
							requestType, licenseType, license.hwmYearly, license.hwmFirst, license, originalLicense);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
				if (requestType != RequestType.CREATE_BOOST && originalLicense.getFunction(License.FUNC_WM_HOSTED) &&
						license.hwmYearly != originalLicense.hwmYearly) {
					LOG.warn("Attempt to wrong request (HWM part) (requestType: {}, licenseType: {}, hwmYearly: {}, orig.hwmYearly: {})\n\tlicense: {}\n\toriginalLicense: {}",
							requestType, licenseType, license.hwmYearly, originalLicense.hwmYearly, license, originalLicense);
					return new RequestLicenseResult(Results.FAIL_ILLEGAL_REQUEST);
				}
			}
			// Check Chain
			if (license.chain != null && !license.chain.isEmpty() && license.getFunction(License.FUNC_WM_HOSTED)
					&& checkChainUseInMonthlyOrYearlyHwm(conn, targetDealer.id, license.chain, !license.hwmYearly)) {
				LOG.warn("Attempt to wrong request (HWM part, chain) (requestType: {}, licenseType: {}, hwmYearly: {}, chain: {})\n\tlicense: {}",
						requestType, licenseType, license.hwmYearly, license.chain, license);
				return new RequestLicenseResult(Results.FAIL_CHAIN_USE_FOR_ANOTHER_HWM_PM);
			}

			// Check that the upgrades are available (or only modify)
			if (requestType == RequestType.UPGRADE && !upgradesFound) {
				LOG.info("only modify");
				if (license.getFunction(License.FUNC_FISCALIZATION)) {
					try {
						fiscalHelper.doFiscalization(conn, license);
					} catch (Exception e) {
						LOG.error("Fiscalization Onboaring error", e);
						throw new RuntimeException(e);
					}
				}
				String extraData = Common.getExtraDataJsonFromLicense(license);
				String sql = "UPDATE LICENSES\n"
						+ "SET CLIENT_NAME = ?,\n"       // 1
						+ "  CLIENT_DISPLAY_NAME = ?,\n" // 2
						+ "  CLIENT_ADDRESS = ?,\n"      // 3
						+ "  CLIENT_EMAIL = ?,\n"        // 4
						+ "  CLIENT_PHONE = ?,\n"        // 5
						+ "  HARD_CODE = ?,\n"           // 6
						+ "  FUNC_LIMITED = ?,\n"        // 7
						+ "  STATUS = ?,\n"              // 8
						+ "  DEALER_COMMENTS = ?,\n"     // 9
						+ (license.isOnline() ? "  AVAILABLE_REINSTALLATIONS = COALESCE(AVAILABLE_REINSTALLATIONS, 3),\n" : "")
						+ "  CHAIN = ?,\n"               // 10
						+ "  AUTO_PROLONGABLE = ?,\n"    // 11
						+ "  PROLONG_PARAMS = ?,\n"      // 12
						+ "  MIN_VERSION = ?,\n"         // 13
						+ "  LARGE_ACCOUNT = ?,\n"       // 14
						+ "  EXTRA_DATA = ?\n"           // 15
						+ "  DRIVERS = ?\n"              // 16
						+ "WHERE ID = ?";                // 17
				try (PreparedStatement stmt2 = conn.prepareStatement(sql)) {
					int fld = 1;
					stmt2.setString(fld++, license.getClientName());                  // 1
					stmt2.setString(fld++, license.getClientDisplayName());           // 2
					stmt2.setString(fld++, license.getClientAddress());               // 3
					stmt2.setString(fld++, license.getClientEmail());                 // 4
					stmt2.setString(fld++, license.getClientPhone());                 // 5
					stmt2.setString(fld++, license.hardCode);                         // 6
					stmt2.setLong(fld++, license.getFuncLimited());                   // 7
					stmt2.setShort(fld++, license.getStatus());                       // 8
					stmt2.setString(fld++, license.dealerComments);                   // 9
					stmt2.setString(fld++, license.chain);                            // 10
					stmt2.setShort(fld++, (short) (license.autoProlongable ? 1 : 0)); // 11
					if (license.prolongParams == null) {
						stmt2.setNull(fld++, Types.BLOB);                             // 12 (PROLONG_PARAMS)
					} else {
						Gson gson = new Gson();
						stmt2.setString(fld++, gson.toJson(license.prolongParams));   // 12
					}
					stmt2.setLong(fld++, license.minVersion);                         // 13
					stmt2.setShort(fld++, (short) (license.largeAccount ? 1 : 0));    // 14
					if (extraData == null) {
						stmt2.setNull(fld++, Types.BLOB);                             // 15 (EXTRA_DATA)
					} else {
						stmt2.setString(fld++, extraData);                            // 15
					}
					stmt2.setString(fld++, DbLicense.driversToText(license.getDrivers())); // 16
					stmt2.setLong(fld++, originalLicense.id);                         // 17
					if (stmt2.executeUpdate() == 0) throw new RuntimeException();
				}
				conn.commit(); // XXX Just in case (if autocommit is not enabled)
				return new RequestLicenseResult(Results.SUCCESS_NO_UPGRADE);
			}

			// Generate confirmation UID
			String confirmUid = commonHelper.generateGuid();

			// Generate license issue date
			license.setIssueDate(currentUtcDate);

			boolean noOrder = false;

			// Demo-license can be prolonged only once and without ordering
			if (requestType == RequestType.PROLONG && licenseType == LicenseType.DEMO) {
				license.nonProlongable = true;
				noOrder = true;
			} else {
				license.nonProlongable = false;
			}

			// Definitive license can be prolonged without ordering
			if (requestType == RequestType.PROLONG && license.isDefinitive())
				noOrder = true;

			// No order for Demo Dealer
			if (targetDealer.demoDealer)
				noOrder = true;

			// Get license id for new license
			stmt = conn.prepareStatement("SELECT * FROM GENERATE_TABLE_ID\n");
			rs = stmt.executeQuery();
			rs.next();
			license.id = rs.getLong(1);
			rs.close(); rs = null; stmt.close(); stmt = null;
			LOG.trace("license id for new license {}", license.id);

			String extraData = Common.getExtraDataJsonFromLicense(license);

			// Create requested license in LICENSES table
			//                                  1   2            3                    4               5
			String sql = "INSERT INTO LICENSES (ID, CLIENT_NAME, CLIENT_DISPLAY_NAME, CLIENT_ADDRESS, CLIENT_EMAIL,\n"
					//  6             7          8         9               10
					+ " CLIENT_PHONE, HARD_CODE, ACT_CODE, LB_CONNECTIONS, BO_CONNECTIONS,\n"
					//  11               12               13              14              15              16
					+ " POS_CONNECTIONS, REM_CONNECTIONS, OM_CONNECTIONS, HQ_CONNECTIONS, PS_CONNECTIONS, HE_CONNECTIONS,\n"
					//  17                18          19          20            21            22
					+ " ACT_CHECK_PERIOD, START_DATE, ISSUE_DATE, EXPIRED_DATE, FUNC_LIMITED, STATUS,\n"
					//  23        24               25          26         27           28
					+ " COMMENTS, DEALER_COMMENTS, ID_DEALERS, IS_ACTIVE, CONFIRM_UID, REQUEST_DATE,\n"
					//  29            30             31                32
					+ " CONFIRM_DATE, REQUEST_PHASE, ID_LICENSES_PREV, REQUEST_TYPE,\n"
					//  33                   34              35               36
					+ " EMERGENCY_AVAILABLE, REQUEST_ACTIVE, NON_PROLONGABLE, TEMP_EXPIRED_DATE,\n"
					//  37                      38                         39     40                41
					+ " DEFINITIVE_MAX_VERSION, AVAILABLE_REINSTALLATIONS, CHAIN, AUTO_PROLONGABLE, PROLONG_PARAMS,\n"
					//  42           43                     44             45          46         47          48
					+ " MIN_VERSION, REQUEST_LARGE_ACCOUNT, LARGE_ACCOUNT, HWM_YEARLY, HWM_FIRST, EXTRA_DATA, DRIVERS)\n"
					//         1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20
					+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
					// 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48
					+ " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n";

			stmt = conn.prepareStatement(sql);
			int fld = 1;
			stmt.setLong(fld++, license.id);                       // 1
			stmt.setString(fld++, license.getClientName());        // 2
			stmt.setString(fld++, license.getClientDisplayName()); // 3
			stmt.setString(fld++, license.getClientAddress());     // 4
			stmt.setString(fld++, license.getClientEmail());       // 5
			stmt.setString(fld++, license.getClientPhone());       // 6
			stmt.setString(fld++, license.hardCode);      // 7
			stmt.setString(fld++, "");                    // 8 (ACT_CODE)
			stmt.setShort(fld++, license.getLbConnections());  // 9
			stmt.setShort(fld++, (short) 0);                   // 10 (BO_CONNECTIONS)
			stmt.setShort(fld++, (short) 0);                   // 11 (POS_CONNECTIONS)
			stmt.setShort(fld++, license.getRemConnections()); // 12
			stmt.setShort(fld++, license.getOmConnections());  // 13
			stmt.setShort(fld++, (short) 0);                   // 14 //license.getHqConnections()
			stmt.setShort(fld++, license.getPsConnections());  // 15
			stmt.setShort(fld++, license.getHeConnections());  // 16
			stmt.setShort(fld++, (short) 48);             // 17 (ACT_CHECK_PERIOD)
			stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.getStartDate()).getTime())); // 18
			stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.getIssueDate()).getTime())); // 19
			if (license.getExpiredDate() == null) {
				stmt.setNull(fld++, Types.DATE);          // 20 (EXPIRED_DATE)
			} else {
				stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.getExpiredDate()).getTime())); // 19
			}
			stmt.setLong(fld++, license.getFuncLimited()); // 21
			stmt.setShort(fld++, license.getStatus());     // 22
			stmt.setString(fld++, licenseComment); // 23 (COMMENTS)
			stmt.setString(fld++, license.dealerComments); // 24

			stmt.setLong(fld++, targetDealer.id);    // 25
			stmt.setShort(fld++, (short) 0);         // 26 (IS_ACTIVE)
			stmt.setString(fld++, confirmUid);       // 27
			stmt.setTimestamp(fld++, new Timestamp(commonHelper.getCurrentDate().getTime())); // 28 (REQUEST_DATE)
			stmt.setNull(fld++, Types.DATE); // 29 (CONFIRM_DATE)
			if (requestType == RequestType.EMERGENCY)
				stmt.setString(fld++, "-EMR");   // 30 (REQUEST_PHASE)
			else if (noOrder)
				stmt.setString(fld++, "1DRQ");   // 30 (REQUEST_PHASE)
			else
				stmt.setString(fld++, "1REQ");   // 30 (REQUEST_PHASE)
			if (requestType == RequestType.CREATE)
				stmt.setNull(fld++, Types.BIGINT);       // 31 (ID_LICENSES_PREV)
			else
				stmt.setLong(fld++, originalLicense.id); // 31
			switch (requestType) {
			case CREATE:  stmt.setString(fld++, "CREATE");  break; // 32 (REQUEST_TYPE)
			case PROLONG: stmt.setString(fld++, "PROLONG"); break; // 32
			case UPGRADE: stmt.setString(fld++, "UPGRADE"); break; // 32
			case EMERGENCY: stmt.setString(fld++, "EMERGENCY"); break; // 32
			case CREATE_BOOST: stmt.setString(fld++, "CREATE"); break; // 32
			}
			if (license.isApostill() || license.isTrial())
				stmt.setNull(fld++, Types.SMALLINT); // 33 (EMERGENCY_AVAILABLE)
			else
				stmt.setShort(fld++, (short) 3);     // 33 (EMERGENCY_AVAILABLE)
			if (noOrder || license.requestLargeAccount) {
				stmt.setShort(fld++, (short) 1); // 34 (REQUEST_ACTIVE)
			} else {
				stmt.setShort(fld++, (short) 0); // 34 (REQUEST_ACTIVE)
			}
			stmt.setShort(fld++, (short) (license.nonProlongable ? 1 : 0)); // 35 (NON_PROLONGABLE)
			if (license.tempExpiredDate == null) {
				stmt.setNull(fld++, Types.DATE); // 36 (TEMP_EXPIRED_DATE)
			} else {
				stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.tempExpiredDate).getTime())); // 36
			}
			if (license.isDefinitive())
				stmt.setInt(fld++, license.getDefinitiveMaxVersion()); // 37 (DEFINITIVE_MAX_VERSION)
			else
				stmt.setNull(fld++, Types.INTEGER);                    // 37 (DEFINITIVE_MAX_VERSION)
			if (license.isOnline())
				stmt.setInt(fld++, 3);              // 38 (AVAILABLE_REINSTALLATIONS)
			else
				stmt.setNull(fld++, Types.INTEGER); // 38 (AVAILABLE_REINSTALLATIONS)
			stmt.setString(fld++, license.chain);   // 39
			stmt.setShort(fld++, (short) (license.autoProlongable ? 1 : 0)); // 40
			if (license.prolongParams == null) {
				stmt.setNull(fld++, Types.BLOB);    // 41 (PROLONG_PARAMS)
			} else {
				Gson gson = new Gson();
				stmt.setString(fld++, gson.toJson(license.prolongParams)); // 41
			}
			stmt.setLong(fld++, license.minVersion);                       // 42
			stmt.setShort(fld++, (short) (license.requestLargeAccount ? 1 : 0)); // 43 (REQUEST_LARGE_ACCOUNT)
			stmt.setShort(fld++, (short) (license.largeAccount ? 1 : 0));        // 44 (LARGE_ACCOUNT)
			stmt.setShort(fld++, (short) (license.hwmYearly ? 1 : 0));           // 45 (HWM_YEARLY)
			stmt.setShort(fld++, (short) (license.hwmFirst ? 1 : 0));            // 46 (HWM_FIRST)
			if (extraData == null) {
				stmt.setNull(fld++, Types.BLOB);    // 47 (EXTRA_DATA)
			} else {
				stmt.setString(fld++, extraData);   // 47
			}
			stmt.setString(fld++, DbLicense.driversToText(license.getDrivers())); // 48

			if (stmt.executeUpdate() == 0) {
				LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when create requested license in LICENSES table\n\tlicense: {}", license);
				throw new DbErrorException("Unknown mistake during save the license in DB");
			}

			stmt.close(); stmt = null;

			conn.commit();
			LOG.trace("new license inserted into DB ({})", license);

			if (requestType == RequestType.EMERGENCY) {

				// try send license by email
				File oldLicenseFile = null;
				File licenseFile = null;
				try {
					oldLicenseFile = !license.isOldLicReq() ? null
							: untillLicensesPluginHelper.generateLicenseFile(license.id, license.getOldLicVer(), false);
					licenseFile = untillLicensesPluginHelper.generateLicenseFile(license.id, license.getLicVer(), false);
					emailHelper.emailLicense(SendLicenseReason.EMERGENCY, oldLicenseFile, licenseFile, false, license,
							targetDealer);
				} catch (Exception e) {
					LOG.error("Generate/Send emergency license error", e);
					// try to delete created request
					stmt = conn.prepareStatement("DELETE FROM LICENSES\n"
							+ "WHERE ID = ?\n");
					stmt.setLong(1, license.id);
					stmt.executeUpdate();
					stmt.close(); stmt = null;
					conn.commit();
					return new RequestLicenseResult(Results.FAIL_SEND_EMERGENCY_LICENSE);
				} finally {
					// delete generated license files (if is created)
					if (oldLicenseFile != null)
						oldLicenseFile.delete();
					if (licenseFile != null)
						licenseFile.delete();
				}

				// send the report via e-mail the owner
				emailHelper.emailReportToOwner(ReportType.EMERGENCY_LICENSE_SENT_TO_DEALER, license,
						targetDealer, superDealer, null, null);

				// decrement field "EMERGENCY_AVAILABLE" in the original license
				stmt = conn.prepareStatement("UPDATE LICENSES\n"
						+ "SET EMERGENCY_AVAILABLE = ?\n"
						+ "WHERE ID = ?\n");
				stmt.setLong(1, originalLicense.emergencyAvailable - 1);
				stmt.setLong(2, originalLicense.id);
				stmt.executeUpdate();
				stmt.close(); stmt = null;
				conn.commit();

				return new RequestLicenseResult(Results.SUCCESS);

			} else if (requestType == RequestType.CREATE && license.largeAccount) { // requestType != RequestType.EMERGENCY

				// create and send request e-mail to owner
				if (createRequestEmail(conn, commonHelper.getServletUrl(), superDealer, targetDealer, license.id, false)) {
					return new RequestLicenseResult(Results.SUCCESS);
				} else {
					return new RequestLicenseResult(Results.SUCCESS_BUT_FAIL_SEND_REQUEST_MESSAGE); // TODO: !: return another message
				}

			} else {

				if (!noOrder) {
					// order license by LICENSES unTill plug-in
					boolean orderLicenseResult = false;
					try {
						orderLicenseResult = orderLicense(conn, requestType, license, originalLicense, license.id);
					} finally {
						if (!orderLicenseResult) {
							// try to delete created request
							stmt = conn.prepareStatement("DELETE FROM LICENSES\n"
									+ "WHERE ID = ?\n");
							stmt.setLong(1, license.id);
							stmt.executeUpdate();
							stmt.close(); stmt = null;
							conn.commit();
							return new RequestLicenseResult("Order license error"); // TODO message
						}
					}
				}

				// create and send request e-mail (and send report e-mail to owner)
				if (automaticNoEmails || createRequestEmail(conn, commonHelper.getServletUrl(), superDealer, targetDealer, license.id, true)) {
					return new RequestLicenseResult(Results.SUCCESS);
				} else {
					return new RequestLicenseResult(Results.SUCCESS_BUT_FAIL_SEND_REQUEST_MESSAGE);
				}

			} // if (requestType == RequestType.EMERGENCY)

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public AddApostillHddCodesResult addApostillHddCodes(Connection conn, Dealer dealer, long apostillDealerId, String hddCodes)
			throws LicenseServiceException, SQLException
	{
		// Check parameters
		if (hddCodes == null)
			throw new InvalidArgumentException("parameter 'hddCodes' is null");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Check apostillDealerId
			try {
				Dealer apostillDealer = new Dealer(conn, apostillDealerId);
				if (!apostillDealer.apostillDealer)
					throw new InvalidArgumentException("parameter 'apostillDealerId' incorrect (dealer not associated with Apostill)");
			} catch (RequiredDealerIsNotFoundException e) {
				throw new InvalidArgumentException("parameter 'apostillDealerId' incorrect (dealer is not found)");
			} catch (SQLException e) {
				LOG.error("get apostill dealer error", e);
				throw new DbErrorException();
			}

			for (String hddCode : hddCodes.split("\n")) {
				hddCode = hddCode.trim();

				// Skip empty lines
				if (hddCode.isEmpty())
					continue;

				// Check hddCode Length
				if (hddCode.length() > 100) {
					LOG.warn("HDD code is very long (hddCode: {})", hddCode);
					return new AddApostillHddCodesResult("HDD code is very long (" + hddCode + ")");
				}

				// Check hddCode for duplicate
				stmt = conn.prepareStatement("SELECT ID FROM APOSTILL_HDDCODES\n"
						+ "WHERE HDD_CODE = ?\n");
				stmt.setString(1, hddCode);
				rs = stmt.executeQuery();
				if (rs.next()) {
					LOG.warn("HDD code is already exists (hddCode: {})", hddCode);
					return new AddApostillHddCodesResult("HDD code is already exists (" + hddCode + ")");
				}
				rs.close(); rs = null; stmt.close(); stmt = null;

				// Get license id for new license
				stmt = conn.prepareStatement("SELECT * FROM GENERATE_TABLE_ID\n");
				rs = stmt.executeQuery();
				rs.next();
				Long apostillHddCodesId = rs.getLong(1);
				rs.close(); rs = null; stmt.close(); stmt = null;

				// Create requested license in LICENSES table
				stmt = conn.prepareStatement("INSERT INTO APOSTILL_HDDCODES (ID, ID_DEALERS, HDD_CODE)\n"
						+ "VALUES (?, ?, ?)\n");
				int fld = 1;
				stmt.setLong(fld++, apostillHddCodesId);
				stmt.setLong(fld++, apostillDealerId);
				stmt.setString(fld++, hddCode);

				if (stmt.executeUpdate() == 0) {
					LOG.error("The method executeUpdate of PreparedStatement class has returned a zero (hddCode: {})", hddCode);
					throw new DbErrorException("Unknown mistake during add HDD code to DB");
				}

				stmt.close(); stmt = null;
			}

			conn.commit();

			return new AddApostillHddCodesResult();

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	// XXX This method is not currently used, but it is in working condition
	public boolean voidRequest(Connection conn, Dealer dealer, long licenseId) throws LicenseServiceException, SQLException {
		// Check authentication
		Dealer superDealer = null;
		Dealer targetDealer = dealer;

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Check license id and get license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found by id (licenseId: {})", licenseId);
				return false;
			}
			long licenseDealerId = rs.getLong("ID_DEALERS");
			if (licenseDealerId != targetDealer.id && !targetDealer.superDealer) {
				LOG.warn("This license/request does not belong to this dealer (licenseId: {}, targetDealer.id: {})", licenseId, targetDealer.id);
				return false;
			}
			// Update target dealer and super dealer
			if (licenseDealerId != 0 && licenseDealerId != targetDealer.id) {
				superDealer = targetDealer;
				try {
					targetDealer = new Dealer(conn, licenseDealerId);
				} catch (RequiredDealerIsNotFoundException e) {
					LOG.warn("Dealer of the license is not found", e);
					throw new ServerErrorException("Dealer of the license is not found");
				}
			}
			// Check license state
			if (rs.getLong("IS_ACTIVE") != 0) {
				LOG.warn("This is a active license, not request for license (licenseId: {})", licenseId);
				return false;
			}
			if (rs.getLong("REQUEST_ACTIVE") == 0) {
				LOG.warn("This is not request for license (licenseId: {})", licenseId);
				return false;
			}

			// Get license data
			DbLicense license = Common.getDbLicenseFromResultSet(rs);
			int tableNo = rs.getInt("REQUEST_TABLENO");

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Attempt to delete license request
			stmt = conn.prepareStatement("DELETE FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			stmt.executeUpdate();

			// Void order
			if (untillLicensesPluginHelper.voidOrder(tableNo)) {
				conn.commit();
				emailHelper.emailReportToOwner(ReportType.DEALER_CANCELED_REQUEST, license,
						targetDealer, superDealer, null, null);
				return true;
			} else {
				conn.rollback();
				return false;
			}

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public boolean resendRequest(Connection conn, Dealer dealer, long licenseId) throws SQLException {
		// Check authentication
		Dealer superDealer = null;
		Dealer targetDealer = dealer;

		return createRequestEmail(conn, commonHelper.getServletUrl(), superDealer, targetDealer, licenseId, false);
	}

	public boolean resendLicense(Connection conn, Dealer dealer, long licenseId, Date tempLicenseEndDate)
			throws LicenseServiceException, SQLException
	{
		// Check authentication
		Dealer authorizedDealer = dealer;
		//Dealer superDealer = null;
		Dealer targetDealer = authorizedDealer;

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Check license id and get license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found by id (licenseId: {})", licenseId);
				return false;
			}
			long licenseDealerId = rs.getLong("ID_DEALERS");
			if (licenseDealerId != targetDealer.id && !targetDealer.superDealer) {
				LOG.warn("This license does not belong to this dealer (licenseId: {}, authorizedDealer.id: {})", licenseId, authorizedDealer.id);
				return false;
			}
			// Update target dealer and super dealer
			if (licenseDealerId != 0 && licenseDealerId != targetDealer.id) {
				//superDealer = authorizedDealer;
				try {
					targetDealer = new Dealer(conn, licenseDealerId);
				} catch (RequiredDealerIsNotFoundException e) {
					LOG.warn("Dealer of the license is not found", e);
					throw new ServerErrorException("Dealer of the license is not found");
				}
			}
			// Check license state
			if (rs.getLong("IS_ACTIVE") == 0) {
				LOG.warn("This license is not active (licenseId: {})", licenseId);
				return false;
			}

			// Get license data
			DbLicense license = Common.getDbLicenseFromResultSet(rs);

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Check license status
			if (license.isCanceled()) {
				LOG.warn("Attempt to receive the cancelled license\n\tlicense: {}", license);
				return false;
			}

			// Check license expired date
			Date nowDate = Common.toUtcDateOnly(commonHelper.getCurrentDate());
			if (tempLicenseEndDate != null) {
				if (!license.isNeverExpired()) {
					if (nowDate.compareTo(license.getExpiredDate()) > 0) {
						LOG.warn("Attempt to receive the temp license for expired/ended license\n\tlicense: {}", license);
						return false;
					}
					if (tempLicenseEndDate.compareTo(license.getExpiredDate()) > 0) {
						LOG.warn("Attempt to request a temporary license to a later expired/ended date\n\tlicense: {}\n\ttempLicenseEndDate: {}", license, tempLicenseEndDate);
						return false;
					}
				}
				if (tempLicenseEndDate.compareTo(license.getStartDate()) < 0) {
					LOG.warn("Attempt to request a temporary license with the expire date before the start/ended date\n\tlicense: {}\n\ttempLicenseEndDate: {}", license, tempLicenseEndDate);
					return false;
				}
			} else {
				if (!license.isNeverExpired()) {
					if (nowDate.compareTo(license.getExpiredDate()) > 0) {
						LOG.warn("Attempt to receive the expired license\n\tlicense: {}", license);
						return false;
					}
				}
			}

			// update license
			stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET TEMP_EXPIRED_DATE = ?\n"
					+ "WHERE ID = ?\n");
			if (tempLicenseEndDate == null) {
				stmt.setNull(1, Types.DATE);
			} else {
				stmt.setDate(1, new java.sql.Date(Common.toLocalDateOnly(tempLicenseEndDate).getTime()));
			}
			stmt.setLong(2, license.id);
			if (stmt.executeUpdate() == 0) {
				LOG.warn("The method executeUpdate of PreparedStatement class has returned a zero (license.id: {})", license.id);
				throw new DbErrorException("Unknown mistake during update the license in DB");
			}
			conn.commit();
			license.tempExpiredDate = tempLicenseEndDate;

			File oldLicenseFile = null;
			File licenseFile = null;
			try {
				if (!license.isOnline()) {
					oldLicenseFile = !license.isOldLicReq() ? null
							: untillLicensesPluginHelper.generateLicenseFile(license.id, license.getOldLicVer(),
									tempLicenseEndDate != null);
					licenseFile = untillLicensesPluginHelper.generateLicenseFile(license.id, license.getLicVer(),
							tempLicenseEndDate != null);
				}
				emailHelper.emailLicense(SendLicenseReason.RESEND, oldLicenseFile, licenseFile,
						tempLicenseEndDate != null, license, targetDealer);
			} catch (Exception e) {
				LOG.error("Regenerate/Resend license error", e);
				return false;
			} finally {
				// delete generated license files (if is created)
				if (oldLicenseFile != null)
					oldLicenseFile.delete();
				if (licenseFile != null)
					licenseFile.delete();
			}

			emailHelper.emailReportToOwner(ReportType.LICENSE_RE_SENT_TO_DEALER, license,
					targetDealer, null, null, null);
			return true;

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	  /////////////////////
	 // Service methods //
	/////////////////////

	public void serviceApostillRequest(Connection conn, String apostillHddCode,
			String apostillDealer, String client, String city, String activate, String version,
			HttpServletResponse resp)
			throws IOException, SQLException
	{
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {}, {})", apostillHddCode, apostillDealer, client, city, activate, version, resp);

		try {

			// Check parameters
			if (activate != null) {
				activate = activate.trim().toLowerCase();
				if (!activate.equals("trial") && !activate.equals("normal")) {
					LOG.warn("Illegal activate method ({})", activate);
					serviceApostillRequest_sendError(resp, false, "Illegal activate method");
					return;
				}
			}

			int versionInt = 0;
			if (version != null) {
				try {
					versionInt = Integer.parseInt(version);
				} catch (Exception e) {
					LOG.warn("Wrong format version ({})", version);
					serviceApostillRequest_sendError(resp, false, "Wrong format version");
				}
				if (versionInt < 0) {
					LOG.warn("Wrong version number ({})", version);
					serviceApostillRequest_sendError(resp, false, "Wrong version number");
				}
			}

			String licenseClientName = null;
			if (apostillDealer == null) {
				if (client == null) {
					licenseClientName = null;
				} else {
					licenseClientName = "(" + client + ")";
				}
			} else {
				if (client == null) {
					licenseClientName = apostillDealer;
				} else {
					licenseClientName = apostillDealer + " (" + client + ")";
				}
			}

			byte[] licenseContent;

			// Begin transaction
			PreparedStatement stmt = null;
			ResultSet rs = null;
			try {

				// Find apostillHddCode in DB (APOSTILL_HDDCODES)
				stmt = conn.prepareStatement("SELECT *\n"
						+ "FROM APOSTILL_HDDCODES\n"
						+ "WHERE HDD_CODE = ?\n");
				stmt.setString(1, apostillHddCode);
				rs = stmt.executeQuery();
				if (!rs.next()) {
					LOG.warn("This HDD code (\"{}\") is not registered in DB", apostillHddCode);
					serviceApostillRequest_sendError(resp, false, "This HDD code is not registered in DB");
					return;
				}
				Long apostillHddCodesId = rs.getLong("ID");
				Long dealerId = rs.getLong("ID_DEALERS");
				String hardCode = rs.getString("HARD_CODE");
				Date trialActDate = rs.getDate("TRIAL_ACT_DATE");
				Date normalActDate = rs.getDate("NORMAL_ACT_DATE");

				rs.close(); rs = null; stmt.close(); stmt = null;

				long licenseId;

				if (activate == null || normalActDate != null || activate.equals("trial") && trialActDate != null) {
					/// Request update license

					if (hardCode == null) {
						LOG.warn("No activated license (apostillHddCode: {})", apostillHddCode);
						serviceApostillRequest_sendError(resp, false, "No activated license");
						return;
					}

					// Find active license in DB (LICENSES) by HARD_CODE
					stmt = conn.prepareStatement("SELECT ID, STATUS\n"
							+ "FROM LICENSES\n"
							+ "WHERE COALESCE(IS_ACTIVE, 0) <> 0 AND HARD_CODE = ?\n");
					stmt.setString(1, hardCode);
					rs = stmt.executeQuery();
					if (!rs.next()) {
						LOG.warn("Active license is not found (hardCode: {})", hardCode);
						serviceApostillRequest_sendError(resp, false, "Active license is not found");
						return;
					}
					licenseId = rs.getLong("ID");
					if ((rs.getShort("STATUS") & DbLicense.DESCRIPTION_CANCELED_BIT) != 0) {
						LOG.warn("Active license is canceled (hardCode: {})", hardCode);
						serviceApostillRequest_sendError(resp, false, "Active license is canceled");
						return;
					}

					// TODO !? check for expire license

					rs.close(); rs = null; stmt.close(); stmt = null;

					if (licenseClientName != null || city != null) {
						// (BEGIN TRANSACTION)
						stmt = conn.prepareStatement("UPDATE LICENSES\n"
								+ "SET CLIENT_NAME = ?,\n" // 1
								+ "    CLIENT_ADDRESS = ?,\n" // 2
								+ "    MIN_VERSION = ?\n" // 3
								+ "WHERE ID = ?"); // 4
						stmt.setString(1, licenseClientName != null ? licenseClientName : apostillHddCode);
						stmt.setString(2, city != null ? city : "");
						stmt.setInt(3, versionInt);
						stmt.setLong(4, licenseId);
						if (stmt.executeUpdate() == 0) {
							LOG.error("Unknown mistake during update LICENSES in DB (licenseId: {})", licenseId);
							serviceApostillRequest_sendError(resp, true, "Unknown mistake during update LICENSES in DB");
							return;
						}
						stmt.close(); stmt = null;
						// (COMMIT TRANSACTION)
						conn.commit();
					}

				} else {
					/// Request activate license

					// Generate HARD_CODE id DB
					if (!untillLicensesPluginHelper.apostillGenerateHardCode(apostillHddCodesId)) {
						LOG.error("Error generate HARD_CODE");
						serviceApostillRequest_sendError(resp, true, "Error generate HARD_CODE");
						return;
					}

					// Get HARD_CODE
					// Find apostillHddCode in DB (APOSTILL_HDDCODES)
					stmt = conn.prepareStatement("SELECT HARD_CODE\n"
							+ "FROM APOSTILL_HDDCODES\n"
							+ "WHERE ID = ?\n");
					stmt.setLong(1, apostillHddCodesId);
					rs = stmt.executeQuery();
					if (!rs.next()) {
						LOG.error("Unknown mistake (APOSTILL_HDDCODE not found) (apostillHddCodesId: {})", apostillHddCodesId);
						serviceApostillRequest_sendError(resp, true, "Unknown mistake (APOSTILL_HDDCODE not found)");
						return;
					}
					hardCode = rs.getString("HARD_CODE");

					// Generate license (trial/normal)
					DbLicense license = generateApostillLicense(activate.equals("trial"), hardCode,
							(licenseClientName != null) ? licenseClientName : apostillHddCode, city);

					// get dealer
					Dealer dealer = new Dealer(conn, dealerId);

					/// XXX based on part of requestLicense method

					// Get license id for new license
					stmt = conn.prepareStatement("SELECT * FROM GENERATE_TABLE_ID\n");
					rs = stmt.executeQuery();
					rs.next();
					licenseId = rs.getLong(1);
					license.id = licenseId;
					rs.close(); rs = null; stmt.close(); stmt = null;

					// Create inactive license in LICENSES table
					//                                  1   2            3               4
					String sql = "INSERT INTO LICENSES (ID, CLIENT_NAME, CLIENT_ADDRESS, CLIENT_EMAIL,\n"
						//  5             6          7         8               9
						+ " CLIENT_PHONE, HARD_CODE, ACT_CODE, LB_CONNECTIONS, BO_CONNECTIONS,\n"
						//  10               11               12              13              14              15
						+ " POS_CONNECTIONS, REM_CONNECTIONS, OM_CONNECTIONS, HQ_CONNECTIONS, PS_CONNECTIONS, HE_CONNECTIONS,\n"
						//  16                17          18          19            20            21
						+ " ACT_CHECK_PERIOD, START_DATE, ISSUE_DATE, EXPIRED_DATE, FUNC_LIMITED, STATUS,\n"
						//  22        23               24          25         26           27
						+ " COMMENTS, DEALER_COMMENTS, ID_DEALERS, IS_ACTIVE, CONFIRM_UID, REQUEST_DATE,\n"
						//  28            29             30                31
						+ " CONFIRM_DATE, REQUEST_PHASE, ID_LICENSES_PREV, REQUEST_TYPE,\n"
						//  32                   33              34               35                 36
						+ " EMERGENCY_AVAILABLE, REQUEST_ACTIVE, NON_PROLONGABLE, TEMP_EXPIRED_DATE, CHAIN,\n"
						//  37
						+ " MIN_VERSION)\n"
						//         1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19 20
						+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,\n"
						// 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37
						+ " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n";

					stmt = conn.prepareStatement(sql);
					int fld = 1;
					stmt.setLong(fld++, licenseId);               // 1
					stmt.setString(fld++, license.getClientName());    // 2
					stmt.setString(fld++, license.getClientAddress()); // 3
					stmt.setString(fld++, license.getClientEmail());   // 4
					stmt.setString(fld++, license.getClientPhone());   // 5
					stmt.setString(fld++, license.hardCode);      // 6
					stmt.setString(fld++, "");                    // 7 (ACT_CODE)
					stmt.setShort(fld++, license.getLbConnections());  // 8
					stmt.setShort(fld++, (short) 0);                   // 9 (BO_CONNECTIONS)
					stmt.setShort(fld++, (short) 0);                   // 10 (POS_CONNECTIONS)
					stmt.setShort(fld++, license.getRemConnections()); // 11
					stmt.setShort(fld++, license.getOmConnections());  // 12
					stmt.setShort(fld++, (short) 0);                   // 13 //license.getHqConnections()
					stmt.setShort(fld++, license.getPsConnections());  // 14
					stmt.setShort(fld++, license.getHeConnections());  // 15
					stmt.setShort(fld++, (short) 48);             // 16 (ACT_CHECK_PERIOD)
					stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.getStartDate()).getTime())); // 17
					stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.getIssueDate()).getTime())); // 18
					if (license.getExpiredDate() == null) {
						stmt.setNull(fld++, Types.DATE);           // 19 (EXPIRED_DATE)
					} else {
						stmt.setDate(fld++, new java.sql.Date(Common.toLocalDateOnly(license.getExpiredDate()).getTime())); // 19
					}
					stmt.setLong(fld++, license.getFuncLimited()); // 20
					stmt.setShort(fld++, license.getStatus());     // 21
					stmt.setString(fld++, "Created by unTill License Server"); // 22 (COMMENTS)
					stmt.setString(fld++, "");                                 // 23 (DEALER_COMMENTS)

					stmt.setLong(fld++, dealer.id);      // 24
					stmt.setShort(fld++, (short) 0);     // 25 (IS_ACTIVE)
					stmt.setString(fld++, "");           // 26 (CONFIRM_UID)
					stmt.setTimestamp(fld++, new Timestamp(commonHelper.getCurrentDate().getTime())); // 27 (REQUEST_DATE)
					stmt.setNull(fld++, Types.DATE);     // 28 (CONFIRM_DATE)
					stmt.setString(fld++, "1REQ");       // 29
					stmt.setNull(fld++, Types.BIGINT);   // 30 (ID_LICENSES_PREV)
					stmt.setString(fld++, "CREATE");     // 31 (REQUEST_TYPE)
					stmt.setNull(fld++, Types.SMALLINT); // 32 (EMERGENCY_AVAILABLE)
					stmt.setShort(fld++, (short) 0); // 33 (REQUEST_ACTIVE)
					stmt.setShort(fld++, (short) 0); // 34 (NON_PROLONGABLE)
					stmt.setNull(fld++, Types.DATE); // 35 (TEMP_EXPIRED_DATE)
					stmt.setString(fld++, license.chain); // 36
					stmt.setInt(fld++, versionInt); // 37

					if (stmt.executeUpdate() == 0) {
						LOG.error("Unknown mistake during save the license to DB (apostillHddCodesId: {})", apostillHddCodesId);
						serviceApostillRequest_sendError(resp, true, "Unknown mistake during save the license to DB");
						conn.rollback();
						return;
					}

					stmt.close(); stmt = null;

					conn.commit();

					// Attempt to order, payment (if not trial) and activate license.
					// If an error is made remove created license
					boolean licenseIsSuccessfullyActivated = false;
					try {

						if (!activate.equals("trial")) {
							// Order license by LICENSES unTill plug-in
							// TODO use untillTpapiHelper or orderLicense()
							if (!orderLicense(conn, RequestType.CREATE, license, null, licenseId)) {
								LOG.error("Order license error");
								serviceApostillRequest_sendError(resp, true, "Order license error");
								return;
							}
						}

						// (BEGIN TRANSACTION)
						// update apostillHddCode record and set license as active in DB
						stmt = conn.prepareStatement("UPDATE APOSTILL_HDDCODES\n"
								+ "SET " + (activate.equals("trial") ? "TRIAL_ACT_DATE" : "NORMAL_ACT_DATE" ) + " = ?,\n" // 1
								+ "  HARD_CODE = ?\n" // 2
								+ "WHERE ID = ?"); // 3
						stmt.setDate(1, new java.sql.Date(commonHelper.getCurrentDate().getTime()));
						stmt.setString(2, hardCode);
						stmt.setLong(3, apostillHddCodesId);
						if (stmt.executeUpdate() == 0) {
							LOG.error("Unknown mistake during update APOSTILL_HDDCODES in DB (apostillHddCodesId: {})", apostillHddCodesId);
							serviceApostillRequest_sendError(resp, true, "Unknown mistake during update APOSTILL_HDDCODES in DB");
							return;
						}
						stmt.close(); stmt = null;
						// deactivate previous license if exist
						stmt = conn.prepareStatement("UPDATE LICENSES\n"
								+ "SET IS_ACTIVE = 0,\n"
								+ "  REQUEST_PHASE = ?\n"    // 1
								+ "WHERE IS_ACTIVE = 1 AND HARD_CODE = ?\n"); // 2
						stmt.setString(1, "0UPG");
						stmt.setString(2, hardCode);
						stmt.executeUpdate();
						stmt.close(); stmt = null;
						// activate created license
						stmt = conn.prepareStatement("UPDATE LICENSES\n"
								+ "SET IS_ACTIVE = 1,\n"
								+ "  CONFIRM_DATE = ?,\n"  // 1
								+ "  REQUEST_PHASE = ?,\n" // 2
								+ "  REQUEST_ACTIVE = 0,\n"
								+ "  NON_APPROVED = ?\n"   // 3
								+ "WHERE ID = ?\n");       // 4
						stmt.setDate(1, new java.sql.Date(commonHelper.getCurrentDate().getTime()));
						if (activate.equals("trial")) {
							stmt.setString(2, "2TCN");
							stmt.setShort(3, (short) 0);
						} else {
							stmt.setString(2, "3PAY");
							stmt.setShort(3, (short) 1);
						}
						stmt.setLong(4, licenseId);
						if (stmt.executeUpdate() == 0) {
							LOG.error("Unknown mistake during update LICENSES in DB (licenseId: {})", licenseId);
							serviceApostillRequest_sendError(resp, true, "Unknown mistake during update LICENSES in DB");
							return;
						}
						stmt.close(); stmt = null;

						// (COMMIT TRANSACTION)
						conn.commit();
						licenseIsSuccessfullyActivated = true;

					} finally {
						if (!licenseIsSuccessfullyActivated) {
							// (ROLLBACK TRANSACTION)
							conn.rollback();
							// Try to delete created request
							stmt = conn.prepareStatement("DELETE FROM LICENSES\n"
									+ "WHERE ID = ?\n");
							stmt.setLong(1, licenseId);
							stmt.executeUpdate();
							conn.commit();
						}
					}
				} // if (activate == null || normalActDate != null || activate.equals("trial") && trialActDate != null) else

				// Generate license file
				File licenseFile;
				try {
					licenseFile = untillLicensesPluginHelper.generateLicenseFile(licenseId,
							version == null ? 0 : DbLicense.verToLicVer(versionInt), false);
				} catch (Exception e) {
					LOG.error("Error generate license file", e);
					serviceApostillRequest_sendError(resp, true, "Error generate license file");
					return;
				}

				// Prepare response
				licenseContent = new byte[(int) licenseFile.length()];
				FileInputStream fis = new FileInputStream(licenseFile);
				try {
					fis.read(licenseContent);
				} finally {
					fis.close();
				}

				// Send license
				resp.setContentType("application/octet-stream");
				resp.setContentLength(licenseContent.length);
				ServletOutputStream os = resp.getOutputStream();
				os.write(licenseContent);
				os.flush();

				// Delete temporary license file
				licenseFile.delete();

				return;

			} catch (RequiredDealerIsNotFoundException e) {
				LOG.error("RequiredDealerIsNotFoundException", e);
				serviceApostillRequest_sendError(resp, true, "RequiredDealerIsNotFoundException");
				return;
			} finally {
				if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
				if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
			}

		} finally {
			LOG.debug("RETURN");
		}
	}

	// Auxiliary function of serviceApostillRequest method
	private void serviceApostillRequest_sendError(HttpServletResponse resp, boolean error, String message) {
		resp.setStatus(error ? HttpServletResponse.SC_INTERNAL_SERVER_ERROR : HttpServletResponse.SC_BAD_REQUEST);
		resp.addHeader("Error-Message", message);
	}

	private DbLicense generateApostillLicense(boolean trial, String hardCode, String licenseClientName,
			String city) {
		LicensePeriod licensePeriod =
			getNewLicensePeriod(trial ? LicenseType.APOSTILL_TRIAL : LicenseType.APOSTILL);

		DbLicense license = new DbLicense();
		license.id = 0; // ignored
		license.setClientName(licenseClientName);
		license.setClientAddress((city != null) ? city : "");
		license.setClientEmail("");
		license.setClientPhone("");
		license.hardCode = hardCode;
		license.setLbConnections((short) 1);
		license.setRemConnections((short) 0);
		if (trial)
			license.setOmConnections((short) 3);
		else
			license.setOmConnections((short) 0);
//		license.setHwConnections((short) 0);
		license.setPsConnections((short) 0);
		license.setHeConnections((short) 0);
		license.setStartDate(licensePeriod.getStartDate());
		license.setIssueDate(Common.toUtcDateOnly(commonHelper.getCurrentDate()));
		license.setExpiredDate(licensePeriod.getExpiredDate());

		// Set functions
		license.setFuncLimited(license.getFuncLimited() | (1 << DbLicense.FUNC_EFT_INTERFACE));

		license.setDrivers(null);

		license.setStatusActive(true);
		license.setApostill(true);
		license.setTrial(trial);

		license.dealerComments = "";

		return license;
	}

	  ////////////////////
	 // Public classes //
	////////////////////

	protected static class ConfirmOrderResult {
		public enum Result { SUCCESS, SUCCESS_BUT_FAIL_SEND_MESSAGE, FAIL, ERROR }
		Result result;
		public String email; // fill, if result = SUCCESS
		public String errorMessage; // fill, if result = FAIL
		public ConfirmOrderResult(Result result, String email, String errorMessage) {
			this.result = result; this.email = email; this.errorMessage = errorMessage;
		}
	}

	  ////////////////////
	 // Public methods //
	////////////////////

	/**
	 * Approve or deny Large Account request
	 * @param conn
	 * @param laApprUid
	 * @param deny
	 * @return
	 * @throws LicenseServiceException
	 * @throws SQLException
	 */
	public boolean approveLAByLink(Connection conn, String laApprUid, boolean deny)
			throws LicenseServiceException, SQLException {
		// Find license
		DbLicense license;
		try (PreparedStatement stmt = conn.prepareStatement("SELECT *\n"
				+ "FROM LICENSES\n"
				+ "WHERE CONFIRM_UID = ?\n" // TODO: AND REQUEST_LARGE_ACCOUNT = 1
		)) {
			stmt.setString(1, laApprUid);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					LOG.warn("Attempt to approve/deny Large Account wrong la_appr_uid (laApprUid: {})", laApprUid);
					throw new InvalidArgumentException("License with the specified 'la_appr_uid' does not exist");
				}
				if (rs.getShort("IS_ACTIVE") != 0) {
					LOG.warn("Attempt to approve/deny Large Account for already existing license (laApprUid: {})", laApprUid);
					return false;
				}
				if (rs.getShort("REQUEST_ACTIVE") == 0) {
					LOG.warn("Attempt to approve/deny Large Account for no longer existing license (laApprUid: {})", laApprUid);
					return false;
				}
				if (rs.getShort("REQUEST_LARGE_ACCOUNT") == 0) {
					LOG.warn("Attempt to approve/deny Large Account again (laApprUid: {})", laApprUid);
					return false;
				}

				license = Common.getDbLicenseFromResultSet(rs);
			}
		}

		// Get license dealer
		Dealer dealer;
		try {
			dealer = new Dealer(conn, license.idDealers);
		} catch (RequiredDealerIsNotFoundException e) {
			LOG.error("RequiredDealerIsNotFoundException", e);
			throw new ServerErrorException("RequiredDealerIsNotFoundException");
		}

		boolean noOrder = license.requestType.equals("PROLONG") && license.isTrial()
				|| license.requestType.equals("PROLONG") && license.isDefinitive()
				|| dealer.demoDealer;

		license.requestLargeAccount = false;
		license.largeAccount = !deny;
		try (PreparedStatement stmt = conn.prepareStatement(
				"UPDATE LICENSES\n" +
				"SET REQUEST_LARGE_ACCOUNT = 0,\n" +
				"  LARGE_ACCOUNT = ?\n" +
				"WHERE ID = ?\n" // TODO: AND REQUEST_LARGE_ACCOUNT = 1
		)) {
			stmt.setShort(1, (short) (license.largeAccount ? 1 : 0));
			stmt.setLong(2, license.id);
			if (stmt.executeUpdate() == 0) throw new RuntimeException();
		}
		conn.commit();

		if (!noOrder) {
			// order license by LICENSES unTill plug-in
			boolean orderLicenseResult = false;
			try {
				orderLicenseResult = orderLicense(conn, RequestType.CREATE, license, null, license.id);
			} finally {
				if (!orderLicenseResult) {
					// try to delete created request
					try (PreparedStatement stmt = conn.prepareStatement(
							"DELETE FROM LICENSES\n"
							+ "WHERE ID = ?\n"
					)) {
						stmt.setLong(1, license.id);
						if (stmt.executeUpdate() == 0) throw new RuntimeException();
					}
					conn.commit();
					throw new ServerErrorException("Order license error");
				}
			}
		}

		// create and send request e-mail (and send report e-mail to owner)
		createRequestEmail(conn, commonHelper.getServletUrl(), null, dealer, license.id, true);

		return true;
	}

	/**
	 * Confirm or cancel license request
	 * @param conn
	 * @param confirmUid
	 * @param cancel
	 * @param comment
	 * @return ConfirmOrderResult (error confirmation; confirmed, but not send by e-mail; success and e-mail address)
	 * @throws SQLException
	 */
	// TODO Split this method to: confirmLicense and rejectLicense (cancelLicense)
	public ConfirmOrderResult confirmOrder(Connection conn, String confirmUid, boolean cancel,
			String comment, boolean noEmails) throws SQLException {
		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Find license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE CONFIRM_UID = ?\n");
			stmt.setString(1, confirmUid);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("Attempt to confirm/cancel license with wrong confirm_uid (confirmUid: {})", confirmUid);
				return new ConfirmOrderResult(ConfirmOrderResult.Result.FAIL, "",
						"License request with the specified 'confirm_uid' does not exist");
			}
			if (rs.getShort("IS_ACTIVE") != 0) {
				LOG.warn("Attempt to confirm/cancel already confirmed license (confirmUid: {})", confirmUid);
				return new ConfirmOrderResult(ConfirmOrderResult.Result.FAIL, "",
						"This request has already been confirmed");
			}
			if (rs.getShort("REQUEST_ACTIVE") == 0) {
				LOG.warn("Attempt to confirm/cancel no longer existing license (confirmUid: {})", confirmUid);
				return new ConfirmOrderResult(ConfirmOrderResult.Result.FAIL, "",
						"This request has already been canceled or license has been deleted");
			}

			DbLicense license = Common.getDbLicenseFromResultSet(rs);
			long prevLicenseId = rs.getLong("ID_LICENSES_PREV");
			int tableNo = rs.getInt("REQUEST_TABLENO");
			String orderText = rs.getString("REQUEST_ORDER");
			String dealerComments = rs.getString("DEALER_COMMENTS");

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Get license dealer
			Dealer dealer = new Dealer(conn, license.idDealers);

			boolean noOrder = license.requestType.equals("PROLONG") && license.isTrial()
					|| license.requestType.equals("PROLONG") && license.isDefinitive()
					|| dealer.demoDealer;

			if (cancel) {

				conn.setAutoCommit(false); // begin transaction

				// Attempt to delete (deactivate) license request
				stmt = conn.prepareStatement("UPDATE LICENSES\n"
						+ "SET DEALER_COMMENTS = ?,\n"         // 1
						+ "  REQUEST_PHASE = ?,\n"      // 2
						+ "  REQUEST_ACTIVE = 0,\n"
						+ "  IS_ACTIVE = 0,\n"
						+ "  IS_ACTIVE_MODIFIED = ?,\n" // 3
						+ "  IS_ACTIVE_MODIFIER = ?\n"  // 4
						+ "WHERE ID = ?\n");            // 5
				stmt.setString(1, (dealerComments == null || dealerComments.isEmpty() ? "" :
					dealerComments + "\r\n") + "Rejection: " + comment + "\r\n");
				stmt.setString(2, "0CNC");
				stmt.setTimestamp(3, new Timestamp(commonHelper.getCurrentDate().getTime()));
				stmt.setString(4, "UntillLicenseServer");
				stmt.setLong(5, license.id);
				if (stmt.executeUpdate() == 0) {
					return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
					"DB error");
				}
				if (noOrder) {
					// no required void order
					conn.commit(); // end transaction 1/2
					// TODO check emailReportToOwner result and rollback if false
					if (!noEmails)
						emailHelper.emailReportToOwner(ReportType.DEALER_CANCELED_REQUEST, license,
								dealer, null, null, comment);
					return new ConfirmOrderResult(ConfirmOrderResult.Result.SUCCESS, "", "");
				} else {
					// void order
					if (untillLicensesPluginHelper.voidOrder(tableNo)) {
						conn.commit(); // end transaction 2/2
						// TODO check emailReportToOwner result and rollback if false
						if (!noEmails)
							emailHelper.emailReportToOwner(ReportType.DEALER_CANCELED_REQUEST, license,
									dealer, null, null, comment);
						return new ConfirmOrderResult(ConfirmOrderResult.Result.SUCCESS, "", "");
					} else {
						conn.rollback();
						return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
								"Void order error");
					}
				}

			} else { // confirm

				// check license request type
				if (!license.requestType.equals("CREATE") && !license.requestType.equals("PROLONG")
						&& !license.requestType.equals("UPGRADE")) {
					LOG.warn("License requset type is not valid (license.requestType: {})", license.requestType);
					return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
							"License requset type is not valid");
				}

				// Check prev license
				short prevLicenseStatus = 0;
				if (!license.requestType.equals("CREATE")) {

					stmt = conn.prepareStatement("SELECT *\n"
							+ "FROM LICENSES\n"
							+ "WHERE ID = ?\n");
					stmt.setLong(1, prevLicenseId);
					rs = stmt.executeQuery();
					if (!rs.next()) {
						LOG.warn("Prev. license is not found (prevLicenseId: {})", prevLicenseId);
						return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
								"Prev. license is not found");
					}
					if (rs.getShort("IS_ACTIVE") == 0) {
						LOG.warn("Prev. license is not active (prevLicenseId: {})", prevLicenseId);
						return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
								"Prev. license is not active");
					}
					prevLicenseStatus = rs.getShort("STATUS");

					rs.close(); rs = null; stmt.close(); stmt = null;
				}

				if (!license.isBoost()) {
					// Check if record with the same hard code is already exists
					if (isLicenseDuplicateHardCode(conn, license,
							!license.requestType.equals("CREATE") ? new Long(prevLicenseId) : null))
						return new ConfirmOrderResult(ConfirmOrderResult.Result.FAIL, "",
								"License for this hard code already exists");

					// Check if license with same client name, address, email and phone already exists
					if (isLicenseDuplicateClient(conn, license,
							!license.requestType.equals("CREATE") ? new Long(prevLicenseId) : null))
						return new ConfirmOrderResult(ConfirmOrderResult.Result.FAIL, "",
								"License for this client already exists");
				}

				if (license.getFunction(License.FUNC_FISCALIZATION)) {
					try {
						fiscalHelper.doFiscalization(conn, license);
					} catch (Exception e) {
						LOG.error("Fiscalization Onboaring error\n\tlicense: {}", license, e);
						return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
								"Fiscalization Onboaring error: <p><pre style=\"white-space:pre-wrap\">"
										+ e.getMessage().replaceAll("(\\\\r)?\\\\n", System.lineSeparator())
										+ "</p><pre>");
					}
				}

				conn.setAutoCommit(false); // begin transaction

				// activate license (+set require invoice flag)
				stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET IS_ACTIVE = 1,\n"
					+ "  CONFIRM_DATE = ?,\n"       // 1
					+ "  REQUEST_PHASE = ?,\n"      // 2
					+ "  REQUEST_ACTIVE = 0,\n"
					+ "  NON_APPROVED = ?,\n"       // 3
					+ "  EXTRA_DATA = ?,\n"         // 4
					+ "  IS_ACTIVE_MODIFIED = ?,\n" // 5
					+ "  IS_ACTIVE_MODIFIER = ?\n"  // 6
					+ "WHERE ID = ?\n");            // 7
				stmt.setDate(1, new java.sql.Date(commonHelper.getCurrentDate().getTime()));
				if (noOrder) {
					// do not required approve
					stmt.setString(2, "2DCN");
					stmt.setShort(3, (short) 0);
				} else {
					stmt.setString(2, "3CNF");
					stmt.setShort(3, (short) 2);
				}
				stmt.setString(4, Common.getExtraDataJsonFromLicense(license));
				stmt.setTimestamp(5, new Timestamp(commonHelper.getCurrentDate().getTime()));
				stmt.setString(6, "UntillLicenseServer");
				stmt.setLong(7, license.id);
				if (stmt.executeUpdate() == 0) {
					LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table\n\tlicense: {}", license);
					conn.rollback();
					return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
							"DB error");
				}
				stmt.close(); stmt = null;

				if (!license.requestType.equals("CREATE")) {

					// deactivate previous license
					stmt = conn.prepareStatement("UPDATE LICENSES\n"
						+ "SET IS_ACTIVE = 0,\n"
						+ "  REQUEST_PHASE = ?,\n" // 1
						+ "  IS_ACTIVE_MODIFIED = ?,\n" // 2
						+ "  IS_ACTIVE_MODIFIER = ?\n"  // 3
						+ "WHERE ID = ?\n"); // 4
					stmt.setString(1, "0UPG");
					stmt.setTimestamp(2, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setString(3, "UntillLicenseServer");
					stmt.setLong(4, prevLicenseId);
					if (stmt.executeUpdate() == 0) {
						LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (prevLicenseId: {})", prevLicenseId);
						conn.rollback();
						return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
								"DB error");
					}
					stmt.close(); stmt = null;

				}

				if (!noOrder) {
					// generate proforma message
					List<String> emailList = getApprovalEmails(conn, dealer.id);
					if (emailList == null || emailList.isEmpty()) {
						// TODO ... (may be make license as non auto-approved?)
					} else {
						// TODO XXX Use new generated UID
						String approveUrl = commonHelper.getServletUrl() + "?approve_uid=" + confirmUid;
						String toObjectUrl = approveUrl + "&object=1";
						if (!noEmails)
							if (!emailHelper.emailProforma(emailList, dealer, license, approveUrl,
									toObjectUrl, orderText)) {
								conn.rollback();
								return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
										"Send proforma error");
							}
					}
				}

				conn.commit(); // end transaction

				if (!noEmails)
					emailHelper.emailReportToOwner(ReportType.DEALER_CONFIRMED_REQUEST, license,
							dealer, null, orderText, null);

				File oldLicenseFile = null;
				File licenseFile = null;
				try {

					if (!license.isOnline()) {
						oldLicenseFile = !license.isOldLicReq() ? null
								: untillLicensesPluginHelper.generateLicenseFile(license.id, license.getOldLicVer(),
										license.tempExpiredDate != null);
						licenseFile = untillLicensesPluginHelper.generateLicenseFile(license.id, license.getLicVer(),
								license.tempExpiredDate != null);
					}

					if (!noEmails && (!license.isOnline()
							|| license.requestType.equals("CREATE")
							|| (prevLicenseStatus & DbLicense.ONLINE_BIT) == 0
					)) {
						emailHelper.emailLicense(SendLicenseReason.CONFIRM, oldLicenseFile, licenseFile,
								license.tempExpiredDate != null, license, dealer);
						return new ConfirmOrderResult(ConfirmOrderResult.Result.SUCCESS,
								dealer.email, "");
					}

					return new ConfirmOrderResult(ConfirmOrderResult.Result.SUCCESS,
							"", "");

				} catch (Exception e) {

					LOG.error("Generate/Send license error", e);
					return new ConfirmOrderResult(ConfirmOrderResult.Result.SUCCESS_BUT_FAIL_SEND_MESSAGE,
							"", "");

				} finally {
					// delete generated license files (if is created)
					if (oldLicenseFile != null)
						oldLicenseFile.delete();
					if (licenseFile != null)
						licenseFile.delete();
				}

			} // if (cancel) ... else ...

		} catch (RequiredDealerIsNotFoundException e) {
			LOG.error("RequiredDealerIsNotFoundException", e);
			return new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "",
					"Dealer is not found id DB");
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public boolean approveLicenseByLink(Connection conn, String approveUid, boolean toObject,
			String comment) throws SQLException, LicenseServiceException {
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Find license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE CONFIRM_UID = ?\n");
			stmt.setString(1, approveUid);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("Attempt to approve/object license with wrong approve_uid (approveUid: {})", approveUid);
				throw new InvalidArgumentException("License with the specified 'approve_uid' does not exist");
			}
			if (rs.getShort("IS_ACTIVE") == 0) {
				LOG.warn("Attempt to approve/object inactive license (approveUid: {})", approveUid);
				throw new InvalidArgumentException("License with the specified 'approve_uid' is no longer exists");
			}
			if (rs.getShort("NON_APPROVED") != 2) {
				LOG.warn("Attempt to approve/object already approve/object license (approveUid: {})", approveUid);
				return false;
			}

			DbLicense license = Common.getDbLicenseFromResultSet(rs);
			String orderText = rs.getString("REQUEST_ORDER");
			int tableNo = rs.getInt("REQUEST_TABLENO");
			String dealerComments = rs.getString("DEALER_COMMENTS");

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Get license dealer
			Dealer dealer = new Dealer(conn, license.idDealers);

			if (toObject) {

				conn.setAutoCommit(false); // begin transaction
				// TODO Update license set NON_APPROVED=1 instead 2
				stmt = conn.prepareStatement("UPDATE LICENSES\n"
						+ "SET DEALER_COMMENTS = ?,\n" // 1
						+ "  REQUEST_PHASE = ?,\n" // 2
						+ "  NON_APPROVED = 1\n"
						+ "WHERE ID = ?\n"         // 3
						+ "  AND COALESCE(IS_ACTIVE, 0) <> 0\n"
						+ "  AND COALESCE(NON_APPROVED, 0) = 2\n");
				stmt.setString(1, (dealerComments == null || dealerComments.isEmpty() ? "" :
					dealerComments + "\r\n") + "Objection: " + comment + "\r\n");
				stmt.setString(2, "4OBJ");
				stmt.setLong(3, license.id);
				if (stmt.executeUpdate() == 0) {
					LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (license.id: {})", license.id);
					throw new DbErrorException("");
				}

				// Send Email to Owner with orderText and commend
				// TODO if unable to send then rollback
				emailHelper.emailReportToOwner(ReportType.DEALER_OBJECT_PROFORMA, license,
						dealer, null, orderText, comment);

				conn.commit();

			} else {

				approveLicense(conn, license.id, tableNo, false);

			}

			return true;

		} catch (RequiredDealerIsNotFoundException e) {
			LOG.warn("Dealer of the license is not found", e);
			throw new ServerErrorException("Dealer of the license is not found");
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	private List<String> getApprovalEmails(Connection conn, long idDealer)
			throws SQLException, RequiredDealerIsNotFoundException {
		// get approval email addresses
		String approvalEmails = null;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {
			// Get ID_EXTRA_FIELD_VALUES
			stmt = conn.prepareStatement("SELECT C.ID_EXTRA_FIELD_VALUES\n"
					+ "FROM DEALERS D\n"
					+ "INNER JOIN CLIENTS C ON C.ID = D.ID_CLIENTS\n"
					+ "WHERE D.ID = ?\n");
			stmt.setLong(1, idDealer);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				throw new RequiredDealerIsNotFoundException();
			}
			long idExtraFieldValues = rs.getLong("ID_EXTRA_FIELD_VALUES");
			if (idExtraFieldValues != 0) {
				Set<ExtraField> extraFields = Common.getExtraFileds(conn, idExtraFieldValues);
				for (ExtraField extraField : extraFields)
					if (extraField.name.equalsIgnoreCase("approval emails")) {
						approvalEmails = extraField.valAsString;
						break;
					} else if (extraField.name.equalsIgnoreCase("invoice emails")) { // XXX temporary
						approvalEmails = extraField.valAsString;
					}
			}
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		if (approvalEmails == null)
			return null;
		else
			return Common.parseEmailList(approvalEmails);
	}

	  ///////////////////////////////////////
	 // Public methods (for super dealer) //
	///////////////////////////////////////

	public RrOrder getLicenseOrder(Connection conn, Dealer dealer, long licenseId) throws LicenseServiceException, SQLException {
		RrOrder result;

		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get bill id
			stmt = conn.prepareStatement("SELECT ID_BILL\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			long billId = rs.getLong("ID_BILL");

			if (billId == 0) {

				//throw new InvalidArgumentException("For the specified licence (by 'licenseId') there is no order");
				result = null;

			} else {

				result = new RrOrder();

				// Get currency parameters (if exists)
				rs.close(); rs = null; stmt.close(); stmt = null;
				stmt = conn.prepareStatement("SELECT CURRENCY.*\n"
						+ "FROM CURRENCY\n"
						+ "INNER JOIN BANKS ON BANKS.ID_CURRENCY = CURRENCY.ID\n"
						+ "  INNER JOIN DEALERS ON DEALERS.ID_BANKS = BANKS.ID\n"
						+ "    INNER JOIN LICENSES ON LICENSES.ID_DEALERS = DEALERS.ID\n"
						+ "WHERE LICENSES.ID = ?\n");
				stmt.setLong(1, licenseId);
				rs = stmt.executeQuery();
				if (rs.next() ) {
					result.currencyCode = rs.getString("CODE");
					result.currencyRound = rs.getInt("ROUND");
					result.currencyRate = rs.getFloat("RATE");
					result.currencySymbol = rs.getString("SYMBOL");
					result.currencySymbolAlignment = rs.getShort("SYM_ALIGNMENT");
				} else {
					result.currencyCode = null;
					result.currencyRound = 2;
					result.currencyRate = 1.0f;
					result.currencySymbol = null;
				}

				// Get order as Order object
				Order order = Common.getOrderByBillId(conn, billId);

				// Copy Order object to RrOrder result object
				result.orderItemList = new ArrayList<RrOrderItem>();
				result.totalWoVat = 0.0;
				result.totalVat = 0.0;
				result.total = 0.0;
				for (Order.Item orderItem : order.itemList) {
					RrOrderItem rrOrderItem = new RrOrderItem();
					rrOrderItem.quantity = orderItem.quantity;
					rrOrderItem.articleId = orderItem.articleId;
					rrOrderItem.articleNumber = orderItem.articleNumber;
					rrOrderItem.articleName = orderItem.articleName;
					rrOrderItem.price = orderItem.price.doubleValue();
					rrOrderItem.negative = orderItem.negative;
					rrOrderItem.text = orderItem.text;
					result.orderItemList.add(rrOrderItem);
					if (orderItem.priceExcludesVat) {
						result.totalWoVat += orderItem.price.doubleValue() * orderItem.quantity;
						result.totalVat += orderItem.vat * orderItem.quantity;
						result.total += orderItem.price.doubleValue() * orderItem.quantity
								+ orderItem.vat * orderItem.quantity;
					} else {
						result.total += orderItem.price.doubleValue() * orderItem.quantity;
						result.totalVat += orderItem.vat * orderItem.quantity;
						result.totalWoVat += orderItem.price.doubleValue() * orderItem.quantity
								- orderItem.vat * orderItem.quantity;
					}
				}

			}

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		return result;
	}

	public void addDiscount(Connection conn, Dealer dealer, long licenseId, double price,
			String text) throws LicenseServiceException, SQLException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");
		if (text == null) throw new InvalidArgumentException("text");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get 'table no' and check license state
			stmt = conn.prepareStatement("SELECT REQUEST_TABLENO, NON_APPROVED\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			if (rs.getShort("NON_APPROVED") == 0) {
				LOG.warn("Attempting to add a discount on the already approved license (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' has already been approved");
			}
			int tableNo = rs.getInt("REQUEST_TABLENO");

			if (!untillLicensesPluginHelper.licensesPluginAddDiscount(tableNo, price, text))
				throw new ServerErrorException("Error invoking licenses plugin from unTill");

		} catch (UnsupportedEncodingException e) {
			LOG.error("", e);
			throw new ServerErrorException(e.getMessage());
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public void addNegativeArticle(Connection conn, Dealer dealer, long licenseId,
			long articleId, int quantity, String text) throws LicenseServiceException, SQLException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");
		if (articleId == 0) throw new InvalidArgumentException("articleId");
		if (quantity < 1) throw new InvalidArgumentException("quantity");
		if (text == null) throw new InvalidArgumentException("text");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get 'table no' and check license state
			stmt = conn.prepareStatement("SELECT REQUEST_TABLENO, NON_APPROVED\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			if (rs.getShort("NON_APPROVED") == 0) {
				LOG.warn("Attempting to add a discount on the already approved license (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' has already been approved");
			}
			int tableNo = rs.getInt("REQUEST_TABLENO");

			if (!untillLicensesPluginHelper.licensesPluginAddNegativeArticle(
					tableNo, articleId, quantity,  text))
				throw new ServerErrorException("Error invoking licenses plugin from unTill");

		} catch (UnsupportedEncodingException e) {
			LOG.error("", e);
			throw new ServerErrorException(e.getMessage());
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public void addArticle(Connection conn, Dealer superDealer, long licenseId,
			long articleId, int quantity, double manualPrice, String text) throws Exception {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");
		if (articleId == 0) throw new InvalidArgumentException("articleId");
		if (quantity < 1) throw new InvalidArgumentException("quantity");

		// Check authentication
		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Get 'table no' and check license state
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID_DEALERS, REQUEST_TABLENO, NON_APPROVED\n" +
				"FROM LICENSES\n" +
				"WHERE ID = ?\n")
		) {
			stmt.setLong(1, licenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					LOG.warn("License is not found (licenseId: {})", licenseId);
					throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
				}
				if (rs.getShort("NON_APPROVED") == 0) {
					LOG.warn("Attempting to add Article to the already approved license (licenseId: {})", licenseId);
					throw new InvalidArgumentException("License with the specified 'licenseId' has already been approved");
				}
				int tableNo = rs.getInt("REQUEST_TABLENO");
				long dealerId = rs.getLong("ID_DEALERS");

				untillTpapiHelper.addArticleToTable(conn, tableNo, dealerId, articleId, quantity, manualPrice, text);
			}
		}
	}

	public void approveLicenseByGui(Connection conn, Dealer dealer, long licenseId, boolean noInvoice)
			throws LicenseServiceException, SQLException
	{
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Check authentication
		if (!dealer.superDealer)
			throw new AuthFailedException();

		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get 'table no' and check license state
			stmt = conn.prepareStatement("SELECT REQUEST_TABLENO, NON_APPROVED\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			if (rs.getShort("NON_APPROVED") == 0) {
				LOG.warn("Attempting to approve the already approved license (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' has already been approved");
			}
			int tableNo = rs.getInt("REQUEST_TABLENO");

			approveLicense(conn, licenseId, tableNo, noInvoice);

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public void orderManualLicense(Connection conn, Dealer dealer, long licenseId)
			throws LicenseServiceException, SQLException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Check license (ID, REQUEST_TYPE IS NULL, IS_ACTIVE = 1) and get license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			// Check license state
			if (rs.getLong("IS_ACTIVE") == 0) {
				LOG.warn("This license is not active (licenseId: {})", licenseId);
				throw new InvalidArgumentException("This license is not active");
			}
			if (rs.getString("REQUEST_TYPE") != null) {
				LOG.warn("This license is not created manually (licenseId: {})", licenseId);
				throw new InvalidArgumentException("This license is not created manually");
			}
			// Get license data
			DbLicense license = Common.getDbLicenseFromResultSet(rs);

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Check license data
			if (license.isNeverExpired() || license.isUnlimited()
					|| license.isCanceled() || license.isTrial() || license.idDealers == 0) {
				throw new InvalidArgumentException("Invalid license status");
			}
			// TODO check date range (1Y for unTill, 3Y for Apostill)
			// ...

			// manual request existing license
			stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET\n"
					+ "  REQUEST_DATE = ?,\n"  // 1
					+ "  REQUEST_PHASE = ?,\n" // 2
					+ "  REQUEST_TYPE = ?\n"   // 3
					+ "WHERE ID = ?\n");       // 4
			int fld = 1;
			stmt.setTimestamp(fld++, new Timestamp(commonHelper.getCurrentDate().getTime())); // 1 (REQUEST_DATE)
			stmt.setString(fld++, "1HRQ");   // 2 (REQUEST_PHASE)
			stmt.setString(fld++, "CREATE"); // 3 (REQUEST_TYPE)
			stmt.setLong(fld++, licenseId);  // 4
			if (stmt.executeUpdate() == 0) {
				LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
				throw new DbErrorException("Unknown mistake during update the license in DB");
			}
			stmt.close(); stmt = null;
			conn.commit();

			// manual order existing license
			boolean orderLicenseResult = false;
			try {
				orderLicenseResult = orderLicense(conn, RequestType.CREATE, license, null, licenseId);
			} finally {
				if (!orderLicenseResult) {
					// try to remove modifications
					stmt = conn.prepareStatement("UPDATE LICENSES\n"
							+ "SET "
							+ "  REQUEST_DATE = NULL,\n"
							+ "  REQUEST_PHASE = NULL,\n"
							+ "  REQUEST_TYPE = NULL\n"
							+ "WHERE ID = ?\n");
					stmt.setLong(1, licenseId);
					stmt.executeUpdate();
					stmt.close(); stmt = null;
					conn.commit();
					throw new ServerErrorException("Order license error");
				}
			}

			// manual confirm existing license
			// activate license (+set require invoice flag)
			stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET "
					//+ "  IS_ACTIVE = 1,\n"
					+ "  CONFIRM_DATE = ?,\n"   // 1
					+ "  REQUEST_PHASE = ?,\n"  // 2
					+ "  REQUEST_ACTIVE = 0,\n"
					+ "  NON_APPROVED = 1\n"
					+ "WHERE ID = ?\n");        // 3
			stmt.setDate(1, new java.sql.Date(commonHelper.getCurrentDate().getTime()));
			stmt.setString(2, "3HCN");
			stmt.setLong(3, licenseId);
			if (stmt.executeUpdate() == 0) {
				LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
				throw new DbErrorException("Unknown mistake during update the license in DB");
			}
			stmt.close(); stmt = null;
			conn.commit();

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public void recreateInvoice(Connection conn, Dealer dealer, long licenseId) throws LicenseServiceException, SQLException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Check license (ID, REQUEST_TYPE IS NULL, IS_ACTIVE <> 0) and get license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			// Check license state
			if (rs.getLong("ID_BILL") == 0) {
				LOG.warn("This license is without a bill (licenseId: {})", licenseId);
				throw new InvalidArgumentException("This license is without a bill");
			}
			// Get license data
			DbLicense license = Common.getDbLicenseFromResultSet(rs);

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Check license data
			if (license.needApprove()) {
				LOG.warn("This license is non-approved (licenseId: {})", licenseId);
				throw new InvalidArgumentException("This license is non-approved");
			}
			if (license.invoiceRequired) {
				LOG.warn("For this license is already requested invoice (licenseId: {})", licenseId);
				throw new InvalidArgumentException("For this license is already requested invoice");
			}

			// manual request existing license
			stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET INVOICE_REQUIRED = 1\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			if (stmt.executeUpdate() == 0) {
				LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
				throw new DbErrorException("Unknown mistake during update the license in DB");
			}
			stmt.close(); stmt = null;
			conn.commit();

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public CommonRpcResult reorderLicense(Connection conn, Dealer dealer, long licenseId)
			throws LicenseServiceException, SQLException {
		CommonRpcResult result;

		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction

		// Check license (ID, NON_APPROVED <> 0) and get REQUEST_TABLENO and ID_BILL
		DbLicense license;
		int tableNo;
		long idBill;
		long idLicensesPrev;
		try (PreparedStatement stmt = conn.prepareStatement("SELECT * FROM LICENSES WHERE ID = ?")) {
			stmt.setLong(1, licenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					LOG.warn("License is not found (licenseId: {})", licenseId);
					throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
				}
				license = Common.getDbLicenseFromResultSet(rs);
				tableNo = rs.getInt("REQUEST_TABLENO");
				idBill = rs.getLong("ID_BILL");
				idLicensesPrev = rs.getLong("ID_LICENSES_PREV");
			}
		}
		// Check license state
		if (license.nonApproved == 0) {
			LOG.warn("This license is not non-approved (licenseId: {})", licenseId);
			throw new InvalidArgumentException("This license is not non-approved");
		}

		// TODO ?? Check license data (REQUEST_TYPE is valid, ID_LICENSES_PREV <> 0 (if REQUEST_TYPE <> "CREATE"), ID_DEALERS <> 0,
		// STATUS is not included NO_EXPIRED_DATE_BIT, DESCRIPTION_UNLIMITED_BIT, DESCRIPTION_CANCELED_BIT, DESCRIPTION_TRIAL_BIT)
		// TODO ?? Check date range (1Y for unTill, 3Y for Apostill)

		// Restore request type
		RequestType requestType = license.requestType.equals("CREATE") ?
				(license.isBoost() ? RequestType.CREATE_BOOST : RequestType.CREATE)
				: license.requestType.equals("PROLONG") ? RequestType.PROLONG
				: license.requestType.equals("UPGRADE") ? RequestType.UPGRADE
				: null;
		if (requestType == null) {
			LOG.warn("License requset type is not valid (license.requestType: {})", license.requestType);
			throw new DbErrorException("Unknown license requset type");
		}

		// get prev. license
		DbLicense prevLicense = null;
		if (requestType != RequestType.CREATE) {
			try (PreparedStatement stmt = conn.prepareStatement("SELECT * FROM LICENSES WHERE ID = ?")) {
				stmt.setLong(1, idLicensesPrev);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next()) {
						LOG.warn("Prev. license is not found (licenseId: {})", idLicensesPrev);
						throw new InvalidArgumentException("Prev. license is not found");
					}
					prevLicense = Common.getDbLicenseFromResultSet(rs);
				}
			}
		}

		if (idBill != 0) {
			// Void old order
			try (PreparedStatement stmt = conn.prepareStatement(
					"UPDATE LICENSES\n"
					+ "SET\n"
					+ "  REQUEST_TABLENO = NULL,\n"
					+ "  REQUEST_ORDER = NULL,\n"
					+ "  ID_BILL = NULL\n"
					+ "WHERE ID = ?\n")
			) {
				stmt.setLong(1, licenseId);
				if (stmt.executeUpdate() == 0) {
					LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
					throw new DbErrorException("Unknown mistake during update the license in DB");
				}
			}
			if (!untillLicensesPluginHelper.voidOrder(tableNo)) {
				conn.rollback();
				throw new ServerErrorException("Void order error");
			}
			conn.commit();
		}

		// manual order existing license
		if (!orderLicense(conn, requestType, license, prevLicense, licenseId)) {
			result = new CommonRpcResult("Order license error");
		} else {
			result = new CommonRpcResult();
			// manual "confirm" existing license
			// activate license (+set require invoice flag)
			try (PreparedStatement stmt = conn.prepareStatement(
					"UPDATE LICENSES\n"
					+ "SET "
					+ "  REQUEST_PHASE = ?,\n"  // 1
					+ "  REQUEST_ACTIVE = 0\n"
					+ "WHERE ID = ?\n")         // 2
			) {
				stmt.setString(1, "9ROR");
				stmt.setLong(2, licenseId);
				if (stmt.executeUpdate() == 0) {
					LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
					throw new DbErrorException("Unknown mistake during update the license in DB");
				}
			}
			conn.commit();
		}

		return result;
	}

	public void disapproveLicense(Connection conn, Dealer dealer, long licenseId) throws LicenseServiceException, SQLException {
		// Check parameters
		if (licenseId == 0) throw new InvalidArgumentException("licenseId");

		// Check authentication
		Dealer superDealer = dealer;

		if (!superDealer.superDealer)
			throw new AuthFailedException();

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get PBILL number and suffix and check license state
			stmt = conn.prepareStatement("SELECT PBILL.NUMBER, PBILL.SUFFIX, "
					+ "LICENSES.ID_BILL, LICENSES.NON_APPROVED, LICENSES.REQUEST_ACTIVE\n"
					+ "FROM LICENSES\n"
					+ "LEFT OUTER JOIN PBILL ON PBILL.ID_BILL = LICENSES.ID_BILL AND "
					+ "NOT EXISTS (SELECT * FROM NEG_PBILL WHERE NEG_PBILL.ID_PBILL = PBILL.ID)\n"
					+ "WHERE LICENSES.ID = ?\n"
					+ "ORDER BY PBILL.PDATETIME DESC\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not found");
			}
			if (rs.getLong("ID_BILL") == 0) {
				LOG.warn("This license is without a bill (licenseId: {})", licenseId);
				throw new InvalidArgumentException("This license is without a bill");
			}
			if (rs.getShort("NON_APPROVED") != 0) {
				LOG.warn("Attempting to disapprove non-approved license (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' non-approved");
			}
			if (rs.getLong("REQUEST_ACTIVE") != 0) {
				LOG.warn("Attempting to disapprove not confirmed license request (licenseId: {})", licenseId);
				throw new InvalidArgumentException("License with the specified 'licenseId' is not confirmed");
			}
			int pbillNumber = rs.getInt("NUMBER");
			String pbillSuffix = rs.getString("SUFFIX");

			rs.close(); rs = null; stmt.close(); stmt = null;

			// Disapprove license
			stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET REQUEST_PHASE = ?,\n" // 1
					+ "  NON_APPROVED = 1,\n"
					+ "  INVOICE_REQUIRED = 0\n"
					+ "WHERE ID = ?\n");     // 2
			stmt.setString(1, "6DIS");
			stmt.setLong(2, licenseId);
			if (stmt.executeUpdate() == 0) {
				LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
				throw new DbErrorException("");
			}
			stmt.close(); stmt = null;

			// attempt to payment
			if (!untillLicensesPluginHelper.reopenBill(pbillNumber, pbillSuffix)) {
				conn.rollback();
				throw new ServerErrorException("Reopen bill error");
			}

			conn.commit();

		} catch (UnsupportedEncodingException e) {
			LOG.error("", e);
			throw new ServerErrorException(e.getMessage());
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	public int getMaxDefinitiveMaxVersion() throws LicenseServiceException {
		return commonHelper.getSettings().getMaxDefinitiveMaxVersion();
	}

	public List<ClientNameChange> getClientNameChanges(Connection conn, Dealer dealer, long dealerId,
			String filter) throws SQLException {

		List<ClientNameChange> result = new ArrayList<>();

		String sql = "WITH RECURSIVE RL AS (\n"
				+ "    SELECT L.ID, L.ID_LICENSES_PREV, L.ID_DEALERS, C.NAME DEALER_NAME, L.CLIENT_NAME, L.HARD_CODE,"
				+ " L.ISSUE_DATE, L.IS_ACTIVE, L.REQUEST_ACTIVE\n"
				+ "    FROM LICENSES L\n"
				+ "        INNER JOIN DEALERS D ON D.ID = L.ID_DEALERS\n"
				+ "            INNER JOIN CLIENTS C ON C.ID = D.ID_CLIENTS\n"
				+ "    WHERE (L.REQUEST_ACTIVE = 1 OR L.IS_ACTIVE = 1\n"
				+ "        AND NOT EXISTS (SELECT LP.ID FROM LICENSES LP\n"
				+ "            WHERE LP.ID_LICENSES_PREV = L.ID AND LP.REQUEST_ACTIVE = 1))\n"
				+ (dealerId != 0 ?  "    AND L.ID_DEALERS = ?\n" : "")
				+ "    AND L.ID_DEALERS > 0\n"
				+ "    UNION ALL\n"
				+ "    SELECT L.ID, L.ID_LICENSES_PREV, L.ID_DEALERS, NULL, L.CLIENT_NAME, L.HARD_CODE,"
				+ " L.ISSUE_DATE, L.IS_ACTIVE, L.REQUEST_ACTIVE\n"
				+ "    FROM LICENSES L\n"
				+ "    INNER JOIN RL ON L.ID = RL.ID_LICENSES_PREV\n"
				+ ")\n"
				+ "SELECT * FROM RL\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			int parIndex = 1;
			if (dealerId != 0)
				stmt.setLong(parIndex++, dealerId);
			try (ResultSet rs = stmt.executeQuery()) {
				long prevLicenseId = 0;
				String curLicDealerName = null, curLicClientName = null, curLicHardCode = null;
				String cur2LicClientName = null;
				String cur3LicClientName;
				while (rs.next()) {
					if (prevLicenseId == rs.getLong("ID")) {
						prevLicenseId = rs.getLong("ID_LICENSES_PREV");
						cur3LicClientName = rs.getString("CLIENT_NAME");
						if (!cur3LicClientName.equals(cur2LicClientName)) {
							ClientNameChange change = new ClientNameChange();
							change.dealerName = curLicDealerName;
							change.clientName = curLicClientName;
							change.hardCode = curLicHardCode;
							change.issueDate = rs.getDate("ISSUE_DATE");
							change.clientNameBefore = cur3LicClientName;
							change.clientNameAfter = cur2LicClientName;
							if (filter == null || filter.trim().isEmpty()
									|| change.clientName.toLowerCase().contains(filter.trim().toLowerCase())
									|| change.hardCode.toLowerCase().contains(filter.replace("-", "").trim().toLowerCase())
									|| change.clientNameBefore.toLowerCase().contains(filter.trim().toLowerCase())
									|| change.clientNameAfter.toLowerCase().contains(filter.trim().toLowerCase())
							) {
								result.add(change);
							}
							cur2LicClientName = cur3LicClientName;
						}
					} else {
						if (prevLicenseId != 0) {
							LOG.warn("Expected license {}, but {}", prevLicenseId, rs.getLong("ID"));
						}
						prevLicenseId = rs.getLong("ID_LICENSES_PREV");
						curLicDealerName = rs.getString("DEALER_NAME");
						curLicClientName = rs.getString("CLIENT_NAME");
						curLicHardCode = rs.getString("HARD_CODE");
						cur2LicClientName = curLicClientName;
					}
				}
			}
		}

		result.sort((a, b) -> b.issueDate.compareTo(a.issueDate));
		if ((filter == null || filter.trim().isEmpty()) && result.size() > 100)
			result.subList(100, result.size()).clear();
		return result;
	}

	public List<DbLicense> getLicenseBoosts(Connection conn, Dealer dealer, String licenseUid) throws SQLException {
		List<DbLicense> result = new ArrayList<DbLicense>();
		try (PreparedStatement stmt = conn.prepareStatement(
				"SELECT *\n"
				+ "FROM LICENSES\n"
				+ "WHERE IS_ACTIVE = 1 AND HARD_CODE = ?\n"
				+ " AND BIN_AND(STATUS, " + License.DESCRIPTION_BOOST_BIT + ") > 0\n"
				+ (dealer.superDealer ? "" : " AND ID_DEALERS = ?\n")
				+ "ORDER BY START_DATE\n"
		)) {
			stmt.setString(1, licenseUid);
			if (!dealer.superDealer)
				stmt.setLong(2, dealer.id);
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					DbLicense license = Common.getDbLicenseFromResultSet(rs);
					result.add(license);
				}
			}
		}
		return result;
	}

	public RrArticle getCreditArticle(Connection conn, Dealer dealer) throws SQLException {
		return getArticleByBarcode(conn, "CREDIT");
	}

	public RrArticle getHandlingFeeArticle(Connection conn, Dealer dealer) throws SQLException {
		return getArticleByBarcode(conn, "HANDLINGFEE");
	}

	public List<DbHwmInvoice> getHwmInvoiceReport(Connection conn, Dealer dealer, Date month) throws SQLException {
		List<DbHwmInvoice> result = new ArrayList<>();

		String sql = "SELECT CHAIN,\n"
				+ "  (SELECT FIRST 1 CLIENT_NAME FROM LICENSES\n"
				+ "    INNER JOIN HWM_INVOICE_ITEM ON HWM_INVOICE_ITEM.ID_LICENSES = LICENSES.ID\n"
				+ "    WHERE HWM_INVOICE_ITEM.ID_HWM_INVOICE = HWM_INVOICE.ID) FIRST_CLIENT_NAME,\n"
				+ "  (SELECT COUNT(*) FROM HWM_INVOICE_ITEM WHERE ID_HWM_INVOICE = HWM_INVOICE.ID) CNT\n"
				+ "FROM HWM_INVOICE\n"
				+ "WHERE DATEDIFF(MONTH, HWM_INVOICE.CALC_MONTH, ?) = 0\n"; // 1
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setDate(1, new java.sql.Date(month.getTime()));
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					DbHwmInvoice rec = new DbHwmInvoice();
					rec.chain = rs.getString("CHAIN");
					rec.firstClientName = rs.getString("FIRST_CLIENT_NAME");
					rec.count = rs.getInt("CNT");
					result.add(rec);
				}
			}
		}

		return result;
	}

	public List<ArticleWithPrices> getDealerPrices(Connection conn, Dealer dealer, long dealerId, String pricesPass) throws Exception {
		// Parameters "dealerId" only for super dealer
		if (!dealer.superDealer) {
			dealerId = dealer.id;
			String sql = "SELECT PRICES_PASS\n"
					+ "FROM DEALERS\n"
					+ "WHERE ID = ?\n"; // 1
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setLong(1, dealerId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next())
						throw new Exception(String.format("Dealer (%d) is not found", dealerId));
					String expectedPricesPass = rs.getString("PRICES_PASS");
					if (expectedPricesPass != null && !expectedPricesPass.isEmpty() && !expectedPricesPass.equals(pricesPass))
						return null;
				}
			}
		}

		return untillTpapiHelper.getDealerPrices(conn, dealerId);
	}


	public String getLicenseComment(Connection conn, Dealer dealer, long licenseId) throws SQLException {
		String sql = "SELECT COMMENTS\n"
				+ "FROM LICENSES\n"
				+ "WHERE ID = ?\n"; // 1
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, licenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					LOG.error("License not found (licenseId: {})", licenseId);
					throw new RuntimeException();
				}
				return rs.getString("COMMENTS");
			}
		}
	}

	public void setLicenseComment(Connection conn, Dealer dealer, long licenseId, String licenseComment) throws SQLException {
		boolean oldAutoCommit = conn.getAutoCommit();
		conn.setAutoCommit(false);
		try {

			String sql = "UPDATE LICENSES\n"
					+ "SET COMMENTS = ?\n" // 1
					+ "WHERE ID = ?\n"; // 2
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setString(1, licenseComment);
				stmt.setLong(2, licenseId);
				if (stmt.executeUpdate() == 0) throw new RuntimeException();
			}

			conn.commit();

		} catch (Exception e) {
			conn.rollback();
			throw e;
		} finally {
			conn.setAutoCommit(oldAutoCommit);
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	/**
	 * Create request e-mail(s)<br>
	 * if <tt>superDealer</tt> is <tt>null</tt> and <tt>targetDealer</tt> is
	 * super dealer, then <tt>targetDealer</tt> depend on license
	 * @param conn
	 * @param baseUrl
	 * @param superDealer
	 * @param targetDealer (required)
	 * @param licenseId
	 * @param sendReportToOwner
	 * @return <tt>true</tt> - if success
	 * @throws SQLException
	 */
	private boolean createRequestEmail(Connection conn, String baseUrl, Dealer superDealer, Dealer targetDealer, long licenseId,
			boolean sendReportToOwner) throws SQLException
	{
		DbLicense license;
		String confirmUid;
//		String orderText;

		// Begin transaction
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Check license id and get license
			stmt = conn.prepareStatement("SELECT *\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n");
			stmt.setLong(1, licenseId);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				LOG.warn("License is not found by id (licenseId: {})", licenseId);
				return false;
			}
			long licenseDealerId = rs.getLong("ID_DEALERS");
			if (licenseDealerId != targetDealer.id && !(targetDealer.superDealer && superDealer == null)) {
				LOG.warn("This license does not belong to this dealer (licenseId: {}, targetDealer.id: {})", licenseId, targetDealer.id);
				return false;
			}
			// Update target dealer and super dealer
			if (licenseDealerId != 0 && licenseDealerId != targetDealer.id) {
				superDealer = targetDealer;
				try {
					targetDealer = new Dealer(conn, licenseDealerId);
				} catch (RequiredDealerIsNotFoundException e) {
					LOG.warn("dealer not found", e);
					return false;
				}
			}
			if (rs.getLong("REQUEST_ACTIVE") == 0) {
				LOG.warn("Request for this license is not active (licenseId: {})", licenseId);
				return false;
			}

			// Get license data
			license = Common.getDbLicenseFromResultSet(rs);
			confirmUid = rs.getString("CONFIRM_UID");
//			orderText = rs.getString("REQUEST_ORDER");

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		boolean result;

		if (license.requestLargeAccount) {

			// send email request to dealer (and superDealer first)
			String laApprUid = baseUrl + "?la_appr_uid=" + confirmUid;
			String laDenyUrl = laApprUid + "&deny=1";

			result = emailHelper.emailLargeAccountRequest(superDealer, targetDealer, license, laApprUid, laDenyUrl);

		} else {

			// send report e-mail to owner
			if (sendReportToOwner)
				emailHelper.emailReportToOwner(ReportType.DEALER_REQUESTED_LICENSE, license,
						targetDealer, superDealer, null, null);

			// send email request to dealer (and superDealer first)
			String confirmUrl = baseUrl + "?confirm_uid=" + confirmUid;
			String cancelUrl = confirmUrl + "&cancel=1";

			result = emailHelper.emailRequest(superDealer, targetDealer, license, confirmUrl, cancelUrl);
			if (superDealer != null && result) {
				result = emailHelper.emailRequest(null, targetDealer, license, confirmUrl, cancelUrl);
			}

		}

		return result;
	}

	private void approveLicense(Connection conn, long licenseId, int tableNo, boolean noInvoice)
			throws LicenseServiceException, SQLException
	{
		boolean oldAutoCommit = conn.getAutoCommit();
		conn.setAutoCommit(false); // Begin transaction
		try {

			// TODO? get from caller in parameters or vice versa (
			try (PreparedStatement stmt = conn.prepareStatement(
					"SELECT NON_APPROVED, FUNC_LIMITED, HWM_YEARLY, HWM_FIRST, ID_DEALERS, CHAIN, ISSUE_DATE\n"
					+ "FROM LICENSES\n"
					+ "WHERE ID = ?\n"
					+ "FOR UPDATE\n"
			)) {
				stmt.setLong(1, licenseId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next()) {
						LOG.error("License not found (licenseId: {})", licenseId);
						throw new DbErrorException("");
					}
					if (rs.getShort("NON_APPROVED") == 0) {
						LOG.warn("License already approved (licenseId: {})", licenseId);
						throw new DbErrorException("");
					}
					if ((rs.getLong("FUNC_LIMITED") & (1L << License.FUNC_WM_HOSTED)) != 0
							&& rs.getShort("HWM_YEARLY") == 1 && rs.getShort("HWM_FIRST") == 1
							&& !noInvoice)
					{
						Common.getHwmInvoiceHandler().prepareInvoicesForRestOfYear(conn, rs.getLong("ID_DEALERS"),
								rs.getString("CHAIN"), licenseId, rs.getDate("ISSUE_DATE").toLocalDate());
					}
				}
			}

			// Approve license
			try (PreparedStatement stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET REQUEST_PHASE = ?,\n" // 1
					+ "  NON_APPROVED = 0,\n"
					+ "  DEALER_COMMENTS = '',\n"
					+ "  INVOICE_REQUIRED = ?\n" // 2
					+ "WHERE ID = ?\n"           // 3
//					+ "  AND COALESCE(IS_ACTIVE, 0) <> 0\n"
//					+ "  AND COALESCE(NON_APPROVED, 0) <> 0\n"
			)) {
				stmt.setString(1, "4APP");
				stmt.setShort(2, (short) (noInvoice ? 0 : 1));
				stmt.setLong(3, licenseId);
				if (stmt.executeUpdate() == 0) {
					LOG.error("The method executeUpdate of PreparedStatement class has returned a zero when upgrade LICENSES table (licenseId: {})", licenseId);
					throw new DbErrorException("");
				}
			}

			// attempt to payment
			if (!paymentOrder(conn, tableNo)) {
				throw new ServerErrorException("Payment order error");
			}

			conn.commit(); // End transaction

		} catch (Exception e) {
			conn.rollback();
			throw e;
		} finally {
			conn.setAutoCommit(oldAutoCommit);
		}
	}

	private RrArticle getArticleByBarcode(Connection conn, String barcode) throws SQLException {

		try (PreparedStatement stmt = conn.prepareStatement(
				"SELECT A.ID ID, A.ARTICLE_NUMBER ARTICLE_NUMBER, A.NAME NAME FROM ARTICLES A\n" +
				"INNER JOIN ARTICLE_BARCODES AB ON AB.ID_ARTICLES = A.ID\n" +
				"WHERE UPPER(AB.BARCODE) = UPPER(?)\n" +
				"  AND A.IS_ACTIVE = 1 AND AB.IS_ACTIVE = 1\n")
		) {
			stmt.setString(1, barcode);
			try (ResultSet rs = stmt.executeQuery()) {
				if (rs.next())
					return new RrArticle(rs.getLong("ID"), rs.getInt("ARTICLE_NUMBER"), rs.getString("NAME"));
			}
		}

		// Try old method for compatibility (no need in future)
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID, ARTICLE_NUMBER, NAME FROM ARTICLES\n" +
				"WHERE UPPER(BARCODE) = UPPER(?) AND IS_ACTIVE = 1\n")
		) {
			stmt.setString(1, barcode);
			try (ResultSet rs = stmt.executeQuery()) {
				if (rs.next())
					return new RrArticle(rs.getLong("ID"), rs.getInt("ARTICLE_NUMBER"), rs.getString("NAME"));
			}
		}

		return null;
	}

	private boolean checkChainUseInMonthlyOrYearlyHwm(Connection conn, long dealerId, String chain, boolean hwmYearly) throws SQLException {

		try (PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM LICENSES\n"
				+ "WHERE (IS_ACTIVE = 1 OR REQUEST_ACTIVE = 1)\n"
				+ "  AND ID_DEALERS = ?\n" // 1
				+ "  AND UPPER(CHAIN) = UPPER(?)\n" // 2
				+ "  AND BIN_AND(FUNC_LIMITED, BIN_SHL(1, ?)) > 0\n" // 3
				+ "  AND COALESCE(HWM_YEARLY, 0) = ?\n" // 4
				+ "FOR UPDATE\n"
		)) {
			stmt.setLong(1, dealerId);
			stmt.setString(2, chain);
			stmt.setInt(3, License.FUNC_WM_HOSTED);
			stmt.setShort(4, (short) (hwmYearly ? 1 : 0));
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) throw new RuntimeException();
				return rs.getInt(1) > 0;
			}
		}

	}

	// XXX temporary
	private boolean orderLicense(Connection conn, RequestType requestType, DbLicense license, DbLicense prevLicense,
			long licenseId) {
		try {
			untillTpapiHelper.orderLicense(conn, requestType, license, prevLicense);
			return true;
		} catch (Exception e) {
			LOG.warn("Order license error", e);
			return false;
		}
	}

	// XXX temporary
	private boolean paymentOrder(Connection conn, int tableNo) {
		try {
			untillTpapiHelper.closeOrder(conn, tableNo);
			return true;
		} catch (Exception e) {
			LOG.warn("Order license error", e);
			return false;
		}
	}

	  ////////////////////////////
	 // Private static methods //
	////////////////////////////

	private static boolean isEquals(String s1, String s2) {
		if (s1 == null)
			return s2 == null;
		return s1.equals(s2);
	}

	private static boolean isExistsLicenseRequestForLicense(Connection conn, long licenseId) throws SQLException {
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID\n"
				+ "FROM LICENSES\n"
				+ "WHERE ID_LICENSES_PREV = ? AND REQUEST_ACTIVE = 1\n"
				+ "  AND BIN_AND(STATUS, " + License.DESCRIPTION_BOOST_BIT + ") = 0\n")
		) {
			stmt.setLong(1, licenseId);
			try (ResultSet rs = stmt.executeQuery()) {
				return rs.next();
			}
		}
	}

	// Check if record with the same hard code is already exists
	private static boolean isLicenseDuplicateHardCode(Connection conn, DbLicense license,
			Long prevLicenseId) throws SQLException
	{
		LOG.debug("ENTRY ({}, {})", license, prevLicenseId);

		boolean result;
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID\n"
				+ "FROM LICENSES\n"
				+ "WHERE IS_ACTIVE = 1\n"
				+ "  AND BIN_AND(STATUS, " + License.DESCRIPTION_BOOST_BIT + ") = 0\n"
				+ "  AND HARD_CODE = ?\n"                           // 1
				+ (prevLicenseId != null ? "  AND ID <> ?\n" : "")) // 2
		) {
			stmt.setString(1, license.hardCode);
			if (prevLicenseId != null)
				stmt.setLong(2, prevLicenseId.longValue());
			try (ResultSet rs = stmt.executeQuery()) {
				result = rs.next();
			}
		}

		LOG.debug("RETURN {}", result);
		return result;
	}

	// Check if license with same client name, address, email and phone already exists
	private static boolean isLicenseDuplicateClient(Connection conn, DbLicense license,
			Long prevLicenseId) throws SQLException
	{
		LOG.debug("ENTRY ({}, {})", license, prevLicenseId);

		boolean result;
		try (PreparedStatement stmt = conn.prepareStatement("SELECT ID\n"
				+ "FROM LICENSES\n"
				+ "WHERE IS_ACTIVE = 1\n"
				+ "  AND BIN_AND(STATUS, " + License.DESCRIPTION_BOOST_BIT + ") = 0\n"
				+ "  AND UPPER(CLIENT_NAME) = UPPER(?)\n"           // 1
				+ "  AND UPPER(CLIENT_ADDRESS) = UPPER(?)\n"        // 2
				+ "  AND UPPER(CLIENT_EMAIL) = UPPER(?)\n"          // 3
				+ "  AND UPPER(CLIENT_PHONE) = UPPER(?)\n"          // 4
				+ (prevLicenseId != null ? "  AND ID <> ?\n" : "")) // 5
		) {
			stmt.setString(1, license.getClientName());
			stmt.setString(2, license.getClientAddress());
			stmt.setString(3, license.getClientEmail());
			stmt.setString(4, license.getClientPhone());
			if (prevLicenseId != null)
				stmt.setLong(5, prevLicenseId.longValue());
			try (ResultSet rs = stmt.executeQuery()) {
				result = rs.next();
			}
		}

		LOG.debug("RETURN {}", result);
		return result;
	}

	public CommonRpcResult createCreditInvoice(Connection conn, Dealer dealer, long dealerId,
			List<RrOrderItem> orderItems, String clientName) throws SQLException, LicenseServiceException {
		// TODO use UntillTpapiHelperImpl

		// Check parameters
		if (orderItems == null || orderItems.isEmpty())
			throw new InvalidArgumentException("orderItems");

		boolean oldAutoCommit = conn.getAutoCommit();
		conn.setAutoCommit(false);
		try {

			// Create order
			int tableNo;
			long billId;
			try {
				List<TOrderItem> items = new ArrayList<TOrderItem>(orderItems.size() * 2);
				int i = 0;
				for (RrOrderItem orderItem : orderItems) {
					items.add(new TOrderItem(i++, orderItem.articleId, 0, "", orderItem.price, orderItem.quantity, new TExtraInfo[0]));
					if (orderItem.text != null && !orderItem.text.isEmpty())
						items.add(new TOrderItem(i++, 0, 6, orderItem.text, 0, 0, new TExtraInfo[0]));
				}
				tableNo = untillTpapiHelper.createOrderOnNewTable(conn, items, dealerId);
				billId = untillTpapiHelper.getActiveBillIdByTableNo(conn, tableNo);
			} catch (Exception e) {
				LOG.warn("Create order error", e);
				return new CommonRpcResult("Create order error");
			}

			// Generate invoice, close order and increasing next (negative) invoice number transactionally
			Invoice invoice;
			synchronized (commonHelper.getSettingsLock()) {
				Settings settings = commonHelper.getSettings();

				// Generate invoice by billId
				invoice = invoiceHelper.generateInvoiceForDealer(conn, settings, commonHelper.getCurrentDate(),
						billId, dealerId, clientName);

				if (invoice == null) {
					return new CommonRpcResult("Error generating invoice");
				}

				// Close order (pay)
				try {
					untillTpapiHelper.closeOrder(conn, tableNo);
				} catch (Exception e) {
					invoice.getFile().delete();
					LOG.warn("Close order error", e);
					return new CommonRpcResult("Close order error");
				}

				// Set next invoice number and save
				if (invoice.isPositive()) {
					settings.setNextInvoiceNumber(invoice.getNumber() + 1);
				} else {
					settings.setNextNegativeInvoiceNumber(invoice.getNumber() + 1);
				}
				settings.save();
			}

			// Get license id for new license
			long licenseId;
			try (
				PreparedStatement stmt = conn.prepareStatement("SELECT * FROM GENERATE_TABLE_ID\n");
				ResultSet rs = stmt.executeQuery();
			) {
				if (!rs.next())
					throw new DbErrorException();
				licenseId = rs.getLong(1);
			}

			// Create fake license for Credit Invoice
			//                                  1   2            3               4
			String sql = "INSERT INTO LICENSES (ID, CLIENT_NAME, CLIENT_ADDRESS, CLIENT_EMAIL, "
					// 5             6          7         8           9           10
					+ "CLIENT_PHONE, HARD_CODE, ACT_CODE, START_DATE, ISSUE_DATE, STATUS, "
					// 11          12       13                14            15
					+ "ID_DEALERS, ID_BILL, INVOICE_REQUIRED, INVOICE_DATE, INVOICE_NUMBER, "
					// 16             17         18                  19
					+ "REQUEST_PHASE, IS_ACTIVE, IS_ACTIVE_MODIFIED, IS_ACTIVE_MODIFIER)\n"
					//         1  2  3  4  5  6  7  8  9 10 11 12 13 14 15 16 17 18 19
					+ "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				int fld = 1;
				stmt.setLong(fld++, licenseId);      // 1
				stmt.setString(fld++, clientName);   // 2
				stmt.setNull(fld++, Types.VARCHAR);  // 3 (CLIENT_ADDRESS)
				stmt.setNull(fld++, Types.VARCHAR);  // 4 (CLIENT_EMAIL)
				stmt.setNull(fld++, Types.VARCHAR);  // 5 (CLIENT_PHONE)
				stmt.setString(fld++, "");           // 6 (HARD_CODE)
				stmt.setString(fld++, "");           // 7 (ACT_CODE)
				java.sql.Date invoiceSqlDate = new java.sql.Date(invoice.getDate().getTime());
				stmt.setDate(fld++, invoiceSqlDate); // 8 (START_DATE)
				stmt.setDate(fld++, invoiceSqlDate); // 9 (ISSUE_DATE)
				stmt.setShort(fld++, (short) 0);     // 10 (STATUS)
				stmt.setLong(fld++, dealerId);       // 11
				stmt.setLong(fld++, billId);         // 12
				stmt.setShort(fld++, (short) 0);     // 13 (INVOICE_REQUIRED)
				stmt.setDate(fld++, invoiceSqlDate); // 14
				stmt.setLong(fld++, (invoice.isPositive() ? +1 : -1) * invoice.getNumber()); // 15
				stmt.setString(fld++, "-CRI");       // 16 (REQUEST_PHASE)
				stmt.setShort(fld++, (short) 0);     // 17 (IS_ACTIVE)
				stmt.setTimestamp(fld++, new Timestamp(commonHelper.getCurrentDate().getTime())); // 18 (IS_ACTIVE_MODIFIED)
				stmt.setString(fld++, "UntillLicenseServer"); // 19 (IS_ACTIVE_MODIFIER)

				if (stmt.executeUpdate() == 0)
					throw new DbErrorException();
			}

			conn.commit();

			// Send invoice to dealer
			if (invoice.getEmails() != null) {
				List<String> emailList = Common.parseEmailList(invoice.getEmails());
				if (!emailList.isEmpty()) {
					if (!emailHelper.emailInvoice(invoice.getFile(), invoice.getDate(),
							invoice.getFullNumber(), emailList, null, clientName)) {
						return new CommonRpcResult(true, "but email is not sent");
					}
				}
			}

			return new CommonRpcResult();

		} catch (Exception e) {
			conn.rollback();
			throw e;
		} finally {
			conn.setAutoCommit(oldAutoCommit);
		}
	}

	public List<RrArticle> getManualArticles(Connection conn, Dealer authorize,
			String departmentName) throws SQLException {

		List<RrArticle> result;
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get manual articles from DB
			stmt = conn.prepareStatement("SELECT A.ID, A.ARTICLE_NUMBER, A.NAME\n" +
					"FROM ARTICLES A\n" +
					"INNER JOIN DEPARTMENT D ON D.ID = A.ID_DEPARTAMENT\n" +
					"INNER JOIN DEPARTMENT_AVAILABLE DA ON DA.ID_DEPARTMENT = D.ID\n" +
					"INNER JOIN SALES_AREA SA ON SA.ID = DA.ID_SALES_AREA\n" +
					"WHERE A.IS_ACTIVE = 1 AND D.IS_ACTIVE = 1 AND DA.IS_ACTIVE = 1 AND SA.IS_ACTIVE = 1\n" +
					"AND UPPER(D.NAME) = UPPER(?) AND COALESCE(A.PROMO, 0) <> 1\n" +
					"UNION\n" +
					"SELECT A.ID, A.ARTICLE_NUMBER, A.NAME\n" +
					"FROM DEPARTMENT_ARTICLES DAR\n" +
					"INNER JOIN ARTICLES A ON A.ID = DAR.ID_ARTICLES\n" +
					"INNER JOIN DEPARTMENT D ON D.ID = DAR.ID_DEPARTMENT\n" +
					"INNER JOIN DEPARTMENT_AVAILABLE DA ON DA.ID_DEPARTMENT = D.ID\n" +
					"INNER JOIN SALES_AREA SA ON SA.ID = DA.ID_SALES_AREA\n" +
					"WHERE DAR.IS_ACTIVE = 1 AND A.IS_ACTIVE = 1 AND D.IS_ACTIVE = 1 AND DA.IS_ACTIVE = 1 AND SA.IS_ACTIVE = 1\n" +
					"AND UPPER(D.NAME) = UPPER(?) AND COALESCE(A.PROMO, 0) <> 1\n" +
					"ORDER BY 2\n");
			stmt.setString(1, departmentName);
			stmt.setString(2, departmentName);
			rs = stmt.executeQuery();

			// Get results
			result = new ArrayList<RrArticle>();
			while (rs.next()) {
				result.add(new RrArticle(rs.getLong("ID"), rs.getInt("ARTICLE_NUMBER"), rs.getString("NAME")));
			}
			rs.close(); rs = null; stmt.close(); stmt = null;

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		return result;
	}

	public List<String> getChains(Connection conn, Dealer dealer, long dealerId, boolean onlyOnline) throws SQLException {

		// Parameters "dealerId" only for super dealer
		if (!dealer.superDealer) {
			dealerId = dealer.id;
		}

		List<String> result = new ArrayList<>();

//		boolean oldAutoCommit = conn.getAutoCommit();
//		conn.setAutoCommit(false);
//		try {

			String sql = "SELECT MAX(CHAIN), COUNT(*) FROM LICENSES\n"
					+ "WHERE (IS_ACTIVE > 0 OR REQUEST_ACTIVE > 0)\n"
					+ (dealerId != 0 ? "  AND ID_DEALERS = ?\n" : "")
					+ (onlyOnline ? " AND BIN_AND(STATUS, ?) <> 0\n" : "")
					+ "  AND CHAIN >= ''\n"
					+ "GROUP BY UPPER(CHAIN)\n"
					+ "ORDER BY COUNT(*) DESC\n";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				int parIndex = 1;
				if (dealerId != 0)
					stmt.setLong(parIndex++, dealerId);
				if (onlyOnline)
					stmt.setShort(parIndex++, DbLicense.ONLINE_BIT);
				try (ResultSet rs = stmt.executeQuery()) {
					while (rs.next()) {
						result.add(rs.getString(1));
					}
				}
			}

//			conn.commit();
//		} catch (Exception e) {
//			conn.rollback();
//			throw e;
//		} finally {
//			conn.setAutoCommit(oldAutoCommit);
//		}

		return result;
	}

	// TODO ? move to new class
	/*
	 * Auto invoicing (approving)
	 */
	public void autoApproving(Connection conn) throws SQLException, LicenseServiceException {
		while (true) {

			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				// get date now-7d
				Calendar cal = Calendar.getInstance();
				cal.setTime(commonHelper.getCurrentDate());
				cal.add(Calendar.DATE, -7);

				// Get one licenses with NON_APPROVED = 2
				long licenseId = 0;
				int tableNo = 0;
				boolean nothingFound = true;
				try (PreparedStatement stmt = conn.prepareStatement("SELECT FIRST 1 *\n"
					+ "FROM LICENSES\n"
					+ "WHERE COALESCE(IS_ACTIVE, 0) <> 0\n"
					+ "  AND COALESCE(NON_APPROVED, 0) = 2\n"
					+ "  AND CONFIRM_DATE < ?\n"
					+ "ORDER BY ID\n") // DESC
				) {
					stmt.setDate(1, new java.sql.Date(cal.getTimeInMillis()));
					try (ResultSet rs = stmt.executeQuery()) {
						if (rs.next()) {
							licenseId = rs.getLong("ID");
							tableNo = rs.getInt("REQUEST_TABLENO");
							nothingFound = false;
						}
					}
				}
				if (nothingFound) {
					conn.commit(); // or rollback()
					break;
				}

				// TODO log: start approving

				approveLicense(conn, licenseId, tableNo, false);

				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}

			// TODO log: finish approving

		} // while (true)
	}

	// TODO ? move to new class
	/*
	 * Auto prolong (renewal)
	 */
	public void autoProlong(Connection conn) throws SQLException, LicenseServiceException, RequiredDealerIsNotFoundException {
		LocalDate currentDate = LocalDateTime
				.ofInstant(Common.toUtcDateOnly(commonHelper.getCurrentDate()).toInstant(), ZoneOffset.UTC)
				.toLocalDate();

		LocalDate expectedExpiredDateForSaas;
		if (currentDate.getDayOfMonth() < commonHelper.getSettings().getAutoProlongFromDay())
			expectedExpiredDateForSaas = LocalDate.MIN; // skip for Saas
		else
			expectedExpiredDateForSaas = currentDate.withDayOfMonth(currentDate.lengthOfMonth());

		LocalDate expectedExpiredDate = currentDate.plusDays(commonHelper.getSettings().getAutoProlongDaysBeforeExpired());

		while (true) {

			// Get one licenses with AUTO_PROLONGABLE = 1
			DbLicense license = null;
			boolean found = false;
			try (PreparedStatement stmt = conn.prepareStatement("SELECT FIRST 1 *\n"
				+ "FROM LICENSES\n"
				+ "WHERE IS_ACTIVE = 1\n"
				+ "  AND AUTO_PROLONGABLE = 1\n"
				+ "  AND COALESCE(NON_APPROVED, 0) = 0\n"
//				+ "  AND EXPIRED_DATE <= ?\n"
				+ "  AND (\n"
				+ "    BIN_AND(STATUS, " + DbLicense.DESCRIPTION_SAAS_BIT + ") > 0 AND EXPIRED_DATE <= ?\n"
				+ "    OR BIN_AND(STATUS, " + DbLicense.DESCRIPTION_SAAS_BIT + ") = 0 AND EXPIRED_DATE <= ?\n"
				+ "  )\n"
				+ "  AND NOT EXISTS (SELECT LP.ID FROM LICENSES LP\n"
				+ "    WHERE LP.ID_LICENSES_PREV = LICENSES.ID AND LP.REQUEST_ACTIVE = 1)\n"
				+ "ORDER BY ID\n") // DESC
			) {
				stmt.setDate(1, java.sql.Date.valueOf(expectedExpiredDateForSaas)); // TODO test it
				stmt.setDate(2, java.sql.Date.valueOf(expectedExpiredDate));
				try (ResultSet rs = stmt.executeQuery()) {
					if (rs.next()) {
						license = Common.getDbLicenseFromResultSet(rs);
						found = true;
					}
				}
			}
			if (!found) {
				break;
			}

			long licenseId = license.id;
			String appendToComments;

			// Check expired date and restrictions
			LocalDate licenseExpiredDate = license.getExpiredDate() instanceof java.sql.Date
					? ((java.sql.Date) license.getExpiredDate()).toLocalDate()
					: LocalDateTime.ofInstant(license.getExpiredDate().toInstant(), ZoneOffset.UTC).toLocalDate();
			if (license.isSaas() && !expectedExpiredDateForSaas.isEqual(licenseExpiredDate)) {
				appendToComments = "Auto-prolong canceled: expired or does not correspond to the end of the month";
				LOG.warn(appendToComments + "\n\tlicense: {}\n\tlicenseExpiredDate: {}", license, licenseExpiredDate);
			} else if (Restrictions.AUTO_PROLONG_ONLY_FOR_SAAS && !license.isSaas()) {
				appendToComments = "Auto-prolong canceled: only for SaaS licenses";
				LOG.warn(appendToComments + "\n\tlicense: {}", license);
			} else if (Restrictions.AUTO_PROLONG_ONLY_FOR_ONLINE && !license.isOnline()) {
				appendToComments = "Auto-prolong canceled: only for online licenses";
				LOG.warn(appendToComments + "\n\tlicense: {}", license);
			} else {

				Dealer dealer = new Dealer(conn, license.idDealers);

				// modify license dates
				LicenseType licenseType = license.isApostill() ?
						(license.isTrial() ? LicenseType.APOSTILL_TRIAL : LicenseType.APOSTILL)
						: (license.isTrial() ? LicenseType.DEMO : (license.isSaas() ? LicenseType.SAAS : LicenseType.GENERAL));
				LicensePeriod newLicensePeriod = getProlongLicensePeriod(licenseType,
						new LicensePeriod(license.getStartDate(), license.getExpiredDate()));
				license.setStartDate(newLicensePeriod.getStartDate());
				license.setExpiredDate(newLicensePeriod.getExpiredDate());

				// apply PROLONG_PARAMS
				if (license.prolongParams != null && license.prolongParams.getLbConnections() != null)
					license.setLbConnections(license.prolongParams.getLbConnections());
				if (license.prolongParams != null && license.prolongParams.getRemConnections() != null)
					license.setRemConnections(license.prolongParams.getRemConnections());
				if (license.prolongParams != null && license.prolongParams.getOmConnections() != null)
					license.setOmConnections(license.prolongParams.getOmConnections());
//				if (license.prolongParams != null && license.prolongParams.getHqConnections() != null)
//					license.setHqConnections(license.prolongParams.getHqConnections());
				if (license.prolongParams != null && license.prolongParams.getPsConnections() != null)
					license.setPsConnections(license.prolongParams.getPsConnections());
				if (license.prolongParams != null && license.prolongParams.getHeConnections() != null)
					license.setHeConnections(license.prolongParams.getHeConnections());

				try {
					// XXX requestLicense not compatible with transaction
					RequestLicenseResult requestResult = requestLicense(conn, dealer, license, RequestType.PROLONG, true);
					if (requestResult.getResult() != Results.SUCCESS) {
						appendToComments = "Auto-prolong error (request): " + (requestResult.getResult() != null ? requestResult.getResult().toString() : "");
						if (requestResult.getUnexpectedMessage() != null && !requestResult.getUnexpectedMessage().isEmpty())
							appendToComments += " (" + requestResult.getUnexpectedMessage() + ")";
						LOG.warn(appendToComments + "\n\tlicense: {}", license);
					} else {
						String confirmUid = null;
						int tableNo = 0;
						boolean found2 = false;
						try (PreparedStatement stmt = conn.prepareStatement("SELECT FIRST 1 *\n"
								+ "FROM LICENSES\n"
								+ "WHERE ID = ?\n")
						) {
							stmt.setLong(1, license.id);
							try (ResultSet rs = stmt.executeQuery()) {
								if (rs.next()) {
									confirmUid = rs.getString("CONFIRM_UID");
									tableNo = rs.getInt("REQUEST_TABLENO");
									found2 = true;
								}
							}
						}
						if (!found2) {
							appendToComments = "Auto-prolong error: requested license has disappeared";
							LOG.warn(appendToComments + "\n\tlicense: {}", license);
						} else {
							ConfirmOrderResult confirmResult = confirmOrder(conn, confirmUid, false, null, true);
							if (confirmResult.result != ConfirmOrderResult.Result.SUCCESS) {
								appendToComments = "Auto-prolong error (confirm): " + (confirmResult.result != null ? confirmResult.result.toString() : "");
								if (confirmResult.errorMessage != null && !confirmResult.errorMessage.isEmpty())
									appendToComments += " (" + confirmResult.errorMessage + ")";
								LOG.warn(appendToComments + "\n\tlicense: {}", license);
							} else {
								approveLicense(conn, license.id, tableNo, false);
								SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								appendToComments = "Auto-prolonged " + dateFormat.format(commonHelper.getCurrentDate());
								LOG.info(appendToComments + "\n\tlicense: {}", license);
							}
						}
					}
				} catch (LicenseServiceException e) {
					appendToComments = "Auto-prolong error: " + e.getMessage();
					LOG.warn(appendToComments + "\n\tlicense: {}", license);
				}

			}

			// update this license (set AUTO_PROLONGABLE = 0)
			try (PreparedStatement stmt = conn.prepareStatement("UPDATE LICENSES\n"
					+ "SET AUTO_PROLONGABLE = 0, COMMENTS = COALESCE(COMMENTS, '') || ?\n"
					+ "WHERE ID = ?\n")
			) {
				stmt.setString(1, appendToComments + "\r\n");
				stmt.setLong(2, licenseId);
				if (stmt.executeUpdate() == 0) throw new RuntimeException();
			}
			conn.commit(); // XXX just in case (if autocommit is not enabled)

		} // while (true)

	}

}
