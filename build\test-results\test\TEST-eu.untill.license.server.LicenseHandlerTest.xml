<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="eu.untill.license.server.LicenseHandlerTest" tests="13" skipped="1" failures="1" errors="0" timestamp="2025-08-06T13:09:01.214Z" hostname="UNP-WS2" time="0.142">
  <properties/>
  <testcase name="testGetClientNames" classname="eu.untill.license.server.LicenseHandlerTest" time="0.007"/>
  <testcase name="testRequestLicense_upgradeToDefinitive" classname="eu.untill.license.server.LicenseHandlerTest" time="0.025"/>
  <testcase name="testAutoProlong_badExpiryDate" classname="eu.untill.license.server.LicenseHandlerTest" time="0.013"/>
  <testcase name="testConfirmOrder" classname="eu.untill.license.server.LicenseHandlerTest" time="0.008">
    <failure message="java.lang.AssertionError: expected:&lt;SUCCESS&gt; but was:&lt;SUCCESS_BUT_FAIL_SEND_MESSAGE&gt;" type="java.lang.AssertionError">java.lang.AssertionError: expected:&lt;SUCCESS&gt; but was:&lt;SUCCESS_BUT_FAIL_SEND_MESSAGE&gt;
	at org.junit.Assert.fail(Assert.java:88)
	at org.junit.Assert.failNotEquals(Assert.java:743)
	at org.junit.Assert.assertEquals(Assert.java:118)
	at org.junit.Assert.assertEquals(Assert.java:144)
	at eu.untill.license.server.LicenseHandlerTest.testConfirmOrder(LicenseHandlerTest.java:748)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:47)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:44)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at eu.untill.license.server.TestDb$1.evaluate(TestDb.java:162)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:271)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:70)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:50)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:238)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:63)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:236)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:53)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:229)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:309)
	at org.mockito.internal.runners.JUnit45AndHigherRunnerImpl.run(JUnit45AndHigherRunnerImpl.java:37)
	at org.mockito.runners.MockitoJUnitRunner.run(MockitoJUnitRunner.java:62)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at com.sun.proxy.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</failure>
  </testcase>
  <testcase name="testConfirmOrder_wrongRequest" classname="eu.untill.license.server.LicenseHandlerTest" time="0.009"/>
  <testcase name="testGetClientNames_fromEmptyDb" classname="eu.untill.license.server.LicenseHandlerTest" time="0.007"/>
  <testcase name="testRequestLicense_definitive" classname="eu.untill.license.server.LicenseHandlerTest" time="0.005">
    <skipped message="got: &lt;false&gt;, expected: is &lt;true&gt;" type="org.junit.internal.AssumptionViolatedException">org.junit.internal.AssumptionViolatedException: got: &lt;false&gt;, expected: is &lt;true&gt;
	at org.junit.Assume.assumeThat(Assume.java:95)
	at org.junit.Assume.assumeTrue(Assume.java:41)
	at org.junit.Assume.assumeFalse(Assume.java:48)
	at eu.untill.license.server.LicenseHandlerTest.testRequestLicense_definitive(LicenseHandlerTest.java:441)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:47)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:44)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at eu.untill.license.server.TestDb$1.evaluate(TestDb.java:162)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:271)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:70)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:50)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:238)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:63)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:236)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:53)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:229)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:309)
	at org.mockito.internal.runners.JUnit45AndHigherRunnerImpl.run(JUnit45AndHigherRunnerImpl.java:37)
	at org.mockito.runners.MockitoJUnitRunner.run(MockitoJUnitRunner.java:62)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at com.sun.proxy.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
</skipped>
  </testcase>
  <testcase name="testRequestLicense_badLicenses" classname="eu.untill.license.server.LicenseHandlerTest" time="0.012"/>
  <testcase name="testRequestLicense_upgradeToDefinitiveByOrdinaryDealer" classname="eu.untill.license.server.LicenseHandlerTest" time="0.006"/>
  <testcase name="testConfirmOrder_cancel" classname="eu.untill.license.server.LicenseHandlerTest" time="0.007"/>
  <testcase name="testAutoProlong_beforeAutoProlongFromDay" classname="eu.untill.license.server.LicenseHandlerTest" time="0.002"/>
  <testcase name="testRequestLicense" classname="eu.untill.license.server.LicenseHandlerTest" time="0.012"/>
  <testcase name="testAutoProlong" classname="eu.untill.license.server.LicenseHandlerTest" time="0.021"/>
  <system-out><![CDATA[16:09:01.256 [Test worker] WARN  e.u.license.server.LicenseHandler autoProlong : Auto-prolong canceled: expired or does not correspond to the end of the month
	license: [DbLicense: id=**********; client="SaaS test"; hardCode=SAASCLIENTLICENSEHARDC0D8; connections=3,3,3,3,1; dates=1538352000000,1538352000000,1540857600000; funcLimited=0; status=641]
	licenseExpiredDate: 2018-10-30
16:09:01.297 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=0]
16:09:01.297 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status
	license: [DbLicense: id=0; client="Good License Client"; hardCode=8UWLY1NCY823YA5DVFHWWC1F6; connections=unlimited; dates=1754438400000,null; funcLimited=0; status=-1]
16:09:01.298 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license for the period not appointed by server
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438399000,null,1785888000000; funcLimited=0; status=1]
	expectedLicensePeriod: [Wed Aug 06 03:00:00 MSK 2025; Wed Aug 05 03:00:00 MSK 2026]
16:09:01.300 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license for the period not appointed by server
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888001000; funcLimited=0; status=1]
	expectedLicensePeriod: [Wed Aug 06 03:00:00 MSK 2025; Wed Aug 05 03:00:00 MSK 2026]
16:09:01.300 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with wrong period
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1754438399000; funcLimited=0; status=1]
16:09:01.300 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with wrong period
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=null,null,null; funcLimited=0; status=1]
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (create) and license type (apostill)
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (emergency) and license type (apostill)
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (create) and license type (apostill_trial)
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (prolong) and license type (apostill_trial)
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (upgrade) and license type (apostill_trial)
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (emergency) and license type (apostill_trial)
16:09:01.301 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (create) and license type (demo)
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (upgrade) and license type (demo)
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (emergency) and license type (demo)
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=513]
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=XFWV82MLPLD1RVHCAWESEUGG0; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=673]
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=KJ71NBULLDDHEJCW3XNEM7VU8; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=705]
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=1025]
16:09:01.302 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=1185]
16:09:01.303 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=1217]
16:09:01.303 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with the wrong status combination
	license: [DbLicense: id=0; client="Good License Client"; hardCode=G00DCLIENTLICENSEHARDC0D7; connections=3,3,3,3,0; dates=1754438400000,null,1785888000000; funcLimited=0; status=1665]
16:09:01.303 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (create) and license type (boost)
16:09:01.303 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (prolong) and license type (boost)
16:09:01.303 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (upgrade) and license type (boost)
16:09:01.303 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Attempt to request the license with unacceptability request type (emergency) and license type (boost)
16:09:01.310 [Test worker] WARN  e.u.license.server.LicenseHandler requestLicense : Activated restriction ONLY_SUPER_DEALER_CAN_UPGRADE_TO_DEFINITIVE
	license: [DbLicense: id=2; client="Test Client 1"; hardCode=TEST1HARDCODE000000000007; connections=1,9,5,7,0; dates=1330560000000,1754438400000,1785888000000; funcLimited=4928; status=17]
16:09:01.350 [Test worker] INFO  e.u.license.server.LicenseHandler autoProlong : Auto-prolonged 2018-10-16 04:30:00
	license: [DbLicense: id=**********; client="SaaS test"; hardCode=SAASCLIENTLICENSEHARDC0D8; connections=3,3,3,3,3; dates=1538352000000,1539648000000,1543536000000; funcLimited=0; status=641]
]]></system-out>
  <system-err><![CDATA[16:09:01.266 [Test worker] ERROR e.u.license.server.LicenseHandler confirmOrder : Generate/Send license error
java.lang.NullPointerException: null
	at eu.untill.license.shared.DbLicense.getLicVer(DbLicense.java:87)
	at eu.untill.license.shared.DbLicense.isOldLicReq(DbLicense.java:63)
	at eu.untill.license.server.LicenseHandler.confirmOrder(LicenseHandler.java:2513)
	at eu.untill.license.server.LicenseHandlerTest.testConfirmOrder(LicenseHandlerTest.java:747)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.runners.model.FrameworkMethod$1.runReflectiveCall(FrameworkMethod.java:47)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.junit.runners.model.FrameworkMethod.invokeExplosively(FrameworkMethod.java:44)
	at org.junit.internal.runners.statements.InvokeMethod.evaluate(InvokeMethod.java:17)
	at eu.untill.license.server.TestDb$1.evaluate(TestDb.java:162)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:271)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:70)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:50)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:238)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:63)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:236)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:53)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:229)
	at org.junit.internal.runners.statements.RunBefores.evaluate(RunBefores.java:26)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:309)
	at org.mockito.internal.runners.JUnit45AndHigherRunnerImpl.run(JUnit45AndHigherRunnerImpl.java:37)
	at org.mockito.runners.MockitoJUnitRunner.run(MockitoJUnitRunner.java:62)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.runTestClass(JUnitTestClassExecutor.java:112)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:58)
	at org.gradle.api.internal.tasks.testing.junit.JUnitTestClassExecutor.execute(JUnitTestClassExecutor.java:40)
	at org.gradle.api.internal.tasks.testing.junit.AbstractJUnitTestClassProcessor.processTestClass(AbstractJUnitTestClassProcessor.java:54)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.processTestClass(SuiteTestClassProcessor.java:53)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at com.sun.proxy.$Proxy4.processTestClass(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$2.run(TestWorker.java:183)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
]]></system-err>
</testsuite>
