/**
 * 
 */
package eu.untill.license.client;

import com.google.gwt.i18n.client.Constants;

import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;

//TODO 2 Implement and use this class
/**
 * <AUTHOR>
 *
 */
public class LicenseClientHelper {

	public static interface UlsConstants extends Constants {
		// TODO ...
	}

//	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();


	DbLicense license;

	public LicenseClientHelper(DbLicense license) {
		this.license = license;
	}

	public String getFormattedHardCode() {
		return HardCodeHelper.formatHardCode(license.hardCode);
	}


	// TODO ...

}
