<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Test SU WS</title>
</head>
<script type="text/javascript">
	var socket = new WebSocket("ws://" + window.location.hostname + ":"
			+ window.location.port + "/suws");

	socket.onopen = function() {
		alert("Connection is established");
	};

	socket.onclose = function(event) {
		if (event.wasClean) {
			alert('Connection is closed');
		} else {
			alert('Connection lost');
		}
		alert('Code: ' + event.code + ' reason: ' + event.reason);
	};
	socket.onmessage = function(event) {
		var logarea = document.getElementById("log");
		logarea.value = event.data + "\n" + logarea.value;
	};
	socket.onerror = function(error) {
		alert("Error: " + error.message);
	};

	function send() {
		var s = document.getElementById("in").value;
		socket.send(s);
	}
</script>
<body>
	<input type="text" id="in" />
	<input type="button" onclick="send()" value="send" />
	<br />
	<textarea id="log" rows="8" cols="23" ></textarea>
</body>
</html>