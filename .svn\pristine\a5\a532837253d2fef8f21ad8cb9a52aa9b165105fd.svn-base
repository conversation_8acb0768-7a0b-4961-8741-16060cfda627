package eu.untill.license.shared;

public class Restrictions {

	public static boolean DISABLE_CREATE_DEFINITIVE = true;
	public static boolean DEFINITIVE_DISABLE_UPGRADE = false;
	public static boolean DEFINITIVE_DISABLE_PROLONG = false;
	public static boolean DEFINITIVE_DISABLE_BOOST = true;

	public static boolean DISABLE_UPGRADE_TO_DEFINITIVE = false;
	public static boolean ONLY_SUPER_DEALER_CAN_UPGRADE_TO_DEFINITIVE = true;

	public static boolean DISABLE_SCM4J_DEPLOYER_ENGINE = false;
	public static boolean SU_SHOW_ALL_PRODUCTS = false;
	public static boolean SU_SHOW_ALL_PRODUCT_VERSIONS = false;
	public static boolean SU_HIDE_MANUAL_ACTION = true;

	public static boolean AUTO_PROLONG_ONLY_FOR_SAAS = false;
	public static boolean AUTO_PROLONG_ONLY_FOR_ONLINE = true;

	public static boolean ONLY_SUPER_DEALER_CAN_CHANGE_CLIENT_NAME = true;

	public static boolean SUPER_DEALER_CAN_CHANGE_HARD_CODE = true;
}
