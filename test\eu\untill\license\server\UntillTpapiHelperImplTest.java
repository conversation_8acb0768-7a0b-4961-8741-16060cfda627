package eu.untill.license.server;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.sql.Connection;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import eu.untill.license.shared.DbLicense;

public class UntillTpapiHelperImplTest {

	private Connection mockConn;
	private UntillTpapiHelperImpl spyHelper;
	private List<UntillTpapiHelperImpl.OrderItem> orderItems;

	@Before
	public void setUp() {
		mockConn = mock(Connection.class);
		UntillTpapiHelperImpl helper = new UntillTpapiHelperImpl(null, null);
		spyHelper = spy(helper);
		orderItems = new ArrayList<>();
	}

	@Test
	public void testCalcMonthsAndDaysBetween() {
		UntillTpapiHelperImpl.MothtsAndDays mothtsAndDays;

		mothtsAndDays = UntillTpapiHelperImpl.calcMonthsAndDaysBetween(
				Date.from(Instant.parse("2010-01-01T00:00:00Z")), Date.from(Instant.parse("2010-01-01T00:00:00Z")));
		assertEquals(0, mothtsAndDays.months);
		assertEquals(1, mothtsAndDays.days);

		mothtsAndDays = UntillTpapiHelperImpl.calcMonthsAndDaysBetween(
				Date.from(Instant.parse("2011-02-28T00:00:00Z")), Date.from(Instant.parse("2011-03-27T00:00:00Z")));
		assertEquals(1, mothtsAndDays.months);
		assertEquals(0, mothtsAndDays.days);

		mothtsAndDays = UntillTpapiHelperImpl.calcMonthsAndDaysBetween(
				Date.from(Instant.parse("2011-03-01T00:00:00Z")), Date.from(Instant.parse("2011-03-31T00:00:00Z")));
		assertEquals(1, mothtsAndDays.months);
		assertEquals(0, mothtsAndDays.days);

		mothtsAndDays = UntillTpapiHelperImpl.calcMonthsAndDaysBetween(
				Date.from(Instant.parse("2011-02-28T00:00:00Z")), Date.from(Instant.parse("2011-03-31T00:00:00Z")));
		assertEquals(1, mothtsAndDays.months);
		assertEquals(4, mothtsAndDays.days);

		mothtsAndDays = UntillTpapiHelperImpl.calcMonthsAndDaysBetween(
				Date.from(Instant.parse("2013-02-06T00:00:00Z")), Date.from(Instant.parse("2014-01-17T00:00:00Z")));
		assertEquals(11, mothtsAndDays.months);
		assertEquals(12, mothtsAndDays.days);

		mothtsAndDays = UntillTpapiHelperImpl.calcMonthsAndDaysBetween(
				Date.from(Instant.parse("2014-01-02T00:00:00Z")), Date.from(Instant.parse("2016-01-30T00:00:00Z")));
		assertEquals(24, mothtsAndDays.months);
		assertEquals(29, mothtsAndDays.days);
	}

	@Test
	public void testLAddLicenseArticles_BasicLicense() throws Exception {
		// Setup basic license
		DbLicense license = new DbLicense();
		license.setLbConnections((short) 2);
		license.setRemConnections((short) 1);
		license.setOmConnections((short) 1);
		license.setPsConnections((short) 1);
		license.setFunction(DbLicense.FUNC_STOCK, true);
		license.setDefinitive(true);

		// Mock getArticleIdByBarcode calls
		doReturn(100L).when(spyHelper).getArticleIdByBarcode(mockConn, "2LOC1Y");
		doReturn(101L).when(spyHelper).getArticleIdByBarcode(mockConn, "REM1Y");
		doReturn(102L).when(spyHelper).getArticleIdByBarcode(mockConn, "1HH1Y");
		doReturn(103L).when(spyHelper).getArticleIdByBarcode(mockConn, "PS1Y");
		doReturn(104L).when(spyHelper).getArticleIdByBarcode(mockConn, "STOCK1Y");
		doReturn(105L).when(spyHelper).getArticleIdByBarcode(mockConn, "NU");

		// Execute
		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		// Verify
		assertEquals(6, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 100L && item.quantity == 1 && item.baseDuration == 1));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 101L && item.quantity == 1 && item.baseDuration == 1));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 102L && item.quantity == 1 && item.baseDuration == 1));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 103L && item.quantity == 1 && item.baseDuration == 1));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 104L && item.quantity == 1 && item.baseDuration == 1));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 105L && item.quantity == 1 && item.baseDuration == 0));
	}

	@Test
	public void testLAddLicenseArticles_ApostillLicense() throws Exception {
		DbLicense license = new DbLicense();
		license.setApostill(true);
		license.setLbConnections((short) 3);
		license.setFunction(DbLicense.FUNC_KITCHENSCREEN, true);

		doReturn(200L).when(spyHelper).getArticleIdByBarcode(mockConn, "APOSTILL3Y");
		doReturn(201L).when(spyHelper).getArticleIdByBarcode(mockConn, "A3LOC3Y");
		doReturn(202L).when(spyHelper).getArticleIdByBarcode(mockConn, "AKS3Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(3, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 200L && item.baseDuration == 3));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 201L && item.baseDuration == 3));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 202L && item.baseDuration == 3));
	}

	@Test
	public void testLAddLicenseArticles_ModuledLicense() throws Exception {
		DbLicense license = new DbLicense();
		license.setModuled(true);
		license.setLbConnections((short) 2);
		license.setFunction(DbLicense.FUNC_MODULE_BASIC, true);
		license.setFunction(DbLicense.FUNC_MODULE_A, true);

		doReturn(300L).when(spyHelper).getArticleIdByBarcode(mockConn, "2MODBAS1Y");
		doReturn(301L).when(spyHelper).getArticleIdByBarcode(mockConn, "2MODA1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(2, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 300L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 301L));
	}

	@Test
	public void testLAddLicenseArticles_AllConnectionTypes() throws Exception {
		DbLicense license = new DbLicense();
		license.setLbConnections((short) 2);
		license.setRemConnections((short) 3);
		license.setOmConnections((short) 4);
		license.setPsConnections((short) 2);
		license.setHeConnections((short) 1);

		doReturn(400L).when(spyHelper).getArticleIdByBarcode(mockConn, "2LOC1Y");
		doReturn(401L).when(spyHelper).getArticleIdByBarcode(mockConn, "REM1Y");
		doReturn(402L).when(spyHelper).getArticleIdByBarcode(mockConn, "4HH1Y");
		doReturn(403L).when(spyHelper).getArticleIdByBarcode(mockConn, "PS1Y");
		doReturn(404L).when(spyHelper).getArticleIdByBarcode(mockConn, "1HE1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(5, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 401L && item.quantity == 3));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 403L && item.quantity == 2));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 404L && item.sameRenewalPrice == true));
	}

	@Test
	public void testLAddLicenseArticles_CoreFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_GLOBEEXACT, true);
		license.setFunction(DbLicense.FUNC_HOTELCONCEPTS, true);
		license.setFunction(DbLicense.FUNC_STOCK, true);
		license.setFunction(DbLicense.FUNC_KITCHENSCREEN, true);
		license.setFunction(DbLicense.FUNC_EFT_INTERFACE, true);
		license.setFunction(DbLicense.FUNC_RESERVATIONS, true);
		license.setFunction(DbLicense.FUNC_MUSICPLAYER, true);
		license.setFunction(DbLicense.FUNC_EMENU, true);

		doReturn(500L).when(spyHelper).getArticleIdByBarcode(mockConn, "AI1Y");
		doReturn(501L).when(spyHelper).getArticleIdByBarcode(mockConn, "HCI1Y");
		doReturn(502L).when(spyHelper).getArticleIdByBarcode(mockConn, "STOCK1Y");
		doReturn(503L).when(spyHelper).getArticleIdByBarcode(mockConn, "KS1Y");
		doReturn(504L).when(spyHelper).getArticleIdByBarcode(mockConn, "CPINI1Y");
		doReturn(505L).when(spyHelper).getArticleIdByBarcode(mockConn, "RESERV1Y");
		doReturn(506L).when(spyHelper).getArticleIdByBarcode(mockConn, "MPLAYER1Y");
		doReturn(507L).when(spyHelper).getArticleIdByBarcode(mockConn, "EMENU1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(8, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 500L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 501L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 502L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 503L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 504L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 505L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 506L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 507L));
	}

	@Test
	public void testLAddLicenseArticles_BeverageFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_BEVERAGE_CONTROL, true);
		license.setFunction(DbLicense.FUNC_BERG, true);
		license.setFunction(DbLicense.FUNC_BECONET, true);
		license.setFunction(DbLicense.FUNC_FRIJADO, true);

		doReturn(600L).when(spyHelper).getArticleIdByBarcode(mockConn, "BAI1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		// All beverage functions should map to single BAI article
		assertEquals(1, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 600L));
	}

	@Test
	public void testLAddLicenseArticles_WarehouseManagementFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_WM_REST, true);
		license.setFunction(DbLicense.FUNC_WM_OFF, true);
		license.setFunction(DbLicense.FUNC_WM_HQ, true);
		license.setFunction(DbLicense.FUNC_WM_CENTRAL_BO, true);
		license.setFunction(DbLicense.FUNC_WM_HOSTED, true);

		doReturn(700L).when(spyHelper).getArticleIdByBarcode(mockConn, "WMREST1Y");
		doReturn(701L).when(spyHelper).getArticleIdByBarcode(mockConn, "WMOFF1Y");
		doReturn(702L).when(spyHelper).getArticleIdByBarcodeIfExist(mockConn, "WMHQWH1Y");
		doReturn(703L).when(spyHelper).getArticleIdByBarcodeIfExist(mockConn, "WMCBOWH1Y");
		doReturn(704L).when(spyHelper).getArticleIdByBarcode(mockConn, "HWM1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(5, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 700L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 701L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 702L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 703L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 704L));
	}

	@Test
	public void testLAddLicenseArticles_WarehouseManagement_NonHosted() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_WM_HQ, true);
		license.setFunction(DbLicense.FUNC_WM_CENTRAL_BO, true);
		// FUNC_WM_HOSTED is false

		doReturn(710L).when(spyHelper).getArticleIdByBarcode(mockConn, "WMHQ1Y");
		doReturn(711L).when(spyHelper).getArticleIdByBarcode(mockConn, "WMCBO1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(2, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 710L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 711L));
	}

	@Test
	public void testLAddLicenseArticles_ReserveFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_RESERVE1, true);
		license.setFunction(DbLicense.FUNC_RESERVE2, true);
		license.setFunction(DbLicense.FUNC_RESERVE3, true);

		doReturn(800L).when(spyHelper).getArticleIdByBarcode(mockConn, "RES11Y");
		doReturn(801L).when(spyHelper).getArticleIdByBarcode(mockConn, "RES21Y");
		doReturn(802L).when(spyHelper).getArticleIdByBarcode(mockConn, "RES31Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(3, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 800L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 801L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 802L));
	}

	@Test
	public void testLAddLicenseArticles_TPAPIFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_RENTAL, true);
		license.setFunction(DbLicense.FUNC_TP_API_POS, true);
		license.setFunction(DbLicense.FUNC_TP_API_RES, true);
		license.setFunction(DbLicense.FUNC_TP_API_AM, true);

		doReturn(900L).when(spyHelper).getArticleIdByBarcode(mockConn, "RENTAL1Y");
		doReturn(901L).when(spyHelper).getArticleIdByBarcode(mockConn, "TPAPI1Y");
		doReturn(902L).when(spyHelper).getArticleIdByBarcode(mockConn, "TPAPIRES1Y");
		doReturn(903L).when(spyHelper).getArticleIdByBarcode(mockConn, "TPAPIAM1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(4, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 900L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 901L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 902L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 903L && item.sameRenewalPrice == true));
	}

	@Test
	public void testLAddLicenseArticles_IntegrationFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_PROTEL, true);
		license.setFunction(DbLicense.FUNC_TAKEAWAY_COM, true);
		license.setFunction(DbLicense.FUNC_SEVENROOMS, true);
		license.setFunction(DbLicense.FUNC_NETRESTO, true);
		license.setFunction(DbLicense.FUNC_CLOCK_PMS, true);
		license.setFunction(DbLicense.FUNC_GUESTLINE, true);
		license.setFunction(DbLicense.FUNC_DINETIME, true);

		doReturn(1000L).when(spyHelper).getArticleIdByBarcode(mockConn, "PROTEL1Y");
		doReturn(1001L).when(spyHelper).getArticleIdByBarcode(mockConn, "TAKEAWAY1Y");
		doReturn(1002L).when(spyHelper).getArticleIdByBarcode(mockConn, "SEVENROOMS1Y");
		doReturn(1003L).when(spyHelper).getArticleIdByBarcode(mockConn, "NETRESTO1Y");
		doReturn(1004L).when(spyHelper).getArticleIdByBarcode(mockConn, "CLOCKPMS1Y");
		doReturn(1005L).when(spyHelper).getArticleIdByBarcode(mockConn, "GUESTLINE1Y");
		doReturn(1006L).when(spyHelper).getArticleIdByBarcode(mockConn, "DINETIME1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(7, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1000L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1001L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1002L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1003L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1004L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1005L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1006L && item.sameRenewalPrice == true));
	}

	@Test
	public void testLAddLicenseArticles_PaymentIntegrations() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_GLORY_MONEY_MACHINE, true);
		license.setFunction(DbLicense.FUNC_INTERSOLVE, true);
		license.setFunction(DbLicense.FUNC_WORLDLINE_TIM, true);

		doReturn(1100L).when(spyHelper).getArticleIdByBarcode(mockConn, "GLORYMM1Y");
		doReturn(1101L).when(spyHelper).getArticleIdByBarcode(mockConn, "INTERSOLVE1Y");
		doReturn(1102L).when(spyHelper).getArticleIdByBarcode(mockConn, "WORLDLINETIM1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(3, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1100L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1101L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1102L && item.sameRenewalPrice == true));
	}

	@Test
	public void testLAddLicenseArticles_NewerIntegrations() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_TAPP, true);
		license.setFunction(DbLicense.FUNC_ADORIA, true);
		license.setFunction(DbLicense.FUNC_APALEO, true);
		license.setFunction(DbLicense.FUNC_XAFAX, true);
		license.setFunction(DbLicense.FUNC_FISCALIZATION, true);
		license.setFunction(DbLicense.FUNC_SHIJI, true);
		license.setFunction(DbLicense.FUNC_TFLEX, true);
		license.setFunction(DbLicense.FUNC_ASA, true);
		license.setFunction(DbLicense.FUNC_LOYYO, true);
		license.setFunction(DbLicense.FUNC_DRGT, true);

		doReturn(1200L).when(spyHelper).getArticleIdByBarcode(mockConn, "TAPP1Y");
		doReturn(1201L).when(spyHelper).getArticleIdByBarcode(mockConn, "ADORIA1Y");
		doReturn(1202L).when(spyHelper).getArticleIdByBarcode(mockConn, "APALEO1Y");
		doReturn(1203L).when(spyHelper).getArticleIdByBarcode(mockConn, "XAFAX1Y");
		doReturn(1204L).when(spyHelper).getArticleIdByBarcode(mockConn, "FISCALIZATION1Y");
		doReturn(1205L).when(spyHelper).getArticleIdByBarcode(mockConn, "SHIJI1Y");
		doReturn(1206L).when(spyHelper).getArticleIdByBarcode(mockConn, "TFLEX1Y");
		doReturn(1207L).when(spyHelper).getArticleIdByBarcode(mockConn, "ASA1Y");
		doReturn(1208L).when(spyHelper).getArticleIdByBarcode(mockConn, "LOYYO1Y");
		doReturn(1209L).when(spyHelper).getArticleIdByBarcode(mockConn, "DRGT1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(10, orderItems.size());
		// All newer integrations have sameRenewalPrice = true
		assertTrue(orderItems.stream().allMatch(item -> item.sameRenewalPrice == true));
	}

	@Test
	public void testLAddLicenseArticles_LatestIntegrations() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_SENTOO, true);
		license.setFunction(DbLicense.FUNC_COMO, true);
		license.setFunction(DbLicense.FUNC_ZENCHEF, true);
		license.setFunction(DbLicense.FUNC_OPERA_CLOUD, true);
		license.setFunction(DbLicense.FUNC_KLEARLYCLOUD, true);
		license.setFunction(DbLicense.FUNC_ACCOUNTILL, true);

		doReturn(1300L).when(spyHelper).getArticleIdByBarcode(mockConn, "SENTOO1Y");
		doReturn(1301L).when(spyHelper).getArticleIdByBarcode(mockConn, "COMO1Y");
		doReturn(1302L).when(spyHelper).getArticleIdByBarcode(mockConn, "ZENCHEF1Y");
		doReturn(1303L).when(spyHelper).getArticleIdByBarcode(mockConn, "OPERACLOUD1Y");
		doReturn(1304L).when(spyHelper).getArticleIdByBarcode(mockConn, "KLEARLYCLOUD1Y");
		doReturn(1305L).when(spyHelper).getArticleIdByBarcode(mockConn, "ACCOUNTILL1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(6, orderItems.size());
		assertTrue(orderItems.stream().allMatch(item -> item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1300L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1301L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1302L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1303L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1304L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1305L));
	}

	@Test
	public void testLAddLicenseArticles_ProlongMode() throws Exception {
		DbLicense license = new DbLicense();
		license.setLbConnections((short) 2);
		license.setFunction(DbLicense.FUNC_STOCK, true);

		doReturn(1400L).when(spyHelper).getArticleIdByBarcode(mockConn, "U2LOC1Y");
		doReturn(1401L).when(spyHelper).getArticleIdByBarcode(mockConn, "USTOCK1Y");

		// Test prolong mode (second parameter = true)
		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, true);

		assertEquals(2, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1400L));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1401L));
	}

	@Test
	public void testLAddLicenseArticles_ApostillNoEFTInterface() throws Exception {
		DbLicense license = new DbLicense();
		license.setApostill(true);
		license.setLbConnections((short) 1);
		license.setFunction(DbLicense.FUNC_EFT_INTERFACE, true); // Should be ignored for apostill

		doReturn(1500L).when(spyHelper).getArticleIdByBarcode(mockConn, "APOSTILL3Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(1, orderItems.size());
		// Verify that EFT interface was not added for apostill license
		verify(spyHelper, never()).getArticleIdByBarcode(mockConn, "ACPINI3Y");
	}

	@Test
	public void testLAddLicenseArticles_SkippedFunctions() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_TOOL, true); // Should be skipped

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(0, orderItems.size());
		// Verify that TOOL function doesn't add any articles
	}

	@Test
	public void testLAddLicenseArticles_AnnouncerKS() throws Exception {
		DbLicense license = new DbLicense();
		license.setFunction(DbLicense.FUNC_ANNONCER_KS, true);

		doReturn(1600L).when(spyHelper).getArticleIdByBarcode(mockConn, "AKS1Y");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(1, orderItems.size());
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 1600L));
	}

	@Test(expected = Exception.class)
	public void testLAddLicenseArticles_UnknownFunction() throws Exception {
		DbLicense license = new DbLicense();
		// Set a function bit beyond the enabled functions number
		license.setFuncLimited(1L << DbLicense.ENABLED_FUNCTIONS_NUMBER + 1); // Beyond ENABLED_FUNCTIONS_NUMBER

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);
	}

	@Test(expected = Exception.class)
	public void testLAddLicenseArticles_NeverExpiredStatus() throws Exception {
		DbLicense license = new DbLicense();
		license.setNeverExpired(true);

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);
	}

	@Test(expected = Exception.class)
	public void testLAddLicenseArticles_UnlimitedStatus() throws Exception {
		DbLicense license = new DbLicense();
		license.setUnlimited(true);

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);
	}

	@Test
	public void testLAddLicenseArticles_ComplexLicense() throws Exception {
		// Test a complex license with multiple features
		DbLicense license = new DbLicense();
		license.setLbConnections((short) 5);
		license.setRemConnections((short) 2);
		license.setOmConnections((short) 3);
		license.setPsConnections((short) 1);
		license.setHeConnections((short) 2);
		license.setFunction(DbLicense.FUNC_STOCK, true);
		license.setFunction(DbLicense.FUNC_KITCHENSCREEN, true);
		license.setFunction(DbLicense.FUNC_WM_REST, true);
		license.setFunction(DbLicense.FUNC_TP_API_POS, true);
		license.setFunction(DbLicense.FUNC_TAKEAWAY_COM, true);
		license.setDefinitive(true);

		// Mock all required articles
		doReturn(2000L).when(spyHelper).getArticleIdByBarcode(mockConn, "5LOC1Y");
		doReturn(2001L).when(spyHelper).getArticleIdByBarcode(mockConn, "REM1Y");
		doReturn(2002L).when(spyHelper).getArticleIdByBarcode(mockConn, "3HH1Y");
		doReturn(2003L).when(spyHelper).getArticleIdByBarcode(mockConn, "PS1Y");
		doReturn(2004L).when(spyHelper).getArticleIdByBarcode(mockConn, "2HE1Y");
		doReturn(2005L).when(spyHelper).getArticleIdByBarcode(mockConn, "STOCK1Y");
		doReturn(2006L).when(spyHelper).getArticleIdByBarcode(mockConn, "KS1Y");
		doReturn(2007L).when(spyHelper).getArticleIdByBarcode(mockConn, "WMREST1Y");
		doReturn(2008L).when(spyHelper).getArticleIdByBarcode(mockConn, "TPAPI1Y");
		doReturn(2009L).when(spyHelper).getArticleIdByBarcode(mockConn, "TAKEAWAY1Y");
		doReturn(2010L).when(spyHelper).getArticleIdByBarcode(mockConn, "NU");

		spyHelper.LAddLicenseArticles(mockConn, orderItems, license, false);

		assertEquals(11, orderItems.size());

		// Verify connections with correct quantities
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 2001L && item.quantity == 2));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 2003L && item.quantity == 1));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 2004L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 2009L && item.sameRenewalPrice == true));
		assertTrue(orderItems.stream().anyMatch(item -> item.articleId == 2010L && item.baseDuration == 0));
	}

	@Test @Ignore
	public void testGetDealerPrices() throws Exception {
		// TODO ...
	}
}
