/**
 *
 */
package eu.untill.license.server;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import javax.mail.Address;
import javax.mail.Authenticator;
import javax.mail.BodyPart;
import javax.mail.Message.RecipientType;
import javax.mail.MessagingException;
import javax.mail.Part;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.AddressException;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.License.DriverInfo;
import eu.untill.license.shared.License.FuncProp.FuncType;

/**
 * <AUTHOR>
 *
 */
public class EmailHelperImpl implements EmailHelper {

	static final Logger LOG = LoggerFactory.getLogger(EmailHelperImpl.class);

	private static final String emailCharset = Charset.forName("UTF-8").name();

	@Override
	public boolean emailRequest(Dealer superDealer, Dealer dealer, DbLicense license,
			String confirmUrl, String cancelUrl) {
		LOG.debug("ENTRY ({}, {}, {}, {}, {})", superDealer, dealer, license, confirmUrl, cancelUrl);

		String subjectTemplate;
		String msgTextTemplate;
		String msgHtmlTemplate;

		// determinate message content
		if (license.isBoost()) {
			subjectTemplate = "[unTill] Email confirmation of license request (Client: {0})";
			msgHtmlTemplate = "<div>You have requested an addition to an existing license with the following parameters:</div>\n"
					+ GetLicenseInfoHtmlTemplate(license.isOnline())
					+ "<br/>";
		} else {
			subjectTemplate = "[unTill] Email confirmation of license request (Client: {0})";
			//msgTextTemplate = "Your email client does not support HTML messages";
			msgHtmlTemplate = "<div>You have requested "
				+ (license.requestType.equals("CREATE") ? " (for create) " : "")
				+ (license.requestType.equals("PROLONG") ? " (for prolong) " : "")
				+ (license.requestType.equals("UPGRADE") ? " (for upgrade) " : "")
				+ "a license with the following parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline())
				+ "<br/>";
		}
//		switch (requestType) {
//		case CREATE:
//		case PROLONG:
//		case UPGRADE:
//		}
		msgHtmlTemplate = msgHtmlTemplate.replaceAll("\n", "\r\n");
		msgTextTemplate = msgHtmlTemplate.replaceAll("<[^>]*?>", "");

		msgHtmlTemplate += "<p>To confirm this request open <a href=\""
				+ StringEscapeUtils.escapeHtml(confirmUrl) + "\">this link</a></p>\n"
				+ "<p>To cancel this request open <a href=\""
				+ StringEscapeUtils.escapeHtml(cancelUrl) + "\">this link</a></p>\n";
		msgTextTemplate += "To confirm this request open: " + confirmUrl + "\n"
			+ "To cancel this request open: " + cancelUrl + "\n";

		// parse templates
		String subject = parseTemplate(subjectTemplate, license, dealer, superDealer, false, null);
		String msgText = parseTemplate(msgTextTemplate, license, dealer, superDealer, false, null);
		String msgHtml = parseTemplate(msgHtmlTemplate, license, dealer, superDealer, true, null);

		Dealer toDealer = superDealer == null ? dealer : superDealer;

		// send message
		boolean result = sendMessage(toDealer.email, toDealer.name, subject, msgText, msgHtml, null);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean emailLargeAccountRequest(Dealer superDealer, Dealer dealer, DbLicense license,
			String laApprUid, String laDenyUrl) {
		LOG.debug("ENTRY ({}, {}, {}, {}, {})", superDealer, dealer, license, laApprUid, laApprUid);

		String subjectTemplate;
		String msgTextTemplate;
		String msgHtmlTemplate;

		if (superDealer == null) {
			subjectTemplate = "Dealer ({20}) Large Account confirmation (Client: {0})";
		} else {
			subjectTemplate = "Super dealer ({30}) Large Account confirmation for dealer ({20}) (Client: {0})";
		}
		msgHtmlTemplate = "<p>"
				+ (superDealer != null ? "<b>Super dealer email:</b> {32}"
						: "<b>Dealer e-mail:</b> {22}")
				+ "</p>\n"
				+ "<p><b>Dealer name:</b> {20}</p>\n"
				+ "<br/><div>License parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());

		msgHtmlTemplate = msgHtmlTemplate.replaceAll("\n", "\r\n");
		msgTextTemplate = msgHtmlTemplate.replaceAll("<[^>]*?>", "");

		msgHtmlTemplate += "<p>To approve Large Acount open <a href=\""
				+ StringEscapeUtils.escapeHtml(laApprUid) + "\">this link</a></p>\n"
				+ "<p>To deny Large Acount open <a href=\""
				+ StringEscapeUtils.escapeHtml(laDenyUrl) + "\">this link</a></p>\n";
		msgTextTemplate += "To approve Large Acount open: " + laApprUid + "\n"
			+ "To deny Large Acount open: " + laDenyUrl + "\n";

		// parse templates
		String subject = parseTemplate(subjectTemplate, license, dealer, superDealer, false, null);
		String msgText = parseTemplate(msgTextTemplate, license, dealer, superDealer, false, null);
		String msgHtml = parseTemplate(msgHtmlTemplate, license, dealer, superDealer, true, null);

		// get settings
		Settings settings = Common.getSettings();

		// send message
		boolean result = sendMessage(settings.getOwnerEmail(), settings.getOwnerName(), subject, msgText, msgHtml, null);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public void emailLicense(SendLicenseReason sendLicenseReason, File oldLicenseFile, File licenseFile,
			boolean tempLicense, DbLicense license, Dealer dealer) throws Exception {
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {})", sendLicenseReason, oldLicenseFile, licenseFile, tempLicense, license, dealer);

		String subjectTemplate;
		String msgTextTemplate;
		String msgHtmlTemplate;

		if (license.isOnline()) {
			subjectTemplate = "unTill license UID (Client: {0})";
			msgHtmlTemplate = "<div>Thank you for purchasing our program. Use this License UID in registration window:</div>"
					+ "<br/><div><b>{4}</b></div>\n"
					+ (tempLicense ? "<br/><div><b>This temporary license will expire: {18}</b></div>\n" : "")
					+ "<br/><div>" + (tempLicense ? "Original license" : "License") + " parameters:</div>\n"
					+ GetLicenseInfoHtmlTemplate(license.isOnline());
		} else {
			// determinate message content
			// TODO use sendLicenseReason
			if (tempLicense)
				subjectTemplate = "unTill temporary license file (Client: {0})";
			else if (sendLicenseReason == SendLicenseReason.EMERGENCY)
				subjectTemplate = "unTill emergency license file (Client: {0})";
			else
				subjectTemplate = "unTill license file (Client: {0})";
			//msgTextTemplate = "Your email client does not support HTML messages";
			msgHtmlTemplate = "<div>Thank you for purchasing our program. Load this license file from registration window</div>"
				+ (tempLicense ? "<br/><div><b>This temporary license will expire: {18}</b></div>\n" : "")
				+ "<br/><div>" + (tempLicense ? "Original license" : "License") + " parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());
//			switch (requestType) {
//			case CREATE:
//			case PROLONG:
//			case UPGRADE:
//			}
		}
		msgHtmlTemplate = msgHtmlTemplate.replaceAll("\n", "\r\n");
		msgTextTemplate = msgHtmlTemplate.replaceAll("<[^>]*?>", "");

		// parse templates
		String subject = parseTemplate(subjectTemplate, license, dealer, null, false, null);
		String msgText = parseTemplate(msgTextTemplate, license, dealer, null, false, null);
		String msgHtml = parseTemplate(msgHtmlTemplate, license, dealer, null, true, null);

		// prepare attachments (if exist)
		ArrayList<MimeBodyPart> attachments = new ArrayList<MimeBodyPart>();
		if (oldLicenseFile != null) {
			MimeBodyPart bpAttachment = new MimeBodyPart();
			bpAttachment.attachFile(oldLicenseFile);
			bpAttachment.setDisposition(Part.ATTACHMENT);
			bpAttachment.setFileName(Common.fixFileName(license.getClientName() + (tempLicense ? ".temp.lic" : ".lic")));
			bpAttachment.setDescription(tempLicense ? "unTill old temporary license file"
					: (sendLicenseReason == SendLicenseReason.EMERGENCY ? "unTill old emergency license file"
							:"unTill old license file"));
			attachments.add(bpAttachment);
		}
		if (licenseFile != null) {
			MimeBodyPart bpAttachment = new MimeBodyPart();
			bpAttachment.attachFile(licenseFile);
			bpAttachment.setDisposition(Part.ATTACHMENT);
			bpAttachment.setFileName(Common.fixFileName(license.getClientName() + (tempLicense ? ".temp.li4" : ".li4")));
			bpAttachment.setDescription(tempLicense ? "unTill temporary license file"
					: (sendLicenseReason == SendLicenseReason.EMERGENCY ? "unTill emergency license file"
							:"unTill license file"));
			attachments.add(bpAttachment);
		}
		// send message
		boolean result = sendMessage(dealer.email, dealer.name, subject, msgText, msgHtml,
						attachments.isEmpty() ? null : attachments.toArray(new MimeBodyPart[attachments.size()]));
		if (!result)
			throw new Exception("sent message error");

		LOG.debug("RETURN");
	}

	@Override
	public void emailReportToOwner(ReportType reportType, DbLicense license, Dealer dealer,
			Dealer superDealer, String orderText, String comment) {
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {})", reportType, license, dealer, superDealer,
				orderText == null ? null : orderText.length(), comment);

		String subjectTemplate = "";
		String msgTextTemplate = "";
		String msgHtmlTemplate = "";

		// determinate message content
		switch (reportType) {
		case DEALER_REQUESTED_LICENSE:
			if (superDealer == null) {
				subjectTemplate = "Dealer ({20}) requested license (Client: {0})";
			} else {
				subjectTemplate = "Super dealer ({30}) requested license for dealer ({20})";
			}
			msgHtmlTemplate = "<p>"
					+ (superDealer != null ? "<b>Super dealer email:</b> {32}"
							: "<b>Dealer e-mail:</b> {22}")
					+ "</p>\n"
					+ "<p><b>Dealer name:</b> {20}</p>\n"
					+ "<br/><div>License parameters:</div>\n"
					+ GetLicenseInfoHtmlTemplate(license.isOnline());
			break;
		case DEALER_CONFIRMED_REQUEST:
			subjectTemplate = "Dealer ({20}) confirmed request (Client: {0})";
			msgHtmlTemplate = "<p>"
				+ "<b>Dealer e-mail:</b> {22}</p>\n"
				+ "<p><b>Dealer name:</b> {20}</p>\n"
				+ "<br/><div>License parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());
			break;
		case DEALER_CANCELED_REQUEST:
			subjectTemplate = "Dealer ({20}) canceled request (Client: {0})";
			msgHtmlTemplate = "<p>"
				+ "<b>Dealer e-mail:</b> {22}</p>\n"
				+ "<p><b>Dealer name:</b> {20}</p>\n"
				+ "<p><b>Comment:</b> {41}</p>\n"
				+ "<br/><div>License parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());
			break;
		case LICENSE_RE_SENT_TO_DEALER:
			if (superDealer == null) {
				subjectTemplate = "Dealer ({20}) requested re-send license file (Client: {0})";
			} else {
				subjectTemplate = "Super dealer ({30}) requested re-send license file for dealer ({20}) (Client: {0})";
			}
			msgHtmlTemplate = "<p>"
				+ "<b>Dealer e-mail:</b> {22}</p>\n"
				+ "<p><b>Dealer name:</b> {20}</p>\n"
				+ "<br/><div>License parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());
			break;
		case EMERGENCY_LICENSE_SENT_TO_DEALER:
			if (superDealer == null) {
				subjectTemplate = "Dealer ({20}) requested emergency license file (Client: {0})";
			} else {
				subjectTemplate = "Super dealer ({30}) requested emergency license file for dealer ({20}) (Client: {0})";
			}
			msgHtmlTemplate = "<p>"
				+ "<b>Dealer e-mail:</b> {22}</p>\n"
				+ "<p><b>Dealer name:</b> {20}</p>\n"
				+ "<br/><div>License parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());
			break;
		case DEALER_OBJECT_PROFORMA:
			subjectTemplate = "Dealer ({20}) objected proforma (Client: {0})";
			msgHtmlTemplate = "<p>"
				+ "<b>Dealer e-mail:</b> {22}</p>\n"
				+ "<p><b>Dealer name:</b> {20}</p>\n"
				+ "<p><b>Comment:</b> {41}</p>\n"
				+ "<p><b>Proforma:</b></p>\n<pre>\n{40}\n</pre>\n"
				+ "<br/><div>License parameters:</div>\n"
				+ GetLicenseInfoHtmlTemplate(license.isOnline());
			break;
		default:
			LOG.error("Unexpected report type ({})", reportType);
			LOG.debug("RETURN");
			return;
		}
		msgHtmlTemplate = msgHtmlTemplate.replaceAll("\n", "\r\n");
		msgTextTemplate = msgHtmlTemplate.replaceAll("<[^>]*?>", "");

		// parse templates
		String[] extraData = new String[]{orderText, comment};
		String subject = parseTemplate(subjectTemplate, license, dealer, superDealer, false, extraData);
		String msgText = parseTemplate(msgTextTemplate, license, dealer, superDealer, false, extraData);
		String msgHtml = parseTemplate(msgHtmlTemplate, license, dealer, superDealer, true, extraData);

		// get settings
		Settings settings = Common.getSettings();

		// create attachment
		MimeBodyPart bpAttachment[] = null;
		if (orderText != null) {
			try {
				bpAttachment = new MimeBodyPart[1];
				bpAttachment[0] = new MimeBodyPart();
				bpAttachment[0].setText(orderText, emailCharset);
				bpAttachment[0].setDisposition(Part.ATTACHMENT);
				bpAttachment[0].setFileName("order.txt");
				bpAttachment[0].setDescription("Order file");
			} catch (MessagingException e) {
				LOG.error("create attachment error", e);
			}
		}

		// send message
		sendMessage(settings.getOwnerEmail(), settings.getOwnerName(), subject, msgText, msgHtml,
				bpAttachment);

		LOG.debug("RETURN");
	}


	@Override
	public boolean emailProforma(List<String> emailList, Dealer dealer, DbLicense license,
			String approveUrl, String toObjectUrl, String orderText) {
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {})", emailList, dealer, license, approveUrl, toObjectUrl,
				orderText == null ? null : orderText.length());

		String subjectTemplate;
		String msgTextTemplate;
		String msgHtmlTemplate;

		// determinate message content
		subjectTemplate = "[unTill] Email approval of proforma license (Client: {0})";
		//msgTextTemplate = "Your email client does not support HTML messages";
		msgHtmlTemplate = "<pre>\n{40}\n</pre>\n<br/>";

		msgHtmlTemplate = msgHtmlTemplate.replaceAll("\n", "\r\n");
		msgTextTemplate = msgHtmlTemplate.replaceAll("<[^>]*?>", "");

		msgHtmlTemplate += "<p>To approve, open <a href=\""
				+ StringEscapeUtils.escapeHtml(approveUrl) + "\">this link</a></p>\n"
				+ "<p>To object, open <a href=\""
				+ StringEscapeUtils.escapeHtml(toObjectUrl) + "\">this link</a></p>\n";
		msgTextTemplate += "To confirm this request open: " + approveUrl + "\n"
			+ "To cancel this request open: " + toObjectUrl + "\n";

		// parse templates
		String[] extraData = new String[]{orderText};
		String subject = parseTemplate(subjectTemplate, license, dealer, null, false, extraData);
		String msgText = parseTemplate(msgTextTemplate, license, dealer, null, false, extraData);
		String msgHtml = parseTemplate(msgHtmlTemplate, license, dealer, null, true, extraData);

		// send message
		boolean result = sendMessage(emailList, subject, msgText, msgHtml, null);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean emailInvoice(File invoiceFile, Date invoiceDate,
			String invoiceNumber, List<String> emailList, DbLicense license, String clientName) {
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {})", invoiceFile, invoiceDate, invoiceNumber, emailList, license, clientName);

		String subjectTemplate;
		String msgTextTemplate;
		String msgHtmlTemplate;

		if (clientName == null && license != null) clientName = license.getClientName();

		// determinate message content
		// TODO use sendLicenseReason
		subjectTemplate = "unTill invoice (Client: " + clientName + ")"; // XXX clientName
		//msgTextTemplate = "Your email client does not support HTML messages";
		msgHtmlTemplate = "<div>Invoice (" + invoiceNumber + ") is in the attached file in pdf format</div>"
			+ (license == null ? "" : "<br/><div>License parameters:</div>\n"
					+ GetLicenseInfoHtmlTemplate(license.isOnline()));

		msgHtmlTemplate = msgHtmlTemplate.replaceAll("\n", "\r\n");
		msgTextTemplate = msgHtmlTemplate.replaceAll("<[^>]*?>", "");

		// parse templates
		String subject = parseTemplate(subjectTemplate, license, null, null, false, null);
		String msgText = parseTemplate(msgTextTemplate, license, null, null, false, null);
		String msgHtml = parseTemplate(msgHtmlTemplate, license, null, null, true, null);

		boolean result = false;
		try {
			// create attachment
			MimeBodyPart bpAttachment = new MimeBodyPart();
			bpAttachment.attachFile(invoiceFile);
			bpAttachment.setDisposition(Part.ATTACHMENT);
			bpAttachment.setDescription("unTill invoice file");

			// send message
			result = sendMessage(emailList, subject, msgText, msgHtml,
					new MimeBodyPart[]{bpAttachment});
		} catch (MessagingException | IOException e) {
			LOG.error("send message error", e);
		}

		LOG.debug("RETURN {}", result);
		return result;
	}

	  /////////////////
	 // Static part //
	/////////////////

	private static String GetLicenseInfoHtmlTemplate(boolean hideHardCode) {
		return "<ul>"
		+ " <li>Client name: <b>{0}</b></li>\n"
		+ " <li>Start date: <b>{11}</b></li>\n"
		+ " <li>Issue date: <b>{12}</b></li>\n"
		+ " <li>Expired date: <b>{13}</b></li>\n"
		+ (hideHardCode ? "" : " <li>Hardcode: <b>{4}</b></li>\n")
		+ " <li>Number of Local Databases: <b>{5}</b></li>\n"
		+ " <li>Number of Remote Connections: <b>{8}</b></li>\n"
		+ " <li>Number of Handhelds: <b>{9}</b></li>\n"
		+ " <li>Number of Production Screen Professional: <b>{10}</b></li>\n"
		+ " <li>Number of HHT-EFT: <b>{6}</b></li>\n"
		+ " <li>The modules: <b>{19}</b></li>\n"
		+ " <li>The extra modules: <b>{14}</b></li>\n"
		+ " <li>The extra interfaces: <b>{15}</b></li>\n"
		+ " <li>License type: <b>{16}</b></li>\n"
		+ "</ul>\n";
	}

	private static String prepareString(boolean asHtml, String str) {
		return asHtml ? StringEscapeUtils.escapeHtml(str) : str;
	}

	private static String prepareBoolean(boolean asHtml, Boolean value) {
		return (value ? "Yes" : "No");
	}

	private static String parseTemplate(String template, DbLicense license,
			Dealer dealer, Dealer superDealer, boolean asHtml, String[] extraData)
	{
		String licenseHardCode = null;

		String licenseLbConnections = null;
		String licenseRemConnections = null;
		String licenseOmConnections = null;
		//String licenseHqConnections = null;
		String licensePsConnections = null;
		String licenseHeConnections = null;

		String licenseModules = null;
		String licenseExtraModules = null;
		String licenseInterfaces = null;
		String licenseDrivers = null;

		String licenseStartDate = null;
		String licenseIssueDate = null;
		String licenseExpiredDate = null;
		String licenseTempExpiredDate = null;

		String licenseStatus = null;

		if (license != null) {
//			String licenseHardCode = license.hardCode.substring(0, 5) + "-"
//					+ license.hardCode.substring(5, 10) + "-"
//					+ license.hardCode.substring(10, 15) + "-"
//					+ license.hardCode.substring(15, 20) + "-"
//					+ license.hardCode.substring(20, 25);
			licenseHardCode = HardCodeHelper.formatHardCode(license.hardCode);

			licenseLbConnections = Short.toString(license.getLbConnections());
			licenseRemConnections = Short.toString(license.getRemConnections());
			licenseOmConnections = Short.toString(license.getOmConnections());
//			licenseHqConnections = Short.toString(license.getHqConnections());
			licensePsConnections = Short.toString(license.getPsConnections());
			licenseHeConnections = Short.toString(license.getHeConnections());

			licenseModules = "";
			licenseExtraModules = "";
			licenseInterfaces = "";
			licenseDrivers = "";
			for (int i = 0; i < DbLicense.ENABLED_FUNCTIONS_NUMBER; i++) {
				if ((1l << i & license.getFuncLimited()) != 0) {
					if (!DbLicense.isHiddenFunc(i)) {
					DriverInfo[] driverInfos = DbLicense.getDriverInfosByFunc(i);
						if (driverInfos.length > 0) {
							for (DriverInfo driverInfo : driverInfos) {
								if (!licenseDrivers.isEmpty())
									licenseDrivers += "; ";
								licenseDrivers += driverInfo.getDisplayName();
							}
						} else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE) {
							if (!licenseModules.isEmpty())
								licenseModules += "; ";
							licenseModules += DbLicense.FUNC_NAMES[i];
						} else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_EXTRA_MODULE) {
							if (!licenseExtraModules.isEmpty())
								licenseExtraModules += "; ";
							licenseExtraModules += DbLicense.FUNC_NAMES[i];
						} else { // Type.FT_INTERFACE
							if (!licenseInterfaces.isEmpty())
								licenseInterfaces += "; ";
							licenseInterfaces += DbLicense.FUNC_NAMES[i];
						}
					}
				}
			}

			if (license.getDrivers() != null) {
				for (String driverId : license.getDrivers()) {
					DriverInfo driverInfo = DbLicense.getDriverInfoByDriverId(driverId);
					if (driverInfo != null) {
						if (driverInfo.hasFunc())
							continue; // skip drivers with function id, they are already handled above
						if (!licenseDrivers.isEmpty())
							licenseDrivers += "; ";
						licenseDrivers += driverInfo.getDisplayName();
					} else {
						if (!licenseDrivers.isEmpty())
							licenseDrivers += "; ";
						licenseDrivers += driverId;
					}
				}
			}

			licenseStartDate = "-";
			licenseIssueDate = "-";
			licenseExpiredDate = "-";
			licenseTempExpiredDate = "-";
			if (license.getStartDate() != null)
				licenseStartDate = DateFormat.getDateInstance().format(Common.toLocalDateOnly(license.getStartDate()));
			if (license.getIssueDate() != null)
				licenseIssueDate = DateFormat.getDateInstance().format(Common.toLocalDateOnly(license.getIssueDate()));
			if (license.getExpiredDate() != null)
				licenseExpiredDate = DateFormat.getDateInstance().format(Common.toLocalDateOnly(license.getExpiredDate()));
			if (license.tempExpiredDate != null)
				licenseTempExpiredDate = DateFormat.getDateInstance().format(Common.toLocalDateOnly(license.tempExpiredDate));

			licenseStatus = "";
			if (!license.isStatusActive()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "Inactive";
			}
			if (license.isNeverExpired()) {
				licenseExpiredDate = "never expired";
			}
			if (license.isUnlimited()) {
				licenseLbConnections = "unlimited";
				licenseRemConnections = "unlimited";
				licenseOmConnections = "unlimited";
//				licenseHqConnections = "unlimited";
				licensePsConnections = "unlimited";
				licenseHeConnections = "unlimited";
			}
			if (license.isCanceled()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "Canceled";
			}
			if (license.isDefinitive()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "Definitive (" + Long.toString(license.getDefinitiveMaxVersion()) + ")";
			}
			if (license.isOnline()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "Online";
			}
			if (license.isModuled()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "Moduled";
			}
			if (license.isSaas()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "SaaS";
			}
			if (license.isBoost()) {
				if (!licenseStatus.isEmpty()) licenseStatus += "; ";
				licenseStatus += "Boost";
			}
		}

		MessageFormat mf = new MessageFormat(template);

		return mf.format(new Object[]{

				(license == null ? null : prepareString(asHtml, license.getClientName())),    // {0} String
				(license == null ? null : prepareString(asHtml, license.getClientAddress())), // {1} String
				(license == null ? null : prepareString(asHtml, license.getClientEmail())),   // {2} String
				(license == null ? null : prepareString(asHtml, license.getClientPhone())),   // {3} String
				(license == null ? null : prepareString(asHtml, licenseHardCode)),       // {4} String
				(license == null ? null : prepareString(asHtml, licenseLbConnections)),  // {5} String (short)
				(license == null ? null : prepareString(asHtml, licenseHeConnections)),  // {6} String (short)
				null, //(license == null ? null : prepareString(asHtml, licenseHqConnections)),  // {7} String (short)
				(license == null ? null : prepareString(asHtml, licenseRemConnections)), // {8} String (short)
				(license == null ? null : prepareString(asHtml, licenseOmConnections)),  // {9} String (short)
				(license == null ? null : prepareString(asHtml, licensePsConnections)),  // {10} String (short)
				(license == null ? null : prepareString(asHtml, licenseStartDate)),       // {11} Date
				(license == null ? null : prepareString(asHtml, licenseIssueDate)),       // {12} Date
				(license == null ? null : prepareString(asHtml, licenseExpiredDate)),     // {13} Date
				(license == null ? null : prepareString(asHtml, licenseExtraModules)),    // {14}
				(license == null ? null : prepareString(asHtml, licenseInterfaces)),      // {15}
				(license == null ? null : prepareString(asHtml, licenseStatus)),          // {16}
				(license == null ? null : prepareString(asHtml, license.dealerComments)), // {17}
				(license == null ? null : prepareString(asHtml, licenseTempExpiredDate)), // {18} Date
				(license == null ? null : prepareString(asHtml, licenseModules)),         // {19}

				(dealer == null ? null : prepareString(asHtml, dealer.name)),         // {20} String
				(dealer == null ? null : prepareString(asHtml, dealer.login)),        // {21} String
				(dealer == null ? null : prepareString(asHtml, dealer.email)),        // {22} String
				(dealer == null ? null : prepareBoolean(asHtml, dealer.superDealer)), // {23} boolean

				(license == null ? null : prepareString(asHtml, licenseDrivers)),         // {24}

				null, null, null, null, null, // {25}-{29}

				(superDealer == null ? null : prepareString(asHtml, superDealer.name)),  // {30} String
				(superDealer == null ? null : prepareString(asHtml, superDealer.login)), // {31} String
				(superDealer == null ? null : prepareString(asHtml, superDealer.email)), // {32} String
				(superDealer == null ? null : prepareBoolean(asHtml, superDealer.superDealer)), // {33} boolean

				null, null, null, null, null, null, // {34}-{39}

				(extraData == null || extraData.length < 1 ? null : extraData[0]),    // {40} String
				(extraData == null || extraData.length < 2 ? null : extraData[1]),    // {41} String
				(extraData == null || extraData.length < 3 ? null : extraData[2]),    // {42} String
				(extraData == null || extraData.length < 4 ? null : extraData[3]),    // {43} String
				(extraData == null || extraData.length < 5 ? null : extraData[4]),    // {44} String

		});
	}

	protected static boolean sendMessage(String toEmail, String toName, String subject,
			String msgText,	String msgHtml,	MimeBodyPart[] attachments) {
//		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {})", toEmail, toName, subject, msgText, msgHtml, attachments);

		boolean result = false;
		try {
			// construct email addresses
			Address fromAddress = getFromAddressFromSettings();
			InternetAddress toAddress = new InternetAddress(toEmail);
			if (toName != null)
				toAddress.setPersonal(toName, emailCharset);
			sendMessage(fromAddress, new Address[] {toAddress}, subject, msgText, msgHtml, attachments);
			result = true;
		} catch (MessagingException | IOException e) {
			LOG.error("send message error", e);
		}

//		LOG.debug("RETURN {}", result);
		return result;
	}

	protected static boolean sendMessage(List<String> emailToList, String subject,
			String msgText,	String msgHtml,	MimeBodyPart[] attachments) {
//		LOG.debug("ENTRY ({}, {}, {}, {}, {})", emailToList, subject, msgText, msgHtml, attachments);

		boolean result = false;
		try {
			// construct email addresses
			Address emailFromAddress = getFromAddressFromSettings();
			Address[] emailToAddresses = new Address[emailToList.size()];
			for (int i = 0; i < emailToAddresses.length; i++)
				emailToAddresses[i] = new InternetAddress(emailToList.get(i));
			sendMessage(emailFromAddress, emailToAddresses, subject, msgText, msgHtml, attachments);
			result = true;
		} catch (MessagingException | IOException e) {
			LOG.error("send message error", e);
		}

//		LOG.debug("RETURN {}", result);
		return result;
	}

	private static Address getFromAddressFromSettings() throws UnsupportedEncodingException,
			AddressException
	{
		// get settings
		Settings settings = Common.getSettings();

		InternetAddress fromAddress = new InternetAddress(settings.getEmailFromEmail());
		if (settings.getEmailFromName() != null)
			fromAddress.setPersonal(settings.getEmailFromName(), emailCharset);
		return fromAddress;
	}

	private static void sendMessage(Address fromAddress, Address[] toAddresses, String subject,
			String msgText, String msgHtml, MimeBodyPart[] attachments)
			throws MessagingException, IOException {
		sendMessage(fromAddress, toAddresses, subject, msgText, msgHtml, attachments, true);
	}
	private static void sendMessage(Address fromAddress, Address[] toAddresses, String subject,
			String msgText, String msgHtml, MimeBodyPart[] attachments, boolean withHeaderAndFooter)
			throws MessagingException, IOException {
//		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {}, {})", fromAddress, toAddresses, subject, msgText, msgHtml,
//		        attachments, withHeaderAndFooter);

		Settings settings = Common.getSettings();

		// create message container
		MimeMultipart messageContainer = new MimeMultipart();
		messageContainer.setSubType("alternative");

		if (withHeaderAndFooter) {
			msgText = Common.getStringFromFile(settings.getTemplate("MessageHeader.txt"))
				+ msgText + Common.getStringFromFile(settings.getTemplate("MessageFooter.txt"));
			msgHtml = Common.getStringFromFile(settings.getTemplate("MessageHeader.html"))
			+ msgHtml + Common.getStringFromFile(settings.getTemplate("MessageFooter.html"));
		}

		// create text parts
		BodyPart bpMsgText = new MimeBodyPart();
		bpMsgText.setContent(msgText, "text/plain; charset=" + emailCharset);
		messageContainer.addBodyPart(bpMsgText);

		BodyPart bpMsgHtml = new MimeBodyPart();
		bpMsgHtml.setContent(msgHtml, "text/html; charset=" + emailCharset);
		messageContainer.addBodyPart(bpMsgHtml);

		if (withHeaderAndFooter) {
			// assign message container as sub container
			MimeMultipart subContainer = messageContainer;
			// create contentContainer as message ("super") container
			messageContainer = new MimeMultipart();
			messageContainer.setSubType("related");
			// append sub container into message container
			MimeBodyPart bpSubContainer = new MimeBodyPart();
			bpSubContainer.setContent(subContainer);
			messageContainer.addBodyPart(bpSubContainer);
		}

		if (attachments != null && attachments.length > 0) {
			// assign message container as sub container
			MimeMultipart subContainer = messageContainer;
			// create contentContainer as message ("super") container
			messageContainer = new MimeMultipart();
			messageContainer.setSubType("mixed");
			// append sub container into message container
			MimeBodyPart bpTextContainer = new MimeBodyPart();
			bpTextContainer.setContent(subContainer);
			messageContainer.addBodyPart(bpTextContainer);
			// append attachments into message container
			for (MimeBodyPart attachment : attachments) {
				messageContainer.addBodyPart(attachment);
			}
		}

		// prepare attachment picture
		MimeBodyPart bpPictureAttachment = new MimeBodyPart();
		bpPictureAttachment.attachFile(settings.getTemplate("MessageHeader.jpg")); // XXX Hard coded
		bpPictureAttachment.setDisposition(Part.INLINE);
		bpPictureAttachment.setFileName("MessageHeader.jpg");
		bpPictureAttachment.setContentID("<MessageHeader.jpg.ContentId>"); // XXX Hard coded
		messageContainer.addBodyPart(bpPictureAttachment);

		// get session
		Session session = getEmailSeccion(settings);

		// construct the message
		MimeMessage message = new MimeMessage(session);
		message.setSubject(subject, emailCharset);
		message.setContent(messageContainer);
		message.setFrom(fromAddress);
		message.setRecipients(RecipientType.TO, toAddresses);

		// set sent date
		message.setSentDate(new Date());

		// send message
		Transport.send(message);

//		LOG.debug("RETURN");
	}

	private static Session getEmailSeccion(Settings settings) throws MessagingException {
		Properties props = (Properties) System.getProperties().clone();

		String emailHostName = settings.getEmailHostName();
		if (emailHostName != null)
			props.put("mail.smtp.host", emailHostName);
		String emailSmtpPort = settings.getEmailSmtpPort();
		if (emailSmtpPort != null)
			props.put("mail.smtp.port", emailSmtpPort);

		String emailSecurity = settings.getEmailSecurity();
		if (emailSecurity != null) {
			if (emailSecurity.toUpperCase().contains("SSL")) {
				props.put("mail.smtp.ssl.enable", "true");
			}
			if (emailSecurity.toUpperCase().contains("TLS")) {
				props.put("mail.smtp.starttls.enable", "true");
			}
		}

		final String emailAuthUserName = settings.getEmailAuthUserName();
		final String emailAuthPassword = settings.getEmailAuthPassword();

		Authenticator authenticator = null;
		if (emailAuthUserName != null && emailAuthPassword != null) {
			props.put("mail.smtp.auth", "true");
			authenticator = new Authenticator() {
				protected PasswordAuthentication getPasswordAuthentication() {
					return new PasswordAuthentication(emailAuthUserName, emailAuthPassword);
				}
			};
		}

		return Session.getInstance(props, authenticator);
	}

}
