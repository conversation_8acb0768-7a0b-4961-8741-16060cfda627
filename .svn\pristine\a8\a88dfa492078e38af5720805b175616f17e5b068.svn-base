package eu.untill.license.shared;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.google.gwt.user.client.rpc.IsSerializable;

public class SuTaskBundle implements IsSerializable {

	public long taskBundleId;
	public long dealerId;

	public Date createDatetime;
	public String title;
	public String action;
	public Map<String, String> params;
	public Date startDatetime;

	// auxiliary fields
	public List<SuTask> tasks;

	@Override
	public String toString() {
		return "[" + getClass().getSimpleName() + ": " + Long.toString(taskBundleId) + ", \"" + title + "\"]";
	}

}
