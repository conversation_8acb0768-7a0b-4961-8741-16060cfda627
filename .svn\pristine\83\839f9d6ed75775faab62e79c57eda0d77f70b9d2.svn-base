package eu.untill.license.server;

import java.util.Locale;
import java.util.UUID;
import java.util.prefs.Preferences;

import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;

import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.api.AuthenticationApi;
import com.untill.retailforce.api.MasterDataDistributorsApi;
import com.untill.retailforce.api.MasterDataOrganisationsApi;
import com.untill.retailforce.api.MasterDataStoresApi;
import com.untill.retailforce.api.MasterDataTerminalsApi;
import com.untill.retailforce.auth.ApiKeyAuth;
import com.untill.retailforce.model.CompanyIdentification;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.IdentificationType;
import com.untill.retailforce.model.Organisation;
import com.untill.retailforce.model.OrganisationModelPageResultModel;
import com.untill.retailforce.model.PlatformType;
import com.untill.retailforce.model.Session;
import com.untill.retailforce.model.Store;
import com.untill.retailforce.model.Terminal;

public class RetailForceInv {

	private static String retailForceApiBasePath = "https://api.retailforce.cloud";
	private static String retailForceApiKey;
	private static String retailForceApiSecret;
	private static String retailForceDistributorId;
	private static String retailForceConfigurationId;
	private static boolean retailForceTest = true;
	
	@BeforeClass
	public static void beforeClass() {
		Preferences prefs = Preferences.systemRoot().node(Settings.prefsNode);
		retailForceApiBasePath = prefs.get("RetailForceApiBasePath", retailForceApiBasePath);
		retailForceApiKey = prefs.get("RetailForceApiKey", retailForceApiKey);
		retailForceApiSecret = prefs.get("RetailForceApiSecret", retailForceApiSecret);
		retailForceDistributorId = prefs.get("RetailForceDistributorId", retailForceDistributorId);
		retailForceConfigurationId = prefs.get("RetailForceConfigurationId", retailForceConfigurationId);
		retailForceTest = Boolean.parseBoolean(prefs.get("RetailForceTest", Boolean.toString(retailForceTest)));
	}
	

	@Test @Ignore
	public void getOrganizations() throws ApiException {
		ApiClient client = getApiClient();
		MasterDataOrganisationsApi mdOrganisationsApi = new MasterDataOrganisationsApi(client);
		OrganisationModelPageResultModel result = mdOrganisationsApi.apiV10MasterdataOrganisationsGet(null, null, null, null, null);
		System.out.println("Organisations count:" + result.getCount()); 
		System.out.println(result);
	}

	@Test @Ignore
	public void getDistributors() throws ApiException {
		ApiClient client = getApiClient();
		MasterDataDistributorsApi mdDistributors = new MasterDataDistributorsApi(client);
		System.out.println(mdDistributors.apiV10MasterdataDistributorsGet(null, null, null));
	}


	@Test @Ignore
	public void getSession() throws ApiException {
		ApiClient client = getApiClient();
		AuthenticationApi apiInstance = new AuthenticationApi(client);
		Session session = apiInstance.apiV10AuthenticateSessionGet();
		System.out.println(session);
	}

	@Test @Ignore
	public void createTerminal() throws ApiException {
		ApiClient client = getApiClient();

		Locale locale = new Locale("en", "DK");
		UUID organisationId = UUID.fromString("0deface0-0000-0000-0000-036a413a5104"); // UUID.randomUUID();
		UUID storeId = UUID.fromString("0deface0-0000-0000-0000-************"); // UUID.randomUUID();
		UUID terminalId = UUID.fromString("0deface0-0000-0000-0000-733314a10000"); // UUID.randomUUID();

		// Create organization
		Organisation organisation = new Organisation()
				.city("test-city")                                      // ["The City field is required."]
				.street("test-street")                                  // ["The Street field is required."]
				.postalCode("ABCDEFGHIJKL")                             // ["The PostalCode field is required."]; max: 12
				.countryCode(locale.getISO3Country())                   // ["The CountryCode field is required."]
				.streetNumber("test-streetNumber")                      // ["The StreetNumber field is required."]
				.addCompanyIdentificationItem(new CompanyIdentification()
						.type(IdentificationType._0_VATNUMBER)
						.identification("0")                            // column does not allow nulls
				)
				.caption("test-organization")                           // column does not allow nulls
				.fiscalCountry(FiscalCountry._4_DENMARK) // FiscalCountry.NUMBER_4: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark
				.organisationId(organisationId)
				.distributorId(UUID.fromString(retailForceDistributorId))
				.clientConfigurationId(UUID.fromString(retailForceConfigurationId))
				;
		MasterDataOrganisationsApi mdOrganisations = new MasterDataOrganisationsApi(client);
		System.out.println(mdOrganisations.apiV10MasterdataOrganisationsPost(organisation));

		// Create store
		Store store = new Store()
				.city("test-city")                    // ["The City field is required."]
				.street("test-street")                // ["The Street field is required."]
				.postalCode("abcdefghijkl")           // ["The PostalCode field is required."]; max: 12
				.countryCode(locale.getISO3Country()) // ["The CountryCode field is required."]
				.streetNumber("test-streetNumber")    // ["The StreetNumber field is required."]
				.storeNumber("")                      // column does not allow nulls
				.caption("")                          // column does not allow nulls
				.organisationId(organisationId)
				.storeId(storeId)
				;
		MasterDataStoresApi mdStores = new MasterDataStoresApi(client);
		System.out.println(mdStores.apiV10MasterdataStoresPost(store));

		// Create terminal
		Terminal terminal = new Terminal()
				.terminalNumber("1")       // Value cannot be null. (Parameter 'terminalNumber') (cannot be empty)
				.storeId(storeId)          // Property storeId of terminal must not be Guid.Empty
				.terminalId(terminalId)
				.isTest(retailForceTest)
				;
		MasterDataTerminalsApi mdTerminals = new MasterDataTerminalsApi(client);
		System.out.println(mdTerminals.apiV10MasterdataTerminalsPost(terminal));

//		SecurityApi securityApi = new SecurityApi(client);
//		System.out.println(securityApi.apiV10SecurityApikeyEntityIdPost(organisationId));

		mdTerminals.apiV10MasterdataTerminalsTerminalIdDelete(terminalId);
		mdStores.apiV10MasterdataStoresStoreIdDelete(storeId);
		mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, null, null);

		logout(client);
	}

	/*
Violation of PRIMARY KEY constraint 'PK_OrganisationIdentification'. Cannot insert duplicate key in object 'Cloud.OrganisationIdentification'.
The duplicate key value is (0deface0-0000-0000-0000-036a413a5104, 0, 0000).
INSERT INTO Cloud.OrganisationIdentification ([OrganisationId],[IdentificationType],[Identification])
VALUES (@OrganisationId,@Type,@Identification)

Violation of UNIQUE KEY constraint 'IX_OrganisationIdentification'. Cannot insert duplicate key in object 'Cloud.OrganisationIdentification
The duplicate key value is (0, 0001).
INSERT INTO Cloud.OrganisationIdentification ([OrganisationId],[IdentificationType],[Identification])
VALUES (@OrganisationId,@Type,@Identification)

Given organisationId must be equal to OrganisationId property of parameter organisation.
	 */
	@Test @Ignore
	public void createTwoOrganisations() throws ApiException {
		ApiClient client = getApiClient();

		Locale locale = new Locale("en", "DK");
		UUID organisationId = UUID.fromString("0deface0-0000-0000-0000-036a413a5104"); // UUID.randomUUID();
		boolean organisationCreated = false;
		UUID organisation2Id = UUID.fromString("0deface0-0000-0000-0002-036a413a5104"); // UUID.randomUUID();
		boolean organisation2Created = false;

		MasterDataOrganisationsApi mdOrganisations = new MasterDataOrganisationsApi(client);

		try {

			// Create organization
			Organisation organisation = new Organisation()
					.city("test-city")                                      // ["The City field is required."] 
					.street("test-street")                                  // ["The Street field is required."]
					.postalCode("ABCDEFGHIJKL")                             // ["The PostalCode field is required."]; max: 12
					.countryCode(locale.getISO3Country())                   // ["The CountryCode field is required."]
					.streetNumber("test-streetNumber")                      // ["The StreetNumber field is required."]
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification("0000")                            // column does not allow nulls
					)
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification("0001")                            // column does not allow nulls
					)
					.caption("test-organization")                           // column does not allow nulls
					.fiscalCountry(FiscalCountry._4_DENMARK) // FiscalCountry.NUMBER_4: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark
					.distributorId(UUID.fromString(retailForceDistributorId))
					.clientConfigurationId(UUID.fromString(retailForceConfigurationId))
					.organisationId(organisationId)
					;
			System.out.println(mdOrganisations.apiV10MasterdataOrganisationsPost(organisation));
			organisationCreated = true;

			Organisation organisationM = new Organisation()
					.city("test-city")                                      // ["The City field is required."] 
					.street("test-street")                                  // ["The Street field is required."]
					.postalCode("ABCDEFGHIJKL")                             // ["The PostalCode field is required."]; max: 12
					.countryCode(locale.getISO3Country())                   // ["The CountryCode field is required."]
					.streetNumber("test-streetNumber")                      // ["The StreetNumber field is required."]
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification("0000")                            // column does not allow nulls
					)
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._3_BUSINESSIDENTIFICATIONNUMBER)
							.identification("3333")                            // column does not allow nulls
					)
					.caption("test-organization")                           // column does not allow nulls
					.fiscalCountry(FiscalCountry._4_DENMARK) // FiscalCountry.NUMBER_4: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark
					.distributorId(UUID.fromString(retailForceDistributorId))
					.clientConfigurationId(UUID.fromString(retailForceConfigurationId))
					.organisationId(organisationId)
					;
			System.out.println(mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdPut(organisationId, organisationM));

			Organisation organisation2 = new Organisation()
					.city("test-city2")                                      // ["The City field is required."] 
					.street("test-street2")                                  // ["The Street field is required."]
					.postalCode("ABCDEFGHIJKL")                             // ["The PostalCode field is required."]; max: 12
					.countryCode(locale.getISO3Country())                   // ["The CountryCode field is required."]
					.streetNumber("test-streetNumber")                      // ["The StreetNumber field is required."]
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification("000")                            // column does not allow nulls
					)
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._3_BUSINESSIDENTIFICATIONNUMBER)
							.identification("333")                            // column does not allow nulls
					)
					.caption("test-organization")                           // column does not allow nulls
					.fiscalCountry(FiscalCountry._4_DENMARK) // FiscalCountry.NUMBER_4: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark
					.distributorId(UUID.fromString(retailForceDistributorId))
					.clientConfigurationId(UUID.fromString(retailForceConfigurationId))
					.organisationId(organisation2Id)
					;
			System.out.println(mdOrganisations.apiV10MasterdataOrganisationsPost(organisation2));
			organisation2Created = true;

		} finally {

			if (organisation2Created)
				mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdDelete(organisation2Id, null, null);
			if (organisationCreated)
				mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, null, null);

		}

		logout(client);
	}

	/*
	 * Invalid object name 'Cloud.Organsisation_Security'
	 */
	@Test @Ignore
	public void createCredentions() throws ApiException {
		ApiClient client = getApiClient();

		Locale locale = new Locale("en", "DK");
		UUID organisationId = UUID.fromString("0deface0-0000-0000-0000-036a413a5104"); // UUID.randomUUID();
		boolean organisationCreated = false;

		MasterDataOrganisationsApi mdOrganisations = new MasterDataOrganisationsApi(client);
		//SecurityApi securityApi = new SecurityApi(client);

		try {

			// Create organization
			Organisation organisation = new Organisation()
					.city("test-city")                                      // ["The City field is required."] 
					.street("test-street")                                  // ["The Street field is required."]
					.postalCode("ABCDEFGHIJKL")                             // ["The PostalCode field is required."]; max: 12
					.countryCode(locale.getISO3Country())                   // ["The CountryCode field is required."]
					.streetNumber("test-streetNumber")                      // ["The StreetNumber field is required."]
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification("0000")                            // column does not allow nulls
					)
					.caption("test-organization")                           // column does not allow nulls
					.fiscalCountry(FiscalCountry._4_DENMARK) // FiscalCountry.NUMBER_4: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark
					.distributorId(UUID.fromString(retailForceDistributorId))
					.clientConfigurationId(UUID.fromString(retailForceConfigurationId))
					.organisationId(organisationId)
					;
			System.out.println(mdOrganisations.apiV10MasterdataOrganisationsPost(organisation));
			organisationCreated = true;

			// Create ApiKey
//			System.out.println(securityApi.apiV10SecurityApikeyEntityIdPost(organisationId));
//			System.out.println(securityApi.apiV10SecurityApikeyEntityIdPost(organisationId));

			// Try delete ApiKey
			// ...

//			System.out.println(securityApi.apiV10SecurityAccessEntityIdGet(organisationId, null, null));
//			System.out.println(securityApi.apiV10SecurityAccessEntityIdGet(UUID.fromString(retailForceDistributorId), null, null));
//			System.out.println(securityApi.apiV10SecurityApikeyEntityIdGet(organisationId));
//			List<ApiKey> l = securityApi.apiV10SecurityApikeyEntityIdGet(UUID.fromString(retailForceDistributorId));
//			for (ApiKey apiKey : l) {
//				System.out.println(apiKey);
//				System.out.println(apiKey.getSecret());				
//			}
			//System.out.println(securityApi.apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet(organisationId).size());


		} finally {

			if (organisationCreated)
				mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, null, null);

		}

		logout(client);
	}

//	@Test
//	public void t1() throws ApiException {
//		ApiClient client = getApiClient();
//
//		UUID organisationId = UUID.fromString("daa0a510-4aba-4525-85eb-21491f44946e"); // UUID.randomUUID();
//		MasterDataStoresApi mdStores = new MasterDataStoresApi(client);
//		StoreModelPageResultModel stores = mdStores.apiV10MasterdataStoresGet(organisationId, null, null, null, null, true);
//		for (StoreModel store : stores.getItems()) {
//			System.out.println(store);
//		}
//
//		UUID storeId = UUID.fromString("553a2923-892e-49a8-94df-6ccc66c2d116"); // UUID.randomUUID();
//		//UUID terminalId = UUID.fromString("0deface0-0000-0000-0000-733314a10000"); // UUID.randomUUID();
//
//		MasterDataTerminalsApi mdTerminals = new MasterDataTerminalsApi(client);
//		TerminalModelPageResultModel terminals = mdTerminals.apiV10MasterdataTerminalsGet(storeId, organisationId, null, null, null, null);
//		for (TerminalModel terminal : terminals.getItems()) {
//			System.out.println(terminal);
//		}
//	}

	/*
String or binary data would be truncated.
The statement has been terminated.
INSERT INTO Cloud.Organisation ([OrganisationId],[DistributorId],[ContactPrincipal],[TechnicalContactPrincipal],[EMail],[Caption],[Caption2],[Description],[FiscalYearStartMonth],[FiscalCountry],[LegalForm],[Capital],[ClientConfiguration],[NewClientConfiguration],[NewClientConfigurationValidFrom],[Street],[StreetNumber],[PostalCode],[City],[Community],[CountryCode])
VALUES (@OrganisationId,@DistributorId,@ContactPrincipal,@TechnicalContactPrincipal,@EMail,@Caption,@Caption2,@Description,@FiscalYearStartMonth,@FiscalCountry,@LegalForm,@Capital,@ClientConfigurationId,@NewClientConfigurationId,@NewClientConfigurationValidFrom,@Street,@StreetNumber,@PostalCode,@City,@Community,@CountryCode)

String or binary data would be truncated.
The statement has been terminated.
INSERT INTO Cloud.Organisation ([OrganisationId],[DistributorId],[ContactPrincipal],[TechnicalContactPrincipal],[EMail],[Caption],[Caption2],[Description],[FiscalYearStartMonth],[FiscalCountry],[LegalForm],[Capital],[ClientConfiguration],[NewClientConfiguration],[NewClientConfigurationValidFrom],[Street],[StreetNumber],[PostalCode],[City],[Community],[CountryCode])
VALUES (@OrganisationId,@DistributorId,@ContactPrincipal,@TechnicalContactPrincipal,@EMail,@Caption,@Caption2,@Description,@FiscalYearStartMonth,@FiscalCountry,@LegalForm,@Capital,@ClientConfigurationId,@NewClientConfigurationId,@NewClientConfigurationValidFrom,@Street,@StreetNumber,@PostalCode,@City,@Community,@CountryCode)

String or binary data would be truncated.
The statement has been terminated.
INSERT INTO Cloud.Store ([StoreId],[OrganisationId],[CompanyId],[StoreNumber],[Caption],[FiscalRegion],[Telephone],[Fax],[EMail],[OpeningDate],[ClosingDate],[HiddenDate],[ClientConfiguration],[NewClientConfiguration],[NewClientConfigurationValidFrom],[Street],[StreetNumber],[PostalCode],[City],[Community],[CountryCode])
VALUES (@StoreId,@OrganisationId,@CompanyId,@StoreNumber,@Caption,@FiscalRegion,@Telephone,@Fax,@EMail,@OpeningDate,@ClosingDate,@HiddenDate,@ClientConfigurationId,@NewClientConfigurationId,@NewClientConfigurationValidFrom,@Street,@StreetNumber,@PostalCode,@City,@Community,@CountryCode)

String or binary data would be truncated.
The statement has been terminated.
INSERT INTO Cloud.Terminal ([TerminalId],[StoreId],[TerminalNumber],[Caption],[IsTest],[ClientConfiguration],[NewClientConfiguration],[NewClientConfigurationValidFrom])
VALUES (@TerminalId,@StoreId,@TerminalNumber,@Caption,@IsTest,@ClientConfigurationId,@NewClientConfigurationId,@NewClientConfigurationValidFrom)


Country code not an iso alpha 3 country code. (Parameter 'CountryCode')
	 */
	@Test @Ignore
	public void maxFieldSizes() throws ApiException {
		ApiClient client = getApiClient();

		Locale locale = new Locale("en", "DK");
		UUID organisationId = UUID.fromString("0deface0-0000-0000-0000-036a413a5104"); // UUID.randomUUID();
		UUID storeId = UUID.fromString("0deface0-0000-0000-0000-************"); // UUID.randomUUID();
		UUID terminalId = UUID.fromString("0deface0-0000-0000-0000-733314a10000"); // UUID.randomUUID();
		boolean organisationCreated = false;
		boolean storeCreated = false;
		boolean terminalCreated = false;

		MasterDataOrganisationsApi mdOrganisations = new MasterDataOrganisationsApi(client);
		MasterDataStoresApi mdStores = new MasterDataStoresApi(client);
		MasterDataTerminalsApi mdTerminals = new MasterDataTerminalsApi(client);

//		mdTerminals.apiV10MasterdataTerminalsTerminalIdDelete(terminalId);
//		mdStores.apiV10MasterdataStoresStoreIdDelete(storeId);
//		mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, null, null);
		
		try {

			// Create organization
			Organisation organisation = new Organisation()
					.city("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // ["The City field is required."]; max: 100 
					.street("01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // ["The Street field is required."]; max: 200
					.postalCode("ABCDEFGHIJKL")                             // ["The PostalCode field is required."]; max: 12
					.countryCode(locale.getISO3Country())                   // ["The CountryCode field is required."]; Country code not an iso alpha 3 country code
					.streetNumber("ABCDEFGHIJKLMNOPQRST")                   // ["The StreetNumber field is required."]; max: 20
					.community("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // "The Community field is required."; max: 100
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // column does not allow nulls; max: 100
					)
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._3_BUSINESSIDENTIFICATIONNUMBER)
							.identification("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // column does not allow nulls; max: 100
					)
					.caption("01234567890123456789012345678901234567890123456789") // column does not allow nulls; max: 50
					.fiscalCountry(FiscalCountry._4_DENMARK) // FiscalCountry.NUMBER_4: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark
					.fiscalYearStartMonth(1) // Fiscal year start month must be between 1 and 12.
					.distributorId(UUID.fromString(retailForceDistributorId))
					.clientConfigurationId(UUID.fromString(retailForceConfigurationId))
					.organisationId(organisationId)
					;
			System.out.println(mdOrganisations.apiV10MasterdataOrganisationsPost(organisation));
			organisationCreated = true;

			// Create store
			Store store = new Store()
					.city("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // ["The City field is required."]; max: 100
					.street("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // ["The Street field is required."]; max: 100
					.postalCode("abcdefghijkl") // ["The PostalCode field is required."]; max: 12
					.countryCode(locale.getISO3Country()) // ["The CountryCode field is required."]; Country code not an iso alpha 3 country code
					.streetNumber("ABCDEFGHIJKLMNOPQRST") // ["The StreetNumber field is required."]; max: 20
					.community("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // "The Community field is required."; max: 100
					.storeNumber("01234567890123456789012345678901234567890123456789") // column does not allow nulls; max: 50
					.caption("0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789") // column does not allow nulls; max: 100
					.organisationId(organisationId)
					.storeId(storeId)
					;
			System.out.println(mdStores.apiV10MasterdataStoresPost(store));
			storeCreated = true;

			// Create terminal
			Terminal terminal = new Terminal()
					.terminalNumber("0123456789")       // Value cannot be null. (Parameter 'terminalNumber') (cannot be empty); max: 10
					.caption("01234567890123456789012345678901234567890123456789") // max: 50
					.storeId(storeId)          // Property storeId of terminal must not be Guid.Empty
					.terminalId(terminalId)
					.platformType(PlatformType.WINDOWS)
					.isTest(retailForceTest)
					;
			System.out.println(mdTerminals.apiV10MasterdataTerminalsPost(terminal));
			terminalCreated = true;

		} catch (Exception e) {

			e.printStackTrace();
			throw e;

		} finally {

			if (terminalCreated)
				mdTerminals.apiV10MasterdataTerminalsTerminalIdDelete(terminalId);

			if (storeCreated)
				mdStores.apiV10MasterdataStoresStoreIdDelete(storeId);

			if (organisationCreated)
				mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, null, null);

		}

		logout(client);
	}

	private ApiClient getApiClient() throws ApiException {
		ApiClient defaultClient = Configuration.getDefaultApiClient();
		defaultClient.setBasePath(retailForceApiBasePath);

		String apiKey = getApiKey(defaultClient);

		// Configure API key authorization: Bearer
		ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
		Bearer.setApiKey(apiKey);
		Bearer.setApiKeyPrefix("Bearer");

		return defaultClient;
	}

	private String getApiKey(ApiClient apiClient) throws ApiException {
		AuthenticationApi apiInstance = new AuthenticationApi(apiClient);
		return apiInstance.apiV10AuthenticateLogon2Post(retailForceApiKey, retailForceApiSecret);
	}

	private void logout(ApiClient apiClient) throws ApiException {
		AuthenticationApi apiInstance = new AuthenticationApi(apiClient);
		apiInstance.apiV10AuthenticateLogoutPost();
	}

}

