package eu.untill.license.server;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.maven.artifact.versioning.ComparableVersion;
import org.scm4j.deployer.api.ProductInfo;
import org.scm4j.deployer.engine.DeployerEngine;

import com.google.common.io.Files;

import eu.untill.license.shared.ShProductInfo;

public class DeployerEngineHandler {

	private static final String PRODUCT_LIST_URL = "https://dev.untill.com/artifactory/repo";
	private static final long PRODUCTS_REFRESH_FREQUENCY = 15 * 60 * 1000; // 15 min
	private static final long PRODUCT_VERSIONS_REFRESH_FREQUENCY = 5 * 60 * 1000; // 5 min
	
	// DeployerEngine "singleton"
	private volatile DeployerEngine deployerEngine;
	private long lastProductsRefreshTime = 0;
	private Map<String, Long> lastProductVersionsRefreshTimeMap = new HashMap<>();
	

	private DeployerEngine getDeployerEngine() {
		DeployerEngine localDeployerEngine = deployerEngine;
		if (localDeployerEngine == null) {
			synchronized (DeployerEngine.class) {
				localDeployerEngine = deployerEngine;
				if (localDeployerEngine == null) {
					File workingFolder = Files.createTempDir();
					workingFolder.deleteOnExit();
					deployerEngine = localDeployerEngine = new DeployerEngine(null, workingFolder, PRODUCT_LIST_URL);
				}
			}
		}
		return deployerEngine;
	}

	public Map<String, ShProductInfo> getProductList() {
		Map<String, ShProductInfo> result = new HashMap<>();
		DeployerEngine deployerEngine = getDeployerEngine();
		synchronized (deployerEngine) {
			boolean refresh = System.currentTimeMillis() > lastProductsRefreshTime + PRODUCTS_REFRESH_FREQUENCY;
			Map<String, ProductInfo> productList = refresh ? deployerEngine.refreshProducts() : deployerEngine.listProducts();
			for (String product : productList.keySet()) {
				ProductInfo productInfo = productList.get(product);
				result.put(product, new ShProductInfo(productInfo.getArtifactId(), productInfo.isHidden()));
			}
			if (refresh)
				lastProductsRefreshTime = System.currentTimeMillis();
		}
		return result;
	}

	public Map<String, Boolean> getProductVersionList(String product) {
		Map<String, Boolean> result;
		DeployerEngine deployerEngine = getDeployerEngine();
		synchronized (deployerEngine) {
			Long lastVersionsRefreshTime = lastProductVersionsRefreshTimeMap.get(product);
			boolean refresh = lastVersionsRefreshTime == null
					|| System.currentTimeMillis() > lastVersionsRefreshTime + PRODUCT_VERSIONS_REFRESH_FREQUENCY;
			result = refresh ? deployerEngine.refreshProductVersions(product)
					: deployerEngine.listProductVersions(product);
			if (refresh)
				lastProductVersionsRefreshTimeMap.put(product, System.currentTimeMillis());
		}
		// filter and sort result
		List<String> versionList = new ArrayList<>(result.keySet());
		versionList.removeIf(version -> version == null || version.endsWith("-SNAPSHOT"));
		versionList.sort((v1, v2) -> Comparator.nullsLast(ComparableVersion::compareTo).compare(
				v2 == null ? null : new ComparableVersion(v2), v1 == null ? null : new ComparableVersion(v1)));
		return versionList.stream().collect(Collectors.toMap(
				Function.identity(),
				version -> result.get(version),
				(u, v) -> { throw new IllegalStateException(String.format("Duplicate key %s", u)); },
				LinkedHashMap::new));
	}

}
