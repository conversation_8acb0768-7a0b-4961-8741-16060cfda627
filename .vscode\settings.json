{"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "java.debug.settings.onBuildFailureProceed": true, "java.debug.settings.console": "internalConsole", "java.debug.settings.hotCodeReplace": "auto", "java.project.sourcePaths": ["src", "test"], "java.project.outputPath": "build/classes", "java.project.referencedLibraries": ["build/libs/**/*.jar"], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "build/": true, ".gradle/": true, "gwt-unitCache/": true, "war/WEB-INF/classes/": true, "war/WEB-INF/lib/": true, "war/untilllicenseserver/": true, "www-test/": true}, "editor.tabSize": 4, "editor.insertSpaces": false, "editor.detectIndentation": true, "java.saveActions.organizeImports": true, "java.sources.organizeImports.starThreshold": 99, "java.sources.organizeImports.staticStarThreshold": 99}