/**
 * 
 */
package eu.untill.license.server;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class Order {

	public List<Item> itemList = null;
	public boolean priceExcludesVat = false;

	public static class Item {

		int quantity;
		long articleId;
		int articleNumber;
		String articleName;
		BigDecimal price;
		BigDecimal originalPrice;
		Double vat;
		boolean negative;
		BigDecimal vatPercent;
		String text = null;
		List<Option> optionList = null;

		boolean priceExcludesVat;

		public static class Option {
			
			int quantity;
			long articleId;
			int articleNumber;
			String articleName;
			BigDecimal price;
			BigDecimal originalPrice;
			Double vat;
			boolean negative;
			BigDecimal vatPercent;
			String text = null;
		}

	}

	@Override public String toString() { return "[Order ("
		+ (itemList != null ? Integer.toString(itemList.size()) : "0") + ")]"; }
}
