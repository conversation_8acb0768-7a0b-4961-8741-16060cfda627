<?xml version="1.0" encoding="UTF-8"?>
<data>
	<categories>
		<category>
			<id>1017</id>
			<name>Licenses-26.3</name>
		</category>
	</categories>
	<printers>
	</printers>
	<groups>
		<group>
			<id>2017</id>
			<name>Licenses-26.3</name>
			<vat></vat>
			<idCategory>1017</idCategory>
			<preparationArea>Licenses</preparationArea>
		</group>
	</groups>
	<options>
	</options>
	<departments>
		<department>
			<id>3017</id>
			<number>263</number>
			<name>Licenses-26.3</name>
			<idGroup>2017</idGroup>
			<minNumber></minNumber>
			<maxNumber></maxNumber>
			<supplement></supplement>
			<condiment></condiment>
		</department>
	</departments>
	<articles>
		<article>
			<id>6700</id>
			<name>unTill(R) Apaleo license one year</name>
			<price></price>
			<barcode>APALEO1Y</barcode>
			<idDepartment>3017</idDepartment>
			<idFreeOption></idFreeOption>
		</article>
		<article>
			<id>6701</id>
			<name>unTill(R) Upgrade Apaleo license one year</name>
			<price></price>
			<barcode>UAPALEO1Y</barcode>
			<idDepartment>3017</idDepartment>
			<idFreeOption></idFreeOption>
		</article>
	</articles>
	<option_structures>
	</option_structures>
	<art_options>
	</art_options>
	<users>
	</users>
</data>
