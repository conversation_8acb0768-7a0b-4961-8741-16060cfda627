package eu.untill.license.shared;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

public class SuSubTask implements IsSerializable {

	public enum Status { CREATED, PREPARING, PREPARED, WAIT_FOR_EXECUTING, EXECUTING, SUCCESS, FA<PERSON>UR<PERSON>, UNKNOWN }

	public long subTaskId;
	public long taskId;
	public long hostId;

	public Status status;
	public Date statusChangeTime;
	public Date statusSentTime;
	public String result;

	// auxiliary fields
	public SuHost host;
	
	@Override
	public String toString() {
		return "[" + getClass().getSimpleName() + ": " + Long.toString(subTaskId) + ", " + status + "]";
	}

}
