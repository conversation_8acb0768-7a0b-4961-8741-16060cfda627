package eu.untill.license.server;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * Test class to verify the driver diff logic
 */
public class DriverDiffTest {

	/**
	 * Test the logic for finding drivers that are in license but not in prevLicense
	 */
	@Test
	public void testDriverDiffLogic() {
		// Test case 1: license has drivers, prevLicense is null
		List<String> licenseDrivers1 = Arrays.asList("driver1", "driver2", "driver3");
		List<String> prevLicenseDrivers1 = null;

		List<String> result1 = calculateDriverDiff(licenseDrivers1, prevLicenseDrivers1);
		assertEquals(3, result1.size());
		assertTrue(result1.contains("driver1"));
		assertTrue(result1.contains("driver2"));
		assertTrue(result1.contains("driver3"));

		// Test case 2: license has drivers, prevLicense has some drivers
		List<String> licenseDrivers2 = Arrays.asList("driver1", "driver2", "driver3", "driver4");
		List<String> prevLicenseDrivers2 = Arrays.asList("driver1", "driver3");

		List<String> result2 = calculateDriverDiff(licenseDrivers2, prevLicenseDrivers2);
		assertEquals(2, result2.size());
		assertTrue(result2.contains("driver2"));
		assertTrue(result2.contains("driver4"));
		assertFalse(result2.contains("driver1"));
		assertFalse(result2.contains("driver3"));

		// Test case 3: license has no new drivers
		List<String> licenseDrivers3 = Arrays.asList("driver1", "driver2");
		List<String> prevLicenseDrivers3 = Arrays.asList("driver1", "driver2", "driver3");

		List<String> result3 = calculateDriverDiff(licenseDrivers3, prevLicenseDrivers3);
		assertEquals(0, result3.size());

		// Test case 4: license is null
		List<String> licenseDrivers4 = null;
		List<String> prevLicenseDrivers4 = Arrays.asList("driver1", "driver2");

		List<String> result4 = calculateDriverDiff(licenseDrivers4, prevLicenseDrivers4);
		assertEquals(0, result4.size());

		// Test case 5: both are empty
		List<String> licenseDrivers5 = new ArrayList<>();
		List<String> prevLicenseDrivers5 = new ArrayList<>();

		List<String> result5 = calculateDriverDiff(licenseDrivers5, prevLicenseDrivers5);
		assertEquals(0, result5.size());
	}

	/**
	 * Helper method that implements the same logic as in UntillTpapiHelperImpl
	 */
	private List<String> calculateDriverDiff(List<String> licenseDrivers, List<String> prevLicenseDrivers) {
		List<String> newDrivers = new ArrayList<>();
		if (licenseDrivers != null) {
			Set<String> prevDriversSet = prevLicenseDrivers == null ? new HashSet<>()
					: new HashSet<>(prevLicenseDrivers);
			for (String driver : licenseDrivers) {
				if (!prevDriversSet.contains(driver)) {
					newDrivers.add(driver);
				}
			}
		}
		return newDrivers;
	}
}
