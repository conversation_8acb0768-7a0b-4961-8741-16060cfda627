package eu.untill.license.server;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Test;
import static org.junit.Assert.*;

import eu.untill.license.shared.DbLicense;

/**
 * Test class to verify the driver diff and combine logic
 */
public class DriverDiffTest {

	/**
	 * Test the logic for finding drivers that are in license but not in prevLicense
	 */
	@Test
	public void testDriverDiffLogic() {
		// Test case 1: license has drivers, prevLicense is null
		List<String> licenseDrivers1 = Arrays.asList("driver1", "driver2", "driver3");
		List<String> prevLicenseDrivers1 = null;

		List<String> result1 = calculateDriverDiff(licenseDrivers1, prevLicenseDrivers1);
		assertEquals(3, result1.size());
		assertTrue(result1.contains("driver1"));
		assertTrue(result1.contains("driver2"));
		assertTrue(result1.contains("driver3"));

		// Test case 2: license has drivers, prevLicense has some drivers
		List<String> licenseDrivers2 = Arrays.asList("driver1", "driver2", "driver3", "driver4");
		List<String> prevLicenseDrivers2 = Arrays.asList("driver1", "driver3");

		List<String> result2 = calculateDriverDiff(licenseDrivers2, prevLicenseDrivers2);
		assertEquals(2, result2.size());
		assertTrue(result2.contains("driver2"));
		assertTrue(result2.contains("driver4"));
		assertFalse(result2.contains("driver1"));
		assertFalse(result2.contains("driver3"));

		// Test case 3: license has no new drivers
		List<String> licenseDrivers3 = Arrays.asList("driver1", "driver2");
		List<String> prevLicenseDrivers3 = Arrays.asList("driver1", "driver2", "driver3");

		List<String> result3 = calculateDriverDiff(licenseDrivers3, prevLicenseDrivers3);
		assertEquals(0, result3.size());

		// Test case 4: license is null
		List<String> licenseDrivers4 = null;
		List<String> prevLicenseDrivers4 = Arrays.asList("driver1", "driver2");

		List<String> result4 = calculateDriverDiff(licenseDrivers4, prevLicenseDrivers4);
		assertEquals(0, result4.size());

		// Test case 5: both are empty
		List<String> licenseDrivers5 = new ArrayList<>();
		List<String> prevLicenseDrivers5 = new ArrayList<>();

		List<String> result5 = calculateDriverDiff(licenseDrivers5, prevLicenseDrivers5);
		assertEquals(0, result5.size());
	}

	/**
	 * Helper method that implements the same logic as in UntillTpapiHelperImpl
	 */
	private List<String> calculateDriverDiff(List<String> licenseDrivers, List<String> prevLicenseDrivers) {
		List<String> newDrivers = new ArrayList<>();
		if (licenseDrivers != null) {
			Set<String> prevDriversSet = prevLicenseDrivers == null ? new HashSet<>()
					: new HashSet<>(prevLicenseDrivers);
			for (String driver : licenseDrivers) {
				if (!prevDriversSet.contains(driver)) {
					newDrivers.add(driver);
				}
			}
		}
		return newDrivers;
	}

	/**
	 * Test DbLicense.combineDrivers method
	 */
	@Test
	public void testCombineDrivers() {
		// Test case 1: both lists have drivers
		List<String> first1 = Arrays.asList("driver1", "driver2");
		List<String> second1 = Arrays.asList("driver2", "driver3", "driver4");
		List<String> result1 = DbLicense.combineDrivers(first1, second1);
		assertEquals(4, result1.size());
		assertEquals("driver1", result1.get(0)); // order preserved
		assertEquals("driver2", result1.get(1)); // no duplicates
		assertEquals("driver3", result1.get(2));
		assertEquals("driver4", result1.get(3));

		// Test case 2: first list is null
		List<String> result2 = DbLicense.combineDrivers(null, second1);
		assertEquals(3, result2.size());
		assertTrue(result2.containsAll(second1));

		// Test case 3: second list is null
		List<String> result3 = DbLicense.combineDrivers(first1, null);
		assertEquals(2, result3.size());
		assertTrue(result3.containsAll(first1));

		// Test case 4: both lists are null
		List<String> result4 = DbLicense.combineDrivers(null, null);
		assertEquals(0, result4.size());
	}

	/**
	 * Test DbLicense.getDriversDifference method
	 */
	@Test
	public void testGetDriversDifference() {
		// Test case 1: second has drivers, first has some drivers
		List<String> first1 = Arrays.asList("driver1", "driver3");
		List<String> second1 = Arrays.asList("driver1", "driver2", "driver3", "driver4");
		List<String> result1 = DbLicense.getDriversDifference(first1, second1);
		assertEquals(2, result1.size());
		assertTrue(result1.contains("driver2"));
		assertTrue(result1.contains("driver4"));

		// Test case 2: first is null
		List<String> result2 = DbLicense.getDriversDifference(null, second1);
		assertEquals(4, result2.size());
		assertTrue(result2.containsAll(second1));

		// Test case 3: second is null
		List<String> result3 = DbLicense.getDriversDifference(first1, null);
		assertEquals(0, result3.size());

		// Test case 4: both are null
		List<String> result4 = DbLicense.getDriversDifference(null, null);
		assertEquals(0, result4.size());

		// Test case 5: no new drivers
		List<String> result5 = DbLicense.getDriversDifference(second1, first1);
		assertEquals(0, result5.size());
	}
}
