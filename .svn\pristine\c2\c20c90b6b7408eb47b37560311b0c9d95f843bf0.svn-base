/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;

import eu.untill.license.client.UntillLicenseServer;

/**
 * <AUTHOR>
 *
 */
public class WaitDialog {

	public static interface UlsMessages extends Messages {
		String pleaseWaitMessage();
	}

	private static UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	static PopupPanel popupPanel = null;
	static Label messageLabel = null;
	static int count = 0;

	public static void show() {
		if (count == 0) {
			popupPanel = new PopupPanel(false, true);
			HorizontalPanel panel = new HorizontalPanel();
			panel.setVerticalAlignment(HasVerticalAlignment.ALIGN_MIDDLE);
			panel.setSpacing(10);
			panel.add(new Image("images/progress.gif"));
			panel.add(messageLabel = new Label(messages.pleaseWaitMessage()));
			popupPanel.setWidget(panel);
			popupPanel.show();
			popupPanel.center();
		} else {
			messageLabel.setText(messageLabel.getText() + '.');
		}
		count++;
	}

	public static void hideAll() {
		if (count > 0) {
			count = 0;
			popupPanel.hide();
			popupPanel = null;
		}
	}

	public static void hide() {
		if (count == 1)
			hideAll();
		else
			if (count > 0) {
				count--;
				String message = messageLabel.getText();
				if (message.charAt(message.length() - 1) == '|')
					messageLabel.setText(message.substring(0, message.length() - 1));
			}
	}
}
