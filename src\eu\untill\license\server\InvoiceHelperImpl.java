/**
 *
 */
package eu.untill.license.server;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Set;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.sax.SAXResult;
import javax.xml.transform.stream.StreamSource;

import org.apache.fop.apps.FOUserAgent;
import org.apache.fop.apps.Fop;
import org.apache.fop.apps.FopFactory;
import org.apache.fop.apps.MimeConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import eu.untill.license.server.Common.ExtraField;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.License.DriverInfo;
import eu.untill.license.shared.License.FuncProp.FuncType;

/**
 * <AUTHOR>
 *
 */
public class InvoiceHelperImpl implements InvoiceHelper {

	static final Logger LOG = LoggerFactory.getLogger(InvoiceHelperImpl.class);

	private static class DealerIsNotFoundException extends Exception {
		private static final long serialVersionUID = 1L;
	}

	private static class TemplateIsNotFoundException extends Exception {
		private static final long serialVersionUID = 1L;
	}

	//private final Locale localeBE = new Locale("nl", "BE");
	private final Locale localeGB = new Locale("en", "GB");
	private final SimpleDateFormat dateFormatLoc = new SimpleDateFormat("dd-MM-yyyy");
	// XXX hard coded
	private final DecimalFormatSymbols decimalFormatSymbolsLoc = DecimalFormatSymbols.getInstance(localeGB); // XXX hard coded
	private final int defaultCurrencyScale = 2; // XXX hard coded
	private final int priceRoundingMode = BigDecimal.ROUND_HALF_UP; // XXX hard coded

	// Factories
	private DocumentBuilderFactory documentBuilderFactory = DocumentBuilderFactory.newInstance();
	private TransformerFactory transformerFactory = TransformerFactory.newInstance();
	private FopFactory fopFactory = FopFactory.newInstance();

	@Override
	public Invoice generateInvoiceForLicense(Connection conn, Settings settings, Date date, long billId,
			DbLicense license) {
		return generateInvoice(conn, settings, date, billId, license, 0, null);
	}

	@Override
	public Invoice generateInvoiceForDealer(Connection conn, Settings settings, Date date, long billId,
			long dealerId, String reference) {
		return generateInvoice(conn, settings, date, billId, null, dealerId, reference);
	}

	private Invoice generateInvoice(Connection conn, Settings settings, Date date, long billId,
			DbLicense license, long dealerId, String reference) {
		LOG.debug("ENTRY ({}, {}, {}, {}, {})", date, billId, license, dealerId, reference);

		Invoice result = null;
		Invoice invoice = new Invoice(date);
		try {

			Order order = Common.getOrderByBillId(conn, billId);

			Document xmlDoc = generateInvoiceXmlDocument(invoice, conn, settings, order, license, dealerId,
					reference);

			File invoicesPath = new File(settings.getInvoicesPath());
			if (!invoicesPath.exists())
				invoicesPath.mkdirs();
			File pdfFile = new File(invoicesPath, invoice.fullNumber.replace("/", "_") + ".pdf");
			for (int i = 1; pdfFile.exists() && i < 100; ++i)
				pdfFile = new File(invoicesPath, invoice.fullNumber.replace("/", "_") + "[" + i + "].pdf");
			File xslFile = settings.getTemplate("invoice.xsl");

			if (xslFile == null || !xslFile.exists())
				throw new TemplateIsNotFoundException();
/*
			// Write result xmlDoc to file
			try {
				DOMSource source = new DOMSource(xmlDoc);
				javax.xml.transform.stream.StreamResult streamResult =
						new javax.xml.transform.stream.StreamResult(new FileOutputStream(
								new File(invoicesPath, invoiceFullNumber.replace("/", "_") + ".xml")));
				TransformerFactory transFactory = TransformerFactory.newInstance();
				Transformer transformer = transFactory.newTransformer();
				transformer.setOutputProperty(javax.xml.transform.OutputKeys.INDENT, "yes");
				transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "4");
				transformer.transform(source, streamResult);
			} catch (FileNotFoundException e) {
				e.printStackTrace();
			} catch (TransformerConfigurationException e) {
				e.printStackTrace();
			} catch (TransformerException e) {
				e.printStackTrace();
			}
*/

			// Set fop.xconf and fontBaseURL
			File fopXconfFile = settings.getTemplate("fop.xconf");
			if (fopXconfFile.exists()) {
				fopFactory.getFontManager()
						.setFontBaseURL(fopXconfFile.getParentFile().toURI().toURL().toString());
				fopFactory.setUserConfig(fopXconfFile);
			}

			FOUserAgent foUserAgent = fopFactory.newFOUserAgent();
			// configure foUserAgent as desired

			// Set baseURL
			foUserAgent.setBaseURL(xslFile.getParentFile().toURI().toURL().toString());

			// Setup output
			OutputStream out = new BufferedOutputStream(new FileOutputStream(pdfFile));

			try {
				// Construct fop with desired output format and output stream
				Fop fop = fopFactory.newFop(MimeConstants.MIME_PDF, foUserAgent, out);

				//Setup XSLT
				Transformer transformer = transformerFactory.newTransformer(new StreamSource(xslFile));

				// Setup input for XSLT transformation
				Source src = new DOMSource(xmlDoc);

				// Resulting SAX events (the generated FO) must be piped through to FOP
				Result res = new SAXResult(fop.getDefaultHandler());

				// Start XSLT transformation and FOP processing
				transformer.transform(src, res);
			} finally {
				out.close();
			}

			invoice.file = pdfFile;
			result = invoice;

		} catch (Exception e) {
			LOG.error("generate invoice error", e);
		}

		LOG.debug("RETURN {}", result);
		return result;
	}

	  /////////////
	 // Private //
	/////////////

	private Document generateInvoiceXmlDocument(Invoice invoice, Connection conn, Settings settings, Order order,
			DbLicense license, long dealerId, String reference) throws SQLException, ParserConfigurationException,
			DealerIsNotFoundException, UnsupportedEncodingException {
		int currencyScale = defaultCurrencyScale;
		float currencyRate = 1.0f;

		// Generate xml document
		//documentBuilderFactory.setNamespaceAware(true);
		DocumentBuilder db = documentBuilderFactory.newDocumentBuilder();
		Document xmlDoc = db.newDocument();

		Element rootNode = addChildNode(xmlDoc, xmlDoc, "invoice");

		// generate dealer nodes
		Element dealerNode = addChildNode(xmlDoc, rootNode, "dealer");

		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {
			// Get license dealer base parameters
			stmt = conn.prepareStatement("SELECT D.*, C.*, COUNTRIES.NAME COUNTRIES_NAME\n"
					+ "FROM DEALERS D\n"
					+ "INNER JOIN CLIENTS C ON C.ID = D.ID_CLIENTS\n"
					+ "LEFT OUTER JOIN COUNTRIES ON COUNTRIES.ID = C.ID_COUNTRIES\n"
					+ "WHERE D.ID = ?\n");
			if (license == null)
				stmt.setLong(1, dealerId);
			else
				stmt.setLong(1, license.idDealers);
			rs = stmt.executeQuery();
			if (!rs.next()) {
				throw new DealerIsNotFoundException();
			}
			invoice.emails = rs.getString("INVOICE_EMAIL");
			// Fill dealer information
			addChildNode(xmlDoc, dealerNode, "name", rs.getString("NAME"));
			{Element addressNode = addChildNode(xmlDoc, dealerNode, "address");
				for (String addressLine : rs.getString("ADDRESS").split("\\r?\\n"))
					addChildNode(xmlDoc, addressNode, "line", addressLine);
			}
			{Element countriesNode = addChildNode(xmlDoc, dealerNode, "countries");
				addChildNode(xmlDoc, countriesNode, "name", rs.getString("COUNTRIES_NAME"));
			}
			long idExtraFieldValues = rs.getLong("ID_EXTRA_FIELD_VALUES");
			long idBanks = rs.getLong("ID_BANKS");

			if (idExtraFieldValues != 0) {
				// Add extra fields
				Element extraFieldsNode = addChildNode(xmlDoc, dealerNode, "extra-fields");
				Set<ExtraField> extraFields = Common.getExtraFileds(conn, idExtraFieldValues);
				for (ExtraField extraField : extraFields) {
					if (extraField.valAsString != null)
						addChildNode(xmlDoc, extraFieldsNode, getValidXmlNodeName(extraField.name),
								extraField.valAsString);
				}
			}

			if (idBanks != 0) {
				rs.close(); rs = null; stmt.close(); stmt = null;

				// Add optional bank and currency parameters
				stmt = conn.prepareStatement("SELECT BANKS.NAME, BANKS.DETAILS, "
						+ "CURRENCY.CODE, CURRENCY.ROUND, CURRENCY.RATE, CURRENCY.SYMBOL\n"
						+ "FROM BANKS\n"
						+ "LEFT OUTER JOIN CURRENCY ON CURRENCY.ID = BANKS.ID_CURRENCY\n"
						+ "WHERE BANKS.ID = ?\n");
				stmt.setLong(1, idBanks);
				rs = stmt.executeQuery();
				if (rs.next()) {
					Element bankNode = addChildNode(xmlDoc, rootNode, "bank");
					addChildNode(xmlDoc, bankNode, "name", rs.getString("NAME"));
					String details = rs.getString("DETAILS");
					if (details != null && !details.isEmpty()) {
						Element detailsNode = addChildNode(xmlDoc, bankNode, "details");
						for (String detailsLine : details.split("\\r?\\n"))
							addChildNode(xmlDoc, detailsNode, "line", detailsLine);
					}
					String currencyCode = rs.getString("CODE");
					if (currencyCode != null) {
						Element currencyNode = addChildNode(xmlDoc, bankNode, "currency");
						addChildNode(xmlDoc, currencyNode, "code", currencyCode);
						String currencySymbol = rs.getString("SYMBOL");
						if (currencySymbol != null)
							addChildNode(xmlDoc, currencyNode, "symbol", currencySymbol);
						currencyScale = rs.getInt("ROUND");
						currencyRate = rs.getFloat("RATE");
					}
				} else {
					LOG.warn("Bank with id = {} is not found", idBanks);
				}
			}

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		// generate license nodes
		if (license != null) {
			Element licenseNode = addChildNode(xmlDoc, rootNode, "license");
			generateLicenseNodes(xmlDoc, licenseNode, license);
		}

		// create decimalFormat
		StringBuilder formatSB = new StringBuilder("######0");
		if (currencyScale > 0) {
			formatSB.append(".");
			for (int i = 0; i < currencyScale; i++)
				formatSB.append("0");
		}
		DecimalFormat priceFormatLoc = new DecimalFormat(formatSB.toString(), decimalFormatSymbolsLoc);

		// generate order items
		Element orderNode = addChildNode(xmlDoc, rootNode, "order");
		BigDecimal total = BigDecimal.ZERO;
		BigDecimal subTotal = BigDecimal.ZERO;
		HashMap<BigDecimal, Vat> vats = new HashMap<BigDecimal, Vat>();

		// fill order (ignore options)
		for (Order.Item orderItem : order.itemList) {
			Element itemNode = addChildNode(xmlDoc, orderNode, "item");
			int quantity = orderItem.quantity;
			Double vat = orderItem.vat * (1/currencyRate);
			BigDecimal roundedVat = BigDecimal.valueOf(vat).setScale(currencyScale, priceRoundingMode);
			Double amountVat = vat * quantity;
			BigDecimal roundedAmountVat = BigDecimal.valueOf(amountVat).setScale(currencyScale, priceRoundingMode);

			BigDecimal priceWithoutVat;
			BigDecimal amountPriceWithVat;
			BigDecimal amountPriceWithoutVat;
			if (orderItem.priceExcludesVat) {
				priceWithoutVat = orderItem.price.multiply(BigDecimal.valueOf(1/currencyRate));
				amountPriceWithoutVat = priceWithoutVat.multiply(BigDecimal.valueOf(quantity, 0));
				amountPriceWithVat = amountPriceWithoutVat.add(roundedAmountVat);
			} else {
//				BigDecimal priceWithVat = orderItem.price.divide(BigDecimal.valueOf(currencyRate),
//						4, priceRoundingMode);
				BigDecimal priceWithVat = orderItem.price.multiply(BigDecimal.valueOf(1/currencyRate));
				priceWithoutVat = priceWithVat.subtract(roundedVat);
				amountPriceWithVat = priceWithVat.multiply(BigDecimal.valueOf(quantity, 0));
				amountPriceWithoutVat = amountPriceWithVat.subtract(roundedAmountVat);
			}
			if (orderItem.vatPercent.compareTo(BigDecimal.ZERO) != 0
					/*&& orderItem.price.compareTo(BigDecimal.ZERO) != 0*/)
			{
				Vat vatItem = vats.get(orderItem.vatPercent);
				if (vatItem != null) {
					vatItem.base = vatItem.base.add(amountPriceWithoutVat);
					vatItem.amount += amountVat;
				} else {
					vatItem = new Vat(amountPriceWithoutVat, amountVat);
					vats.put(orderItem.vatPercent, vatItem);
				}
			}

			addChildNode(xmlDoc, itemNode, "quantity", Integer.toString(quantity));
			addChildNode(xmlDoc, itemNode, "article-number", Integer.toString(orderItem.articleNumber));
			addChildNode(xmlDoc, itemNode, "article-name", orderItem.articleName);
			addChildNode(xmlDoc, itemNode, "price", priceFormatLoc.format(priceWithoutVat));
			addChildNode(xmlDoc, itemNode, "discount", "");
			addChildNode(xmlDoc, itemNode, "amount", priceFormatLoc.format(amountPriceWithoutVat));
			if (orderItem.text != null && !orderItem.text.isEmpty()) {
				addChildNode(xmlDoc, itemNode, "text", orderItem.text);
//				Element textItemNode = addChildNode(xmlDoc, orderNode, "item");
//				addChildNode(xmlDoc, textItemNode, "article-name", orderItem.text);
			}

			subTotal = subTotal.add(amountPriceWithoutVat);
			total = total.add(amountPriceWithVat);
		}

		addChildNode(xmlDoc, orderNode, "subtotal", priceFormatLoc.format(subTotal));
		if (!vats.isEmpty()) {
			for (BigDecimal vatPercent : vats.keySet()) {
				Element vatsNode = addChildNode(xmlDoc, orderNode, "vat");
				Vat vatItem = vats.get(vatPercent);
				addChildNode(xmlDoc, vatsNode, "percent", priceFormatLoc.format(vatPercent)); // XXX use price format for percent
				addChildNode(xmlDoc, vatsNode, "base", priceFormatLoc.format(vatItem.base));
				addChildNode(xmlDoc, vatsNode, "amount", priceFormatLoc.format(vatItem.amount));
			}

		}
		addChildNode(xmlDoc, orderNode, "total", priceFormatLoc.format(total));

		// Determine invoice number
		SimpleDateFormat twoDigitYearDateFormat = new SimpleDateFormat("yy");
		DecimalFormat invoiceNumberFormat = new DecimalFormat("0000");
		if (total.compareTo(BigDecimal.ZERO) >= 0) {
			invoice.positive = true;
			invoice.number = settings.getNextInvoiceNumber();
			invoice.fullNumber = "I"	+ twoDigitYearDateFormat.format(invoice.date) + "/"
					+ invoiceNumberFormat.format(invoice.number);
		} else {
			invoice.positive = false;
			invoice.number = settings.getNextNegativeInvoiceNumber();
			invoice.fullNumber = "CR" + twoDigitYearDateFormat.format(invoice.date) + "/"
					+ invoiceNumberFormat.format(invoice.number);
		}

		// generate invoice nodes
		addChildNode(xmlDoc, rootNode, "date", dateFormatLoc.format(invoice.date));
		addChildNode(xmlDoc, rootNode, "number", invoice.fullNumber);
		if (reference != null)
			addChildNode(xmlDoc, rootNode, "reference", reference);
		else if (license != null)
			addChildNode(xmlDoc, rootNode, "reference", license.getClientName());

		return xmlDoc;
	}

	private static class Vat {
		Vat(BigDecimal base, double amount) { this.base = base; this.amount = amount; }
		BigDecimal base;
		double amount;
	}

	private void generateLicenseNodes(Document xmlDoc, Element licenseNode, DbLicense license) throws SQLException {

		// copy-paste from EmailHelper.parseTemplate

		String licenseHardCode = null;

		String licenseLbConnections = null;
		String licenseRemConnections = null;
		String licenseOmConnections = null;
		//String licenseHqConnections = null;
		String licensePsConnections = null;
		String licenseHeConnections = null;

		String licenseModules = null;
		String licenseExtraModules = null;
		String licenseInterfaces = null;
		String licenseDrivers = null;

		String licenseStartDate = null;
		String licenseIssueDate = null;
		String licenseExpiredDate = null;

		String licenseStatus = null;

		licenseHardCode = HardCodeHelper.formatHardCode(license.hardCode);

		licenseLbConnections = Short.toString(license.getLbConnections());
		licenseRemConnections = Short.toString(license.getRemConnections());
		licenseOmConnections = Short.toString(license.getOmConnections());
//		licenseHqConnections = Short.toString(license.getHqConnections());
		licensePsConnections = Short.toString(license.getPsConnections());
		licenseHeConnections = Short.toString(license.getHeConnections());

		licenseModules = "";
		licenseExtraModules = "";
		licenseInterfaces = "";
		licenseDrivers = "";
		for (int i = 0; i < DbLicense.ENABLED_FUNCTIONS_NUMBER; i++) {
			if ((1l << i & license.getFuncLimited()) != 0) {
				if (!DbLicense.isHiddenFunc(i)) {
					DriverInfo[] driverInfos = DbLicense.getDriverInfosByFunc(i);
					if (driverInfos.length > 0) {
						for (DriverInfo driverInfo : driverInfos) {
							if (!licenseDrivers.isEmpty())
								licenseDrivers += "; ";
							licenseDrivers += driverInfo.getDisplayName();
						}
					} else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE) {
						if (!licenseModules.isEmpty())
							licenseModules += "; ";
						licenseModules += DbLicense.FUNC_NAMES[i];
					} else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_EXTRA_MODULE) {
						if (!licenseExtraModules.isEmpty())
							licenseExtraModules += "; ";
						licenseExtraModules += DbLicense.FUNC_NAMES[i];
					} else { // Type.FT_INTERFACE
						if (!licenseInterfaces.isEmpty())
							licenseInterfaces += "; ";
						licenseInterfaces += DbLicense.FUNC_NAMES[i];
					}
				}
			}
		}

		if (license.getDrivers() != null) {
			for (String driverId : license.getDrivers()) {
				DriverInfo driverInfo = DbLicense.getDriverInfoByDriverId(driverId);
				if (driverInfo != null) {
					if (driverInfo.hasFunc())
						continue; // skip drivers with function id, they are already handled above
					if (!licenseDrivers.isEmpty())
						licenseDrivers += "; ";
					licenseDrivers += driverInfo.getDisplayName();
				} else {
					if (!licenseDrivers.isEmpty())
						licenseDrivers += "; ";
					licenseDrivers += driverId;
				}
			}
		}

		licenseStartDate = "-";
		licenseIssueDate = "-";
		licenseExpiredDate = "-";
		if (license.getStartDate() != null)
			licenseStartDate = dateFormatLoc.format(Common.toLocalDateOnly(license.getStartDate()));
		if (license.getIssueDate() != null)
			licenseIssueDate = dateFormatLoc.format(Common.toLocalDateOnly(license.getIssueDate()));
		if (license.getExpiredDate() != null && !license.isNeverExpired())
			licenseExpiredDate = dateFormatLoc.format(Common.toLocalDateOnly(license.getExpiredDate()));

		licenseStatus = "";
		if (!license.isStatusActive()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "Inactive";
		}
		if (license.isNeverExpired()) {
			licenseExpiredDate = "never expired";
		}
		if (license.isUnlimited()) {
			licenseLbConnections = "unlimited";
			licenseRemConnections = "unlimited";
			licenseOmConnections = "unlimited";
			//licenseHqConnections = "unlimited";
			licensePsConnections = "unlimited";
			licenseHeConnections = "unlimited";
		}
		if (license.isCanceled()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "Canceled";
		}
		if (license.isDefinitive()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "Definitive (" + Long.toString(license.getDefinitiveMaxVersion()) + ")";
		}
		if (license.isOnline()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "Online";
		}
		if (license.isModuled()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "Moduled";
		}
		if (license.isSaas()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "SaaS";
		}
		if (license.isBoost()) {
			if (!licenseStatus.isEmpty()) licenseStatus += "; ";
			licenseStatus += "Boost";
		}

		// generate license nodes
		addChildNode(xmlDoc, licenseNode, "client_name", license.getClientName());
		addChildNode(xmlDoc, licenseNode, "client_address", license.getClientAddress());
		addChildNode(xmlDoc, licenseNode, "client_email", license.getClientEmail());
		addChildNode(xmlDoc, licenseNode, "client_phone", license.getClientPhone());
		if (!license.isOnline())
			addChildNode(xmlDoc, licenseNode, "hard_code", licenseHardCode);
		addChildNode(xmlDoc, licenseNode, "lb_connections", licenseLbConnections);
		addChildNode(xmlDoc, licenseNode, "rem_connections", licenseRemConnections);
		addChildNode(xmlDoc, licenseNode, "om_connections", licenseOmConnections);
//		addChildNode(xmlDoc, licenseNode, "hq_connections", licenseHqConnections);
		addChildNode(xmlDoc, licenseNode, "ps_connections", licensePsConnections);
		addChildNode(xmlDoc, licenseNode, "he_connections", licenseHeConnections);
		addChildNode(xmlDoc, licenseNode, "start_date", licenseStartDate);
		addChildNode(xmlDoc, licenseNode, "issue_date", licenseIssueDate);
		addChildNode(xmlDoc, licenseNode, "expired_date", licenseExpiredDate);
		if (!licenseModules.isEmpty())
			addChildNode(xmlDoc, licenseNode, "modules", licenseModules);
		if (!licenseExtraModules.isEmpty())
			addChildNode(xmlDoc, licenseNode, "extra-modules", licenseExtraModules);
		if (!licenseInterfaces.isEmpty())
			addChildNode(xmlDoc, licenseNode, "extra-interfaces", licenseInterfaces);
		if (!licenseDrivers.isEmpty())
			addChildNode(xmlDoc, licenseNode, "drivers", licenseDrivers);
		if (!licenseStatus.isEmpty())
			addChildNode(xmlDoc, licenseNode, "status", licenseStatus);
		if (!license.dealerComments.isEmpty())
			addChildNode(xmlDoc, licenseNode, "dealer_comments", license.dealerComments);
	}

	private Element addChildNode(Document xmlDoc, Node parentNode, String name, String content) {
		Element element = addChildNode(xmlDoc, parentNode, name);
		element.setTextContent(content);
		return element;
	}
	private Element addChildNode(Document xmlDoc, Node parentNode, String name) {
		Element element = xmlDoc.createElement(name);
		parentNode.appendChild(element);
		return element;
	}
	private String getValidXmlNodeName(String name) {
		name = name.replaceAll("\\s", "-");
		name = name.toLowerCase();
		name = name.replaceAll("[^a-z0-9-]", "_");
		return name;
	}
}
