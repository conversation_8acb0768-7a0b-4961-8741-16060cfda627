package eu.untill.license.server;

import java.io.File;
import java.util.Date;
import java.util.List;

import eu.untill.license.shared.DbLicense;

public interface EmailHelper {

	public static enum ReportType {
		DEALER_REQUESTED_LICENSE,
		DEALER_CONFIRMED_REQUEST,
		DEALER_CANCELED_REQUEST,
		LICENSE_RE_SENT_TO_DEALER,
		EMERGENCY_LICENSE_SENT_TO_DEALER,
		DEALER_OBJECT_PROFORMA
	};

	public static enum SendLicenseReason {
		CONFIRM,
		RESEND,
		EMERGENCY
	}

	/**
	 * Send license request to super dealer or dealer
	 * @param superDealer
	 * @param dealer
	 * @param confirmUid
	 * @param cancelUrl
	 * @return
	 */
	boolean emailRequest(Dealer superDealer, Dealer dealer, DbLicense license,
			String confirmUrl, String cancelUrl);

	/**
	 * Send Large Account request to owner
	 * @param superDealer
	 * @param dealer
	 * @param confirmUid
	 * @param cancelUrl
	 * @return
	 */
	boolean emailLargeAccountRequest(Dealer superDealer, Dealer dealer, DbLicense license, String laApprUid, String laDenyUrl);

	/**
	 * Send license file to dealer
	 * @param sendLicenseReason
	 * @param oldLicenseFile
	 * @param licenseFile
	 * @param tempLicense
	 * @param license
	 * @param dealer
	 * @throws Exception
	 */
	void emailLicense(SendLicenseReason sendLicenseReason, File oldLicenseFile, File licenseFile,
			boolean tempLicense, DbLicense license, Dealer dealer) throws Exception;


	/**
	 * Send report e-mail to server owner
	 * @param reportType
	 * @param license
	 * @param dealer
	 * @param superDealer
	 * @param orderText
	 * @param comment
	 */
	void emailReportToOwner(ReportType reportType, DbLicense license, Dealer dealer,
			Dealer superDealer, String orderText, String comment);

	/**
	 * 
	 * @param emailList
	 * @param dealer
	 * @param license
	 * @param confirmUrl
	 * @param cancelUrl
	 * @param orderText
	 * @return
	 */
	boolean emailProforma(List<String> emailList, Dealer dealer, DbLicense license,
			String approveUrl, String toObjectUrl, String orderText);

	boolean emailInvoice(File invoiceFile, Date invoiceDate,
			String invoiceNumber, List<String> emailList, DbLicense license, String clientName);

}
