<!doctype html>
<!--
Copyright 2008 Google Inc.

Licensed under the Apache License, Version 2.0 (the "License"); you may not
use this file except in compliance with the License. You may obtain a copy of
the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
License for the specific language governing permissions and limitations under
the License.
-->
<html>
<head>
<meta charset="utf-8">
<meta name='gwt:onLoadErrorFn' content='junitOnLoadErrorFn'>
<meta name='gwt:onPropertyErrorFn' content='junitOnPropertyErrorFn'>
</head>
<body>
<script nonce="gwt-nonce">
function junitOnLoadErrorFn(moduleName, e) {
  junitLaunchError('Failed to load module ' + moduleName + ': ' + e);
}

function junitOnPropertyErrorFn(propName, allowedValues, badValue) {
  var msg = 'While attempting to load the module, property "' + propName;
  if (badValue != null) {
    msg += '" was set to the unexpected value "' + badValue + '"';
  } else {
    msg += '" was not specified';
  }
  msg += 'Allowed values: ' + allowedValues;
  junitLaunchError(msg);
}

function junitLaunchError(msg) {
  junitError("/launch", msg);
}

function junitError(type, msg) {
  var xmlHttpRequest = new XMLHttpRequest();
  xmlHttpRequest.open('POST', 'junithost/error' + type, true);
  xmlHttpRequest.setRequestHeader('Content-Type', 'text/x-gwt-rpc; charset=utf-8');
  xmlHttpRequest.send(msg);
  if (window.console && window.console.log) {
    window.console.log(type + " error: " + msg);
  }
}

function loadSelectionScript() {
  var moduleName = location.pathname;
  var pos = moduleName.lastIndexOf('/');
  moduleName = moduleName.substr(0, pos);
  pos = moduleName.lastIndexOf('/');
  moduleName = moduleName.substr(pos + 1);
  document.write('<script language="javascript" nonce="gwt-nonce" src="' + encodeURIComponent(moduleName) + '.nocache.js"><\/script>');
}
loadSelectionScript();
</script>
</body>
</html>
