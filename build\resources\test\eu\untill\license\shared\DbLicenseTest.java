package eu.untill.license.shared;

import static org.junit.Assert.assertEquals;

import java.util.Arrays;

import org.junit.Test;

public class DbLicenseTest {
	@Test
	public void testCombineDrivers() {

	}

	@Test
	public void testDriversEqual() {

	}

	@Test
	public void testGetDriversDifference() {

	}

	@Test
	public void testDriversToText() {
		assertEquals(null, DbLicense.driversToText(null));
		assertEquals("", DbLicense.driversToText(Arrays.asList()));
		assertEquals("\r\n", DbLicense.driversToText(Arrays.asList("")));
		assertEquals(" \r\n", DbLicense.driversToText(Arrays.asList(" ")));
		assertEquals("one\r\n", DbLicense.driversToText(Arrays.asList("one")));
		assertEquals("one\r\ntwo\r\n", DbLicense.driversToText(Arrays.asList("one", "two")));
		assertEquals("one \r\ntwo\r\n", DbLicense.driversToText(Arrays.asList("one ", "two")));
		assertEquals("one\r\n two\r\n", DbLicense.driversToText(Arrays.asList("one", " two")));
		assertEquals("\r\n\r\n", DbLicense.driversToText(Arrays.asList("", "")));
		assertEquals("aaa.bbb.ccc\r\n", DbLicense.driversToText(DbLicense.textToDrivers("aaa.bbb.ccc")));
		assertEquals("aaa.bbb.Ccc\r\nddd.eee.Fff\r\n", DbLicense.driversToText(Arrays.asList("aaa.bbb.Ccc", "ddd.eee.Fff")));
		assertEquals("one\r\ntwo\r\n", DbLicense.driversToText(DbLicense.textToDrivers("one\r\ntwo\r\n")));
	}

	@Test
	public void testTextToDrivers() {
		assertEquals(null, DbLicense.textToDrivers(null));
		assertEquals(null, DbLicense.textToDrivers(""));
		assertEquals(Arrays.asList(" "), DbLicense.textToDrivers(" "));
		assertEquals(Arrays.asList("one"), DbLicense.textToDrivers("one\r"));
		assertEquals(Arrays.asList("one", "two"), DbLicense.textToDrivers("one\rtwo"));
		assertEquals(Arrays.asList("one", "two"), DbLicense.textToDrivers("one\rtwo\r\n"));
		assertEquals(Arrays.asList("one", "two"), DbLicense.textToDrivers("abc\r\ndef"));
		assertEquals(Arrays.asList(" abc", "def "), DbLicense.textToDrivers(" abc\r\ndef "));
		assertEquals(Arrays.asList("abc", "def"), DbLicense.textToDrivers("abc\ndef"));
		assertEquals(Arrays.asList("", ""), DbLicense.textToDrivers("\r\n\r\n"));
		assertEquals(Arrays.asList("", ""), DbLicense.textToDrivers("\r\n\n"));
		assertEquals(Arrays.asList(""), DbLicense.textToDrivers("\r\n"));
		assertEquals(Arrays.asList("aaa.bbb.ccc"), DbLicense.textToDrivers("aaa.bbb.ccc"));
		assertEquals(Arrays.asList("aaa.bbb.Ccc", "ddd.eee.Fff"), DbLicense.textToDrivers("aaa.bbb.Ccc\r\nddd.eee.Fff"));
	}
}
