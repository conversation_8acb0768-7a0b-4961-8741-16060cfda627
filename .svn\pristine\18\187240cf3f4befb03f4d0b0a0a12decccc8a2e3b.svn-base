/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.ui.MessageDialog.Style;

/**
 * <AUTHOR>
 *
 */
public class AddNegativeArticleDialog extends DialogBox implements ClickHandler, AsyncCallback<Void> {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String addNegativeArticleHeader();
		String negativeArticleLabel();
		String negativeArticleQuantityLabel();
		String negativeArticleTextLabel();
		String okButton();
		String cancelButton();
	}

	public static interface UlsMessages extends Messages {
		String unknownNumberFormatOfQuantity();
		String invalidNumberOfQuantity(); 
		String unknownNumberFormatOfPrice();
		String negativeArticleSuccessfullyAdded();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  /////////////
	 // Members //
	/////////////

	private long licenseId;
	private long articleId;

	  //////////////////
	 // Constructors //
	//////////////////

	public AddNegativeArticleDialog(long licenseId, long articleId, String articleName, int quantity) {
		this.licenseId = licenseId;
		this.articleId = articleId;

		// Generate base interface
		generateUI();

		txtArticle.setText(articleName);
		txtQuantity.setText(Integer.toString(quantity));

		this.setText(constants.addNegativeArticleHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private FlexTable tblParameters = new FlexTable();
	private Label lblArticle = new Label(constants.negativeArticleLabel() + ":", false);
	private TextBox txtArticle = new TextBox();
	private Label lblQuantity = new Label(constants.negativeArticleQuantityLabel() + ":", false);
	private TextBox txtQuantity = new TextBox();
	private Label lblText = new Label(constants.negativeArticleTextLabel() + ":", false);
	private TextBox txtText = new TextBox();

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		// Assemble parameters table
		txtArticle.setReadOnly(true);
		txtArticle.setVisibleLength(60);
		txtQuantity.setVisibleLength(20);
		txtText.setMaxLength(50);
		txtText.setVisibleLength(60);
		tblParameters.setWidget(0, 0, lblArticle);
		tblParameters.setWidget(0, 1, txtArticle);
		tblParameters.setWidget(1, 0, lblQuantity);
		tblParameters.setWidget(1, 1, txtQuantity);
		tblParameters.setWidget(2, 0, lblText);
		tblParameters.setWidget(2, 1, txtText);

		// Assemble button panel
		btnOk.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.add(btnOk);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(tblParameters);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnOk){

			// Fill and check entered data
			int quantity;
			try {
				quantity = Integer.valueOf(txtQuantity.getText());
			} catch (NumberFormatException e) {
				new MessageDialog(messages.unknownNumberFormatOfQuantity(), Style.WARNING, txtQuantity).show();
				return;
			}
			if (quantity < 1) {
				new MessageDialog(messages.invalidNumberOfQuantity(), Style.WARNING, txtQuantity).show();
				return;
			}

			// Call RPC LicenseService.requestLicense
			UntillLicenseServer.getLicenseServiceAsync().addNegativeArticle(
					UntillLicenseServer.getAuthScheme(),
					licenseId, articleId, quantity, txtText.getText(), this);

			WaitDialog.show();
		}

	}

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		if (caught instanceof AuthFailedException) {
			this.hide();
			LoginPage.get().show();
		} else {
			ErrorDialog.show(caught);
		}
	}

	@Override
	public void onSuccess(Void result) {
		WaitDialog.hide();
		new MessageDialog(messages.negativeArticleSuccessfullyAdded(), Style.INFORMATION).show();
		this.hide();
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtQuantity.selectAll();
		txtQuantity.setFocus(true);
	}

}
