package eu.untill.license.client;

import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.TimeZone;

public class Common {

	@SuppressWarnings("deprecation")
	public static Date toUtcDateOnly(Date localDate) {
		if (localDate == null)
			return null;
		return new Date(Date.UTC(localDate.getYear(), localDate.getMonth(), localDate.getDate(), 0, 0, 0));
	}

	private static final String ISO8601_DATE_ONLY_PATTERN = "yyyy-MM-dd";

	public static Date toLocalDateOnly(Date utcDate) {
		if (utcDate == null)
			return null;
		DateTimeFormat dateOnlyFormat = DateTimeFormat.getFormat(ISO8601_DATE_ONLY_PATTERN);
		TimeZone utcTimeZone = TimeZone.createTimeZone(0);
		return dateOnlyFormat.parse(dateOnlyFormat.format(utcDate, utcTimeZone));
	}

	@SuppressWarnings("deprecation")
	public static int getYear(Date localDate) {
		return localDate.getYear() + 1900;
	}

	@SuppressWarnings("deprecation")
	public static int getMonth(Date localDate) {
		return localDate.getMonth();
	}

	@SuppressWarnings("deprecation")
	public static int getDayOfMonth(Date localDate) {
		return localDate.getDate();
	}

	@SuppressWarnings("deprecation")
	public static void setDayOfMonth(Date localDate, int dayOfMonth) {
		localDate.setDate(dayOfMonth);
	}

	@SuppressWarnings("deprecation")
	public static Date combineDateAndTime(Date date, Date time) {
		return new Date(date.getYear(), date.getMonth(), date.getDate(), time.getHours(), time.getMinutes(),
				time.getSeconds());
	}

	public static String capitalizeProductName(String product) {
		return product.replaceAll("\\buntill\\b", "unTill").replaceAll("\\bfirebird\\b", "Firebird");
	}

	public static String convertToCSV(String... data) {
		return Stream.of(data)
				.map(Common::escapeSpecialCharacters)
				.collect(Collectors.joining(","));
	}

	public static String escapeSpecialCharacters(String data) {
		String escapedData = data.replaceAll("\\r\\n|[\\r\\n]", " ");
		if (data.contains(",") || data.contains("\"") || data.contains("'")) {
			data = data.replace("\"", "\"\"");
			escapedData = "\"" + data + "\"";
		}
		return escapedData;
	}

	public static native void export(String data, String filename, String type) /*-{
		var file = new Blob([data], {type: type});
		if (window.navigator.msSaveOrOpenBlob) // IE10+
			window.navigator.msSaveOrOpenBlob(file, filename);
		else { // Others
			var a = document.createElement("a"),
					url = URL.createObjectURL(file);
			a.href = url;
			a.download = filename;
			document.body.appendChild(a);
			a.click();
			setTimeout(function() {
				document.body.removeChild(a);
				window.URL.revokeObjectURL(url);  
			}, 0); 
		}
	}-*/;

}
