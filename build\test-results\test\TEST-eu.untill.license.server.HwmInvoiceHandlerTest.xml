<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="eu.untill.license.server.HwmInvoiceHandlerTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-08-06T13:09:00.639Z" hostname="UNP-WS2" time="0.554">
  <properties/>
  <testcase name="generateAndSend_noWork1" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.073"/>
  <testcase name="generateAndSend_noWork2" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.015"/>
  <testcase name="generateAndSend_error1" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.04"/>
  <testcase name="prepare_concurrency" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.049"/>
  <testcase name="prepare_wrongDb" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.003"/>
  <testcase name="prepare_noWork" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.0"/>
  <testcase name="order_noWork" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.009"/>
  <testcase name="generateAndSend_test1" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.025"/>
  <testcase name="prepare_test1" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.035"/>
  <testcase name="prepare_test2" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.038"/>
  <testcase name="order_test1" classname="eu.untill.license.server.HwmInvoiceHandlerTest" time="0.262"/>
  <system-out><![CDATA[16:09:00.665 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.673 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.674 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.676 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.678 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.679 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.679 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.681 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.685 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.692 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.693 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.694 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.696 [Test worker] WARN  o.d.dataset.AbstractTableMetaData getDataTypeFactory : Potential problem found: The configured data type factory 'class org.dbunit.dataset.datatype.DefaultDataTypeFactory' might cause problems with the current database 'H2' (e.g. some datatypes may not be supported properly). In rare cases you might see this message because the list of supported database products is incomplete (list=[derby]). If so please request a java-class update via the forums.If you are using your own IDataTypeFactory extending DefaultDataTypeFactory, ensure that you override getValidDbProducts() to specify the supported database products.
16:09:00.785 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-06, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.787 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-07, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.789 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-08, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.790 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-09, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.791 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.793 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.794 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [1003]}}
16:09:00.794 [Thread-6] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.821 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler getNextMonthForInvoiceGeneration : Invalid format for HwmNextMonthForInvoiceGeneration parameter: '2016-HZ'
16:09:00.842 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.843 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 1, emails: null}
16:09:00.843 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.843 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 2, emails: null}
16:09:00.844 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.844 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 3, emails: null}
16:09:00.845 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.845 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 4, emails: null}
16:09:00.846 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.846 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 5, emails: null}
16:09:00.847 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.848 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 6, emails: null}
16:09:00.848 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.849 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 7, emails: null}
16:09:00.849 [Test worker] WARN  e.u.license.server.HwmInvoiceHandler generateAndSend : Dealer has no invoice emails: prefix5671259248114036770suffix
16:09:00.850 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler generateAndSend : Generate and send HWM-invoice: {id: **********, number: 8, emails: null}
16:09:00.878 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-06, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.879 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-07, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.880 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-08, invoice: {dealerId: 1, chain: null, licenseIds: [2001]}}
16:09:00.881 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-09, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.882 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.883 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.884 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [1003]}}
16:09:00.885 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: null, licenseIds: [2002]}}
16:09:00.915 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-08, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102]}}
16:09:00.917 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-09, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102, 3201]}}
16:09:00.918 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102, 3201, 3301]}}
16:09:00.919 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 1, chain: Client4, licenseIds: [4101, 4201]}}
16:09:00.919 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-10, invoice: {dealerId: 2, chain: Client4, licenseIds: [4801]}}
16:09:00.920 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: Client3, licenseIds: [3102, 3201, 3301]}}
16:09:00.920 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 1, chain: Client4, licenseIds: [4101, 4201]}}
16:09:00.920 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-11, invoice: {dealerId: 2, chain: Client4, licenseIds: [4801, 4901]}}
16:09:00.921 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: CLIENT3, licenseIds: [3201, 3301]}}
16:09:00.921 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 1, chain: Client4, licenseIds: [4101, 4201]}}
16:09:00.922 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler insertNewInvoice : New HWM-invoice: {id: **********, month: 2016-12, invoice: {dealerId: 2, chain: Client4, licenseIds: [4801]}}
16:09:01.137 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.145 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.152 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.161 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.168 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.174 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.179 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
16:09:01.186 [Test worker] INFO  e.u.license.server.HwmInvoiceHandler order : Made order for HWM-invoice: {id: **********, billId: 1}
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
