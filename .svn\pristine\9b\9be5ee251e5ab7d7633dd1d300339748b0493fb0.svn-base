package eu.untill.license.server;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class TestDbTest {
	@Test
	public void testTestDb() throws Exception {
		assertNull(TestDb.class.getResourceAsStream("/test-resources/eu/untill/license/server/testdb/structure.sql"));
		assertNotNull(TestDb.class.getResourceAsStream("/eu/untill/license/server/testdb/structure.sql"));
		//assertNotNull(TestDb.class.getResourceAsStream("/test/eu/untill/license/server/testdb/structure.sql"));

		assertNull(TestDb.class.getResourceAsStream("test/eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("server/testdb/structure.sql"));
		assertNotNull(TestDb.class.getResourceAsStream("testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("structure.sql"));

		assertNull(TestDb.class.getResourceAsStream("../test/eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../license/server/testdb/structure.sql"));
		assertNotNull(TestDb.class.getResourceAsStream("../server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../structure.sql"));

		assertNull(TestDb.class.getResourceAsStream("../../test/eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../untill/license/server/testdb/structure.sql"));
		assertNotNull(TestDb.class.getResourceAsStream("../../license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../structure.sql"));

		assertNull(TestDb.class.getResourceAsStream("../../../test/eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../eu/untill/license/server/testdb/structure.sql"));
		assertNotNull(TestDb.class.getResourceAsStream("../../../untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../structure.sql"));

		//assertNotNull(TestDb.class.getResourceAsStream("../../../../test/eu/untill/license/server/testdb/structure.sql"));
		assertNotNull(TestDb.class.getResourceAsStream("../../../../eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../structure.sql"));

		assertNotNull(TestDb.class.getResourceAsStream("../../../../../test/eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../test-resources/eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../eu/untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../untill/license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../license/server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../server/testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../testdb/structure.sql"));
		assertNull(TestDb.class.getResourceAsStream("../../../../../structure.sql"));
	}
}
