/**
 * 
 */
package eu.untill.license.client;

import com.google.gwt.i18n.client.Constants;

import eu.untill.license.client.ui.ActualPricesDialog;
import eu.untill.license.client.ui.AddCreditDialog;
import eu.untill.license.client.ui.AddDiscountDialog;
import eu.untill.license.client.ui.AddHandlingFeeDialog;
import eu.untill.license.client.ui.AddManualArticleDialog;
import eu.untill.license.client.ui.AddNegativeArticleDialog;
import eu.untill.license.client.ui.ApostillHddCodesDialog;
import eu.untill.license.client.ui.ApproveLicenseDialog;
import eu.untill.license.client.ui.ClientNameChangesDialog;
import eu.untill.license.client.ui.CreateManualInvoiceDialog;
import eu.untill.license.client.ui.FiscalDialog;
import eu.untill.license.client.ui.HwmReportDialog;
import eu.untill.license.client.ui.LicenseDetailsDialog;
import eu.untill.license.client.ui.LicenseDialog;
import eu.untill.license.client.ui.LicenseListWidget;
import eu.untill.license.client.ui.LoginWidget;
import eu.untill.license.client.ui.MainPage;
import eu.untill.license.client.ui.PageNavigator;
import eu.untill.license.client.ui.ResendLicenseDialog;
import eu.untill.license.client.ui.SuAddHostsDialog;
import eu.untill.license.client.ui.SuSubtaskDialog;
import eu.untill.license.client.ui.SuTaskDialog;
import eu.untill.license.client.ui.SuTaskListWidget;
import eu.untill.license.client.ui.SuperDealerWidget;
import eu.untill.license.client.ui.TitleWidget;

/**
 * <AUTHOR>
 *
 */
public interface UntillLicenseServerConstants extends Constants,
	MainPage.UlsConstants,
	LoginWidget.UlsConstants,
	LicenseListWidget.UlsConstants,
	LicenseDialog.UlsConstants,
	ApostillHddCodesDialog.UlsConstants,
	TitleWidget.UlsConstants,
	LicenseClientHelper.UlsConstants,
	LicenseDetailsDialog.UlsConstants,
	SuperDealerWidget.UlsConstants,
	PageNavigator.UlsConstants,
	ApproveLicenseDialog.UlsConstants,
	AddDiscountDialog.UlsConstants,
	AddNegativeArticleDialog.UlsConstants,
	ResendLicenseDialog.UlsConstants,
	CreateManualInvoiceDialog.UlsConstants,
	AddCreditDialog.UlsConstants,
	AddManualArticleDialog.UlsConstants,
	SuTaskListWidget.UlsConstants,
	SuTaskDialog.UlsConstants,
	SuSubtaskDialog.UlsConstants,
	SuAddHostsDialog.UlsConstants,
	ClientNameChangesDialog.UlsConstants,
	AddHandlingFeeDialog.UlsConstants,
	HwmReportDialog.UlsConstants,
	ActualPricesDialog.UlsConstants,
	FiscalDialog.UlsConstants
{

	String okButton();
	String cancelButton();
	String retryButton();
	String yesButton();
	String noButton();
}
