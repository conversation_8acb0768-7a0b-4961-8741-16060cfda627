/**
 * 
 */
package eu.untill.license.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import eu.untill.license.client.AuthScheme;
import eu.untill.license.client.PlainAuthScheme;

/**
 * <AUTHOR>
 *
 */
public class Dealer {

	public static class UnknownAuthSchemeException extends Exception { private static final long serialVersionUID = 1L; }
	public static class RequiredDealerIsNotFoundException extends Exception { private static final long serialVersionUID = 1L; }

	final private static String commonQueryPart = "SELECT DEALERS.ID ID, CLIENTS.NAME NAME, "
			+ "CLIENTS.EMAIL EMAIL, "
			+ "DEALERS.SUPER_DEALER SUPER_DEALER, DEALERS.LOGIN LOGIN, "
			+ "DEALERS.PASS_HASH PASS_HASH, DEALERS.APOSTILL_DEALER APOSTILL_DEALER, "
			+ "DEALERS.ONLY_PROLONG ONLY_PROLONG, DEALERS.DEMO_DEALER DEMO_DEALER, COUNTRIES.NAME COUNTRY,"
			+ "DEALERS.AUTO_PROLONG_DEF AUTO_PROLONG_DEF, DEALERS.PRICES_PASS PRICES_PASS\n"
			+ "FROM DEALERS\n"
			+ "INNER JOIN CLIENTS ON DEALERS.ID_CLIENTS = CLIENTS.ID\n"
			+ "LEFT OUTER JOIN COUNTRIES ON CLIENTS.ID_COUNTRIES = COUNTRIES.ID\n"
			+ "WHERE COALESCE(DEALERS.IS_DEALER_ACTIVE, 0) <> 0\n"
			+ "AND COALESCE(CLIENTS.IS_ACTIVE, 0) <> 0\n";

	public long id;                // BIGINT NOT NULL
	public String name;            // VARCHAR(100)
	public String email;           // VARCHAR(50)
	public boolean superDealer;    // SMALLINT
	public String login;           // VARCHAR(50)
	public String pass_hash;       // VARCHAR(50)
	public boolean apostillDealer; // SMAILLINT
	public boolean onlyProlong;    // SMAILLINT
	public String country;         // VARCHAR(100)
	public boolean demoDealer;     // SMAILLINT
	public boolean autoProlongDef; // SMAILLINT
	public String pricesPass;      // VARCHAR(50)

	public Dealer(Connection conn, AuthScheme authScheme)
			throws RequiredDealerIsNotFoundException,
			UnknownAuthSchemeException, SQLException
	{
		if (authScheme instanceof PlainAuthScheme) {

			PlainAuthScheme plainAuthScheme = (PlainAuthScheme) authScheme;

			if (plainAuthScheme.login == null || plainAuthScheme.password == null) {
				throw new RequiredDealerIsNotFoundException();
			}

			PreparedStatement stmt = null;
			ResultSet rs = null;
			try {

				// Get dealer record from DB
				stmt = conn.prepareStatement(commonQueryPart
						+ "AND UPPER(DEALERS.LOGIN) = UPPER(?)\n");
				stmt.setString(1, plainAuthScheme.login);
				rs = stmt.executeQuery();

				// Get result
				if (!rs.next()) {
					// TODO Log this event: User not found (Login incorrect!)
					throw new RequiredDealerIsNotFoundException();
				}

				fillDealerFromResultSet(rs);

				// Check password
				if (!Common.checkPassword(plainAuthScheme.password, this.pass_hash)) {
					// TODO Log this event: Password mismatch (Password incorrect!)
					throw new RequiredDealerIsNotFoundException();
				}

			} finally {
				if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
				if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
			}

		} else {

			throw new UnknownAuthSchemeException();

		}
	}

	public Dealer(Connection conn, long dealerId) throws RequiredDealerIsNotFoundException, SQLException {
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get dealer record from DB
			stmt = conn.prepareStatement(commonQueryPart
					+ "AND DEALERS.ID = ?\n");
			stmt.setLong(1, dealerId);
			rs = stmt.executeQuery();

			// Get result
			if (!rs.next()) {
				// TODO Log this event: Dealer with required id is not found
				throw new RequiredDealerIsNotFoundException();
			}

			fillDealerFromResultSet(rs);

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}


	private Dealer(ResultSet rs) throws SQLException {
		fillDealerFromResultSet(rs);
	}

	public static List<Dealer> getAllDealers(Connection conn, boolean onlyApostill) throws SQLException {
		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {
			List<Dealer> result = new ArrayList<Dealer>();
			stmt = conn.prepareStatement(commonQueryPart
					+ (onlyApostill ? "AND COALESCE(DEALERS.APOSTILL_DEALER, 0) <> 0\n" : "")
					+ "ORDER BY UPPER(CLIENTS.NAME)\n");
			rs = stmt.executeQuery();
			while (rs.next()) {
				result.add(new Dealer(rs));
			}
			return result;
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
	}

	private void fillDealerFromResultSet(ResultSet rs) throws SQLException {
		this.id = rs.getLong("ID");
		this.name = rs.getString("NAME");
		this.name = this.name == null ? "" : this.name.trim();
		this.email = rs.getString("EMAIL");
		this.email = this.email == null ? "" : this.email.trim();
		this.superDealer = rs.getShort("SUPER_DEALER") != 0;
		this.login = rs.getString("LOGIN");
		this.login = this.login == null ? "" : this.login.trim();
		this.pass_hash = rs.getString("PASS_HASH");
		this.pass_hash = this.pass_hash == null ? "" : this.pass_hash.trim();
		this.apostillDealer = rs.getShort("APOSTILL_DEALER") != 0;
		this.onlyProlong = rs.getShort("ONLY_PROLONG") != 0;
		this.demoDealer = rs.getShort("DEMO_DEALER") != 0;
		this.country = rs.getString("COUNTRY");
		this.country = this.country == null ? "" : this.country.trim();
		this.autoProlongDef = rs.getShort("AUTO_PROLONG_DEF") != 0;
		this.pricesPass = rs.getString("PRICES_PASS");
		this.pricesPass = this.pricesPass == null ? "" : this.pricesPass.trim();
	}

	@Override
	public String toString() {
		return "[Dealer: id=" + Long.toString(this.id) + "; name=\"" + this.name +"\"]";
	}
}
