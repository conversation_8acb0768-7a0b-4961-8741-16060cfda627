<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="eu.untill.license.server.OnlineLicenseHandlerTest" tests="2" skipped="0" failures="0" errors="0" timestamp="2025-08-06T13:09:01.368Z" hostname="UNP-WS2" time="0.06">
  <properties/>
  <testcase name="testServiceOnlineLicenseRequest_noLicense" classname="eu.untill.license.server.OnlineLicenseHandlerTest" time="0.009"/>
  <testcase name="testServiceOnlineLicenseRequest_install" classname="eu.untill.license.server.OnlineLicenseHandlerTest" time="0.049"/>
  <system-out><![CDATA[16:09:01.394 [Test worker] INFO  e.u.l.server.OnlineLicenseHandler serviceOnlineLicenseRequest : Activated license (ID: G00DCLIENTLICENSEHARDC0D7, new signature: MCwCFCQv...dCX/Ng==)
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
