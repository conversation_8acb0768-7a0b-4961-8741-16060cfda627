package eu.untill.license.client;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.gwt.user.client.rpc.AsyncCallback;

import eu.untill.license.client.LicenseService.AddApostillHddCodesResult;
import eu.untill.license.client.LicenseService.CommonRpcResult;
import eu.untill.license.client.LicenseService.LicensePeriod;
import eu.untill.license.client.LicenseService.LicenseType;
import eu.untill.license.client.LicenseService.RequestLicenseResult;
import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.client.LicenseService.RrArticle;
import eu.untill.license.client.LicenseService.RrDealer;
import eu.untill.license.client.LicenseService.RrOrder;
import eu.untill.license.client.LicenseService.RrOrderItem;
import eu.untill.license.client.LicenseService.SortingType;
import eu.untill.license.shared.ArticleWithPrices;
import eu.untill.license.shared.ClientNameChange;
import eu.untill.license.shared.DbHwmInvoice;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.DbLicenseOnline;
import eu.untill.license.shared.FiscalOrganisation;
import eu.untill.license.shared.ShProductInfo;
import eu.untill.license.shared.SuHost;
import eu.untill.license.shared.SuLicense;
import eu.untill.license.shared.SuTaskBundle;
import eu.untill.license.shared.SuTaskListOrder;

public interface LicenseServiceAsync {
	void login(AuthScheme authScheme, AsyncCallback<RrDealer> callback);
	void getAllDealers(AuthScheme authScheme, AsyncCallback<List<RrDealer>> callback);
	void getApostillDealers(AuthScheme authScheme, AsyncCallback<List<RrDealer>> callback);
	void getClientNames(AuthScheme authScheme, long dealerId, boolean onlyOnline, AsyncCallback<List<String>> callback);
	void getHardCodes(AuthScheme authScheme, long dealerId, boolean onlyOnline, AsyncCallback<List<String>> callback);
	void getLicenseList(AuthScheme authScheme, long dealerId, SortingType sortingType, 
			String clientNameFilter, String hardCodeFilter, boolean showOnlyTrial, 
			boolean showCanceled, boolean showOnlyNonApproved, AsyncCallback<List<DbLicense>> callback);
	void getNewLicensePeriod(LicenseType licenseType, AsyncCallback<LicensePeriod> callback);
	void getProlongLicensePeriod(LicenseType licenseType,
			LicensePeriod prolongedLicensePeriod, AsyncCallback<LicensePeriod> callback);
	void getEmergencyLicensePeriod(Date baseExpiredDate, AsyncCallback<LicensePeriod> callback);
	void validLicenseHardCode(String hardCode, AsyncCallback<Boolean> callback);
	void requestLicense(AuthScheme authScheme, DbLicense license,
			RequestType requestType, AsyncCallback<RequestLicenseResult> callback);
	void addApostillHddCodes(AuthScheme authScheme, long apostillDealerId, String hddCodes, 
			AsyncCallback<AddApostillHddCodesResult> callback);
	void voidRequest(AuthScheme authScheme, long licenseId, AsyncCallback<Boolean> callback);
	void resendRequest(AuthScheme authScheme, long licenseId, AsyncCallback<Boolean> callback);
	void resendLicense(AuthScheme authScheme, long licenseId, Date tempLicenseEndDate,
			AsyncCallback<Boolean> callback);
	void getLicenseOrder(AuthScheme authScheme, long licenseId, AsyncCallback<RrOrder> callback);
	void addDiscount(AuthScheme authScheme, long licenseId, double price,
			String text, AsyncCallback<Void> callback);
	void addNegativeArticle(AuthScheme authScheme, long licenseId,
			long articleId, int quantity, String text, AsyncCallback<Void> callback);
	void addArticle(AuthScheme authScheme, long licenseId, long articleId, int quantity, double manualPrice,
			String text, AsyncCallback<Void> callback);
	void approveLicense(AuthScheme authScheme, long licenseId, boolean noInvoice, AsyncCallback<Void> callback);
	void orderManualLicense(AuthScheme authScheme, long licenseId, AsyncCallback<Void> callback);
	void recreateInvoice(AuthScheme authScheme, long licenseId, AsyncCallback<Void> callback);
	void reorderLicense(AuthScheme authScheme, long licenseId, AsyncCallback<CommonRpcResult> callback);
	void disapproveLicense(AuthScheme authScheme, long licenseId, AsyncCallback<Void> callback);

	void getMaxDefinitiveMaxVersion(AuthScheme authScheme, AsyncCallback<Integer> callback);

	void createCreditInvoice(AuthScheme authScheme, long dealerId, List<RrOrderItem> orderItems,
			String clientName, AsyncCallback<CommonRpcResult> callback);
	void getManualArticles(AuthScheme authScheme, AsyncCallback<List<RrArticle>> callback);

	void reinstallLicense(AuthScheme authScheme, long licenseId, AsyncCallback<Void> callback);
	void getLicenseOnlineDetails(AuthScheme authScheme, long licenseId, AsyncCallback<DbLicenseOnline> callback);

	void getChains(AuthScheme authScheme, long dealerId, boolean onlyOnline, AsyncCallback<List<String>> callback);

	void getProductList(AsyncCallback<Map<String, ShProductInfo>> callback);
	void getProductVersionList(String product, AsyncCallback<Map<String, Boolean>> callback);

	void getSuTaskBundleCount(AuthScheme authScheme, long dealerId, AsyncCallback<Integer> callback);
	void getSuTaskBundleList(AuthScheme authScheme, long dealerId, SuTaskListOrder order,
			int from, int to, AsyncCallback<List<SuTaskBundle>> callback);
	void createSuTaskBundle(AuthScheme authScheme, SuTaskBundle taskBundle, AsyncCallback<Void> callback);
	void getSuHostsByLicenseIds(AuthScheme authScheme, Set<Long> licenseIds,
			AsyncCallback<List<SuHost>> callback);
	void getSuHostsByHardCodes(AuthScheme authScheme, Set<String> licenseHardCodes,
			AsyncCallback<List<SuHost>> callback);
	void getSuHostsByDealerId(AuthScheme authScheme, long dealerId,
			AsyncCallback<List<SuHost>> callback);
	void getSuLicense(AuthScheme authScheme, String hardCode, AsyncCallback<SuLicense> callback);
	void getSuHostsByHardCode(AuthScheme authScheme, String hardCode, AsyncCallback<List<SuHost>> callback);
	void unregisterSuHost(AuthScheme authScheme, long hostId, AsyncCallback<Void> callback);
	void setSuLicenseHostRegPass(AuthScheme authScheme, String hardCode, String hostRegPass, AsyncCallback<Void> callback);

	void getClientNameChanges(AuthScheme authScheme, long dealerId, String filter,
			AsyncCallback<List<ClientNameChange>> callback);
	void getLicenseBoosts(AuthScheme authScheme, String licenseUid, AsyncCallback<List<DbLicense>> callback);

	void getCreditArticle(AuthScheme authScheme, AsyncCallback<RrArticle> callback);
	void getHandlingFeeArticle(AuthScheme authScheme, AsyncCallback<RrArticle> callback);

	void getHwmInvoiceReport(AuthScheme authScheme, Date month, AsyncCallback<List<DbHwmInvoice>> callback);
	void getDealerPrices(AuthScheme authScheme, long dealerId, String pricesPass, AsyncCallback<List<ArticleWithPrices>> callback);

	void getLicenseComment(AuthScheme authScheme, long licenseId, AsyncCallback<String> callback);
	void setLicenseComment(AuthScheme authScheme, long licenseId, String licenseComment, AsyncCallback<Void> callback);

	void getFiscalOrganisations(AuthScheme authScheme, long dealerId, AsyncCallback<List<FiscalOrganisation>> callback);
}
