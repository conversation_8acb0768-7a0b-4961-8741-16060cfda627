/**
 * 
 */
package eu.untill.license.client.ui;

import static eu.untill.license.client.ui.LicenseDetailsDialog.ProductsSortingType.BY_INST_TIME;
import static eu.untill.license.client.ui.LicenseDetailsDialog.ProductsSortingType.BY_INST_TIME_DESC;
import static eu.untill.license.client.ui.LicenseDetailsDialog.ProductsSortingType.BY_PRODUCT;
import static eu.untill.license.client.ui.LicenseDetailsDialog.ProductsSortingType.BY_PRODUCT_DESC;
import static eu.untill.license.client.ui.LicenseDetailsDialog.ProductsSortingType.BY_VERSION;
import static eu.untill.license.client.ui.LicenseDetailsDialog.ProductsSortingType.BY_VERSION_DESC;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EventListener;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.i18n.client.TimeZone;
import com.google.gwt.json.client.JSONArray;
import com.google.gwt.json.client.JSONNumber;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.json.client.JSONString;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.Cell;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Panel;
import com.google.gwt.user.client.ui.PasswordTextBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TabPanel;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarUtil;

import eu.untill.license.client.Common;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Buttons;
import eu.untill.license.client.ui.MessageDialog.MessageDialogListener;
import eu.untill.license.client.ui.MessageDialog.SelectedButton;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.DbLicenseOnline;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.License.FuncProp.FuncType;
import eu.untill.license.shared.SuHost;
import eu.untill.license.shared.SuLicense;
import eu.untill.license.shared.Version;

/**
 * <AUTHOR>
 *
 */
public class LicenseDetailsDialog extends DialogBox implements ClickHandler {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String closeButton();
		String okButton();
		String cancelButton();

		String licenseDetailsDialogHeader();

		@DefaultStringValue("Confirm addition to existing license")
		String confirmLicenseBoostDialog();

		String startDateLabel();
		String issueDateLabel();
		String expiredDateLabel();

		String clientNameLabel();
		String clientDisplayNameLabel();
		String clientAddressLabel();
		String clientEmailLabel();
		String clientPhoneLabel();
		String chainLabel();
		String licenseTypeLabel();
		String hardCodeLabel();
		String licenseUidLabel();

		String numberOfLocalDatabasesLabel();
		String numberOfRemoteConnectionsLabel();
		String numberOfHandTerminalsLabel();
		//String numberOfHeadQuartersLabel();
		String numberOfProductionScreensLabel();
		String numberOfHhtEftLabel();

		String modulesLabel();
		String extraModulesLabel();
		String interfacesLabel();

		String statusLabel();
		
		String dealerCommentsLabel();

		@DefaultStringValue("Min. version")
		String minVersionLabel();

		String noneLabel();

		String[] funcNames();

		String licenseCanceled();
		String licenseUnconfirmed();
		String licenseExpired();
		String licenseWillSoonExpire();
		String licenseApostill();
		String licenseUntill();
		String licenseTrial();
		String licenseOnline();
		String licenseModuled();
		String licenseDefinitive();
		String licenseUnlimited();
		String licenseNeverExpired();
		String licenseSaas();
		String licenseBoost();

		String recreateInvoiceButton();
		String disapproveLicenseButton();

		String hostColumnHeader();
		String stateColumnHeader();
		@DefaultStringValue("Unregister")
		String unregisterColumnHeader();

		String online();
		String offline();

		@DefaultStringValue("Unregister")
		String unregisterHostButton();

		@DefaultStringValue("Computer registration password")
		String hostRegistrationPasswordLabel();

		@DefaultStringValue("Computers")
		String hostsLabel();

		@DefaultStringValue("Product")
		String productColumnHeader();
		@DefaultStringValue("Version")
		String versionColumnHeader();
		@DefaultStringValue("Installation time")
		String installationTimeColumnHeader();

		@DefaultStringValue("Online details")
		String onlineDetailsLabel();
		@DefaultStringValue("Installation time")
		String installationTimeLabel();
		@DefaultStringValue("Available reinstallations")
		String availableReinstallationsLabel();
		@DefaultStringValue("Active product")
		String activeProductLabel();
		@DefaultStringValue("Active signature")
		String activeSignatureLabel();
		@DefaultStringValue("Activation time [count]")
		String activationTimeAndCountLabel();
		@DefaultStringValue("Activation period")
		String activationPeriodLabel();
		@DefaultStringValue("Failure count")
		String failureCountLabel();
		@DefaultStringValue("Last failure")
		String lastFailureLabel();
		@DefaultStringValue("after next prolong")
		String afterNextProlongLabel();
	}

	public static interface UlsMessages extends Messages {
		String recreateInvoiceForLicenseQuestion();
		String invoiceForLicenseRecreatedSuccessfully();

		String disapproveLicenseQuestion();
		String disapproveLicenseSuccessfully();
		@DefaultMessage("Computer successfully unregistered")
		String hostSuccessfullyUnregistered();
		@DefaultMessage("Are you sure you want to unregister computer?")
		String unregisterHostQuestion();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	public static interface DialogCloseListener extends EventListener {
		void onClose(boolean ok);
	}

	  /////////////
	 // Members //
	/////////////

	private Date nowUtcDate = Common.toUtcDateOnly(new Date());
	private DbLicense license;
	private DbLicenseOnline licenseOnline = null;
	private List<DbLicense> licenseBoosts = null;
	private DbLicense currentBoost = null; // only for confirm boost mode
	private DialogCloseListener dialogCloseListener = null;

	private List<SuHost> hosts;
	private int selectedHost = -1;

	  //////////////////
	 // Constructors //
	//////////////////

	// Show license details
	public LicenseDetailsDialog(DbLicense license) {
		this.license = license;

		generateUI();

		this.setText(constants.licenseDetailsDialogHeader());

		if (!license.isBoost() && license.isOnline()) {
			requestLicenseOnlineDetails();
			if (!license.unconfirmed && !license.isApostill() && !license.isSaas())
				requestLicenseBoosts();
			requestSuLicense();
			requestSuHosts();
		}

		if (UntillLicenseServer.getLogonDealer().isSuperDealer)
			requestLicenseComment();
	}

	// Confirm boost
	public LicenseDetailsDialog(DbLicense license, DbLicense licenseBoost, DialogCloseListener dialogCloseListener) {
		this.license = license;
		this.currentBoost = licenseBoost;
		this.dialogCloseListener = dialogCloseListener;

		generateUI();

		this.setText(constants.confirmLicenseBoostDialog());

		requestLicenseBoosts();

		if (UntillLicenseServer.getLogonDealer().isSuperDealer)
			requestLicenseComment();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private TabPanel tabPanel = new TabPanel();

	private TextArea txtLicenseComment = new TextArea();

	private PasswordTextBox txtHostRegPass = new PasswordTextBox();
	private CheckBox chkShowHostRegPass = new CheckBox("Show");  // TODO to constants
	private Button btnSetHostRegPass = new Button("Set"); // TODO to constants

	private FlexTable tblHosts = new FlexTable();
	private ScrollPanel scrHosts = new ScrollPanel(tblHosts);

	private FlexTable tblProducts = new FlexTable();
	private ScrollPanel scrProducts = new ScrollPanel(tblProducts);

	enum ProductsSortingType {
		BY_PRODUCT, BY_PRODUCT_DESC, BY_VERSION, BY_VERSION_DESC, BY_INST_TIME, BY_INST_TIME_DESC
	};
	private SortingListButtonGroup<ProductsSortingType> tblProductsSortingButtonGroup = new SortingListButtonGroup<>(
			() -> refreshProducts());

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnRecreateInvoice = null;
	private Button btnDisapproveInvoice = null;
	private Button btnClose = new Button(constants.closeButton(), this);

	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private ScrollPanel licensePanel = new ScrollPanel();
	
	private void generateUI() {

		// Assemble license panel with scroll
		licensePanel = new ScrollPanel(assembleLicenseTabWidget());
		licensePanel.setSize("1000px", "600px");
		tabPanel.add(licensePanel, "License"); // TODO to constants
		if (currentBoost != null)
			licensePanel.setHorizontalScrollPosition(Integer.MAX_VALUE);

		if (!license.isBoost() && currentBoost == null) {

			// Assemble hosts panel
			Panel hostsPanel = assembleHostsTabPanel();
			hostsPanel.setSize("1000px", "600px");
			tabPanel.add(hostsPanel, constants.hostsLabel());

		}
	
		// Assemble tabPanel
		tabPanel.selectTab(0);
		mainPanel.add(tabPanel);

		// Assemble button panel
		if (currentBoost == null) {
			pnlButtons.setWidth("100%");
			if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
				if (!license.invoiceRequired && !license.needApprove() && license.invoiceDate != null) {
					btnRecreateInvoice = new Button(constants.recreateInvoiceButton(), this);
					btnRecreateInvoice.setWidth("120px");
					pnlButtons.add(btnRecreateInvoice);
					pnlButtons.setCellHorizontalAlignment(btnRecreateInvoice, HasHorizontalAlignment.ALIGN_LEFT);
				}
				if (!license.isTrial() && !license.needApprove() && !license.unconfirmed) {
					btnDisapproveInvoice = new Button(constants.disapproveLicenseButton(), this);
					btnDisapproveInvoice.setWidth("240px");
					pnlButtons.add(btnDisapproveInvoice);
					pnlButtons.setCellHorizontalAlignment(btnDisapproveInvoice, HasHorizontalAlignment.ALIGN_LEFT);
				}
			}
			Label lblSplitter = new Label(" ");
			pnlButtons.add(lblSplitter);
			pnlButtons.setCellWidth(lblSplitter, "100%");
			btnClose.setWidth("80px");
			pnlButtons.add(btnClose);
			pnlButtons.setCellHorizontalAlignment(btnClose, HasHorizontalAlignment.ALIGN_RIGHT);
			pnlButtons.setSpacing(3);
			mainPanel.add(pnlButtons);
		} else {
			btnOk.setWidth("80px");
			btnCancel.setWidth("80px");
			pnlButtons.add(btnOk);
			pnlButtons.add(btnCancel);
			pnlButtons.setSpacing(3);

			mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
			mainPanel.add(pnlButtons);
		}

		this.setWidget(mainPanel);
	}

	private static final int BOOST_DEF_COLUMNS = 10;
//	private static final String BOOST_COLUMNS_WIDTH = "140px";
	
	private Widget assembleLicenseTabWidget() {
		FlexTable licenseTable = new FlexTable();
		licenseTable.addStyleName("ld-Table");
		int row = 0;

//		// set columns width
//		for (int c = 1; c < BOOST_DEF_COLUMNS + 1; ++c)
//			licenseTable.getColumnFormatter().setWidth(c, BOOST_COLUMNS_WIDTH);
		
		// Common parameters
		licenseTable.setWidget(row, 0, new Label(constants.clientNameLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.getClientName()));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.clientDisplayNameLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.getClientDisplayName() == null ? "" : license.getClientDisplayName()));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.clientAddressLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.getClientAddress()));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.clientEmailLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.getClientEmail()));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.clientPhoneLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.getClientPhone()));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.chainLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.chain));
		row++;

		String licenseType = "";
		if (license.isDefinitive()) {
			licenseType += constants.licenseDefinitive() + " (" + Long.toString(license.getDefinitiveMaxVersion()) + "), ";
		}
		if (license.isTrial())
			licenseType += constants.licenseTrial() + ", ";
		if (license.isOnline())
			licenseType += constants.licenseOnline() + ", ";
		if (license.isModuled())
			licenseType += constants.licenseModuled() + ", ";
		if (license.isSaas())
			licenseType += constants.licenseSaas() + ", ";
		if (license.isBoost())
			licenseType += constants.licenseBoost() + ", ";
		licenseType += (license.isApostill() ? constants.licenseApostill() : constants.licenseUntill());
		
		licenseTable.setWidget(row, 0, new Label(constants.licenseTypeLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(licenseType));
		row++;

		licenseTable.setWidget(row, 0, new Label((license.isOnline() ? constants.licenseUidLabel() :
				constants.hardCodeLabel()) + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(HardCodeHelper.formatHardCode(license.hardCode)));
		row++;

		TimeZone utcTimeZone = TimeZone.createTimeZone(0);
		DateTimeFormat dateFormat = DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT);
		
		licenseTable.setWidget(row, 0, new Label(constants.startDateLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(dateFormat.format(license.getStartDate(), utcTimeZone)));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.issueDateLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(dateFormat.format(license.getIssueDate(), utcTimeZone)));
		row++;
		licenseTable.setWidget(row, 0, new Label(constants.expiredDateLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(license.isNeverExpired() ?	constants.licenseNeverExpired()
				: dateFormat.format(license.getExpiredDate(), utcTimeZone)));
		row++;

		if (licenseBoosts != null && !licenseBoosts.isEmpty() || currentBoost != null) {
			licenseTable.setWidget(row++, 0, new Label("Period" + ": ")); // TODO: to constants
			licenseTable.getFlexCellFormatter().setHorizontalAlignment(row, 1, HasHorizontalAlignment.ALIGN_CENTER);
			licenseTable.setWidget(row++, 1, new Label("original")); // TODO: to constants
		}

		int boostedDataRowBegin = row;

		licenseTable.setWidget(row++, 0, new Label(constants.numberOfLocalDatabasesLabel() + ": "));
		licenseTable.setWidget(row++, 0, new Label(constants.numberOfRemoteConnectionsLabel() + ": "));
		licenseTable.setWidget(row++, 0, new Label(constants.numberOfHandTerminalsLabel() + ": "));
//		licenseTable.setWidget(row++, 0, new Label(constants.numberOfHeadQuartersLabel() + ": "));
		licenseTable.setWidget(row++, 0, new Label(constants.numberOfProductionScreensLabel() + ": "));
		licenseTable.setWidget(row++, 0, new Label(constants.numberOfHhtEftLabel() + ": "));

		licenseTable.setWidget(row++, 0, new Label(constants.modulesLabel() + ": "));
		licenseTable.setWidget(row++, 0, new Label(constants.extraModulesLabel() + ": "));
		licenseTable.setWidget(row++, 0, new Label(constants.interfacesLabel() + ": "));

		if (licenseBoosts != null && !licenseBoosts.isEmpty() || currentBoost != null) {

			fillBoostableColumn(licenseTable, boostedDataRowBegin, 1, license, true, false, false);

			ArrayList<DbLicense> allBoosts = new ArrayList<DbLicense>(licenseBoosts == null ? 1 : licenseBoosts.size()
					+ (currentBoost == null ? 1 : 0));
			if (licenseBoosts != null)
				allBoosts.addAll(licenseBoosts);
			if (currentBoost != null)
				allBoosts.add(currentBoost);

			int col = 2;
			int periodCol = 2;
			Date currentDate = nowUtcDate.before(license.getStartDate()) ? license.getStartDate() : nowUtcDate;
			final Date MAX_DATE = new Date(Long.MAX_VALUE);
			final Date maxDate = license.isNeverExpired() ? MAX_DATE : license.getExpiredDate();
			while (!allBoosts.isEmpty() && !currentDate.after(maxDate)) {
				// get min start date in allBoosts
				final Date minStartDate = Collections.min(allBoosts, Comparator.comparing(b -> b.getStartDate())).getStartDate();
				final Date startDate = currentDate.after(minStartDate) ? currentDate : minStartDate;

				//if (startDate.after(currentDate)) // print only orig period

				// get all boosts that are active at startDate
				List<DbLicense> curBoosts = allBoosts.stream()
						.filter(b -> !b.getStartDate().after(startDate)
								/*&& (b.isNeverExpired() || !b.getExpiredDate().before(minStartDate))*/)
						.sorted(Comparator.comparing(b -> b.getStartDate()))
						.collect(Collectors.toList());
				DbLicense curBoostWithMinExpDate = Collections.min(curBoosts, Comparator.comparing(
						b -> b.isNeverExpired() ? maxDate : b.getExpiredDate()));
				Date minEndDate = curBoostWithMinExpDate.isNeverExpired() ? maxDate : curBoostWithMinExpDate.getExpiredDate();
				// get all boosts that can start before the end of the current boosts
				List<DbLicense> nextBoosts = allBoosts.stream()
						.filter(b -> b.getStartDate().after(startDate))
						.collect(Collectors.toList());
				Date prevMinNextStartDate = null;
				if (!nextBoosts.isEmpty()) {
					final Date minNextStartDate = Collections.min(nextBoosts, Comparator.comparing(b -> b.getStartDate())).getStartDate();
					Date ld = Common.toLocalDateOnly(minNextStartDate);
					CalendarUtil.addDaysToDate(ld, -1);
					prevMinNextStartDate = Common.toUtcDateOnly(ld);
				}
				final Date endDate = prevMinNextStartDate != null && prevMinNextStartDate.before(minEndDate)
						? prevMinNextStartDate : minEndDate;

				// print all curBoosts from startDate to endDate
				licenseTable.getFlexCellFormatter().setColSpan(boostedDataRowBegin - 2, periodCol, curBoosts.size() + 1);
				licenseTable.getFlexCellFormatter().setAlignment(boostedDataRowBegin - 2, periodCol,
						HasHorizontalAlignment.ALIGN_CENTER, HasVerticalAlignment.ALIGN_MIDDLE);
				licenseTable.getFlexCellFormatter().setStyleName(boostedDataRowBegin - 2, periodCol, "ld-BoostPeriodCell");
				if (curBoosts.contains(currentBoost))
					licenseTable.getCellFormatter().addStyleName(boostedDataRowBegin - 2, periodCol, "ld-ThisBoostCell");
				licenseTable.setWidget(boostedDataRowBegin - 2, periodCol, new Label(
						dateFormat.format(startDate, utcTimeZone) + " - " + (endDate.equals(MAX_DATE) ? "" :
						dateFormat.format(endDate, utcTimeZone))));
				periodCol++;
				DbLicense boostedLicense = new DbLicense();
				boostedLicense.setLbConnections(license.getLbConnections());
				boostedLicense.setRemConnections(license.getRemConnections());
				boostedLicense.setOmConnections(license.getOmConnections());
				boostedLicense.setPsConnections(license.getPsConnections());
				boostedLicense.setHeConnections(license.getHeConnections());
				boostedLicense.setFuncLimited(license.getFuncLimited());
				for (DbLicense boost : curBoosts) {
					//licenseTable.getColumnFormatter().setWidth(col, BOOST_COLUMNS_WIDTH);
					licenseTable.getFlexCellFormatter().setStyleName(boostedDataRowBegin - 1, col, "ld-BoostPeriodCell");
					licenseTable.getFlexCellFormatter().setAlignment(boostedDataRowBegin - 1, col,
							HasHorizontalAlignment.ALIGN_CENTER, HasVerticalAlignment.ALIGN_MIDDLE);
					if (licenseBoosts != null && licenseBoosts.contains(boost)) {
						licenseTable.setWidget(boostedDataRowBegin - 1, col, new Label("boost#" + (licenseBoosts.indexOf(boost) + 1))); // TODO: to consts
						fillBoostableColumn(licenseTable, boostedDataRowBegin, col++, boost, true, true, false);
					} else {
						licenseTable.getCellFormatter().addStyleName(boostedDataRowBegin - 1, col, "ld-ThisBoostCell");
						licenseTable.setWidget(boostedDataRowBegin - 1, col, new Label("this boost", false)); // TODO: to consts
						fillBoostableColumn(licenseTable, boostedDataRowBegin, col++, boost, true, true, true);
					}
					boostedLicense.setLbConnections((short) (boostedLicense.getLbConnections() + boost.getLbConnections()));
					boostedLicense.setRemConnections((short) (boostedLicense.getRemConnections() + boost.getRemConnections()));
					boostedLicense.setOmConnections((short) (boostedLicense.getOmConnections() + boost.getOmConnections()));
					boostedLicense.setPsConnections((short) (boostedLicense.getPsConnections() + boost.getPsConnections()));
					boostedLicense.setHeConnections((short) (boostedLicense.getHeConnections() + boost.getHeConnections()));
					boostedLicense.setFuncLimited(boostedLicense.getFuncLimited() | boost.getFuncLimited());
				}
				//licenseTable.getColumnFormatter().setWidth(col, BOOST_COLUMNS_WIDTH);
				licenseTable.getFlexCellFormatter().setStyleName(boostedDataRowBegin - 1, col, "ld-BoostPeriodCell");
				licenseTable.getFlexCellFormatter().setAlignment(boostedDataRowBegin - 1, col,
						HasHorizontalAlignment.ALIGN_CENTER, HasVerticalAlignment.ALIGN_MIDDLE);
				licenseTable.setWidget(boostedDataRowBegin - 1, col, new Label("total"));
				fillBoostableColumn(licenseTable, boostedDataRowBegin, col++, boostedLicense, true, true, false);

				if (endDate.equals(MAX_DATE)) {
					allBoosts.clear(); // == break
				} else {
					// removing expired boosts (expired in endDate)
					allBoosts.removeIf(b -> !b.isNeverExpired() && !endDate.before(b.getExpiredDate()));

					// set currentDate to next day after endDate
					Date ld = Common.toLocalDateOnly(endDate);
					CalendarUtil.addDaysToDate(ld, 1);
					currentDate = Common.toUtcDateOnly(ld);
				}
			}

		} else {

			fillBoostableColumn(licenseTable, boostedDataRowBegin, 1, license, false, false, false);

		}

		// Status
		// TODO Encapsulate it in LicenseClientHelper class
		boolean licenseExpired = !license.isNeverExpired() && license.getExpiredDate().compareTo(nowUtcDate) < 0;
		boolean licenseWillSoonExpire = !license.isNeverExpired() && !licenseExpired && !license.isSaas() && !license.isBoost()
				&& TimeUnit.MILLISECONDS.toDays(license.getExpiredDate().getTime() - nowUtcDate.getTime()) < 30;
		String status = "";
		if (license.isCanceled())
			status += constants.licenseCanceled() + ", ";
		if (license.unconfirmed)
			status += constants.licenseUnconfirmed() + ", ";
		if (licenseExpired)
			status += constants.licenseExpired() + ", ";
		else if (licenseWillSoonExpire)
			status += constants.licenseWillSoonExpire() + ", ";
		if (status.endsWith(", "))
			status = status.substring(0, status.length() - 2);
		// see licenseType above

		licenseTable.setWidget(row, 0, new Label(constants.statusLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(status));
		row++;

		if (!license.dealerComments.isEmpty()) {
			licenseTable.setWidget(row, 0, new Label(constants.dealerCommentsLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(license.dealerComments));
			row++;
		}

		licenseTable.setWidget(row, 0, new Label(constants.minVersionLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(Integer.toString(license.minVersion)));
		row++;
		
		if (license.isOnline() && currentBoost == null) {
			// Append Online license details
			row = appendOnlineLicenseDetails(licenseTable, row);
		}

		// Comment
		if (UntillLicenseServer.getLogonDealer().isSuperDealer && currentBoost == null) {
			licenseTable.setWidget(row, 0, new Label("Comment" + ": ")); // TODO: to constants
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			txtLicenseComment.setWidth("99%");
			//txtLicenseComment.setCharacterWidth(100);
			txtLicenseComment.setVisibleLines(4);
			licenseTable.setWidget(row, 1, txtLicenseComment);
			row++;
		}

		return licenseTable;
	}

	private void fillBoostableColumn(FlexTable licenseTable, int row, int col, DbLicense license, boolean centerNumbers,
			boolean format, boolean thisBoost) {
		if (centerNumbers)
			licenseTable.getFlexCellFormatter().setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		licenseTable.setWidget(row, col, new Label(license.isUnlimited() ?
				constants.licenseUnlimited() : Short.toString(license.getLbConnections()) + (
				license.prolongParams != null && license.prolongParams.getLbConnections() != null ?
				" (" + constants.afterNextProlongLabel() + ":" + license.prolongParams.getLbConnections() + ")" : "")));
		row++;
		if (centerNumbers)
			licenseTable.getFlexCellFormatter().setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		licenseTable.setWidget(row, col, new Label(license.isUnlimited() ?
				constants.licenseUnlimited() : Short.toString(license.getRemConnections()) + (
				license.prolongParams != null && license.prolongParams.getRemConnections() != null ?
				" (" + constants.afterNextProlongLabel() + ":" + license.prolongParams.getRemConnections() + ")" : "")));
		row++;
		if (centerNumbers)
			licenseTable.getFlexCellFormatter().setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		licenseTable.setWidget(row, col, new Label(license.isUnlimited() ?
				constants.licenseUnlimited() : Short.toString(license.getOmConnections()) + (
				license.prolongParams != null && license.prolongParams.getOmConnections() != null ?
				" (" + constants.afterNextProlongLabel() + ":" + license.prolongParams.getOmConnections() + ")" : "")));
		row++;
//		if (centerNumbers)
//			licenseTable.getFlexCellFormatter().setStyleName(row, col, "ld-BoostPeriodCell");
//		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
//		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
//		licenseTable.setWidget(row, col, new Label(license.isUnlimited() ?
//				constants.licenseUnlimited() : Short.toString(license.getHqConnections()) + (
//				license.prolongParams != null && license.prolongParams.getHqConnections() != null ?
//				" (" + constants.afterNextProlongLabel() + ":" + license.prolongParams.getHqConnections() + ")" : "")));
//		row++;
		if (centerNumbers)
			licenseTable.getFlexCellFormatter().setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		licenseTable.setWidget(row, col, new Label(license.isUnlimited() ?
				constants.licenseUnlimited() : Short.toString(license.getPsConnections()) + (
				license.prolongParams != null && license.prolongParams.getPsConnections() != null ?
				" (" + constants.afterNextProlongLabel() + ":" + license.prolongParams.getPsConnections() + ")" : "")));
		row++;
		if (centerNumbers)
			licenseTable.getFlexCellFormatter().setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		licenseTable.setWidget(row, col, new Label(license.isUnlimited() ?
				constants.licenseUnlimited() : Short.toString(license.getHeConnections()) + (
				license.prolongParams != null && license.prolongParams.getHeConnections() != null ?
				" (" + constants.afterNextProlongLabel() + ":" + license.prolongParams.getHeConnections() + ")" : "")));
		row++;

		// Additional functions
		// TODO Encapsulate it in LicenseClientHelper class
		String moduleList = "";
		String extraModuleList = "";
		String interfaceList = "";
		for (int i = 0; i < DbLicense.ENABLED_FUNCTIONS_NUMBER; ++i) {
			if (((1l << i) & license.getFuncLimited()) != 0) {
				if (!DbLicense.isHiddenFunc(i)) {
					String functionName = (i < constants.funcNames().length) ?
							constants.funcNames()[i] : DbLicense.FUNC_NAMES[i];
					if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_MODULE) {
						moduleList += functionName + ", ";
					} else if (DbLicense.FUNC_PROPS[i].getType() == FuncType.FT_EXTRA_MODULE)
						extraModuleList += functionName + ", ";
					else // Type.FT_INTERFACE
						interfaceList += functionName +", ";
				}
			}
		}

		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		if (moduleList.isEmpty()) {
			licenseTable.setWidget(row++, col, new Label(constants.noneLabel()));
		} else {
			licenseTable.setWidget(row++, col, new Label(moduleList.substring(0, moduleList.length() - 2)));
		}
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		if (extraModuleList.isEmpty()) {
			licenseTable.setWidget(row++, col, new Label(constants.noneLabel()));
		} else {
			licenseTable.setWidget(row++, col, new Label(extraModuleList.substring(0, extraModuleList.length() - 2)));
		}
		if (format) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-BoostPeriodCell");
		if (thisBoost) licenseTable.getFlexCellFormatter().addStyleName(row, col, "ld-ThisBoostCell");
		if (interfaceList.isEmpty()) {
			licenseTable.setWidget(row++, col, new Label(constants.noneLabel()));
		} else {
			licenseTable.setWidget(row++, col, new Label(interfaceList.substring(0, interfaceList.length() - 2)));
		}
	}
	
	private int appendOnlineLicenseDetails(FlexTable licenseTable, int row) {

		// common part
		licenseTable.getFlexCellFormatter().setColSpan(row, 0, BOOST_DEF_COLUMNS + 1);
		licenseTable.getFlexCellFormatter().setAlignment(row, 0,
				HasHorizontalAlignment.ALIGN_CENTER, HasVerticalAlignment.ALIGN_MIDDLE);
		Label headerLabel = new Label(constants.onlineDetailsLabel());
		headerLabel.addStyleName("ld-HeaderLabel");
		licenseTable.setWidget(row++, 0, headerLabel);
		if (license.installationTime != null) {
			licenseTable.setWidget(row, 0, new Label(constants.installationTimeLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(DateTimeFormat.getFormat(PredefinedFormat.DATE_TIME_SHORT).
					format(license.installationTime)));
			row++;
		}
		licenseTable.setWidget(row, 0, new Label(constants.availableReinstallationsLabel() + ": "));
		licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
		licenseTable.setWidget(row, 1, new Label(Integer.toString(license.availableReinstallations)));
		row++;

		if (licenseOnline == null)
			return row;

		DateTimeFormat dateTimeFormat = DateTimeFormat.getFormat(PredefinedFormat.DATE_TIME_SHORT);
		if (licenseOnline.getActiveProductName() != null) {
			String activeProduct = licenseOnline.getActiveProductName() + " " +
					Integer.toString(licenseOnline.getActiveProductVersion());
			if (licenseOnline.getActiveDatabaseInfo() != null) {
				activeProduct += " [" + reduceString(licenseOnline.getActiveDatabaseInfo()) + "]";
			}
			licenseTable.setWidget(row, 0, new Label(constants.activeProductLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(activeProduct));
			row++;
		}
		if (licenseOnline.getPartialActiveSignature() != null) {
			licenseTable.setWidget(row, 0, new Label(constants.activeSignatureLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(licenseOnline.getPartialActiveSignature()));
			row++;
		}
		if (licenseOnline.getActivationTime() != null) {
			licenseTable.setWidget(row, 0, new Label(constants.activationTimeAndCountLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(dateTimeFormat.format(licenseOnline.getActivationTime())
					+ " [" + Integer.toString(licenseOnline.getActivationCount()) + "]"));
			row++;
		}
		if (licenseOnline.getActivationPeriodBegin() != null && licenseOnline.getActivationPeriodBegin() != null) {
			licenseTable.setWidget(row, 0, new Label(constants.activationPeriodLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(dateTimeFormat.format(licenseOnline.getActivationPeriodBegin())
					+ " - " + dateTimeFormat.format(licenseOnline.getActivationPeriodEnd())));
			row++;
		}
		if (licenseOnline.getFailureCount() > 0) {
			licenseTable.setWidget(row, 0, new Label(constants.failureCountLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(Integer.toString(licenseOnline.getFailureCount())));
			row++;
		}
		if (licenseOnline.getLastFailureTime() != null) {
			String lastFailure = dateTimeFormat.format(licenseOnline.getLastFailureTime()) + " [" +
					Integer.toString(licenseOnline.getLastFailureCount()) + "]";
			if (licenseOnline.getLastFailureField() != null) {
				lastFailure += " (" + licenseOnline.getLastFailureField();
				if (licenseOnline.getLastFailureData() != null) {
					lastFailure += ": " + reduceString(licenseOnline.getLastFailureData());
				}
				lastFailure += ")";
			}
			licenseTable.setWidget(row, 0, new Label(constants.lastFailureLabel() + ": "));
			licenseTable.getFlexCellFormatter().setColSpan(row, 1, BOOST_DEF_COLUMNS);
			licenseTable.setWidget(row, 1, new Label(lastFailure));
			row++;
		}

		return row;
	}

	private VerticalPanel assembleHostsTabPanel() {
		VerticalPanel hostsPanel = new VerticalPanel();
		EntitledDecoratorPanel dpnSuLicense = new EntitledDecoratorPanel();
		FlexTable tblSuLicense = new FlexTable();
		Label lblHostRegPass = new Label(constants.hostRegistrationPasswordLabel() + ":");
		txtHostRegPass.setWidth("20em");
		chkShowHostRegPass.addClickHandler(this);
		btnSetHostRegPass.setWidth("80px");
		btnSetHostRegPass.addClickHandler(this);
		int row = 0;
		tblSuLicense.setWidget(row, 0, lblHostRegPass);
		tblSuLicense.setWidget(row, 1, txtHostRegPass);
		tblSuLicense.setWidget(row, 2, chkShowHostRegPass);
		tblSuLicense.setWidget(row++, 3, btnSetHostRegPass);
		dpnSuLicense.setWidget(tblSuLicense);
//		dpnSuLicense.setHeight("0");
		hostsPanel.add(dpnSuLicense);

		// Create hosts table
		tblHosts.addClickHandler(this);
		tblHosts.addStyleName("al-Table"); 
		tblHosts.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblHosts.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "2000");
		tblHosts.setText(0, col++, constants.hostColumnHeader());
		tableCF.setWidth(0, col, "1000");
		tblHosts.setText(0, col++, constants.stateColumnHeader());
		tableCF.setWidth(0, col, "600");
		tblHosts.setText(0, col++, "CPU usage"); // TODO to constants
		tableCF.setWidth(0, col, "600");
		tblHosts.setText(0, col++, "RAM usage"); // TODO to constants
		tableCF.setWidth(0, col, "600");
		tblHosts.setText(0, col++, "HDD free (C:)"); // TODO to constants
		tableCF.setWidth(0, col, "1000");
		tblHosts.setText(0, col++, constants.unregisterColumnHeader());
		scrHosts.setHeight("318px");
//		scrHosts.setSize("600px", "200px");
		EntitledDecoratorPanel dpnHosts = new EntitledDecoratorPanel(constants.hostsLabel());
		dpnHosts.setWidget(scrHosts);
		hostsPanel.add(dpnHosts);

		// Create products table
		tblProducts.addStyleName("al-Table"); 
		tblProducts.getRowFormatter().addStyleName(0, "al-Header"); 
		tableCF = tblProducts.getFlexCellFormatter();
		col = 0;
		tableCF.setWidth(0, col, "2000");
		tblProducts.setWidget(0, col++, tblProductsSortingButtonGroup.createButton(constants.productColumnHeader(),
				BY_PRODUCT, BY_PRODUCT_DESC));
		tableCF.setWidth(0, col, "1500");
		tblProducts.setWidget(0, col++, tblProductsSortingButtonGroup.createButton(constants.versionColumnHeader(),
				BY_VERSION, BY_VERSION_DESC));
		tableCF.setWidth(0, col, "2000");
		tblProducts.setWidget(0, col++, tblProductsSortingButtonGroup.createButton(constants.installationTimeColumnHeader(),
				BY_INST_TIME, BY_INST_TIME_DESC, true, true));
		scrProducts.setHeight("185px");
		EntitledDecoratorPanel dpnProducts = new EntitledDecoratorPanel("Installed products"); // TODO to constants
		dpnProducts.setWidget(scrProducts);
		hostsPanel.add(dpnProducts);
//		hostsPanel.setCellHeight(dpnProducts, "100%");

		return hostsPanel;
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object source = event.getSource();

		if (source == btnClose) {

			this.hide();

		} else if (source == btnOk) {

			this.hide();
			if (dialogCloseListener != null)
				dialogCloseListener.onClose(true);

		} else if (source == btnCancel) {

			this.hide();
			if (dialogCloseListener != null)
				dialogCloseListener.onClose(false);

		} else if (btnRecreateInvoice != null && source == btnRecreateInvoice) {

			new MessageDialog(messages.recreateInvoiceForLicenseQuestion(), Style.QUESTION, 
					Buttons.YESNO, new recreateInvoiceHandler()).show();

		} else if (btnDisapproveInvoice != null && source == btnDisapproveInvoice) {

			new MessageDialog(messages.disapproveLicenseQuestion(), Style.QUESTION, 
					Buttons.YESNO, new disapproveLicenseHandler()).show();

		} else if (source == chkShowHostRegPass) {

			txtHostRegPass.getElement().setAttribute("type", chkShowHostRegPass.getValue() ? "text" : "password");

			
		} else if (source == btnSetHostRegPass) {

			String hostRegPass = txtHostRegPass.getText();
			if (hostRegPass.isEmpty())
				hostRegPass = null;

			UntillLicenseServer.getLicenseServiceAsync().setSuLicenseHostRegPass(
					UntillLicenseServer.getAuthScheme(), license.hardCode, hostRegPass,
					new AsyncCallback<Void>() {
						@Override
						public void onSuccess(Void result) {
						}
						@Override
						public void onFailure(Throwable caught) {
							ErrorDialog.show(caught);
						}
					}
			);

		} else if (source == tblHosts) {

			Cell cell = tblHosts.getCellForEvent(event);
			if (cell != null && cell.getRowIndex() > 0) {
				if (selectedHost != -1)
					tblHosts.getRowFormatter().removeStyleName(selectedHost + 1, "ll-RowSelected");
				selectedHost = cell.getRowIndex() - 1;
				tblHosts.getRowFormatter().addStyleName(selectedHost + 1, "ll-RowSelected");
				refreshProducts();
			}

		}
	}

	private class recreateInvoiceHandler implements MessageDialogListener, AsyncCallback<Void> {
		@Override
		public void onSelectButton(SelectedButton selectedButton) {
			if (selectedButton == SelectedButton.YES) {
	
				// Call RPC
				UntillLicenseServer.getLicenseServiceAsync().recreateInvoice(
						UntillLicenseServer.getAuthScheme(), license.id, this);
	
				WaitDialog.show();
				
			}
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Void result) {
			WaitDialog.hideAll();
			new MessageDialog(messages.invoiceForLicenseRecreatedSuccessfully(), 
					Style.INFORMATION, Buttons.OK, new MessageDialogListener() {
						@Override
						public void onSelectButton(SelectedButton selectedButton) {
							MainPage.get().refresh();
							LicenseDetailsDialog.this.hide();
						}
					}).show();
		}
	}

	private class disapproveLicenseHandler implements MessageDialogListener, AsyncCallback<Void> {
		@Override
		public void onSelectButton(SelectedButton selectedButton) {
			if (selectedButton == SelectedButton.YES) {
	
				// Call RPC
				UntillLicenseServer.getLicenseServiceAsync().disapproveLicense(
						UntillLicenseServer.getAuthScheme(), license.id, this);
	
				WaitDialog.show();
				
			}
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Void result) {
			WaitDialog.hideAll();
			new MessageDialog(messages.disapproveLicenseSuccessfully(), 
					Style.INFORMATION, Buttons.OK, new MessageDialogListener() {
						@Override
						public void onSelectButton(SelectedButton selectedButton) {
							MainPage.get().refresh();
							LicenseDetailsDialog.this.hide();
						}
					}).show();
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private String reduceString(String s) {
		if (s == null || s.length() < 20)
			return s;
		return s.substring(0, 8) + "..." + s.substring(s.length() - 8);
	}

	private void requestLicenseOnlineDetails() {
		UntillLicenseServer.getLicenseServiceAsync().getLicenseOnlineDetails(
				UntillLicenseServer.getAuthScheme(), license.id,
				new AsyncCallback<DbLicenseOnline>() {
					@Override
					public void onSuccess(DbLicenseOnline result) {
						licenseOnline = result;
						refreshLicenseTab();
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void requestLicenseBoosts() {
		UntillLicenseServer.getLicenseServiceAsync().getLicenseBoosts(
				UntillLicenseServer.getAuthScheme(), license.hardCode,
				new AsyncCallback<List<DbLicense>>() {
					@Override
					public void onSuccess(List<DbLicense> result) {
						// cut expired boosts
						result.removeIf(b -> !b.isNeverExpired() && b.getExpiredDate().before(nowUtcDate));
						if ((licenseBoosts == null || licenseBoosts.isEmpty()) && result.isEmpty())
							return;
						licenseBoosts = result;
						refreshLicenseTab();
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void requestLicenseComment() {
		txtLicenseComment.setReadOnly(true);
		UntillLicenseServer.getLicenseServiceAsync().getLicenseComment(
				UntillLicenseServer.getAuthScheme(), license.id,
				new AsyncCallback<String>() {
					@Override
					public void onSuccess(String result) {
						txtLicenseComment.setText(result);
						txtLicenseComment.setReadOnly(false);
						txtLicenseComment.addValueChangeHandler(event -> {
							WaitDialog.show();
							UntillLicenseServer.getLicenseServiceAsync().setLicenseComment(
									UntillLicenseServer.getAuthScheme(), license.id, event.getValue(),
									new AsyncCallback<Void>() {
										@Override
										public void onSuccess(Void result) {
											WaitDialog.hide();
										}
										@Override
										public void onFailure(Throwable caught) {
											WaitDialog.hideAll();
											ErrorDialog.show(caught);
										}
									});
						});
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void refreshLicenseTab() {
		licensePanel.setWidget(assembleLicenseTabWidget());
		if (currentBoost != null)
			licensePanel.setHorizontalScrollPosition(Integer.MAX_VALUE);
	}

	private void requestSuLicense() {
		UntillLicenseServer.getLicenseServiceAsync().getSuLicense(
				UntillLicenseServer.getAuthScheme(), license.hardCode,
				new AsyncCallback<SuLicense>() {
					@Override
					public void onSuccess(SuLicense result) {
						if (result != null && txtHostRegPass.getText().isEmpty())
							txtHostRegPass.setText(result.hostRegPass == null ? "" : result.hostRegPass);
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void requestSuHosts() {
		UntillLicenseServer.getLicenseServiceAsync().getSuHostsByHardCode(
				UntillLicenseServer.getAuthScheme(), license.hardCode,
				new AsyncCallback<List<SuHost>>() {
					@Override
					public void onSuccess(List<SuHost> result) {
						hosts = result;
						refreshHosts();
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void refreshHosts() {
		selectedHost = -1;
		refreshProducts();
		scrHosts.remove(tblHosts);
		try {
			for (int r = tblHosts.getRowCount() - 1; r > 0; --r)
				tblHosts.removeRow(r);
			fillHosts();
		} finally {
			scrHosts.add(tblHosts);
		}
	}

	private void fillHosts() {

		if (hosts == null)
			return;

		RowFormatter tblHostsRF = tblHosts.getRowFormatter();
		FlexCellFormatter tblHostsCF = tblHosts.getFlexCellFormatter();

		int row = 1;
		for (SuHost host : hosts) {

			tblHostsRF.addStyleName(row, "ll-Row");

			long offlineH = host.offlineDuration / 1000 / 60 / 60;
			long offlineM = host.offlineDuration / 1000 / 60 - offlineH * 60;
			String state = host.offlineDuration == -1 ? constants.offline() 
					: (host.offlineDuration < SuHost.HOST_OFFLINE_TRESHOLD_MS ? constants.online()
							: constants.offline() + " (" + (offlineH != 0 ? offlineH + "h " : "") + offlineM + "m)");

			String cpu = "", ram = "", hdd = "";
			if (host.shortSysInfo != null) {
				try {
					JSONValue jsonValue = JSONParser.parseStrict(host.shortSysInfo);
					JSONObject jsonObjectShortSysInfo = jsonValue.isObject();
					if (jsonObjectShortSysInfo == null) throw new Exception("shortSysInfo == null");
					JSONNumber jsonNumberCpu = jsonObjectShortSysInfo.get("cpu").isNumber();
					JSONNumber jsonNumberRam = jsonObjectShortSysInfo.get("ram").isNumber();
					JSONNumber jsonNumberHdd = jsonObjectShortSysInfo.get("hdd").isNumber();
					cpu = jsonNumberCpu == null ? "-" : Double.toString(jsonNumberCpu.doubleValue()) + "%";
					ram = jsonNumberRam == null ? "-" : Double.toString(jsonNumberRam.doubleValue()) + "%";
					hdd = jsonNumberHdd == null ? "-" : Double.toString(jsonNumberHdd.doubleValue()) + " GB";
				} catch (Exception e) {
					ErrorDialog.show("Parsing shortSysInfo error", e);
				}
			}

			int col = 0;
			tblHosts.setText(row, col++, host.name);
			tblHosts.setText(row, col++, state);
			tblHostsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_RIGHT);
			tblHosts.setText(row, col++, cpu);			
			tblHostsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_RIGHT);
			tblHosts.setText(row, col++, ram);			
			tblHostsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_RIGHT);
			tblHosts.setText(row, col++, hdd);			
			tblHostsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			tblHosts.setWidget(row, col++, new UnregisterHostButton(host));
			
			++row;
		}

	}

	private void refreshProducts() {
		scrProducts.remove(tblProducts);
		try {
			for (int r = tblProducts.getRowCount() - 1; r > 0; --r)
				tblProducts.removeRow(r);
			fillProducts();
		} finally {
			scrProducts.add(tblProducts);
		}
	}

	private void fillProducts() {
		if (selectedHost == -1)
			return;

		SuHost host = hosts.get(selectedHost);

		if (host.deployedProducts == null)
			return;

		class Product {
			String name, version;
			Date depTime;
		}
		List<Product> products = new ArrayList<>(); 

		RowFormatter tblProductsRF = tblProducts.getRowFormatter();
		FlexCellFormatter tblProductsCF = tblProducts.getFlexCellFormatter();

		DateTimeFormat isoTimeFormat = DateTimeFormat.getFormat(PredefinedFormat.ISO_8601);
		DateTimeFormat mediumTimeFormat = DateTimeFormat.getFormat(PredefinedFormat.DATE_TIME_MEDIUM);

		try {
			JSONValue jsonValue = JSONParser.parseStrict(host.deployedProducts);
			JSONArray jsonArrayProducts = jsonValue.isArray();
			if (jsonArrayProducts == null) throw new Exception("deployedProducts == null");
			for (int i = 0; i < jsonArrayProducts.size(); ++i) {
				Product product = new Product();
				JSONObject jsonObjectProduct = jsonArrayProducts.get(i).isObject();
				JSONValue jsonValueName = jsonObjectProduct.get("productName");
				product.name = ""; 
				if (jsonValueName != null) {
					JSONString jsonStringName = jsonValueName.isString();
					product.name = jsonStringName == null ? "-" : jsonStringName.stringValue();
				}
				JSONValue jsonValueVersion = jsonObjectProduct.get("productVersion");
				product.version = "";
				if (jsonValueVersion != null) {
					JSONString jsonStringVersion = jsonValueVersion.isString();
					product.version = jsonStringVersion == null ? "-" : jsonStringVersion.stringValue();
				}
				JSONValue jsonValueTime = jsonObjectProduct.get("deploymentTime");
				product.depTime = null;
				if (jsonValueTime != null) {
					JSONString jsonStringTime = jsonValueTime.isString();
					if (jsonStringTime != null) {
						String deploymentTime = jsonStringTime.stringValue();
						if (deploymentTime != null && !deploymentTime.isEmpty()) {
							product.depTime = isoTimeFormat.parse(deploymentTime);
						}
					}
				}
				products.add(product);
			}
		} catch (Exception e) {
			ErrorDialog.show("Parsing deployedProducts error", e);
		}

		if (tblProductsSortingButtonGroup.getSortingType() != null) {
			products.sort((p1, p2) -> {
				switch (tblProductsSortingButtonGroup.getSortingType()) {
				default:
				case BY_PRODUCT:        return String.CASE_INSENSITIVE_ORDER.compare(p1.name, p2.name);
				case BY_PRODUCT_DESC:   return String.CASE_INSENSITIVE_ORDER.compare(p2.name, p1.name);
				case BY_VERSION:        return Comparator.nullsLast(Version::compareTo).compare(
						Version.create(p1.version), Version.create(p2.version));
				case BY_VERSION_DESC:   return Comparator.nullsFirst(Version::compareTo).compare(
						Version.create(p2.version), Version.create(p1.version));
				case BY_INST_TIME:      return Comparator.nullsFirst(Date::compareTo).compare(p1.depTime, p2.depTime);
				case BY_INST_TIME_DESC: return Comparator.nullsLast(Date::compareTo).compare(p2.depTime, p1.depTime);
				}
			});
		}

		int row = 1;
		for (Product product : products) {
			tblProductsRF.addStyleName(row, "ll-Row");
			int col = 0;
			tblProducts.setText(row, col++, Common.capitalizeProductName(product.name));
			tblProducts.setText(row, col++, product.version);
			tblProductsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			tblProducts.setText(row, col++, product.depTime == null ? "" : mediumTimeFormat.format(product.depTime));
			++row;
		}

	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
	}

	  /////////////////////
	 // Private classes //
	/////////////////////

	private class UnregisterHostButton extends Button implements ClickHandler, AsyncCallback<Void> {
		SuHost host;

		public UnregisterHostButton(SuHost host) {
			super(constants.unregisterHostButton());
			this.host = host;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {
			new MessageDialog(messages.unregisterHostQuestion(), Style.QUESTION, 
					Buttons.YESNO, (selectedButton) -> {
						if (selectedButton == SelectedButton.YES) {
							UntillLicenseServer.getLicenseServiceAsync().unregisterSuHost(
									UntillLicenseServer.getAuthScheme(), host.hostId, this);
							WaitDialog.show();
						}
					}
			).show();
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Void result) {
			WaitDialog.hide();
			new MessageDialog(messages.hostSuccessfullyUnregistered(), Style.INFORMATION).show();
			requestSuHosts();
		}
	}

}
