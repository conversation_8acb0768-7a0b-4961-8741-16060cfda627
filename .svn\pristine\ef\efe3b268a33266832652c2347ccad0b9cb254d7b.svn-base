/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyPressEvent;
import com.google.gwt.event.dom.client.KeyPressHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.Grid;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PasswordTextBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.AuthScheme;
import eu.untill.license.client.PlainAuthScheme;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.LicenseService.RrDealer;
import eu.untill.license.client.ui.MessageDialog.Style;

/**
 * <AUTHOR>
 *
 */
public class LoginWidget extends Composite implements ClickHandler, AsyncCallback<RrDealer>,
		KeyPressHandler {

	public static interface UlsConstants extends Constants {
		String usernameLabel();
		String passwordLabel();
	}

	public static interface UlsMessages extends Messages {
		String logonFailMessage();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	AuthScheme authScheme;

	private VerticalPanel panel = new VerticalPanel();
	private Grid grid = new Grid(2, 2);

	private Label lblUsername = new Label(constants.usernameLabel() + ":");
	private TextBox txtUsername = new TextBox();
	private Label lblPassword = new Label(constants.passwordLabel() + ":");
	private PasswordTextBox txtPassword = new PasswordTextBox();
	private Button btnLogin = new Button("Log in");

	public LoginWidget() {

		txtUsername.setWidth("20ex");
		txtPassword.setWidth("20ex");

		txtUsername.setName("Username");
		txtPassword.setName("Password");

		txtUsername.addKeyPressHandler(this);
		txtPassword.addKeyPressHandler(this);
		
		grid.setWidget(0, 0, lblUsername); grid.setWidget(0, 1, txtUsername);
		grid.setWidget(1, 0, lblPassword); grid.setWidget(1, 1, txtPassword);
		panel.add(grid);

		panel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);
		panel.add(btnLogin);

		btnLogin.addClickHandler(this);

		this.initWidget(panel);
//		this.setStyleName("Login");
	}

	public void refresh() {
	}

	@Override
	public void onKeyPress(KeyPressEvent event) {
		if (event.getCharCode() == '\r') {
			btnLogin.click();
		}
	}

	@Override
	public void onClick(ClickEvent event) {
		if (event.getSource() == btnLogin) {
			authScheme = new PlainAuthScheme(txtUsername.getText(), txtPassword.getText());

			WaitDialog.show();

			// Call RPC login
			UntillLicenseServer.getLicenseServiceAsync().login(authScheme, this);
		}
	}

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		authScheme = null;
		ErrorDialog.show(caught);
	}

	@Override
	public void onSuccess(RrDealer result) {
		WaitDialog.hide();
		txtPassword.setText("");
		if (result != null) {
			UntillLicenseServer.onLogon(authScheme, result);
			authScheme = null;
		} else {
			authScheme = null;
			txtPassword.setFocus(true);
			new MessageDialog(messages.logonFailMessage(), Style.WARNING, txtPassword).show();
		}
	}

}
