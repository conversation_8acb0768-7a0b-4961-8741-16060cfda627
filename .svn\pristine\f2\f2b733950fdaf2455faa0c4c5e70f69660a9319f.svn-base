# Common
okButton = OK
cancelButton = Cancel
retryButton = Retry
yesButton = Yes
noButton = No
closeButton = Close

noneLabel = <none>

# MainPage
licensesTabTitle = Licenses
tasksTabTitle = Tasks

# TitleWidget
mainTitle = unTill License Server
logoutButton = Logout

# LoginWidget

usernameLabel = Username
passwordLabel = Password

# LicenseListWidget / TaskListWidget
dealerLabel = Dealer

createNewLicenseButton = Create new license
refreshButton = Refresh
addApostillHddCodesButton = Add Apostill codes
clientNameFilterLabel = Client name filter
hardCodeFilterLabel = Hard code filter
showOnlyTrialFilterLabel = Show only demo / trial
showCanceledFilterLabel = Show canceled
showOnlyNonApprovedFilterLabel = Show only non-approved
resendThisRequestButton = Re-send request
resendThisLicenseButton = Re-send
prolongThisLicenseButton = Prolong
upgradeThisLicenseButton = Upgrade
boostThisLicenseButton = Boost
getEmergencyLicenseButton = Emergency
reinstallLicenseButton = Re-install
approveLicenseButton = Approve
createManualInvoiceButton = Create invoice
showClientNameChangesButton = Show name changes

statusColumnHeader = Status
clientNameColumnHeader = Client name
clientDisplayNameColumnHeader = Display name
chainColumnHeader = Chain
hardCodeColumnHeader = Hard code / License UID
localDatabasesColumnHeader = Loc. DB
localDatabasesColumnHint = Local databases
remoteConnectionsColumnHeader = Rem. Con.
remoteConnectionsColumnHint = Remote Connections
handTerminalsColumnHeader = Hand term.
handTerminalsColumnHint = Hand terminals
#headQuartersColumnHeader = Head quar.
#headQuartersColumnHint = Head quarters
productionScreensColumnHeader = Prod. Scr. Prof.
productionScreensColumnHint = Production Screen Professional
hhtEftColumnHeader = HHT-EFT
hhtEftColumnHint = HHT-EFT
startDateColumnHeader = Start
issueDateColumnHeader = Issue
expiredDateColumnHeader = Expired
tempExpiredDateColumnHeader = Temp.
resendThisLicenseColumnHeader = Re-send license
prolongThisLicenseColumnHeader = Prolong
upgradeThisLicenseColumnHeader = Upgrade
boostThisLicenseColumnHeader = Boost
getEmergencyLicenseColumnHeader = Get emergency / Re-install 
approveOrderColumnHeader = Approve

licensesNotFound = Licenses is not found
licenseCanceled = Canceled
licenseUnconfirmed = Unconfirmed
licenseDefinitive = Definitive
licenseExpired = Expired
licenseWillSoonExpire = Will soon expire
licenseApostill = Apostill license
licenseUntill = unTill license
licenseTrial = Demo / trial
licenseOnline = Online
licenseModuled = Moduled
licenseUnlimited = Unlimited
licenseNeverExpired = Never
licenseSaas = SaaS
licenseBoost = Boost

allDealersListItem = All dealers

orderManualLicenseButton = Create order 
orderManualLicenseButtonTitle = Crate order for manually entered license

totalLabel = Total

# LicenseDetailsDialog
licenseDetailsDialogHeader = License details
recreateInvoiceButton = Recreate invoice
disapproveLicenseButton = Set license back to Approval state

# LicenseDialog / LicenseDetailsDialog
addNewLicenseHeader = Add new license
prolongLicenseHeader = Prolong license
upgradeLicenseHeader = Upgrade license
getEmergencyLicenseHeader = Get emergency license
licenseBoostHeader = Addition to existing license

overallDataLabel = Overall data

startDateLabel = Start date
issueDateLabel = Issue date
expiredDateLabel = End date

clientPanel = Client details
clientNameLabel = Company/Name
clientDisplayNameLabel = Display name
clientAddressLabel = Address
clientEmailLabel = Email
clientPhoneLabel = Telephone
chainLabel = Chain
licenseTypeLabel = License type
hardCodeLabel = Hard code
testHardCodeButton = Test
licenseUidLabel = License UID

quantitativeRestrictionsPanel = Quantities
numberOfLocalDatabasesLabel = Local Databases
numberOfRemoteConnectionsLabel = Remote Connections
numberOfHandTerminalsLabel = Handhelds
#numberOfHeadQuartersLabel = Number of head quarters
numberOfProductionScreensLabel = Production Screen Professional
numberOfHhtEftLabel = HHT-EFT
unlimitedCheckBox = Unlimited

modulesLabel = Modules
extraModulesLabel = Extras
interfacesLabel = 3rd party interfaces

# Extra-support, Globe Exact accounting, Hotel interface, Beverage control system, Stock, Beverage control system 1, Standard Kitchen Screen, Beverage control system 2, EFT interface, Reservations, Music Player, EMenu, WM - Restaurant, WM - Office, Web Management, Reserve 1, Reserve 2, Reserve 3, Rental, TP API POS, TP API Reservations, Beverage control system 3, Protel, Permanent Logging, Annoncer Kitchen Screen, Basic, Screen editor, Ticket editor, Clients & Accounts, Export & Bookkeeping, User Control, Pricelevels & Areas
funcNames = -
statusLabel = Status
#definitiveLicenseCheckBox = Definitive
definitiveLicenseCheckBox = No software upgrades
trialCheckBox = Demo / trial
onlineCheckBox = Online
moduledCheckBox = Moduled

dealerCommentsLabel = Comments

requestButton = Save
cancelButton = Cancel

# ApostillHddCodesDialog
apostillHddCodesDialog = Apostill HDD codes

apostillDealerLabel = Apostill dealer
hddCodesPanel = HDD codes

# ApproveLicenseDialog
approveLicenseHeader = Approve license
licensePanel = License
quantityColumnHeader = Quantity
articleNumberColumnHeader = Article number 
articleNameColumnHeader = Article
priceColumnHeader = Price
addNegativeColumnHeader = Add negative
addNegativeForThisArticleButton = Add negative
orderIsEmpty = Order is empty
orderDoesNotExist = Order does not exist
orderLabel = Order
addDiscountButton = Add discount
reorderButton = Recreate order
approveButton = Approve
approveNoInvoiceButton = No invoice
approveLaterButton = Close
totalPriceWOVatLabel = Total price excl. VAT
totalVatLabel = VAT
totalPriceLabel = Total

# AddDiscountDialog
addDiscountHeader = Add discount
discountPriceLabel = Price
discountTextLabel = Text

# addNegativeArticleHeader
addNegativeArticleHeader = Add negative article
negativeArticleLabel = Article
negativeArticleQuantityLabel = Quantity
negativeArticleTextLabel = Text

# ResendLicenseDialog
resendLicenseDialogHeader = Re-send license file
fullLicenseFileLabel = Full license file
temporaryLicenseFileLabel = Temporary license file

# LicenseDialog / ResendLicenseDialog
temporaryEndDateLabel = Temporary end date

# LicenseDialog
#generateTemporaryLicenseLabel = Generate a temporary license first
