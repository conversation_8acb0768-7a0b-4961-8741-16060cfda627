/**
 * 
 */
package eu.untill.license.client;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * <AUTHOR>
 *
 */
public class PlainAuthScheme extends AuthScheme implements IsSerializable {
	public String login;
	public String password;

	public PlainAuthScheme() {
	}

	/**
	 * @param login
	 * @param password
	 */
	public PlainAuthScheme(String login, String password) {
		super();
		this.login = login;
		this.password = password;
	}

	@Override
	public String toString() {
		return "[PlainAuthScheme: login=\"" + login + "\"]";
	}
}
