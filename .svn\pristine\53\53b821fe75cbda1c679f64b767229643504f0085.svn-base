package eu.untill.license.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

public class Version implements Comparable<Version>, IsSerializable {

	private String[] parts;

	public Version() {
	}

	public Version(String version) {
		parts = version.split("\\.");
	}

	public static Version create(String version) {
		if (version == null)
			return null;
		return new Version(version);
	}

	@Override
	public int compareTo(Version o) {
		for (int i = 0; i < parts.length && i < o.parts.length; ++i) {
			int strCmp = parts[i].compareTo(o.parts[i]);
			if (strCmp != 0) {
				try {
					return Integer.signum(Integer.valueOf(parts[i]).compareTo(Integer.valueOf(o.parts[i])));
				} catch (NumberFormatException e) {
					return strCmp;
				}
			}
		}
		return Integer.signum(parts.length - o.parts.length);
	}

}
