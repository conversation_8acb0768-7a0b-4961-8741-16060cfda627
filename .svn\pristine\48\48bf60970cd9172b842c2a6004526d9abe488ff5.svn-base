<?xml version="1.0" encoding="UTF-8"?>
<!--
  When updating your version of GWT, you should also update this DTD reference,
  so that your app can take advantage of the latest GWT module capabilities.
-->
<!DOCTYPE module PUBLIC "-//Google Inc.//DTD Google Web Toolkit 2.8.0//EN"
  "http://gwtproject.org/doctype/2.8.0/gwt-module.dtd">
<module rename-to='untilllicenseserver'>
  <!-- Inherit the core Web Toolkit stuff.                        -->
  <inherits name='com.google.gwt.user.User'/>

  <!-- Inherit the default GWT style sheet.  You can change       -->
  <!-- the theme of your GWT application by uncommenting          -->
  <!-- any one of the following lines.                            -->
  <!-- <inherits name='com.google.gwt.user.theme.clean.Clean'/>   -->
  <!-- <inherits name='com.google.gwt.user.theme.standard.Standard'/> -->
  <inherits name='com.google.gwt.user.theme.chrome.Chrome'/>
  <!-- <inherits name='com.google.gwt.user.theme.dark.Dark'/>     -->

  <!-- Other module inherits                                      -->
  <inherits name="com.google.gwt.i18n.I18N"/>
  <extend-property name="locale" values="en_GB"/>
  <!--<extend-property name="locale" values="ru"/>-->
  <!--<extend-property name="locale" values="fr"/>-->
  <!--<extend-property name="locale" values="nl"/>-->
  <set-property name="locale" value="en_GB"/>

  <inherits name="com.google.gwt.json.JSON"/>

  <inherits name='eu.untill.license.shared'/>

  <!-- Specify the app entry point class.                         -->
  <entry-point class='eu.untill.license.client.UntillLicenseServer'/>

  <!-- Specify the paths for translatable code                    -->
  <source path='client'/>
  <source path='shared'/>

  <!-- allow Super Dev Mode -->
  <add-linker name="xsiframe"/>
</module>
