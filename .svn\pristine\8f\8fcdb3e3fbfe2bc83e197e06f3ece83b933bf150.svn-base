def mods = [
	BAS: 'Basic',
	A: 'Screen editor',
	B: 'Ticket editor',
	C: 'Clients &amp; Accounts',
	D: 'Export &amp; Bookkeeping',
	E: 'User Control',
	F: 'Pricelevels &amp; Areas',
]

println """<?xml version="1.0" encoding="UTF-8"?>
		|<data>
		|	<categories>
		|		<category>
		|			<id>1009</id>
		|			<name>Licenses-18</name>
		|		</category>
		|	</categories>
		|	<printers>
		|	</printers>
		|	<groups>
		|		<group>
		|			<id>2009</id>
		|			<name>Licenses-18</name>
		|			<vat></vat>
		|			<idCategory>1009</idCategory>
		|			<preparationArea>Licenses</preparationArea>
		|		</group>
		|	</groups>
		|	<options>
		|	</options>
		|	<departments>""".stripMargin()

println """		<department>
		|			<id>3009</id>
		|			<number>180</number>
		|			<name>Licenses-18</name>
		|			<idGroup>2009</idGroup>
		|			<minNumber></minNumber>
		|			<maxNumber></maxNumber>
		|			<supplement></supplement>
		|			<condiment></condiment>
		|		</department>""".stripMargin()

mods.eachWithIndex { mod, ind ->
	println """		<department>
			|			<id>301${ind}</id>
			|			<number>18${ind+1}</number>
			|			<name>Licenses-18: ${mod.value}</name>
			|			<idGroup>2009</idGroup>
			|			<minNumber></minNumber>
			|			<maxNumber></maxNumber>
			|			<supplement></supplement>
			|			<condiment></condiment>
			|		</department>""".stripMargin()
}

println """	</departments>
		|	<articles>""".stripMargin()

println """		<article>
		|			<id>4901</id>
		|			<name>unTill(R) remote Connections license one year</name>
		|			<price></price>
		|			<barcode>REM1Y</barcode>
		|			<idDepartment>3009</idDepartment>
		|			<idFreeOption></idFreeOption>
		|		</article>""".stripMargin()

println """		<article>
		|			<id>4902</id>
		|			<name>unTill(R) Upgrade remote Connections license one year</name>
		|			<price></price>
		|			<barcode>UREM1Y</barcode>
		|			<idDepartment>3009</idDepartment>
		|			<idFreeOption></idFreeOption>
		|		</article>""".stripMargin()

int nextId = 5000;
mods.eachWithIndex { mod, ind ->
	for (int i = 1; i <= 25; ++i) {
		for (j = 0; j < 2; ++j) {
			println """		<article>
					|			<id>${nextId++}</id>
					|			<name>unTill(R)${(j==1?' Upgrade':'')} $mod.value module one year, $i terminal${i>1?'s':''}</name>
					|			<price></price>
					|			<barcode>${j==1?'U':''}${i}MOD${mod.key}1Y</barcode>
					|			<idDepartment>301${ind}</idDepartment>
					|			<idFreeOption></idFreeOption>
					|		</article>""".stripMargin()
		}
	}
}

println """	</articles>
		|	<option_structures>
		|	</option_structures>
		|	<art_options>
		|	</art_options>
		|	<users>
		|	</users>
		|</data>""".stripMargin()
