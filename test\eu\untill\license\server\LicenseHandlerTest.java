/**
 *
 */
package eu.untill.license.server;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assume.assumeFalse;
import static org.junit.Assume.assumeTrue;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.verifyZeroInteractions;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.StringReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

import org.h2.tools.RunScript;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import eu.untill.license.client.LicenseService.RequestLicenseResult;
import eu.untill.license.client.LicenseService.RequestLicenseResult.Results;
import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.client.PlainAuthScheme;
import eu.untill.license.server.EmailHelper.ReportType;
import eu.untill.license.server.EmailHelper.SendLicenseReason;
import eu.untill.license.server.LicenseHandler.ConfirmOrderResult;
import eu.untill.license.server.LicenseHandler.ConfirmOrderResult.Result;
import eu.untill.license.server.TestDb.TestDbParams;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.Restrictions;

/**
 * <AUTHOR>
 *
 */
@RunWith(MockitoJUnitRunner.class)
public class LicenseHandlerTest {

	@Mock
	private CommonHelper commonHelper;
	@Mock
	private EmailHelper emailHelper;
	@Mock
	private UntillLicensesPluginHelper untillLicensesPluginHelper;
	@Mock
	private UntillTpapiHelper untillTpapiHelper;
	@Mock
	private Settings settings;
	@InjectMocks
	private LicenseHandler licenseHandler;

	@Rule
	public final TestDb testDb;

	public LicenseHandlerTest() throws Exception {
		testDb = TestDb.getInstance();
	}

	@BeforeClass
	public static void setUpBeforeClass() throws Exception {
		TimeZone.setDefault(TimeZone.getTimeZone("Europe/Moscow"));
		//TimeZone.setDefault(TimeZone.getTimeZone("Etc/GMT+12"));
		//TimeZone.setDefault(TimeZone.getTimeZone("Etc/GMT-14"));
	}


	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#getClientNames(java.sql.Connection, eu.untill.license.server.Dealer)}.
	 * @throws SQLException
	 */
	@Test
	@TestDbParams(init = {"base", "common", "license"})
	public void testGetClientNames() throws Exception {
		final Connection conn = testDb.getConnection();

		// Dealer clients
		Dealer dealer = new Dealer(conn, new PlainAuthScheme("d", "dPass"));
		List<String> r = licenseHandler.getClientNames(conn, dealer, 0, false);
		assertArrayEquals(new String[]{"Test Client 1"}, r.toArray());

		// Super Dealer clients
		dealer = new Dealer(conn, new PlainAuthScheme("sd", "sdPass"));
		r = licenseHandler.getClientNames(conn, dealer, dealer.id, false);
		assertArrayEquals(new String[]{}, r.toArray());

		// check for no interaction with mocks
		verifyZeroInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#getClientNames(java.sql.Connection, eu.untill.license.server.Dealer)}.
	 * @throws SQLException
	 */
	@Test
	@TestDbParams(init = {"base", "common",})
	public void testGetClientNames_fromEmptyDb() throws Exception {
		final Connection conn = testDb.getConnection();

		Dealer dealer = new Dealer(conn, new PlainAuthScheme("d", "dPass"));
		List<String> r = licenseHandler.getClientNames(conn, dealer, 0, false);
		assertArrayEquals(new String[]{}, r.toArray());

		// check for no interaction with mocks
		verifyZeroInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getAllDealers(java.sql.Connection, eu.untill.license.server.Dealer)}.
//	 */
//	@Test
//	public void testGetAllDealers() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getApostillDealers(java.sql.Connection, eu.untill.license.server.Dealer)}.
//	 */
//	@Test
//	public void testGetApostillDealers() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getLicenseList(java.sql.Connection, eu.untill.license.server.Dealer, long, eu.untill.license.client.LicenseService.SortingType, java.lang.String, java.lang.String, boolean, boolean, boolean)}.
//	 */
//	@Test
//	public void testGetLicenseList() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getNewLicensePeriod(eu.untill.license.client.LicenseService.LicenseType)}.
//	 */
//	@Test
//	public void testGetNewLicensePeriod() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getProlongLicensePeriod(eu.untill.license.client.LicenseService.LicenseType, eu.untill.license.client.LicenseService.LicensePeriod)}.
//	 */
//	@Test
//	public void testGetProlongLicensePeriod() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getEmergencyLicensePeriod(java.util.Date)}.
//	 */
//	@Test
//	public void testGetEmergencyLicensePeriod() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#validLicenseHardCode(java.lang.String)}.
//	 */
//	@Test
//	public void testValidLicenseHardCode() {
//		fail("Not yet implemented");
//	}
//
	private final String confirmationUid = "4217888b-bdff-4d7d-86cb-6bd01a1afe93";
	private final String requestUrl = "http://test.domain";
	private final long licenseId = 5000000001L;
	private final long licenseId2 = 5000000002L;
	private final int licensRequestTableno = 4;
	private final String licenseRequestOrder = "Request order text";

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#requestLicense(java.sql.Connection, eu.untill.license.server.Dealer, eu.untill.license.shared.DbLicense, eu.untill.license.client.LicenseService.RequestType, java.lang.String)}.
	 * @throws Exception
	 */
	@Test
	@TestDbParams(init = {"base", "common"})
	public void testRequestLicense_badLicenses() throws Exception {
		final Connection conn = testDb.getConnection();
		final Dealer dealer = new Dealer(conn, new PlainAuthScheme("d", "dPass"));

		RequestLicenseResult reqResult;

		// stubbing
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());

		DbLicense license = getGoodLicense();
		license.setClientName(null);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_CLIENT_NAME_IS_EMPTY, reqResult.getResult());

		license = getGoodLicense();
		license.setHeConnections((short) -1);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_HE_CONNECTIONS_IS_NEGATIVE, reqResult.getResult());

		license.setPsConnections((short) -1);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_PS_CONNECTIONS_IS_NEGATIVE, reqResult.getResult());

		license.setOmConnections((short) -1);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_OM_CONNECTIONS_IS_NEGATIVE, reqResult.getResult());

		license.setRemConnections((short) -1);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_REM_CONNECTIONS_IS_NEGATIVE, reqResult.getResult());

		license.setLbConnections((short) -1);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_LB_CONNECTIONS_IS_NEGATIVE, reqResult.getResult());

		license.setLbConnections((short) 0);
		license.setRemConnections((short) 0);
		license.setOmConnections((short) 0);
		license.setPsConnections((short) 0);
		license.setHeConnections((short) 0);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_LB_CONNECTIONS_IS_ZERO, reqResult.getResult());

		license = getGoodLicense();
		license.setStatus((short) 0);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		license = getGoodLicense();
		license.hardCode = "1111111111111111111111111"; // invalid

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_HARD_CODE_IS_NO_VALID, reqResult.getResult());

		license = getGoodLicense();
		license.setStatus((short) 0xFFFF);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		license = getGoodLicense();
		license.setStartDate(new Date(license.getStartDate().getTime() - 1000));

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_PERIOD_IS_INCORRECT, reqResult.getResult());

		license = getGoodLicense();
		license.setExpiredDate(new Date(license.getExpiredDate().getTime() + 1000));

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_PERIOD_IS_INCORRECT, reqResult.getResult());

		license = getGoodLicense();
		license.setExpiredDate(new Date(license.getStartDate().getTime() - 1000));

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		license = getGoodLicense();
		license.setStartDate(null);
		license.setExpiredDate(null);

		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		// Check illegal APOSTILL requests
		license = getGoodLicense();
		license.setApostill(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.EMERGENCY);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		// Check illegal APOSTILL_TRIAL requests
		license.setTrial(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.PROLONG);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.UPGRADE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.EMERGENCY);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		license = getGoodLicense();

		// Check illegal DEMO requests
		license.setTrial(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.UPGRADE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.EMERGENCY);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		license = getGoodLicense();

		// Check illegal SaaS requests
		license.setSaas(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		license.setOnline(true);
		license.setApostill(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		license.setApostill(false);
		license.setTrial(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		license = getGoodLicense();

		// Check illegal Boost requests
		license.setBoost(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE_BOOST);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		license.setOnline(true);
		license.setApostill(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE_BOOST);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		license.setApostill(false);
		license.setTrial(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE_BOOST);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		license.setTrial(false);
		license.setSaas(true);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE_BOOST);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		license.setSaas(false);
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.PROLONG);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.UPGRADE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.EMERGENCY );
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		// verification
		verify(commonHelper, atLeastOnce()).getCurrentDate();
		verifyNoMoreInteractions(commonHelper);
		// check for no interaction with mocks
		verifyZeroInteractions(emailHelper, untillLicensesPluginHelper);
	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#requestLicense(java.sql.Connection, eu.untill.license.server.Dealer, eu.untill.license.shared.DbLicense, eu.untill.license.client.LicenseService.RequestType, java.lang.String)}.
	 * @throws Exception
	 */
	@Test
	@TestDbParams(init = {"base", "common"},
	              check = {"base", "common", "licenseRequest"},
	              nextId = licenseId)
	public void testRequestLicense() throws Exception {
		final Connection conn = testDb.getConnection();

		Dealer dealer = new Dealer(conn, new PlainAuthScheme("sd", "sdPass"));

		// stubbing
		when(commonHelper.getSettings()).thenReturn(settings);
		when(settings.getFrenchFdmForCountries()).thenReturn("");
		when(commonHelper.getServletUrl()).thenReturn(requestUrl);
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		doAnswer(getOrderLicenseAnswer(conn)).when(untillTpapiHelper).orderLicense(any(), any(), any(), any());
		when(commonHelper.generateGuid()).thenReturn(confirmationUid);
		when(emailHelper.emailRequest(any(Dealer.class), any(Dealer.class), any(DbLicense.class),
				anyString(), anyString())).thenReturn(true);

		DbLicense license = getGoodLicense();

		// using
		RequestLicenseResult reqResult;
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.SUCCESS, reqResult.getResult());

		// verification
		//ArgumentCaptor<Long> licenseIdCaptor = ArgumentCaptor.forClass(long.class);
		verify(untillTpapiHelper).orderLicense(eq(conn), eq(RequestType.CREATE), any(DbLicense.class), (DbLicense) eq(null));
		verify(commonHelper).generateGuid();
		ArgumentCaptor<DbLicense> licenseCaptor = ArgumentCaptor.forClass(DbLicense.class);
		verify(emailHelper).emailReportToOwner(eq(ReportType.DEALER_REQUESTED_LICENSE),
				licenseCaptor.capture(), eq(dealer), (Dealer) eq(null), (String) eq(null), (String) eq(null));
		assertEquals(license.toString(), licenseCaptor.getValue().toString());
		verify(commonHelper).getServletUrl();
		verify(emailHelper, times(1)).emailRequest((Dealer) eq(null), eq(dealer), licenseCaptor.capture(),
//				eq(licenseOrderText),
				//matches("http://test\\.domain\\?confirm_uid=[\\da-f-]{36}"),
				eq(requestUrl + "?confirm_uid=" + confirmationUid),
				//matches("http://test\\.domain\\?confirm_uid=[\\da-f-]{36}\\&cancel=1")
				eq(requestUrl + "?confirm_uid=" + confirmationUid + "&cancel=1")
		);
		assertEquals(license.toString(), licenseCaptor.getValue().toString());

		verify(commonHelper, atLeastOnce()).getCurrentDate();
		verify(commonHelper, atLeastOnce()).getSettings();
		verify(settings).getFrenchFdmForCountries();

		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);

	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#requestLicense(java.sql.Connection, eu.untill.license.server.Dealer, eu.untill.license.shared.DbLicense, eu.untill.license.client.LicenseService.RequestType, java.lang.String)}.
	 * @throws Exception
	 */
	@Test
	@TestDbParams(init = {"base", "common"},
	              check = {"base", "common", "licenseRequest-definitive"},
	              nextId = licenseId)
	public void testRequestLicense_definitive() throws Exception {
		assumeFalse(Restrictions.DISABLE_CREATE_DEFINITIVE);

		final Connection conn = testDb.getConnection();

		Dealer dealer = new Dealer(conn, new PlainAuthScheme("sd", "sdPass"));

		// stubbing
		when(commonHelper.getSettings()).thenReturn(settings);
		when(settings.getMaxDefinitiveMaxVersion()).thenReturn(115);
		when(commonHelper.getServletUrl()).thenReturn(requestUrl);
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		doAnswer(getOrderLicenseAnswer(conn)).when(untillTpapiHelper).orderLicense(any(), any(), any(), any());
		when(commonHelper.generateGuid()).thenReturn(confirmationUid);
		when(emailHelper.emailRequest(any(Dealer.class), any(Dealer.class), any(DbLicense.class),
				anyString(), anyString())).thenReturn(true);

		DbLicense license = getGoodLicense();
		license.setDefinitive(true);
		license.setDefinitiveMaxVersion(115);

		// using
		RequestLicenseResult reqResult;
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.CREATE);
		assertEquals(Results.SUCCESS, reqResult.getResult());

		// verification
		verify(commonHelper, atLeastOnce()).getSettings();
		verify(settings).getMaxDefinitiveMaxVersion();
		verify(settings).getFrenchFdmForCountries();
		verify(untillTpapiHelper).orderLicense(eq(conn), eq(RequestType.CREATE), any(DbLicense.class), (DbLicense) eq(null));
		verify(commonHelper).generateGuid();
		ArgumentCaptor<DbLicense> licenseCaptor = ArgumentCaptor.forClass(DbLicense.class);
		verify(emailHelper).emailReportToOwner(eq(ReportType.DEALER_REQUESTED_LICENSE),
				licenseCaptor.capture(), eq(dealer), (Dealer) eq(null), (String) eq(null), (String) eq(null));
		assertEquals(license.toString(), licenseCaptor.getValue().toString());
		verify(commonHelper).getServletUrl();
		verify(emailHelper, times(1)).emailRequest((Dealer) eq(null), eq(dealer), licenseCaptor.capture(),
//				eq(licenseOrderText),
				//matches("http://test\\.domain\\?confirm_uid=[\\da-f-]{36}"),
				eq(requestUrl + "?confirm_uid=" + confirmationUid),
				//matches("http://test\\.domain\\?confirm_uid=[\\da-f-]{36}\\&cancel=1")
				eq(requestUrl + "?confirm_uid=" + confirmationUid + "&cancel=1")

		);
		assertEquals(license.toString(), licenseCaptor.getValue().toString());

		verify(commonHelper, atLeastOnce()).getCurrentDate();

		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper, settings);
	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#requestLicense(java.sql.Connection, eu.untill.license.server.Dealer, eu.untill.license.shared.DbLicense, eu.untill.license.client.LicenseService.RequestType, java.lang.String)}.
	 * @throws Exception
	 */
	@Test
	@TestDbParams(init = {"base", "common", "licenseConfirmed"},
	              check = {"base", "common", "licenseConfirmed", "licenseRequest-definitive-upgrade"},
	              nextId = licenseId2)
	public void testRequestLicense_upgradeToDefinitive() throws Exception {
		assumeFalse(Restrictions.DISABLE_UPGRADE_TO_DEFINITIVE);

		final Connection conn = testDb.getConnection();
		Dealer dealer = new Dealer(conn, new PlainAuthScheme("sd", "sdPass"));

		// stubbing
		when(commonHelper.getSettings()).thenReturn(settings);
		when(settings.getMaxDefinitiveMaxVersion()).thenReturn(115);
		when(settings.getFrenchFdmForCountries()).thenReturn("");
		when(commonHelper.getServletUrl()).thenReturn(requestUrl);
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		doAnswer(getOrderLicenseAnswer(conn)).when(untillTpapiHelper).orderLicense(any(), any(), any(), any());
		when(commonHelper.generateGuid()).thenReturn(confirmationUid);
		when(emailHelper.emailRequest(any(Dealer.class), any(Dealer.class), any(DbLicense.class),
				anyString(), anyString())).thenReturn(true);

		DbLicense license = getGoodLicense();
		license.id = licenseId;
		license.setDefinitive(true);
		license.setDefinitiveMaxVersion(115);

		// using
		RequestLicenseResult reqResult;
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.UPGRADE);
		assertEquals(reqResult.getUnexpectedMessage(), Results.SUCCESS, reqResult.getResult());

		// verification
		verify(commonHelper, atLeastOnce()).getSettings();
		verify(settings).getMaxDefinitiveMaxVersion();
		verify(settings).getFrenchFdmForCountries();
		verify(untillTpapiHelper).orderLicense(eq(conn), eq(RequestType.UPGRADE), any(DbLicense.class), any(DbLicense.class));
		verify(commonHelper).generateGuid();
		ArgumentCaptor<DbLicense> licenseCaptor = ArgumentCaptor.forClass(DbLicense.class);
		verify(emailHelper).emailReportToOwner(eq(ReportType.DEALER_REQUESTED_LICENSE),
				licenseCaptor.capture(), eq(dealer), (Dealer) eq(null), (String) eq(null), (String) eq(null));
		assertEquals(license.toString(), licenseCaptor.getValue().toString());
		verify(commonHelper).getServletUrl();
		verify(emailHelper, times(1)).emailRequest((Dealer) eq(null), eq(dealer), licenseCaptor.capture(),
//				eq(licenseOrderText),
				//matches("http://test\\.domain\\?confirm_uid=[\\da-f-]{36}"),
				eq(requestUrl + "?confirm_uid=" + confirmationUid),
				//matches("http://test\\.domain\\?confirm_uid=[\\da-f-]{36}\\&cancel=1")
				eq(requestUrl + "?confirm_uid=" + confirmationUid + "&cancel=1")

		);
		assertEquals(license.toString(), licenseCaptor.getValue().toString());

		verify(commonHelper, atLeastOnce()).getCurrentDate();

		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper, settings);
	}

	DbLicense getLicenseById(Connection conn, long id) throws SQLException {
		try (PreparedStatement stmt = conn.prepareStatement("SELECT * FROM LICENSES WHERE ID = ?")) {
			stmt.setLong(1, id);
			try (ResultSet rs = stmt.executeQuery()) {
				assertTrue(rs.next());
				return Common.getDbLicenseFromResultSet(rs);
			}
		}
	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#requestLicense(java.sql.Connection, eu.untill.license.server.Dealer, eu.untill.license.shared.DbLicense, eu.untill.license.client.LicenseService.RequestType, java.lang.String)}.
	 * @throws Exception
	 */
	@Test
	@TestDbParams(init = {"base", "common", "license"})
	public void testRequestLicense_upgradeToDefinitiveByOrdinaryDealer() throws Exception {
		assumeTrue(Restrictions.ONLY_SUPER_DEALER_CAN_UPGRADE_TO_DEFINITIVE);

		final Connection conn = testDb.getConnection();
		Dealer dealer = new Dealer(conn, new PlainAuthScheme("d", "dPass"));

		// stubbing
		when(commonHelper.getSettings()).thenReturn(settings);
		when(settings.getMaxDefinitiveMaxVersion()).thenReturn(115);
		when(settings.getFrenchFdmForCountries()).thenReturn("");
		when(commonHelper.getServletUrl()).thenReturn(requestUrl);
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		doAnswer(getOrderLicenseAnswer(conn)).when(untillTpapiHelper).orderLicense(any(), any(), any(), any());
		when(commonHelper.generateGuid()).thenReturn(confirmationUid);
		when(emailHelper.emailRequest(any(Dealer.class), any(Dealer.class), any(DbLicense.class),
				anyString(), anyString())).thenReturn(true);

		DbLicense license = getLicenseById(conn, 2);
		license.setClientDisplayName(license.getClientName());
		license.setDefinitive(true);
		license.setDefinitiveMaxVersion(115);

		// using
		RequestLicenseResult reqResult;
		reqResult = licenseHandler.requestLicense(conn, dealer, license, RequestType.UPGRADE);
		assertEquals(Results.FAIL_ILLEGAL_REQUEST, reqResult.getResult());

		// verification
		verify(commonHelper, atLeastOnce()).getSettings();
		verify(commonHelper, atLeastOnce()).getCurrentDate();
		verify(settings).getMaxDefinitiveMaxVersion();
		verify(settings).getFrenchFdmForCountries();
		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper, settings);
	}

	private Answer<?> getOrderLicenseAnswer(final Connection conn) {
		return new Answer<Boolean>() {
			@Override
			public Boolean answer(InvocationOnMock invocation) throws Throwable {
				conn.setAutoCommit(false);
				RunScript.execute(conn, new StringReader(
						"UPDATE LICENSES\r\n" +
						"SET REQUEST_PHASE = '2ORD',\r\n" +
						"    REQUEST_ACTIVE = 1,\r\n" +
						"    REQUEST_TABLENO = " + licensRequestTableno + ",\r\n" +
						"    REQUEST_ORDER = '" + licenseRequestOrder + "',\r\n" +
						"    ID_BILL = 1234\r\n" +
						"WHERE ID = " + ((DbLicense) invocation.getArguments()[2]).id + "\r\n"));
				conn.commit();
				return true;
			}
		};
	}

	private DbLicense getGoodLicense() {
		Date startDate = Common.toUtcDateOnly(testDb.getCurrentDate());
		Calendar expiredDate = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		expiredDate.setTime(startDate);
		expiredDate.add(Calendar.YEAR, 1);
		expiredDate.add(Calendar.DATE, -1);

		DbLicense goodLicense = new DbLicense();
		goodLicense.id = 0; // previous license id or ignored
		goodLicense.setClientName("Good License Client");
		goodLicense.setClientDisplayName("Good License Client Dipaly Name");
		goodLicense.setClientAddress("Client Address");
		goodLicense.hardCode = "G00DCLIENTLICENSEHARDC0D7";
		goodLicense.setLbConnections((short) 3);
		goodLicense.setRemConnections((short) 3);
		goodLicense.setOmConnections((short) 3);
//		goodLicense.setHqConnections((short) 0);
		goodLicense.setPsConnections((short) 3);
		goodLicense.setHeConnections((short) 0);
		goodLicense.setStartDate(startDate);
		goodLicense.setIssueDate(null); // ignored
		goodLicense.setExpiredDate(expiredDate.getTime());
		goodLicense.setFuncLimited(0L);
		goodLicense.setStatus((short) 1);
		goodLicense.setClientEmail("<EMAIL>");
		goodLicense.setClientPhone("-clientphone-");
		goodLicense.dealerComments = "Dealer Comments";
		goodLicense.idDealers = 2;
		return goodLicense;
	}

//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#addApostillHddCodes(java.sql.Connection, eu.untill.license.server.Dealer, long, java.lang.String)}.
//	 */
//	@Test
//	public void testAddApostillHddCodes() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#voidRequest(java.sql.Connection, eu.untill.license.server.Dealer, long)}.
//	 */
//	@Test
//	public void testVoidRequest() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#resendRequest(java.sql.Connection, eu.untill.license.server.Dealer, long, java.lang.String)}.
//	 */
//	@Test
//	public void testResendRequest() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#resendLicense(java.sql.Connection, eu.untill.license.server.Dealer, long, java.util.Date)}.
//	 */
//	@Test
//	public void testResendLicense() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#serviceApostillRequest(java.sql.Connection, java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String, javax.servlet.http.HttpServletResponse)}.
//	 */
//	@Test
//	public void testServiceApostillRequest() {
//		fail("Not yet implemented");
//	}
//
	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#confirmOrder(java.sql.Connection, java.lang.String, boolean)}.
	 */
	@Test
	@TestDbParams(init = {"base", "common", "licenseCancelled"})
	public void testConfirmOrder_wrongRequest() throws Exception {
//		final Connection conn = testDb.getConnection().getConnection();
//		ConfirmOrderResult result = licenseHandler.confirmOrder(conn, confirmationUid, true, "",
//				requestUrl);
//		assertEquals(Result.FAIL, result.result);
	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#confirmOrder(java.sql.Connection, java.lang.String, boolean)}.
	 */
	@Test
	@TestDbParams(init = {"base", "common", "licenseRequest"},
	              check = {"base", "common", "licenseCancelled"})
	public void testConfirmOrder_cancel() throws Exception {
		final Connection conn = testDb.getConnection();

		// stubbing
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		when(untillLicensesPluginHelper.voidOrder(anyInt())).thenReturn(true);

		// using
		ConfirmOrderResult result = licenseHandler.confirmOrder(conn, confirmationUid, true,
				"Cancel license comment", false);
		assertEquals(Result.SUCCESS, result.result);

		// verification
		verify(commonHelper).getCurrentDate();
		verify(untillLicensesPluginHelper).voidOrder(4);
		verify(emailHelper).emailReportToOwner(eq(ReportType.DEALER_CANCELED_REQUEST),
				(DbLicense) any(), (Dealer) any(), (Dealer) eq(null), (String) eq(null),
				eq("Cancel license comment"));
		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

	/**
	 * Test method for {@link eu.untill.license.server.LicenseHandler#confirmOrder(java.sql.Connection, java.lang.String, boolean)}.
	 */
	@Test
	@TestDbParams(init = {"base", "common", "licenseRequest"},
	              check = {"base", "common", "licenseConfirmed"})
	public void testConfirmOrder() throws Exception {
		final Connection conn = testDb.getConnection();
		final File oldLicenseFile = new File("");
		final File licenseFile = new File("");
		// stubbing
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		when(untillLicensesPluginHelper.generateLicenseFile(licenseId, 3, false)).thenReturn(oldLicenseFile);
		when(untillLicensesPluginHelper.generateLicenseFile(licenseId, 4, false)).thenReturn(licenseFile);

		// using
		ConfirmOrderResult result = licenseHandler.confirmOrder(conn, confirmationUid, false, null, false);
		assertEquals(Result.SUCCESS, result.result);

		// verification
		verify(commonHelper, atLeastOnce()).getCurrentDate();
		verify(untillLicensesPluginHelper).generateLicenseFile(licenseId, 3, false);
		verify(untillLicensesPluginHelper).generateLicenseFile(licenseId, 4, false);
		verify(emailHelper).emailReportToOwner(eq(ReportType.DEALER_CONFIRMED_REQUEST),
				(DbLicense) any(), (Dealer) any(), (Dealer) eq(null), eq("Request order text"),
				(String) eq(null));
		verify(emailHelper).emailLicense(eq(SendLicenseReason.CONFIRM), eq(oldLicenseFile), eq(licenseFile),
				eq(false), (DbLicense) any(), (Dealer) any());
		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

	@Test //currentDate = "2018-11-15T01:30:00.00Z"
	public void testAutoProlong_beforeAutoProlongFromDay() throws Exception {
		final Connection conn = testDb.getConnection();

		// stubbing
		when(commonHelper.getCurrentDate()).thenReturn(Date.from(Instant.parse("2018-11-01T23:30:00.00Z")));
		when(commonHelper.getSettings()).thenReturn(settings);
		when(settings.getAutoProlongFromDay()).thenReturn(Settings.DEFAULT_AUTO_PROLONG_FROM_DAY);

		// using
		licenseHandler.autoProlong(conn);

		// verification
		verify(commonHelper).getCurrentDate();
		verify(commonHelper, atLeastOnce()).getSettings();
		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

	@Test
	@TestDbParams(init = {"base", "common", "saas/approved"},
	              check = {"base", "common", "saas/autoprolonged"},
	              nextId = licenseId2,
	              currentDate = "2018-10-16T01:30:00.00Z")
	public void testAutoProlong() throws Exception {
		final Connection conn = testDb.getConnection();

		// stubbing
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		when(commonHelper.getSettings()).thenReturn(settings);
		doAnswer(getOrderLicenseAnswer(conn)).when(untillTpapiHelper).orderLicense(any(), any(), any(), any());
		when(settings.getAutoProlongFromDay()).thenReturn(Settings.DEFAULT_AUTO_PROLONG_FROM_DAY);
		when(commonHelper.getServletUrl()).thenReturn(requestUrl);
		when(commonHelper.generateGuid()).thenReturn(confirmationUid);

		// using
		licenseHandler.autoProlong(conn);

		// verification
		verify(commonHelper, atLeastOnce()).getCurrentDate();
		verify(commonHelper, atLeastOnce()).getSettings();
		verify(commonHelper).generateGuid();
		verify(untillTpapiHelper).orderLicense(eq(conn), eq(RequestType.PROLONG), any(DbLicense.class), any(DbLicense.class));
		verify(untillTpapiHelper).closeOrder(eq(conn), eq(licensRequestTableno));
		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

	@Test
	@TestDbParams(init = {"base", "common", "saas/approved_badExpiry"},
	              check = {"base", "common", "saas/approved_badExpiry_autoprolonged"},
	              currentDate = "2018-10-31T01:30:00.00Z")
	public void testAutoProlong_badExpiryDate() throws Exception {
		final Connection conn = testDb.getConnection();

		// stubbing
		when(commonHelper.getCurrentDate()).thenReturn(testDb.getCurrentDate());
		when(commonHelper.getSettings()).thenReturn(settings);
		doAnswer(getOrderLicenseAnswer(conn)).when(untillTpapiHelper).orderLicense(any(), any(), any(), any());
		when(settings.getAutoProlongFromDay()).thenReturn(Settings.DEFAULT_AUTO_PROLONG_FROM_DAY);

		// using
		licenseHandler.autoProlong(conn);

		// verification
		verify(commonHelper, atLeastOnce()).getCurrentDate();
		verify(commonHelper, atLeastOnce()).getSettings();
		verifyNoMoreInteractions(commonHelper, emailHelper, untillLicensesPluginHelper);
	}

//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getLicenseOrder(java.sql.Connection, eu.untill.license.server.Dealer, long)}.
//	 */
//	@Test
//	public void testGetLicenseOrder() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#addDiscount(java.sql.Connection, eu.untill.license.server.Dealer, long, double, java.lang.String)}.
//	 */
//	@Test
//	public void testAddDiscount() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#addNegativeArticle(java.sql.Connection, eu.untill.license.server.Dealer, long, long, int, java.lang.String)}.
//	 */
//	@Test
//	public void testAddNegativeArticle() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#approveLicense(java.sql.Connection, eu.untill.license.server.Dealer, long, boolean)}.
//	 */
//	@Test
//	public void testApproveLicense() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#orderManualLicense(java.sql.Connection, eu.untill.license.server.Dealer, long)}.
//	 */
//	@Test
//	public void testOrderManualLicense() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#recreateInvoice(java.sql.Connection, eu.untill.license.server.Dealer, long)}.
//	 */
//	@Test
//	public void testRecreateInvoice() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#reorderLicense(java.sql.Connection, eu.untill.license.server.Dealer, long)}.
//	 */
//	@Test
//	public void testReorderLicense() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#disapproveLicense(java.sql.Connection, eu.untill.license.server.Dealer, long)}.
//	 */
//	@Test
//	public void testDisapproveLicense() {
//		fail("Not yet implemented");
//	}
//
//	/**
//	 * Test method for {@link eu.untill.license.server.LicenseHandler#getDefinitiveDefaultMajorVersion(java.lang.String)}.
//	 */
//	@Test
//	public void testGetDefinitiveDefaultMajorVersion() {
//		fail("Not yet implemented");
//	}
//


}
