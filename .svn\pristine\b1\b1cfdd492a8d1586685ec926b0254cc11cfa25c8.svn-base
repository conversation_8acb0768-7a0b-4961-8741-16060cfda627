/**
 *
 */
package eu.untill.license.client.ui;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.i18n.client.TimeZone;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.DecoratorPanel;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.Cell;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.client.LicenseService.RrDealer;
import eu.untill.license.client.LicenseService.SortingType;
import eu.untill.license.client.Common;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.UntillLicenseServerImages;
import eu.untill.license.client.ui.MessageDialog.Buttons;
import eu.untill.license.client.ui.MessageDialog.MessageDialogListener;
import eu.untill.license.client.ui.MessageDialog.SelectedButton;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.client.ui.PageNavigator.PageChangeListener;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.Restrictions;

/**
 * <AUTHOR>
 *
 */
public class LicenseListWidget extends Composite
		implements AsyncCallback<List<DbLicense>>, ClickHandler, PageChangeListener
{

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String dealerLabel();

		String createNewLicenseButton();
		String refreshButton();
		String addApostillHddCodesButton();
		String clientNameFilterLabel();
		String hardCodeFilterLabel();
		String showOnlyTrialFilterLabel();
		String showCanceledFilterLabel();
		String showOnlyNonApprovedFilterLabel();
		String resendThisRequestButton();
		@DefaultStringValue("Re-send Large Account approval request")
		String resendLargeAccountApprovalRequestButton();
		String resendThisLicenseButton();
		String prolongThisLicenseButton();
		String upgradeThisLicenseButton();
		String boostThisLicenseButton();
		String getEmergencyLicenseButton();
		String reinstallLicenseButton();
		String approveLicenseButton();
		String createManualInvoiceButton();
		String showClientNameChangesButton();
		@DefaultStringValue("Hosted WM report")
		String showHwmReportButton();
		@DefaultStringValue("Actual prices")
		String actualPricesButton();

		String statusColumnHeader();
		String clientNameColumnHeader();
		String clientDisplayNameColumnHeader();
		String chainColumnHeader();
		String hardCodeColumnHeader();
		String localDatabasesColumnHeader();
		String localDatabasesColumnHint();
		String remoteConnectionsColumnHeader();
		String remoteConnectionsColumnHint();
		String handTerminalsColumnHeader();
		String handTerminalsColumnHint();
		//String headQuartersColumnHeader();
		//String headQuartersColumnHeader();
		String productionScreensColumnHeader();
		String productionScreensColumnHint();
		String hhtEftColumnHeader();
		String hhtEftColumnHint();
		String startDateColumnHeader();
		String issueDateColumnHeader();
		String expiredDateColumnHeader();
		String tempExpiredDateColumnHeader();
		String resendThisLicenseColumnHeader();
		String prolongThisLicenseColumnHeader();
		String upgradeThisLicenseColumnHeader();
		String boostThisLicenseColumnHeader();
		String getEmergencyLicenseColumnHeader();
		String approveOrderColumnHeader();

		String licensesNotFound();
		String licenseCanceled();
		String licenseUnconfirmed();
		String licenseExpired();
		String licenseWillSoonExpire();
		String licenseApostill();
		String licenseUntill();
		String licenseTrial();
		String licenseDefinitive();
		String licenseUnlimited();
		String licenseNeverExpired();
		String licenseOnline();
		String licenseModuled();
		@DefaultStringValue("SaaS")
		String licenseSaas();
		@DefaultStringValue("Boost")
		String licenseBoost();
		@DefaultStringValue("Non-approved")
		String licenseNonApporved();
		

		String allDealersListItem();

		String orderManualLicenseButton();
		String orderManualLicenseButtonTitle();

		@DefaultStringValue("Waiting for Large Account approval")
		String waitingForLargeAccountApproval();
	}

	public static interface UlsMessages extends Messages {
		String cancelAllRequestsQuestion();
		String cancelAllRequestsErrorMessage();
		String requestSuccessfullySentMessage();
		String errorSendingRequestMessage();
		String licenseSuccessfullySentMessage();
		String errorSendingLicenseMessage();
		String filterPopupMessage();
		String emailIsNotSpecifiedMessage();
		String orderManuallyEnteredLicenseQuestion();
		String orderManuallyEnteredLicenseCreatedSuccessfully();
		String reintallLicenseQuestion(int attemptsCount);
		String licenseReintalledSuccessfully();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();
	private UntillLicenseServerImages images = UntillLicenseServer.getUntillLicenseServerImages();

	  //////////////////
	 // Members (UI) //
	//////////////////

	private DecoratorPanel decoratorPanel = new DecoratorPanel();
	private VerticalPanel panel = new VerticalPanel();

	private HorizontalPanel toolBar = new HorizontalPanel();
	private Button btnCreateNewLicense = new Button(new Image(images.blank()).toString()
			+ new Label(constants.createNewLicenseButton()).toString(), this);
	private Button btnRefresh = new Button(new Image(images.refresh()).toString()
			+ new Label(constants.refreshButton()).toString(), this);

	private FlexTable grdFilter1 = new FlexTable();
	private Label lblClientNameFilter = new Label(constants.clientNameFilterLabel() + ": ", false);
	private SuggestBox txtClientNameFilter = new SuggestBox();
	private Label lblHardCodeFilter = new Label(constants.hardCodeFilterLabel() + ": ", false);
	private TextBox txtHardCodeFilter = new TextBox();

	private FlexTable grdFilter2 = new FlexTable();
	private CheckBox chkShowOnlyTrialFilter = new CheckBox(constants.showOnlyTrialFilterLabel());
	private CheckBox chkShowCanceledFilter = new CheckBox(constants.showCanceledFilterLabel());

	private Button btnAddApostillHddCodes = new Button(new Image(images.add_apostill_hdd_codes()).toString()
			+ new Label(constants.addApostillHddCodesButton()).toString(), this);

	private Button btnCreateManualInvoice = new Button(new Image(images.create_manual_invoice()).toString()
			+ new Label(constants.createManualInvoiceButton()).toString(), this);

	private Button btnShowClientNameChanges = new Button(new Image(images.show_client_name_changes()).toString()
			+ new Label(constants.showClientNameChangesButton()).toString(), this);

	private Button btnHwmInvoiceReport = new Button(new Image(images.hwm_report()).toString()
			+ new Label(constants.showHwmReportButton()).toString(), this);

	private Button btnActualPrices = new Button(new Image(images.actual_prices()).toString()
			+ new Label(constants.actualPricesButton()).toString(), this);

	private FlexTable grdFilter3 = new FlexTable();
	private Label lblDealer = new Label(constants.dealerLabel() + ": ", false);
	private ListBox lstDealers = new ListBox();
	private CheckBox chkShowOnlyNonApprovedFilter = new CheckBox(constants.showOnlyNonApprovedFilterLabel());

	List<DbLicense> licenseList = null;
	private boolean gotoFirstPage = false;

	private PageNavigator pageNavigator = new PageNavigator(20, this);

	private SortingListButtonGroup<SortingType> tableSortingButtonGroup = new SortingListButtonGroup<>(() -> refresh());

	private FlexTable table = new FlexTable();

	private Date lastUpdateClientNameSuggestOracle = null;
	private Date lastUpdateDealerList = null;

	  //////////////////
	 // Constructors //
	//////////////////

	public LicenseListWidget() {

		setDealerList(null);

		lstDealers.addChangeHandler(new ChangeHandler() {
			@Override
			public void onChange(ChangeEvent event) {
				refresh();
			}
		});

		btnCreateNewLicense.addStyleName("ToolBar-Button");
		btnRefresh.addStyleName("ToolBar-Button");
		btnAddApostillHddCodes.addStyleName("ToolBar-Button");
		btnCreateManualInvoice.addStyleName("ToolBar-Button");
		btnShowClientNameChanges.addStyleName("ToolBar-Button");
		btnHwmInvoiceReport.addStyleName("ToolBar-Button");
		btnActualPrices.addStyleName("ToolBar-Button");

		//lblClientNameFilter.setTitle(messages.filterPopupMessage());
		txtClientNameFilter.setTitle(messages.filterPopupMessage());
		txtClientNameFilter.setWidth("20ex");
		//lblHardCodeFilter.setTitle(messages.filterPopupMessage());
		txtHardCodeFilter.setTitle(messages.filterPopupMessage());
		txtHardCodeFilter.setWidth("20ex");

		grdFilter1.addStyleName("ToolBar-Object");
//		grdFilter1.setHeight("100%");
		grdFilter1.setWidget(0, 0, lblClientNameFilter);
		grdFilter1.setWidget(0, 1, txtClientNameFilter);
		grdFilter1.setWidget(1, 0, lblHardCodeFilter);
		grdFilter1.setWidget(1, 1, txtHardCodeFilter);

		grdFilter2.addStyleName("ToolBar-Object");
//		grdFilter2.setHeight("100%");
		chkShowOnlyTrialFilter.addStyleName("NoWordWrap");
		grdFilter2.setWidget(0, 0, chkShowOnlyTrialFilter);
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			chkShowCanceledFilter.addStyleName("NoWordWrap");
			grdFilter2.setWidget(1, 0, chkShowCanceledFilter);
//			chkShowOnlyNonApprovedFilter.addStyleName("NoWordWrap");
//			grdFilter2.setWidget(2, 0, chkShowOnlyNonApprovedFilter);
		}

		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			grdFilter3.addStyleName("ToolBar-Object");
//			grdFilter3.setHeight("100%");
			grdFilter3.setWidget(0, 0, lblDealer);
			grdFilter3.setWidget(0, 1, lstDealers);
			grdFilter3.getFlexCellFormatter().setColSpan(1, 0, 2);
			chkShowOnlyNonApprovedFilter.addStyleName("NoWordWrap");
			grdFilter3.setWidget(1, 0, chkShowOnlyNonApprovedFilter);
		}

		toolBar.setWidth("100%");
//		toolBar.setHeight("100%");
		toolBar.setVerticalAlignment(HasVerticalAlignment.ALIGN_MIDDLE);
		toolBar.setSpacing(2);
		if (!UntillLicenseServer.getLogonDealer().onlyProlong ||
				UntillLicenseServer.getLogonDealer().isSuperDealer) 
			toolBar.add(btnCreateNewLicense);
		toolBar.add(btnRefresh);
		toolBar.add(grdFilter1);
		toolBar.add(grdFilter2);
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			toolBar.add(btnAddApostillHddCodes);
			toolBar.add(grdFilter3);
			toolBar.add(btnCreateManualInvoice);
			toolBar.add(btnShowClientNameChanges);
			toolBar.add(btnHwmInvoiceReport);
		}
		toolBar.add(btnActualPrices);

		toolBar.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		Label splitter = new Label(" ");
		toolBar.add(splitter);
		toolBar.setCellWidth(splitter, "100%");
		toolBar.setVerticalAlignment(HasVerticalAlignment.ALIGN_BOTTOM);
		toolBar.add(pageNavigator);

		KeyUpHandler kuHandler = new KeyUpHandler() {
			@Override
			public void onKeyUp(KeyUpEvent event) {
				if (event.getNativeKeyCode() == KeyCodes.KEY_ENTER) {
					// TODO May be set filter instead of refresh...
					LicenseListWidget.this.refresh();
				}
			}
		};
		txtClientNameFilter.addKeyUpHandler(kuHandler);
		txtHardCodeFilter.addKeyUpHandler(kuHandler);

		ValueChangeHandler<Boolean> vcHandler = new ValueChangeHandler<Boolean>() {
			@Override
			public void onValueChange(ValueChangeEvent<Boolean> event) {
				refresh();
			}
		};
		chkShowOnlyTrialFilter.addValueChangeHandler(vcHandler);
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			chkShowCanceledFilter.addValueChangeHandler(vcHandler);
			chkShowOnlyNonApprovedFilter.addValueChangeHandler(vcHandler);
		}

		prepareLicenseTable();

		panel.setWidth("100%");
		//panel.add(dpnDealer);
		panel.add(toolBar);
		panel.add(table);

		decoratorPanel.setWidget(panel);

		this.initWidget(decoratorPanel);
		//this.setStyleName("LicenseList");
	}

	  ////////////////////
	 // Public methods //
	////////////////////

	public void refresh() {
		refresh(false);
	}

	public void refresh(boolean holdCurrentPage) {
		gotoFirstPage = !holdCurrentPage;
//		// Clear table
//		for (int r = table.getRowCount() - 1; r > 0; --r)
//			table.removeRow(r);

		// Update client name suggest oracle (not more than once every 5 minutes)
		if (lastUpdateClientNameSuggestOracle == null ||
				new Date().getTime() - lastUpdateClientNameSuggestOracle.getTime() > 5 * (1000*60)) {
			lastUpdateClientNameSuggestOracle = new Date();

			// TODO adjust this feature and remove comment block
//			// Call RPC
//			UntillLicenseServer.getLicenseServiceAsync().getClientNames(
//					UntillLicenseServer.authScheme, new AsyncCallback<List<String>>() {
//						@Override
//						public void onFailure(Throwable caught) {
//							// Do nothing
//						}
//						@Override
//						public void onSuccess(List<String> result) {
//							MultiWordSuggestOracle mwso = (MultiWordSuggestOracle) LicenseListWidget.this.txtClientNameFilter.getSuggestOracle();
//							mwso.clear();
//							mwso.addAll(result);
//						}
//					});
		}

		// Update dealer list (no more frequently than once every 5 minutes)
		if (UntillLicenseServer.getLogonDealer().isSuperDealer &&
				(lastUpdateDealerList == null ||
				new Date().getTime() - lastUpdateDealerList.getTime() > 5 * (1000*60))) {
			lastUpdateDealerList = new Date();

			// Call RPC
			UntillLicenseServer.getLicenseServiceAsync().getAllDealers(UntillLicenseServer.getAuthScheme(),
					new AsyncCallback<List<RrDealer>>() {
						@Override
						public void onFailure(Throwable caught) {
							ErrorDialog.show("Error get all dealer list", caught);
						}
						@Override
						public void onSuccess(List<RrDealer> result) {
							setDealerList(result);
						}
					});
		}

		// Call RPC LicenseService.getLicenseList
		long dealerIdFilter = 0;
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			if (lstDealers.getSelectedIndex() < 0)
				dealerIdFilter = UntillLicenseServer.getLogonDealer().id;
			else if (lstDealers.getSelectedIndex() == 0)
				dealerIdFilter = 0;
			else
				dealerIdFilter = getSelectedDealer().id;
		}
		String clientNameFilter = txtClientNameFilter.getText().trim();
		if (clientNameFilter.isEmpty()) clientNameFilter = null;
		String hardCodeFilter = txtHardCodeFilter.getText().trim();
		boolean showOnlyTrial = chkShowOnlyTrialFilter.getValue().booleanValue();
		boolean showCanceled = false;
		boolean showOnlyNonApproved = false;
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			showCanceled = chkShowCanceledFilter.getValue().booleanValue();
			showOnlyNonApproved = chkShowOnlyNonApprovedFilter.getValue().booleanValue();
		}
		if (hardCodeFilter.isEmpty()) hardCodeFilter = null;
		UntillLicenseServer.getLicenseServiceAsync().getLicenseList(
				UntillLicenseServer.getAuthScheme(), dealerIdFilter, tableSortingButtonGroup.getSortingType(),
				clientNameFilter, hardCodeFilter, showOnlyTrial, showCanceled,
				showOnlyNonApproved, this);

		WaitDialog.show();
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	// RPC LicenseService.getLicenseList call back implementation

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		ErrorDialog.show(caught);
	}

	@Override
	public void onSuccess(List<DbLicense> licenseList) {
		try {
			this.licenseList = licenseList;
			if (gotoFirstPage) pageNavigator.setCurrentPage(0);
			pageNavigator.setItemCount(licenseList.size());
			gotoFirstPage = false;
			refreshLicenseTable();
		} finally {
			WaitDialog.hide();
		}
	}

	// ClickListener implementation
	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnRefresh) {

			this.refresh(true);

		} else if (sender == btnCreateNewLicense) {

			// Create new license

			if (!checkDealerEmail()) return;

			RrDealer licenseDealer = UntillLicenseServer.getLogonDealer();
			if (licenseDealer.isSuperDealer && lstDealers.getSelectedIndex() > 0)
				licenseDealer = getSelectedDealer();

			LicenseDialog ld = new LicenseDialog(licenseDealer);
			ld.show();
			lastUpdateClientNameSuggestOracle = null;

		} else if (sender == btnAddApostillHddCodes) {

			if (UntillLicenseServer.getLogonDealer().isSuperDealer)
				new ApostillHddCodesDialog().show();

		} else if (sender == btnCreateManualInvoice) {

			if (UntillLicenseServer.getLogonDealer().isSuperDealer
					&& lstDealers.getSelectedIndex() > 0) {
				long currentDealerId = getSelectedDealer().id;
				new CreateManualInvoiceDialog(currentDealerId).show();
			}

		} else if (sender == btnShowClientNameChanges) {

			if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
				RrDealer selectedDealer = getSelectedDealer();
				new ClientNameChangesDialog(selectedDealer == null ? 0 : selectedDealer.id).show();
			}

		} else if (sender == btnHwmInvoiceReport) {

			new HwmReportDialog().show();

		} else if (sender == btnActualPrices) {

			long dealerId;
			if (UntillLicenseServer.getLogonDealer().isSuperDealer && lstDealers.getSelectedIndex() > 0)
				dealerId = getSelectedDealer().id;
			else
				dealerId = UntillLicenseServer.getLogonDealer().id;

			boolean passwordRequired = !UntillLicenseServer.getLogonDealer().isSuperDealer
					&& UntillLicenseServer.getLogonDealer().pricesPassIsNotEmpty;
			
			new ActualPricesDialog(dealerId, passwordRequired).show();

		} else if (sender == table) {

			Cell cell = table.getCellForEvent(event);
			if (cell != null
					&& cell.getRowIndex() > 0
					&& table.getFlexCellFormatter().getStyleName(cell.getRowIndex(),
							cell.getCellIndex()).equals("ll-CommonCell")) {
				DbLicense license = ((StatusWidget) table.getWidget(cell.getRowIndex(), 0)).getLicense();
				new LicenseDetailsDialog(license).show();

			}
		}
	}

	@Override
	public void onChangePage() {
		refreshLicenseTable();
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private RrDealer[] arrDealers;

	private RrDealer getSelectedDealer() {
		if (lstDealers.getSelectedIndex() <= 0)
			return null;
		return arrDealers[Integer.parseInt(lstDealers.getValue(lstDealers.getSelectedIndex()))];
	}

	private String getDealerNameById(long dealerId) {
		String result = "#" + Long.toString(dealerId);
		if (arrDealers != null) {
			for (RrDealer dealer: arrDealers) {
				if (dealer.id == dealerId) {
					result = dealer.name;
					break;
				}
			}
		}
		return result;
	}

	private void setDealerList(List<RrDealer> dealers) {
		if (dealers == null) {
			lstDealers.clear();
			dealers = null;
		} else {
			long selectedDealerId;
			if (lstDealers.getSelectedIndex() < 0) {
				selectedDealerId = UntillLicenseServer.getLogonDealer().id;
			} else {
				if (lstDealers.getSelectedIndex() == 0) selectedDealerId = 0;
				else selectedDealerId = getSelectedDealer().id;
			}
			lstDealers.clear();
			lstDealers.addItem(constants.allDealersListItem());
			if (selectedDealerId == 0) lstDealers.setSelectedIndex(0);
			arrDealers = new RrDealer[dealers.size()];
			int i = 0;
			boolean found = false;
			for (RrDealer dealer : dealers) {
				lstDealers.addItem(dealer.name, Integer.toString(i));
				arrDealers[i++] = dealer;
				if (selectedDealerId != 0 && selectedDealerId == dealer.id) {
					lstDealers.setSelectedIndex(lstDealers.getItemCount() - 1);
					found = true;
				}
			}
			lstDealers.setVisible(true);
			if (!found)
				refresh();
		}
	}

	private void prepareLicenseTable() {
		table.addClickHandler(this);
		table.addStyleName("ll-Table");

		// Prepare table header
		table.getRowFormatter().addStyleName(0, "ll-Header");
		FlexCellFormatter tableCF = table.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "24px");
		table.setText(0, col++, constants.statusColumnHeader());
		tableCF.setWidth(0, col, "140px");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.clientNameColumnHeader(),
				SortingType.BY_CLIENT_NAME, SortingType.BY_CLIENT_NAME_DESC, false, true));
		tableCF.setWidth(0, col, "140px");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.clientDisplayNameColumnHeader(),
				SortingType.BY_CLIENT_DISPLAY_NAME, SortingType.BY_CLIENT_DISPLAY_DESC));
		tableCF.setWidth(0, col, "80px");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.chainColumnHeader(),
				SortingType.BY_CHAIN, SortingType.BY_CHAIN_DESC));
		tableCF.setWidth(0, col, "160px");
		table.setText(0, col++, constants.hardCodeColumnHeader());
		tableCF.setWidth(0, col, "20");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.localDatabasesColumnHeader(), constants.localDatabasesColumnHint(),
				SortingType.BY_LB_CONNECTIONS, SortingType.BY_LB_CONNECTIONS_DESC));
		tableCF.setWidth(0, col, "20");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.remoteConnectionsColumnHeader(), constants.remoteConnectionsColumnHint(),
				SortingType.BY_REM_CONNECTIONS, SortingType.BY_REM_CONNECTIONS_DESC));
		tableCF.setWidth(0, col, "20");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.handTerminalsColumnHeader(), constants.handTerminalsColumnHint(),
				SortingType.BY_OM_CONNECTIONS, SortingType.BY_OM_CONNECTIONS_DESC));
//		tableCF.setWidth(0, col, "20");
//		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
//				constants.headQuartersColumnHeader(), constants.headQuartersColumnHint(),
//				SortingType.BY_HQ_CONNECTIONS, SortingType.BY_HQ_CONNECTIONS_DESC));
		tableCF.setWidth(0, col, "20");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.productionScreensColumnHeader(), constants.productionScreensColumnHint(),
				SortingType.BY_PS_CONNECTIONS, SortingType.BY_PS_CONNECTIONS_DESC));
		tableCF.setWidth(0, col, "20");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.hhtEftColumnHeader(), constants.hhtEftColumnHint(),
				SortingType.BY_HE_CONNECTIONS, SortingType.BY_HE_CONNECTIONS_DESC));
		tableCF.setWidth(0, col, "70");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.startDateColumnHeader(),
				SortingType.BY_START_DATE, SortingType.BY_START_DATE_DESC));
		tableCF.setWidth(0, col, "70");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.issueDateColumnHeader(),
				SortingType.BY_ISSUE_DATE, SortingType.BY_ISSUE_DATE_DESC));
		tableCF.setWidth(0, col, "70");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.expiredDateColumnHeader(),
				SortingType.BY_EXPIRED_DATE, SortingType.BY_EXPIRED_DATE_DESC));
		tableCF.setWidth(0, col, "70");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(
				constants.tempExpiredDateColumnHeader(),
				SortingType.BY_TEMP_EXPIRED_DATE, SortingType.BY_TEMP_EXPIRED_DATE_DESC));
		tableCF.setWidth(0, col, "80");
		table.setText(0, col++, constants.resendThisLicenseColumnHeader());
		tableCF.setWidth(0, col, "80");
		table.setText(0, col++, constants.prolongThisLicenseColumnHeader());
		if (!UntillLicenseServer.getLogonDealer().onlyProlong ||
				UntillLicenseServer.getLogonDealer().isSuperDealer) {
			tableCF.setWidth(0, col, "80");
			table.setText(0, col++, constants.upgradeThisLicenseColumnHeader());
			tableCF.setWidth(0, col, "80");
			table.setText(0, col++, constants.boostThisLicenseColumnHeader());
		}
		tableCF.setWidth(0, col, "80");
		table.setText(0, col++, constants.getEmergencyLicenseColumnHeader());
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) { // Only for super dealer
			tableCF.setWidth(0, col, "80");
			table.setText(0, col++, constants.approveOrderColumnHeader());
		}
	}

	private void refreshLicenseTable() {
		panel.remove(table);
		try {
			clearLicenseTable();
			fillLicenseTable();
		} finally {
			panel.add(table);
		}
	}

	private void clearLicenseTable() {
		for (int r = table.getRowCount() - 1; r > 0; --r)
			table.removeRow(r);
	}

	private void fillLicenseTable() {
		RowFormatter tableRowFormatter = table.getRowFormatter();
		FlexCellFormatter tableCellFormatter = table.getFlexCellFormatter();

		// Add licenses into table
		if (licenseList.isEmpty()) {
			tableCellFormatter.setColSpan(1, 0, table.getCellCount(0));
			table.setText(1, 0, constants.licensesNotFound());
			tableCellFormatter.setAlignment(1, 0, HasHorizontalAlignment.ALIGN_CENTER,
					HasVerticalAlignment.ALIGN_MIDDLE);
		} else {
			int row = 1;
			Date nowDate = Common.toUtcDateOnly(new Date());
			TimeZone utcTimeZone = TimeZone.createTimeZone(0);
			DateTimeFormat dateFormat = DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT);

			int licenseIndex = pageNavigator.getBeginItemIndex();
			int endLicenseIndex = pageNavigator.getEndItemIndex();
			for (; licenseIndex < endLicenseIndex; ++licenseIndex) {
				DbLicense license = licenseList.get(licenseIndex);
				RrDealer licenseDealer = UntillLicenseServer.getLogonDealer();
				if (licenseDealer.isSuperDealer && licenseDealer.id != license.idDealers) {
					// find license dealer
					for (RrDealer itDealer : arrDealers) {
						if (itDealer.id == licenseDealer.id) {
							licenseDealer = itDealer;
							break;
						}
					}
				}

				// TODO Encapsulate it in LicenseClientHelper class
				boolean apostill = license.isApostill();
				boolean canceled = license.isCanceled();
				boolean neverExpired = license.isNeverExpired();
				boolean unlimited = license.isUnlimited();
				boolean definitive = license.isDefinitive();
				boolean trial = license.isTrial();
				boolean online = license.isOnline();
				boolean moduled = license.isModuled();
				boolean saas = license.isSaas();
				boolean boost = license.isBoost();
				boolean expired = !neverExpired && license.getExpiredDate().compareTo(nowDate) < 0;
				boolean willSoonExpire = !neverExpired && !expired && !saas && !boost
						&& TimeUnit.MILLISECONDS.toDays(license.getExpiredDate().getTime() - nowDate.getTime()) < 30;
				boolean twoMonthsBeforeExpiration = !neverExpired && !expired
						&& TimeUnit.MILLISECONDS.toDays(license.getExpiredDate().getTime() - nowDate.getTime()) < 60;

				boolean unconfirmed = license.unconfirmed;
				boolean nonApproved = license.needApprove();

				boolean superDealer = UntillLicenseServer.getLogonDealer().isSuperDealer;

				//// TODO Use LicenseClientHelper ...
				//LicenseClientHelper licenseClientHelper = new LicenseClientHelper(license);
				//
				//String licenseHardCode = licenseClientHelper.getFormattedHardCode();

				String hardCode = HardCodeHelper.formatHardCode(license.hardCode);

				StatusWidget statusWidget = new StatusWidget(license, canceled,
						unconfirmed, expired, willSoonExpire, definitive,
						trial, apostill, online, moduled, saas, boost, nonApproved);

				// set row style classes
				tableRowFormatter.addStyleName(row, "ll-Row");
				if (canceled)
					tableRowFormatter.addStyleName(row, "ll-CanceledRow");
				else if (expired)
					tableRowFormatter.addStyleName(row, "ll-ExpiredRow");
				else if (willSoonExpire)
					tableRowFormatter.addStyleName(row, "ll-WillSoonExpireRow");

				// TODO merge with tableCellFormatter
				FlexCellFormatter tableCF = table.getFlexCellFormatter();

				// fill row
				int col = 0;
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				table.setWidget(row, col++, statusWidget);
				table.setText(row, col++, license.getClientName());
				table.setText(row, col++, license.getClientDisplayName() == null ? "" : license.getClientDisplayName());
				table.setText(row, col++, license.chain);
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				Label hardCodeLabel = new Label(hardCode, false);
				hardCodeLabel.addStyleName("HardCodeLabel");
				table.setWidget(row, col++, hardCodeLabel);
//				table.setText(r, col++, license.clientEmail);
//				table.setText(r, col++, license.clientPhone);
				if (unlimited) {
					tableCF.setColSpan(row, col, 5); // 6,);
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					table.setText(row, col++, constants.licenseUnlimited());
				} else {
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					table.setText(row, col++, Short.toString(license.getLbConnections()));
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					table.setText(row, col++, Short.toString(license.getRemConnections()));
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					table.setText(row, col++, Short.toString(license.getOmConnections()));
//					tableCF.setHorizontalAlignment(0, col, HasHorizontalAlignment.ALIGN_CENTER);
//					table.setText(row, col++, Short.toString(license.getHqConnections()));
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					table.setText(row, col++, Short.toString(license.getPsConnections()));
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					table.setText(row, col++, Short.toString(license.getHeConnections()));
				}
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				table.setText(row, col++, dateFormat.format(license.getStartDate(), utcTimeZone));
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				table.setText(row, col++, dateFormat.format(license.getIssueDate(), utcTimeZone));
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				if (neverExpired)
					table.setText(row, col++, constants.licenseNeverExpired());
				else
					table.setText(row, col++, dateFormat.format(license.getExpiredDate(), utcTimeZone));

				for (int i = 0; i < col; ++i)
					tableCF.setStyleName(row, i, "ll-CommonCell");
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				if (license.tempExpiredDate != null)
					table.setText(row, col++, dateFormat.format(license.tempExpiredDate, utcTimeZone));
				else
					table.setText(row, col++, " ");

				if (!unconfirmed) {

					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tableCF.setStyleName(row, col, "ll-CommandCell");
					if (!canceled && !expired && !boost)
						table.setWidget(row, col++, new ResendLicenseButton(license));
					else
						table.setText(row, col++, " ");

					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tableCF.setStyleName(row, col, "ll-CommandCell");
					if (!canceled && !neverExpired && !unlimited
							&& !(definitive && Restrictions.DEFINITIVE_DISABLE_PROLONG)
							&& !(trial && apostill) && !license.nonProlongable && !nonApproved
							&& (twoMonthsBeforeExpiration || expired)
							&& !boost)
						table.setWidget(row, col++, new ProlongLicenseButton(license, licenseDealer));
					else
						table.setText(row, col++, " ");

					if (!UntillLicenseServer.getLogonDealer().onlyProlong || superDealer) {
						tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
						tableCF.setStyleName(row, col, "ll-CommandCell");
						if (!canceled && !neverExpired
								&& !(definitive && Restrictions.DEFINITIVE_DISABLE_UPGRADE)
								&& !trial && !nonApproved && !expired && !boost)
							table.setWidget(row, col++, new UpgradeLicenseButton(license, licenseDealer));
						else
							table.setText(row, col++, " ");

						tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
						tableCF.setStyleName(row, col, "ll-CommandCell");
						if (online && !canceled && !neverExpired
								&& !(definitive && Restrictions.DEFINITIVE_DISABLE_BOOST)
								&& !trial && !nonApproved && !expired && !saas && !boost)
							table.setWidget(row, col++, new BoostLicenseButton(license, licenseDealer));
						else
							table.setText(row, col++, " ");
					}

					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tableCF.setStyleName(row, col, "ll-CommandCell");
					if (!online && !canceled && !expired && !apostill && !trial
							&& license.emergencyAvailable > 0)
						table.setWidget(row, col++, new EmergencyLicenseButton(license, licenseDealer));
					else if (online && !canceled && !expired && !apostill && !trial && !boost
							&& license.availableReinstallations > 0 && license.installationTime != null)
						table.setWidget(row, col++, new ReinstallLicenseButton(license));
					else
						table.setText(row, col++, " ");

					if (superDealer) { // Only for super dealer
						tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
						tableCF.setStyleName(row, col, "ll-CommandCell");
						if (nonApproved) {
							table.setWidget(row, col++, new ApproveLicenseButton(license));
						} else {
							if (license.requestType.isEmpty() && !expired && !neverExpired 
									&& !unlimited && !canceled && !trial && license.idDealers != 0) {
								table.setWidget(row, col++, new OrderManualLicenseButton(license));
							} else {
								table.setText(row, col++, " ");
							}
						}
					}

				} else { // unconfirmed
					// + 1 column for super dealer; -1 for "only prolong" (except super dealer)
					tableCF.setColSpan(row, col, superDealer ? 5 : 
						(UntillLicenseServer.getLogonDealer().onlyProlong ? 3 : 4)); 
					tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tableCF.setStyleName(row, col, "ll-CommandCell");
					if (license.requestLargeAccount && !superDealer) {
						table.setText(row, col++, constants.waitingForLargeAccountApproval());
					} else {
						table.setWidget(row, col++, new ResendRequestButton(license));
					}
				} // if (!unconfirmed) ... else ...

				++row;
			}
		}
	}

	private boolean checkDealerEmail() {
		if (!UntillLicenseServer.getLogonDealer().emailIsValid) {
			new MessageDialog(messages.emailIsNotSpecifiedMessage(), Style.EXCLAMATION).show();
			//return false; // XXX
		}
		return true;
	}

	  /////////////////////
	 // Private classes //
	/////////////////////

	private class StatusWidget extends Composite {
		DbLicense license;
		Image img;

		public StatusWidget(DbLicense license, boolean canceled, boolean unconfirmed,
				boolean expired, boolean willSoonExpire, boolean definitive,
				boolean trial, boolean apostill, boolean online, boolean moduled, boolean saas, boolean boost,
				boolean nonApproved) {
			this.license = license;

			// TODO Encapsulate it in LicenseClientHelper class
			// determinate license status string
			String status = "";
			if (canceled)
				status += constants.licenseCanceled() + ", ";
			if (unconfirmed)
				status += constants.licenseUnconfirmed() + ", ";
			if (nonApproved)
				status += constants.licenseNonApporved() + ", ";
			if (expired)
				status += constants.licenseExpired() + ", ";
			else if (willSoonExpire)
				status += constants.licenseWillSoonExpire() + ", ";
			if (definitive)
				status += constants.licenseDefinitive() + ", ";
			if (trial)
				status += constants.licenseTrial() + ", ";
			if (online)
				status += constants.licenseOnline() + ", ";
			if (moduled)
				status += constants.licenseModuled() + ", ";
			if (saas)
				status += constants.licenseSaas() + ", ";
			if (boost)
				status += constants.licenseBoost() + ", ";
			status += (apostill ? constants.licenseApostill() : constants.licenseUntill());
			// determinate row icon

//			if (apostill && !trial) {
//				if (expired)             img = new Image(images.status_apostill_expired());
//				else if (willSoonExpire) img = new Image(images.status_apostill_will_soon_expire());
//				else if (unconfirmed)    img = new Image(images.status_apostill_unconfirmed());
//				else if (nonApproved)    img = new Image(images.status_apostill_non_approved());
//				else                     img = new Image(images.status_apostill());
//			} else if (apostill && trial) {
//				if (expired)             img = new Image(images.status_apostill_trial_expired());
//				else if (willSoonExpire) img = new Image(images.status_apostill_trial_will_soon_expire());
//				else if (unconfirmed)    img = new Image(images.status_apostill_trial_unconfirmed());
//				else if (nonApproved)    img = new Image(images.status_apostill_trial_non_approved());
//				else                     img = new Image(images.status_apostill_trial());
//			} else 
			if (saas) {
				if (expired)             img = new Image(images.status_saas_expired());
				else if (willSoonExpire) img = new Image(images.status_saas_will_soon_expire());
				else if (unconfirmed)    img = new Image(images.status_saas_unconfirmed());
				else if (nonApproved)    img = new Image(images.status_saas_non_approved());
				else                     img = new Image(images.status_saas());
			} else if (boost) {
				if (expired)             img = new Image(images.status_boost_expired());
				else if (willSoonExpire) img = new Image(images.status_boost_will_soon_expire());
				else if (unconfirmed)    img = new Image(images.status_boost_unconfirmed());
				else if (nonApproved)    img = new Image(images.status_boost_non_approved());
				else                     img = new Image(images.status_boost());
			} else if (trial) {
				if (expired)             img = new Image(images.status_demo_expired());
				else if (willSoonExpire) img = new Image(images.status_demo_will_soon_expire());
				else if (unconfirmed)    img = new Image(images.status_demo_unconfirmed());
				else if (nonApproved)    img = new Image(images.status_demo_non_approved());
				else                     img = new Image(images.status_demo());
			} else if (online) {
				if (expired)             img = new Image(images.status_online_expired());
				else if (willSoonExpire) img = new Image(images.status_online_will_soon_expire());
				else if (unconfirmed)    img = new Image(images.status_online_unconfirmed());
				else if (nonApproved)    img = new Image(images.status_online_non_approved());
				else                     img = new Image(images.status_online());
			} else {
				if (expired)             img = new Image(images.status_common_expired());
				else if (willSoonExpire) img = new Image(images.status_common_will_soon_expire());
				else if (unconfirmed)    img = new Image(images.status_common_unconfirmed());
				else if (nonApproved)    img = new Image(images.status_common_non_approved());
				else                     img = new Image(images.status_common());
			}

			img.setTitle(status);

			this.initWidget(img);
		}

		public DbLicense getLicense() {
			return license;
		}
	}

	private class ResendRequestButton extends Button implements ClickHandler, AsyncCallback<Boolean> {
		DbLicense license;

		public ResendRequestButton(DbLicense license) {
			super(license.requestLargeAccount ? constants.resendLargeAccountApprovalRequestButton()
					: constants.resendThisRequestButton());
			this.license = license;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {
			if (!checkDealerEmail()) return;

			// Call RPC resendRequest
			UntillLicenseServer.getLicenseServiceAsync().resendRequest(UntillLicenseServer.getAuthScheme(),
					license.id, this);

			WaitDialog.show();
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Boolean result) {
			WaitDialog.hide();
			if (result.booleanValue()) {
				new MessageDialog(messages.requestSuccessfullySentMessage(), Style.INFORMATION).show();
			} else {
				new MessageDialog(messages.errorSendingRequestMessage(), Style.WARNING).show();
			}
		}
	}

	private class ResendLicenseButton extends Button implements ClickHandler, AsyncCallback<Boolean>,
			ResendLicenseDialog.DialogListener {
		DbLicense license;

		public ResendLicenseButton(DbLicense license) {
			super(constants.resendThisLicenseButton());
			this.license = license;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {
			if (!checkDealerEmail()) return;

			if (!license.isTrial() && !license.isApostill() && !license.isNeverExpired()) {
				new ResendLicenseDialog(license, this).show();
			} else {
				// Call RPC resendLicense
				UntillLicenseServer.getLicenseServiceAsync().resendLicense(
						UntillLicenseServer.getAuthScheme(), license.id, null, this);
				WaitDialog.show();
			}
		}

		@Override
		public void onCancel() {
		}

		@Override
		public void onOk(Date tempExpiredDate) {
			// Call RPC resendLicense
			UntillLicenseServer.getLicenseServiceAsync().resendLicense(
					UntillLicenseServer.getAuthScheme(), license.id, tempExpiredDate, this);

			WaitDialog.show();
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Boolean result) {
			WaitDialog.hide();
			if (result.booleanValue()) {
				new MessageDialog(messages.licenseSuccessfullySentMessage(), Style.INFORMATION).show();
			} else {
				new MessageDialog(messages.errorSendingLicenseMessage(), Style.WARNING).show();
			}
		}

	}

	private class ProlongLicenseButton extends Button implements ClickHandler {
		DbLicense license;
		RrDealer dealer;

		public ProlongLicenseButton(DbLicense license, RrDealer dealer) {
			super(constants.prolongThisLicenseButton());
			this.license = license;
			this.dealer = dealer;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {
			if (!checkDealerEmail()) return;

			// Prolong license

			LicenseDialog ld = new LicenseDialog(license, dealer, RequestType.PROLONG);
			ld.show();

		}
	}

	private class UpgradeLicenseButton extends Button implements ClickHandler {
		DbLicense license;
		RrDealer dealer;

		public UpgradeLicenseButton(DbLicense license, RrDealer dealer) {
			super(constants.upgradeThisLicenseButton());
			this.license = license;
			this.dealer = dealer;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			if (!checkDealerEmail()) return;

			// Upgrade license

			LicenseDialog ld = new LicenseDialog(license, dealer, RequestType.UPGRADE);
			ld.show();

		}
	}

	private class BoostLicenseButton extends Button implements ClickHandler {
		DbLicense license;
		RrDealer dealer;

		public BoostLicenseButton(DbLicense license, RrDealer dealer) {
			super(constants.boostThisLicenseButton());
			this.license = license;
			this.dealer = dealer;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			if (!checkDealerEmail()) return;

			// Boost license

			LicenseDialog ld = new LicenseDialog(license, dealer, RequestType.CREATE_BOOST);
			ld.show();

		}
	}

	private class EmergencyLicenseButton extends Button implements ClickHandler {
		DbLicense license;
		RrDealer dealer;

		public EmergencyLicenseButton(DbLicense license, RrDealer dealer) {
			super(constants.getEmergencyLicenseButton());
			this.license = license;
			this.dealer = dealer;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			if (!checkDealerEmail()) return;

			// Request emergency license

			LicenseDialog ld = new LicenseDialog(license, dealer, RequestType.EMERGENCY);
			ld.show();

		}
	}

	private class ReinstallLicenseButton extends Button implements ClickHandler, MessageDialogListener,
			AsyncCallback<Void> {
		DbLicense license;

		public ReinstallLicenseButton(DbLicense license) {
			super(constants.reinstallLicenseButton());
			this.license = license;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			new MessageDialog(messages.reintallLicenseQuestion(license.availableReinstallations), Style.QUESTION, 
					Buttons.YESNO, this).show();
			
		}

		@Override
		public void onSelectButton(SelectedButton selectedButton) {
			if (selectedButton == SelectedButton.YES) {

				// Call RPC resendLicense
				UntillLicenseServer.getLicenseServiceAsync().reinstallLicense(
						UntillLicenseServer.getAuthScheme(), license.id, this);

				WaitDialog.show();
				
			}
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Void result) {
			WaitDialog.hideAll();
			new MessageDialog(messages.licenseReintalledSuccessfully(), 
					Style.INFORMATION).show();
			refresh();
		}

	}

	private class ApproveLicenseButton extends Button implements ClickHandler {
		DbLicense license;

		public ApproveLicenseButton(DbLicense license) {
			super(constants.approveLicenseButton());
			this.license = license;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			if (!checkDealerEmail()) return;

			// Show "Approve license" dialog

			ApproveLicenseDialog ld = new ApproveLicenseDialog(license, 
					getDealerNameById(license.idDealers));
			ld.show();

		}
	}

	private class OrderManualLicenseButton extends Button implements ClickHandler, MessageDialogListener,
			AsyncCallback<Void> {
		DbLicense license;

		public OrderManualLicenseButton(DbLicense license) {
			super(constants.orderManualLicenseButton());
			setTitle(constants.orderManualLicenseButtonTitle());
			this.license = license;
			addClickHandler(this);
			addStyleName("ll-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {

			new MessageDialog(messages.orderManuallyEnteredLicenseQuestion(), Style.QUESTION, 
					Buttons.YESNO, this).show();
			
		}

		@Override
		public void onSelectButton(SelectedButton selectedButton) {
			if (selectedButton == SelectedButton.YES) {

				// Call RPC resendLicense
				UntillLicenseServer.getLicenseServiceAsync().orderManualLicense(
						UntillLicenseServer.getAuthScheme(), license.id, this);

				WaitDialog.show();
				
			}
		}

		@Override
		public void onFailure(Throwable caught) {
			WaitDialog.hideAll();
			ErrorDialog.show(caught);
		}

		@Override
		public void onSuccess(Void result) {
			WaitDialog.hideAll();
			new MessageDialog(messages.orderManuallyEnteredLicenseCreatedSuccessfully(), 
					Style.INFORMATION).show();
			refresh();
		}

	}

}
