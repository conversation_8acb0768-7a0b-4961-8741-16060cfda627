﻿<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="1.1" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:fo="http://www.w3.org/1999/XSL/Format" exclude-result-prefixes="fo">
	<xsl:output method="xml" version="1.0" omit-xml-declaration="no" indent="yes"/>
	<xsl:param name="versionParam" select="'1.0'"/>

	<!-- root element: invoice -->
	<xsl:template match="invoice">
		<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" font-family="Poppins">

			<xsl:variable name="unTill">unTill<fo:inline baseline-shift="super" font-size="60%">®</fo:inline></xsl:variable>
			<xsl:variable name="currencySymbol">
				<xsl:choose>
					<xsl:when test="bank/currency/symbol">
						<xsl:value-of select="bank/currency/symbol"/>
					</xsl:when>
					<xsl:otherwise>
						<xsl:text>€</xsl:text>
					</xsl:otherwise>
				</xsl:choose>
			</xsl:variable>

			<fo:layout-master-set>
				<fo:simple-page-master master-name="simpleA4" page-height="297mm" page-width="210mm">
					<!-- For fo:simple-page-master, fo:region-body must be declared before fo:region-before. -->
					<fo:region-body margin-top="45mm" margin-bottom="20mm" margin-left="12mm" margin-right="5mm"/>
					<fo:region-before extent="45mm"/>
					<fo:region-after extent="20mm"/>
				</fo:simple-page-master>
			</fo:layout-master-set>

			<fo:page-sequence master-reference="simpleA4">
				<!-- Header -->
				<fo:static-content flow-name="xsl-region-before">
					<!-- Picture -->
					<fo:block-container width="210mm" height="43mm" left="0" top="-1mm" absolute-position="absolute" padding="0mm" margin-top="0" margin-bottom="0" margin-right="0" margin-left="0">
						<fo:block>
							<fo:external-graphic width="210mm" height="43mm" content-width="210mm" content-height="43mm" src="InvoiceHeader.png" />
						</fo:block>
					</fo:block-container>
				</fo:static-content>

				<!-- Footer -->
				<fo:static-content flow-name="xsl-region-after">
					<fo:block font-size="8pt" font-family="Poppins" text-align="center" margin-left="2mm" margin-right="2mm"> <!-- font-family="sans-serif"  -->
						<!-- Separator -->
						<fo:table table-layout="fixed" width="100%"> <!--  border-collapse="collapse" -->
							<fo:table-column column-width="100%"/>
							<fo:table-body>
							<fo:table-row height="2mm">
								<fo:table-cell border-top-width="1.5pt" border-top-style="solid" border-top-color="black">
									<fo:block></fo:block>
								</fo:table-cell>
							</fo:table-row>
							</fo:table-body>
						</fo:table>
						<fo:block><xsl:copy-of select="$unTill"/> Software Licensing B.V. - Hospitality Software • <EMAIL> • www.untill.com • Korte Eeweg 11 - 4424 NA Wemeldinge - The Netherlands</fo:block>
						<fo:block>
							<xsl:text>VAT No: NL855292532B01 • CC Utrecht: ********</xsl:text>
							<xsl:choose>
								<xsl:when test="bank/details">
									<xsl:for-each select="bank/details/line">
										<xsl:text> • </xsl:text>
										<xsl:value-of select="."/>
									</xsl:for-each>
								</xsl:when>
								<xsl:otherwise>
									<xsl:text> • IBAN: NL64 INGB 0006 8444 49 • BIC: INGBNL2A</xsl:text>
								</xsl:otherwise>
							</xsl:choose>
						</fo:block>
						<fo:block><xsl:copy-of select="$unTill"/> is a registered trademark, all rights of the use of the logo, trademark and software are owned by <xsl:copy-of select="$unTill"/> Software Development Group B.V.</fo:block>
				</fo:block>
				</fo:static-content>

				<!-- Body -->
				<fo:flow flow-name="xsl-region-body">

					<!--<fo:block-container min-height="175mm">-->
					<fo:table table-layout="fixed" width="100%"><fo:table-body><fo:table-row height="170mm"><fo:table-cell>

						<fo:table table-layout="fixed" width="100%">
							<fo:table-column column-width="92mm"/>
							<fo:table-column column-width="80mm"/>
							<fo:table-body>
								<fo:table-row>

									<!-- Top-left block -->
									<fo:table-cell>
										<fo:block font-size="9pt" font-family="Poppins" white-space-collapse="false" wrap-option="wrap">
											<fo:block>unTill Software Licensing B.V.</fo:block>
											<fo:block>Korte Eeweg 11</fo:block>
											<fo:block>4424 NA Wemeldinge – The Netherlands</fo:block>
											<fo:block><EMAIL></fo:block>
											<fo:block>www.untill.com</fo:block>
											<fo:block>VAT No: NL855292532B01</fo:block>
											<fo:block>CC Utrecht: ********</fo:block>
											<xsl:choose>
												<xsl:when test="bank/details">
													<xsl:for-each select="bank/details/line">
														<fo:block>
															<xsl:value-of select="."/>
														</fo:block>
													</xsl:for-each>
												</xsl:when>
											<xsl:otherwise>
													<fo:block>IBAN: NL64 INGB 0006 8444 49</fo:block>
													<fo:block>Bic: INGBNL2A</fo:block>
												</xsl:otherwise>
											</xsl:choose>
										</fo:block>
									</fo:table-cell>

									<!-- Top-right (dealer info) block -->
									<fo:table-cell font-size="10pt" >
										<!--<fo:block-container min-height="30mm">-->
										<fo:table table-layout="fixed" width="100%"><fo:table-body><fo:table-row height="30mm"><fo:table-cell>
											<fo:block>
												<xsl:value-of select="dealer/extra-fields/company"/>
											</fo:block>
											<fo:block>
												<xsl:value-of select="dealer/extra-fields/title"/>
											</fo:block>
											<xsl:for-each select="dealer/address/line">
												<fo:block>
													<xsl:value-of select="."/>
												</fo:block>
											</xsl:for-each>
											<fo:block>
												<xsl:value-of select="dealer/countries/name"/>
											</fo:block>
										</fo:table-cell></fo:table-row></fo:table-body></fo:table>
										<!--</fo:block-container>-->
										<fo:block margin-top="0mm">
											VAT number: <xsl:value-of select="dealer/extra-fields/vat-no"/>
										</fo:block>
									</fo:table-cell>

								</fo:table-row>
							</fo:table-body>
						</fo:table>

						<!-- Invoice header -->
						<fo:block font-size="24pt" text-align="center" font-weight="bold" margin-bottom="2mm" >
							Invoice
						</fo:block>

						<!-- Invoice information -->
						<fo:block font-size="8pt">
							<fo:table table-layout="fixed" width="100%" border-collapse="collapse" >
								<fo:table-column column-width="40mm"/>
								<fo:table-column column-width="75mm"/>
								<fo:table-column column-width="75mm"/>
								<fo:table-body>
									<fo:table-row border-style="solid" border-width="1pt" height="5mm">
										<fo:table-cell display-align="center" padding-left="1mm">
											<fo:block>
												<!-- text-align="center" -->
												Date: <xsl:value-of select="date"/>
											</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block>
												Invoice No: <xsl:value-of select="number"/>
											</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<xsl:choose>
												<xsl:when test="reference">
													<fo:block>
														Your reference: <xsl:value-of select="reference"/>
													</fo:block>
												</xsl:when>
												<xsl:otherwise>
													<fo:block/>
												</xsl:otherwise>
											</xsl:choose>
										</fo:table-cell>
									</fo:table-row>
								</fo:table-body>
							</fo:table>
						</fo:block>

						<!-- Invoice table header -->
						<fo:block font-size="8pt" margin-top="2mm">
							<fo:table table-layout="fixed" width="100%" border-collapse="collapse">
								<fo:table-column column-width="12mm"/>
								<fo:table-column column-width="25mm"/>
								<fo:table-column column-width="83mm"/>
								<fo:table-column column-width="20mm"/>
								<fo:table-column column-width="14mm"/>
								<fo:table-column column-width="6mm"/>
								<fo:table-column column-width="20mm"/>
								<fo:table-column column-width="10mm"/>
								<fo:table-body>
									<fo:table-row border-style="solid" border-width="1pt" height="5mm">
										<fo:table-cell display-align="center" padding-left="1mm">
											<fo:block text-align="right" >Number</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block margin-left="3mm">Article Number</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block>Description</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block text-align="right">Unit Price</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block text-align="right">Discount</fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block></fo:block>
										</fo:table-cell>
										<fo:table-cell display-align="center">
											<fo:block text-align="right">Amount</fo:block>
										</fo:table-cell>
										<fo:table-cell><fo:block/></fo:table-cell>
									</fo:table-row>
								</fo:table-body>
							</fo:table>
						</fo:block>

						<!-- Order items -->
						<fo:block font-size="9pt" margin-top="2mm">
							<xsl:for-each select="order/item">
								<fo:table table-layout="fixed" width="100%" border-collapse="collapse">
									<fo:table-column column-width="12mm"/>
									<fo:table-column column-width="25mm"/>
									<fo:table-column column-width="83mm"/>
									<fo:table-column column-width="20mm"/>
									<fo:table-column column-width="14mm"/>
									<fo:table-column column-width="6mm"/> <!-- for currency symbol -->
									<fo:table-column column-width="20mm"/>
									<fo:table-body>
										<fo:table-row height="5mm">
											<fo:table-cell display-align="center" padding-left="1mm">
												<fo:block text-align="right">
													<xsl:value-of select="quantity"/>
												</fo:block>
											</fo:table-cell>
											<fo:table-cell display-align="center">
												<fo:block margin-left="3mm">
													<xsl:value-of select="article-number"/>
												</fo:block>
											</fo:table-cell>
											<fo:table-cell display-align="center">
												<fo:block>
													<xsl:value-of select="article-name"/>
												</fo:block>
											</fo:table-cell>
											<fo:table-cell display-align="center">
												<fo:block text-align="right">
													<xsl:value-of select="price"/>
												</fo:block>
											</fo:table-cell>
											<fo:table-cell display-align="center">
												<fo:block text-align="right">
													<xsl:value-of select="discount"/>
												</fo:block>
											</fo:table-cell>
											<fo:table-cell display-align="center">
												<fo:block text-align="right">
													<xsl:copy-of select="$currencySymbol"/>
												</fo:block>
											</fo:table-cell>
											<fo:table-cell display-align="center">
												<fo:block text-align="right">
													<xsl:value-of select="amount"/>
												</fo:block>
											</fo:table-cell>
										</fo:table-row>
									</fo:table-body>
								</fo:table>
								<xsl:for-each select="text">
									<fo:block margin-left="40mm" font-style="italic">
										<xsl:value-of select="."/>
									</fo:block>
								</xsl:for-each>
							</xsl:for-each>
						</fo:block>

					</fo:table-cell></fo:table-row></fo:table-body></fo:table>
					<!--</fo:block-container>-->

					<!-- Bottom block -->
					<fo:block-container keep-together.within-page="always" margin-top="1mm">

						<fo:table table-layout="fixed" width="100%">
							<fo:table-column column-width="90mm"/>
							<fo:table-column column-width="90mm"/>
							<fo:table-body>
								<fo:table-row>
									<!-- License data -->
									<fo:table-cell>
										<fo:block font-size="7pt" margin-left="10mm">
											<xsl:if test="license">
												<fo:block>Client name: <xsl:value-of select="license/client_name"/></fo:block>
												<fo:block>Start date: <xsl:value-of select="license/start_date"/></fo:block>
												<fo:block>Issue date: <xsl:value-of select="license/issue_date"/></fo:block>
												<fo:block>Expired date: <xsl:value-of select="license/expired_date"/></fo:block>
												<xsl:if test="license/hard_code"><fo:block>Hardcode: <xsl:value-of select="license/hard_code"/></fo:block></xsl:if>
												<fo:block>Number of Local Databases: <xsl:value-of select="license/lb_connections"/></fo:block>
												<fo:block>Number of Remote Connections: <xsl:value-of select="license/rem_connections"/></fo:block>
												<fo:block>Number of Hand Terminals: <xsl:value-of select="license/om_connections"/></fo:block>
												<fo:block>Number of Production Screen Professional: <xsl:value-of select="license/ps_connections"/></fo:block>
												<fo:block>Number of HHT-EFT: <xsl:value-of select="license/he_connections"/></fo:block>
												<xsl:for-each select="license/modules">
													<fo:block>The modules: <xsl:value-of select="."/></fo:block>
												</xsl:for-each>
												<xsl:for-each select="license/extra-modules">
													<fo:block>The extra modules: <xsl:value-of select="."/></fo:block>
												</xsl:for-each>
												<xsl:for-each select="license/extra-interfaces">
													<fo:block>The extra interfaces: <xsl:value-of select="."/></fo:block>
												</xsl:for-each>
												<xsl:for-each select="license/status">
													<fo:block>License type: <xsl:value-of select="."/></fo:block>
												</xsl:for-each>
											</xsl:if>
										</fo:block>
									</fo:table-cell>
									<!-- Total block -->
									<fo:table-cell>
										<fo:block font-size="9pt" text-align="right" margin-top="0mm">
											<fo:table table-layout="fixed" width="100%" border-collapse="collapse">
												<fo:table-column column-width="44mm"/>
												<fo:table-column column-width="14mm"/>
												<fo:table-column column-width="6mm"/>
												<fo:table-column column-width="6mm"/> <!-- for currency symbol -->
												<fo:table-column column-width="20mm"/>
												<fo:table-body>
													<!-- Separator -->
													<fo:table-row height="2mm">
														<fo:table-cell number-columns-spanned="2">
															<fo:block></fo:block>
														</fo:table-cell>
														<fo:table-cell number-columns-spanned="3" border-top-width="1.5pt" border-top-style="solid">
															<fo:block></fo:block>
														</fo:table-cell>
													</fo:table-row>
													<!-- Sub total -->
													<fo:table-row>
														<fo:table-cell display-align="center">
															<fo:block>Subtotal</fo:block>
														</fo:table-cell>
														<fo:table-cell number-columns-spanned="2">
															<fo:block></fo:block>
														</fo:table-cell>
														<fo:table-cell display-align="center">
															<fo:block>
																<xsl:copy-of select="$currencySymbol"/>
															</fo:block>
														</fo:table-cell>
														<fo:table-cell display-align="center">
															<fo:block>
																<xsl:value-of select="order/subtotal"/>
															</fo:block>
														</fo:table-cell>
													</fo:table-row>
													<!-- VATs -->
													<xsl:for-each select="order/vat">
														<fo:table-row>
															<fo:table-cell display-align="center">
																<fo:block>
																	<xsl:value-of select="percent"/>
																	<xsl:text> % VAT for</xsl:text>
																</fo:block>
															</fo:table-cell>
															<fo:table-cell number-columns-spanned="2">
																<fo:block>
																	<xsl:value-of select="base"/>
																</fo:block>
															</fo:table-cell>
															<fo:table-cell display-align="center">
																<fo:block>
																	<xsl:copy-of select="$currencySymbol"/>
																</fo:block>
															</fo:table-cell>
															<fo:table-cell display-align="center">
																<fo:block>
																	<xsl:value-of select="amount"/>
																</fo:block>
															</fo:table-cell>
														</fo:table-row>
													</xsl:for-each>
													<!-- Blank -->
													<fo:table-row height="2mm">
														<fo:table-cell number-columns-spanned="5">
															<fo:block></fo:block>
														</fo:table-cell>
													</fo:table-row>
													<!-- Separator -->
													<fo:table-row height="2mm">
														<fo:table-cell number-columns-spanned="2">
															<fo:block></fo:block>
														</fo:table-cell>
														<fo:table-cell number-columns-spanned="3" border-top-width="1.5pt" border-top-style="solid">
															<fo:block></fo:block>
														</fo:table-cell>
													</fo:table-row>
													<!-- Total -->
													<fo:table-row>
														<fo:table-cell display-align="center">
															<fo:block>Total</fo:block>
														</fo:table-cell>
														<fo:table-cell number-columns-spanned="2">
															<fo:block></fo:block>
														</fo:table-cell>
														<fo:table-cell display-align="center">
															<fo:block>
																<xsl:copy-of select="$currencySymbol"/>
															</fo:block>
														</fo:table-cell>
														<fo:table-cell display-align="center">
															<fo:block>
																<xsl:value-of select="order/total"/>
															</fo:block>
														</fo:table-cell>
													</fo:table-row>
													<!-- Separator (double) -->
													<fo:table-row height="2mm">
														<fo:table-cell number-columns-spanned="2">
															<fo:block></fo:block>
														</fo:table-cell>
														<fo:table-cell number-columns-spanned="3" border-bottom-width="4pt" border-bottom-style="double">
															<fo:block></fo:block>
														</fo:table-cell>
													</fo:table-row>
												</fo:table-body>
											</fo:table>
											<fo:block margin-top="3mm" text-align="left" margin-left="10mm">
												Payment term: <fo:inline font-weight="bold">within 30 days of date of invoice</fo:inline>
											</fo:block>
											<xsl:if test="not(order/vat)">
												<fo:block font-size="7pt" text-align="justify" margin-left="5mm">
													<fo:block margin-top="2mm">VAT due to the recipient (Article 138(1) Directive 2006/112)</fo:block>
												</fo:block>
											</xsl:if>
										</fo:block>
									</fo:table-cell>
								</fo:table-row>
							</fo:table-body>
						</fo:table>

					</fo:block-container>


				</fo:flow>

			</fo:page-sequence>

		</fo:root>
	</xsl:template>

</xsl:stylesheet>
