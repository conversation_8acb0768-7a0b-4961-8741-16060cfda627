/**
 * 
 */
package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.EventListener;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;

/**
 * <AUTHOR>
 *
 */
public class PageNavigator extends Composite implements ClickHandler, ChangeHandler {

	public static interface UlsConstants extends Constants {
		String totalLabel();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	private int itemsPerPage;

	private int itemCount = 0;
	
	private VerticalPanel pageNavigatorAndTotalPanel = new VerticalPanel();

	private HorizontalPanel pageNavigatorPanel = new HorizontalPanel();
	private Button btnFirstPage = new Button("&lt;&lt;", this);
	private Button btnPrevPage = new Button("&lt;", this);
	private ListBox lstPageNavigator = new ListBox();
	private Button btnNextPage = new Button("&gt;", this);
	private Button btnLastPage = new Button("&gt;&gt;", this);

	private Label lblTotal = new Label("", false);

	  //////////////////
	 // Constructors //
	//////////////////
	
	public PageNavigator() {
		this(20);
	}

	public PageNavigator(PageChangeListener listener) {
		this();
		addPageChangeListener(listener);
	}

	public PageNavigator(int itemsPerPage, PageChangeListener listener) {
		this(itemsPerPage);
		addPageChangeListener(listener);
	}

	public PageNavigator(int itemsPerPage) {
		this.itemsPerPage = itemsPerPage;

		btnFirstPage.setWidth("4ex");
		btnPrevPage.setWidth("4ex");
		btnNextPage.setWidth("4ex");
		btnLastPage.setWidth("4ex");

		pageNavigatorPanel.setVerticalAlignment(HasVerticalAlignment.ALIGN_MIDDLE);
		pageNavigatorPanel.setSpacing(2);
		pageNavigatorPanel.add(btnFirstPage);
		pageNavigatorPanel.add(btnPrevPage);
		pageNavigatorPanel.add(lstPageNavigator);
		pageNavigatorPanel.add(btnNextPage);
		pageNavigatorPanel.add(btnLastPage);
		pageNavigatorPanel.setVisible(false);

		pageNavigatorAndTotalPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		pageNavigatorAndTotalPanel.add(lblTotal);
		pageNavigatorAndTotalPanel.add(pageNavigatorPanel);

		lstPageNavigator.addChangeHandler(this);

		this.initWidget(pageNavigatorAndTotalPanel);
	}

	  ////////////////////
	 // Public methods //
	////////////////////
	
	public int getItemsPerPage() { return itemsPerPage; }

//	public void setItemsPerPage(int itemsPerPage) {
//		this.itemsPerPage = itemsPerPage;
//		...
//	}

	public int getItemCount() { return itemCount; }

	public void setItemCount(int itemCount) {
		this.itemCount = itemCount;

		// Adjust total label
		if (itemCount > 0)
			lblTotal.setText(constants.totalLabel() + ": " + Integer.toString(itemCount) + "  ");
		else
			lblTotal.setText("");

		if (itemCount > itemsPerPage) {
			int pageCount = (int) Math.ceil((double) itemCount / itemsPerPage);
			int currentPage = lstPageNavigator.getSelectedIndex();
			if (currentPage >= pageCount)
				currentPage = pageCount - 1;
			if (currentPage < 0)
				currentPage = 0;
			lstPageNavigator.clear();
			for (int i = 0; i < pageCount; ++i)
				lstPageNavigator.addItem(Integer.toString(i * itemsPerPage + 1)
						+ " - " + Integer.toString(Math.min((i + 1) * itemsPerPage, itemCount)));
//			if (lstPageNavigator.getItemCount() != pageCount) {
//				lstPageNavigator.clear();
//				for (int i = 0; i < pageCount; ++i)
//					lstPageNavigator.addItem(Integer.toString(i + 1));
//			}
			lstPageNavigator.setSelectedIndex(currentPage);
			pageNavigatorPanel.setVisible(true);
			refresh();
		} else {
			pageNavigatorPanel.setVisible(false);
			lstPageNavigator.clear();
		}
	}

	public int getPageCount() {
		return itemCount > itemsPerPage ? lstPageNavigator.getItemCount() : 1;
	}

	public void setCurrentPage(int currentPage) {
		lstPageNavigator.setSelectedIndex(currentPage);
	}

	public int getBeginItemIndex() {
		int currentPage = itemCount > itemsPerPage ? lstPageNavigator.getSelectedIndex() : 0;
		return currentPage * itemsPerPage;
	}

	public int getEndItemIndex() {
		int currentPage = itemCount > itemsPerPage ? lstPageNavigator.getSelectedIndex() : 0;
		return Math.min(itemCount, (currentPage + 1) * itemsPerPage);
	}
	
	  ////////////////////
	 // Event Handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();
		if (sender == btnFirstPage) {
			lstPageNavigator.setSelectedIndex(0);
			firePageChangeListeners();
		} else if (sender == btnPrevPage) {
			int currentPage = lstPageNavigator.getSelectedIndex();
			if (currentPage > 0)
				lstPageNavigator.setSelectedIndex(currentPage - 1);
			firePageChangeListeners();
		} else if (sender == btnNextPage) {
			int currentPage = lstPageNavigator.getSelectedIndex();
			if (currentPage < (lstPageNavigator.getItemCount() - 1))
				lstPageNavigator.setSelectedIndex(currentPage + 1);
			firePageChangeListeners();
		} else if (sender == btnLastPage) {
			lstPageNavigator.setSelectedIndex(lstPageNavigator.getItemCount() - 1);
			firePageChangeListeners();
		}
	}

	@Override
	public void onChange(ChangeEvent event) {
		firePageChangeListeners();
	}

	  ////////////////////////
	 // PageChangeListener //
	////////////////////////
	
	public static interface PageChangeListener extends EventListener {
		void onChangePage();
	}

	ArrayList<PageChangeListener> pageChangeListeners = null;

	public void addPageChangeListener(PageChangeListener listener) {
		if (pageChangeListeners == null) {
			pageChangeListeners = new ArrayList<PageChangeListener>();
		}
		pageChangeListeners.add(listener);
	}

	public void removePageChangeListener(PageChangeListener listener) {
		if (pageChangeListeners != null) {
			pageChangeListeners.remove(listener);
		}
	}

	void firePageChangeListeners() {
		refresh();
		if (pageChangeListeners != null) {
			for (PageChangeListener pageChangeListener : pageChangeListeners) {
				pageChangeListener.onChangePage();
			}
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void refresh() {
		if (itemCount > itemsPerPage) {
			int pageCount = lstPageNavigator.getItemCount();
			int currentPage = lstPageNavigator.getSelectedIndex();
			btnPrevPage.setEnabled(currentPage > 0);
			btnFirstPage.setEnabled(currentPage > 0);
			btnNextPage.setEnabled(currentPage < (pageCount - 1));
			btnLastPage.setEnabled(currentPage < (pageCount - 1));
		}
	}

}
