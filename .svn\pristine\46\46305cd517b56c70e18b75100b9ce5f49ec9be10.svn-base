package eu.untill.license.server;

import java.util.TimerTask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.untill.su.server.TaskDispatcher;

public class SuServerTimerTask extends TimerTask {

	static final Logger LOG = LoggerFactory.getLogger(SuServerTimerTask.class);

	@Override
	public void run() {
		LOG.debug("ENTRY");

		tickTaskDispatcher();

		LOG.debug("RETURN");
	}

	public static void tickTaskDispatcher() {
		try {
			TaskDispatcher taskDispatcher = Common.getTaskDispatcher();
			synchronized (taskDispatcher) {
				taskDispatcher.tick();
			}
		} catch (Exception e) {
			LOG.error("unexpected error", e);
		}
	}

}
