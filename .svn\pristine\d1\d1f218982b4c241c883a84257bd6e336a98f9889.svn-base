package eu.untill.license.server;

import static eu.untill.license.server.SuCommon.deployedProductsToJson;
import static eu.untill.license.server.SuCommon.paramsFromJson;
import static eu.untill.license.server.SuCommon.resultFromJson;
import static eu.untill.license.server.SuCommon.resultToJson;
import static eu.untill.license.server.SuCommon.shortSysInfoToJson;
import static eu.untill.license.server.SuCommon.statusFromInt;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.untill.su.api.ISuServer;
import com.untill.su.api.ITaskHandler;
import com.untill.su.api.entity.DeployedProduct;
import com.untill.su.api.entity.HostTask;
import com.untill.su.api.entity.Result;
import com.untill.su.api.entity.ShortSysInfo;
import com.untill.su.api.entity.Status;
import com.untill.su.api.entity.SubTask;
import com.untill.su.api.entity.Task;
import com.untill.su.api.exceptions.EAlreadyRegisteredException;
import com.untill.su.api.exceptions.EHostIsNotRegistered;
import com.untill.su.api.exceptions.EWrongLicenseUidOrPasswordException;

import eu.untill.license.shared.DbLicense;

public class SuTaskHandler implements ITaskHandler, ISuServer {

	static final Logger LOG = LoggerFactory.getLogger(SuTaskHandler.class);

	private CommonHelper commonHelper;

	public SuTaskHandler(CommonHelper commonHelper) {
		this.commonHelper = commonHelper;
	}

	// ITaskHandler

	@Override
	public List<Task> queryAllWorkingTasks() {
		List<Task> result;
		try (Connection conn = Common.getConnection()) {
			String sql = "SELECT T.ID, TB.ID_DEALERS, TB.CREATE_DATETIME, TB.TITLE, TB.TASK_ACTION, TB.PARAMS, TB.START_DATETIME\n"
					+ "FROM SU_TASK T\n"
					+ "INNER JOIN SU_TASK_BUNDLE TB ON TB.ID = T.ID_SU_TASK_BUNDLE\n"
					+ "WHERE EXISTS (SELECT * FROM SU_SUBTASK WHERE ID_SU_TASK = T.ID AND STATUS < ?)";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setInt(1, Status.SUCCESS.ordinal());
				try (ResultSet rs = stmt.executeQuery()) {
					result = new ArrayList<>();
					while (rs.next()) {
						result.add(getTaskFromResultSet(rs));
					}
				}
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return result;
	}

	@Override
	public Status getSubTaskMinStatus(long taskId) {
		Status result;
		try (Connection conn = Common.getConnection()) {
			String sql = "SELECT MIN(STATUS) FROM SU_SUBTASK WHERE ID_SU_TASK = ?";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setLong(1, taskId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next())
						throw new RuntimeException();
					result = Status.values()[rs.getInt(1)];
					if (rs.wasNull())
						result = null;
				}
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return result;
	}

	@Override
	public Status getSubTaskMaxStatus(long taskId) {
		Status result;
		try (Connection conn = Common.getConnection()) {
			String sql = "SELECT MAX(STATUS) FROM SU_SUBTASK WHERE ID_SU_TASK = ?";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setLong(1, taskId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next())
						throw new RuntimeException();
					result = Status.values()[rs.getInt(1)];
					if (rs.wasNull())
						result = null;
				}
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return result;
	}

	@Override
	public Instant getMinHeartbeatFromHosts(long taskId) {
		Instant result;
		try (Connection conn = Common.getConnection()) {
			String sql = "SELECT MIN(COALESCE(H.HEARTBEAT, '1899-12-30'))\n"
					+ "FROM SU_SUBTASK S\n"
					+ "INNER JOIN SU_HOST H ON H.ID = S.ID_SU_HOST\n"
					+ "WHERE S.ID_SU_TASK = ?";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setLong(1, taskId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next())
						throw new RuntimeException();
					Timestamp timestamp = rs.getTimestamp(1);
					if (timestamp == null)
						result = null;
					else
						result = timestamp.toInstant();
				}
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return result;
	}

	@Override
	public void putSubTaskResult(long subTaskId, Result result) {
		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {
				String sql = "UPDATE SU_SUBTASK SET RESULT = ? WHERE ID = ?";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setString(1, resultToJson(result));
					stmt.setLong(2, subTaskId);
					if (stmt.executeUpdate() == 0) throw new RuntimeException();
				}
				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public List<SubTask> querySubTasks(long taskId) {
		List<SubTask> result;
		try (Connection conn = Common.getConnection()) {
			String sql = "SELECT ID, ID_SU_TASK, ID_SU_HOST, STATUS, STATUS_CHANGE_DT, STATUS_SENT_DT, RESULT\n"
					+ "FROM SU_SUBTASK\n"
					+ "WHERE ID_SU_TASK = ?";
			try (PreparedStatement stmt = conn.prepareStatement(sql)) {
				stmt.setLong(1, taskId);
				try (ResultSet rs = stmt.executeQuery()) {
					result = new ArrayList<>();
					while (rs.next()) {
						SubTask subtask = new SubTask(rs.getLong("ID_SU_TASK"), rs.getLong("ID_SU_HOST"));
						subtask.setSubTaskId(rs.getLong("ID"));
						subtask.setStatus(statusFromInt(rs.getInt("STATUS")));
						subtask.setStatusChangeTime(rs.getTimestamp("STATUS_CHANGE_DT").toInstant());
						Timestamp statusSentTime = rs.getTimestamp("STATUS_SENT_DT");
						if (statusSentTime != null)
							subtask.setStatusSentTime(statusSentTime.toInstant());
						subtask.setResult(resultFromJson(rs.getString("RESULT")));
						result.add(subtask);
					}
				}
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
		return result;
	}

	@Override
	public void updateSubTaskStatus(long subTaskId, Status newStatus) {
		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {
				String sql = "UPDATE SU_SUBTASK SET STATUS = ? WHERE ID = ?";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setInt(1, newStatus.ordinal());
					stmt.setLong(2, subTaskId);
					if (stmt.executeUpdate() == 0) throw new RuntimeException();
				}
				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
	}

	// ISuServer

	@Override
	public String registerHost(String licenseUid, String password, String hostName)
			throws EAlreadyRegisteredException, EWrongLicenseUidOrPasswordException {
		if (licenseUid == null || licenseUid.trim().isEmpty())
			throw new IllegalArgumentException("licenseUid");
		if (password == null)
			throw new IllegalArgumentException("password");
		if (hostName == null || hostName.trim().isEmpty())
			throw new IllegalArgumentException("hostName");

		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				long licenseId = 0;
				String hostRegPass = null;
				String sql = "SELECT ID, HOST_REG_PASS\n"
						+ "FROM SU_LICENSE\n"
						+ "WHERE HARD_CODE = ?";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setString(1, licenseUid);
					try (ResultSet rs = stmt.executeQuery()) {
						if (rs.next()) {
							licenseId = rs.getLong("ID");
							hostRegPass = rs.getString("HOST_REG_PASS");
						}
					}
				}

				if (licenseId == 0) {

					checkDealerPassword(conn, licenseUid, password);

					licenseId = Common.generateTableId(conn);

					sql = "INSERT INTO SU_LICENSE (ID, HARD_CODE) VALUES (?, ?)";
					try (PreparedStatement stmt = conn.prepareStatement(sql)) {
						stmt.setLong(1, licenseId);
						stmt.setString(2, licenseUid);
						if (stmt.executeUpdate() == 0) throw new RuntimeException();
					}

				} else {

					if (hostRegPass != null) {
						if (!hostRegPass.equals(password)) {
							LOG.warn("Password mismatch (HOST_REG_PASS) (licenseUid: {})", licenseUid);
							throw new EWrongLicenseUidOrPasswordException();
						}
					} else {
						checkDealerPassword(conn, licenseUid, password);
					}

					sql = "SELECT ID, IS_ACTIVE FROM SU_HOST WHERE ID_SU_LICENSE = ? AND NAME = ? AND IS_ACTIVE > 0";
					try (PreparedStatement stmt = conn.prepareStatement(sql)) {
						stmt.setLong(1, licenseId);
						stmt.setString(2, hostName);
						try (ResultSet rs = stmt.executeQuery()) {
							if (rs.next())
								throw new EAlreadyRegisteredException();
						}
					}

				}

				long hostId = Common.generateTableId(conn);
				String hostGuid = commonHelper.generateGuid().replaceAll("-", "").toUpperCase();

				sql = "INSERT INTO SU_HOST (ID, ID_SU_LICENSE, NAME, GUID, REGISTRATION_DT, IS_ACTIVE)\n"
						+ "VALUES (?, ?, ?, ?, ?, 1)";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setLong(1, hostId);
					stmt.setLong(2, licenseId);
					stmt.setString(3, hostName);
					stmt.setString(4, hostGuid);
					stmt.setTimestamp(5, new Timestamp(commonHelper.getCurrentDate().getTime()));
					if (stmt.executeUpdate() == 0) throw new RuntimeException();
				}

				conn.commit();
				return hostGuid;
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
	}

	private void checkDealerPassword(Connection conn, String licenseUid, String password)
			throws SQLException, EWrongLicenseUidOrPasswordException {
		String sql = "SELECT DEALERS.PASS_HASH\n"
				+ "FROM LICENSES\n"
				+ "INNER JOIN DEALERS ON DEALERS.ID = LICENSES.ID_DEALERS\n"
				+ "WHERE LICENSES.HARD_CODE = ?\n"
				+ " AND LICENSES.IS_ACTIVE > 0 AND BIN_AND(LICENSES.STATUS, ?) <> 0";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setString(1, licenseUid);
			stmt.setShort(2, DbLicense.ONLINE_BIT);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next()) {
					LOG.warn("License is not found (licenseUid: {})", licenseUid);
					throw new EWrongLicenseUidOrPasswordException();
				}
				if (!Common.checkPassword(password, rs.getString("PASS_HASH"))) {
					LOG.warn("Password mismatch (licenseUid: {})", licenseUid);
					throw new EWrongLicenseUidOrPasswordException();
				}
			}
		}
	}

	@Override
	public List<HostTask> getHostTasks(String hostGuid, ShortSysInfo shortSysInfo,
			List<DeployedProduct> deployedProducts) throws EHostIsNotRegistered {
		if (hostGuid == null || hostGuid.trim().isEmpty())
			throw new IllegalArgumentException("hostGuid");

		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				// check hostGuid
				String sql = "UPDATE SU_HOST\n"
						+ "SET HEARTBEAT = ?, SHORT_SYS_INFO = ?"
						+ (deployedProducts != null ? ", DEPLOYED_PRODUCTS = ?\n" : "\n")
						+ "WHERE GUID = ? AND IS_ACTIVE > 0";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					int parNo = 1;
					stmt.setTimestamp(parNo++, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setString(parNo++, shortSysInfoToJson(shortSysInfo));
					if (deployedProducts != null)
						stmt.setString(parNo++, deployedProductsToJson(deployedProducts));
					stmt.setString(parNo++, hostGuid);
					if (stmt.executeUpdate() == 0)
						throw new EHostIsNotRegistered();
				}

				// update STATUS_SENT_DT
				sql = "UPDATE SU_SUBTASK SET STATUS_SENT_DT = ? WHERE STATUS < ?\n"
						+ " AND ID_SU_HOST IN (SELECT ID FROM SU_HOST WHERE GUID = ?)";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setTimestamp(1, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setInt(2, Status.SUCCESS.ordinal());
					stmt.setString(3, hostGuid);
					stmt.executeUpdate();
				}

				// get tasks
				List<HostTask> result = new ArrayList<>();
				sql = "SELECT ST.ID, ST.STATUS, TB.TASK_ACTION, TB.PARAMS\n"
						+ "FROM SU_SUBTASK ST\n"
						+ "INNER JOIN SU_TASK T ON T.ID = ST.ID_SU_TASK\n"
						+ "INNER JOIN SU_TASK_BUNDLE TB ON TB.ID = T.ID_SU_TASK_BUNDLE\n"
						+ "INNER JOIN SU_HOST H ON H.ID = ST.ID_SU_HOST\n"
						+ "WHERE H.GUID = ? AND ST.STATUS < ?";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setString(1, hostGuid);
					stmt.setInt(2, Status.SUCCESS.ordinal());
					try (ResultSet rs = stmt.executeQuery()) {
						while (rs.next()) {
							HostTask hostTask = new HostTask(rs.getLong("ID"));
							hostTask.setStatus(Status.values()[rs.getInt("STATUS")]);
							hostTask.setAction(rs.getString("TASK_ACTION"));
							if (hostTask.getStatus() == Status.CREATED || hostTask.getStatus() == Status.WAIT_FOR_EXECUTING)
								hostTask.setParams(paramsFromJson(rs.getString("PARAMS")));
							result.add(hostTask);
						}
					}
				}

				conn.commit();
				return result;
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public void setSubTaskStatus(String hostGuid, long subTaskId, Status status) {
		if (hostGuid == null || hostGuid.trim().isEmpty())
			throw new IllegalArgumentException("hostGuid");
		if (status == null)
			throw new IllegalArgumentException("status");
		
		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				// check hostGuid, subTaskId and current status
				if (status.ordinal() <= checkSubTaskAndGetStatus(conn, subTaskId, hostGuid))
					throw new IllegalArgumentException("status");

				String sql = "UPDATE SU_SUBTASK SET STATUS = ?, STATUS_CHANGE_DT = ?, STATUS_SENT_DT = ?\n"
						+ "WHERE ID = ?";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setInt(1, status.ordinal());
					stmt.setTimestamp(2, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setTimestamp(3, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setLong(4, subTaskId);
					if (stmt.executeUpdate() == 0) throw new RuntimeException();
				}

				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}

		if (status == Status.PREPARED)
			SuServerTimerTask.tickTaskDispatcher();
	}

	@Override
	public void setSubTaskResult(String hostGuid, long subTaskId, Result result) {
		if (hostGuid == null || hostGuid.trim().isEmpty())
			throw new IllegalArgumentException("hostGuid");
		if (result == null)
			throw new IllegalArgumentException("result");
		
		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				// check hostGuid, subTaskId and current status
				checkSubTaskAndGetStatus(conn, subTaskId, hostGuid);

				String sql = "UPDATE SU_SUBTASK SET STATUS = ?, STATUS_CHANGE_DT = ?, STATUS_SENT_DT = ?, RESULT = ?\n"
						+ "WHERE ID = ?";
				try (PreparedStatement stmt = conn.prepareStatement(sql)) {
					stmt.setInt(1, result.getExitCode() == 0 ? Status.SUCCESS.ordinal() : Status.FAILURE.ordinal());
					stmt.setTimestamp(2, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setTimestamp(3, new Timestamp(commonHelper.getCurrentDate().getTime()));
					stmt.setString(4, resultToJson(result));
					stmt.setLong(5, subTaskId);
					if (stmt.executeUpdate() == 0) throw new RuntimeException();
				}

				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		}
	}

	private int checkSubTaskAndGetStatus(Connection conn, long subTaskId, String hostGuid) throws SQLException {
		String sql = "SELECT ST.STATUS\n"
				+ "FROM SU_SUBTASK ST\n"
				+ "INNER JOIN SU_HOST H ON H.ID = ST.ID_SU_HOST\n"
				+ "WHERE H.GUID = ? AND H.IS_ACTIVE > 0 AND ST.ID = ?";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setString(1, hostGuid);
			stmt.setLong(2, subTaskId);
			try (ResultSet rs = stmt.executeQuery()) {
				if (!rs.next())
					throw new IllegalArgumentException("subTaskId|hostGuid");
				int status = rs.getInt("STATUS");
				if (status >= Status.SUCCESS.ordinal())
					throw new IllegalArgumentException("status");
				return status;
			}
		}
	}

	private Task getTaskFromResultSet(ResultSet rs) throws SQLException {
		Timestamp startDateTime = rs.getTimestamp("START_DATETIME");
		Task task = new Task(
				rs.getLong("ID_DEALERS"),
				rs.getTimestamp("CREATE_DATETIME").toInstant(),
				rs.getString("TITLE"),
				rs.getString("TASK_ACTION"),
				paramsFromJson(rs.getString("PARAMS")),
				startDateTime == null ? null : startDateTime.toInstant());
		task.setTaskId(rs.getLong("ID"));
		return task;
	}

}
