/**
 * 
 */
package eu.untill.license.server;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.jar.Manifest;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 *
 */
public class Manager extends HttpServlet {

	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException
	{
		req.setCharacterEncoding("utf-8");
		resp.setContentType("text/html");

		// Get settings parameters
		Settings settings = Common.getSettings();

		// Get form name
		String formName = req.getParameter("form-name");
		if (formName == null) formName = "";

		// Get login and password from parameters
		String login = req.getParameter("login");
		String password = req.getParameter("password");

		if (login == null || password == null) {
			// Get login and password from session
			login = (String) req.getSession().getAttribute("login");
			password = (String) req.getSession().getAttribute("password");
			if (login == null || password == null) {
				this.showLogonPage(resp.getWriter(), null);
				return ;
			}
		}

		// Check login and password
		if (!login.toLowerCase().equals("admin")
				|| !Common.checkPassword(password, settings.getAdminPasswordHash())) {
			this.showLogonPage(resp.getWriter(), "Username and password do not match!");
			return;
		}

		// Set session attributes
		req.getSession().setAttribute("login", login);
		req.getSession().setAttribute("password", password);

		// Get and handle "show" parameter
		String show = req.getParameter("show");
		if (show != null) {
			if (show.toLowerCase().equals("untillexceptlog")) {
				long maxLength = 10240;
				String maxLengthParam = req.getParameter("maxLength");
				try {maxLength = Long.parseLong(maxLengthParam);} catch (NumberFormatException e) {};
				this.showUntillExceptLog(resp.getWriter(), maxLength);
				return;
			} else if (show.toLowerCase().equals("serverlog")) {
				int maxCount = 20;
				String maxLengthParam = req.getParameter("maxCount");
				try {maxCount = Integer.parseInt(maxLengthParam);} catch (NumberFormatException e) {};
				this.showServerLog(resp.getWriter(), maxCount);
				return;
			}
		}

		// Handle generateNewKeys parameter
		String generateNewKeys = req.getParameter("generateNewKeys");
		if (generateNewKeys != null) {
			this.showGenerateNewKeysPage(resp.getWriter());
			return;
		}

		// generate keys
		if (formName.equals("generateNewKeys")) {
			try {
				String keyNameBase = req.getParameter("productName") + "." + req.getParameter("productVersion");
				String publicKeyFileName = keyNameBase + ".public.key";
				String privateKeyFileName = keyNameBase + ".private.key";
				Path publicKeyFilePath = Paths.get(settings.getKeysDir(), publicKeyFileName);
				Path privateKeyFilePath = Paths.get(settings.getKeysDir(), privateKeyFileName);
				byte[] pubKeyBytes;
				if (Files.exists(publicKeyFilePath)) {
					pubKeyBytes = Files.readAllBytes(publicKeyFilePath);
				} else {
					KeyPairGenerator keyGen = KeyPairGenerator.getInstance("DSA");
					SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
					keyGen.initialize(1024, random);
					KeyPair pair = keyGen.generateKeyPair();
					PrivateKey priv = pair.getPrivate();
					PublicKey pub = pair.getPublic();
					pubKeyBytes = pub.getEncoded();
					Files.write(publicKeyFilePath, pubKeyBytes);
					Files.write(privateKeyFilePath, priv.getEncoded());
				}

				resp.setContentType("application/octet-stream");
				resp.setHeader("Content-Disposition", "filename=\"" + publicKeyFileName + "\"");
				resp.setHeader("Content-Location", "/manager");
				resp.setContentLength(pubKeyBytes.length);
				ServletOutputStream os = resp.getOutputStream();
				os.write(pubKeyBytes);
				os.flush();
				return;
			} catch (Exception e) {
				this.showSetSettingsPage(resp.getWriter(), e.getMessage());
				return;
			}
		}

		// If parameters is not received then show set settings page
		if (formName.equals("logon") || !formName.equals("settings") ) {
			this.showSetSettingsPage(resp.getWriter(), null);
			return;
		}

		// Generate new settings
		synchronized (Common.getSettingsLock()) {
			Settings newSettings = new Settings(this.getServletContext());
	
			for (String parameterName : newSettings.getParameterNames()) {
				if (parameterName.equals("AdminPasswordHash")) {
					String newAdminPassword = req.getParameter(parameterName);
					if (newAdminPassword == null) {
						newSettings.set(parameterName, null);
					} else {
						if (!newAdminPassword.equals("********"))
							try {
								newSettings.set(parameterName, Common.getPasswordHash(newAdminPassword));
							} catch (NoSuchAlgorithmException e) {
								e.printStackTrace();
							}
					}
				} else if (parameterName.equals("NextInvoiceNumber")
						|| parameterName.equals("NextNegativeInvoiceNumber")
						|| parameterName.equals("EventsStorageLastSyncData")
						|| parameterName.equals("HwmNextMonthForInvoiceGeneration")) {
					if (req.getParameter(parameterName) != null)
						if (req.getParameter(parameterName).isEmpty())
							newSettings.set(parameterName, null);
						else
							newSettings.set(parameterName, req.getParameter(parameterName));
				} else {
					newSettings.set(parameterName, req.getParameter(parameterName));
				}
			}

			// If parameters is wrong then show set settings page with error message
			if (!newSettings.verify()) {
				this.showSetSettingsPage(resp.getWriter(), newSettings.getErrorMessage());
				return;
			}
	
			// Save settings
			newSettings.save();
			Common.setSettings(newSettings);
		}

		// Show success page
		PrintWriter out = resp.getWriter();

		addCommonHeader(out);

		out.println("<h3>Settings succesfully save</h3>");
		out.println("<p><a href=\"UntillLicenseServer.html\">unTill License Server</a></p>");
		out.println("<p><a href=\"manager\">unTill License Server Manager</a></p>");
		addCommonFooter(out);
	}

	private void addCommonHeader(PrintWriter out) {
		out.println("<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
		out.println("<html>");
		out.println(" <head>");
		out.println("  <title>unTill License Server Manager</title>");
		out.println(" </head>");
		out.println(" <body>");
		out.println("<h1>unTill License Server Manager</h1>");
	}

	private void addCommonFooter(PrintWriter out) {
		out.println(" </body>");
		out.println("</html>");
	}

	private void showLogonPage(PrintWriter out, String errorMessage) {
		addCommonHeader(out);
		if (errorMessage != null) {
			out.println("<p style=\"color: Red\">" + errorMessage + "</p>");
		}
		out.println("<form method=\"post\">");
		out.println("  <input type=\"hidden\" name=\"form-name\" value=\"logon\">");
		out.println("  <table><tbody>");
		out.println("    <tr><td>Login:</td><td><input type=\"text\" name=\"login\"/></td></tr>");
		out.println("    <tr><td>Password:</td><td><input type=\"password\" name=\"password\"/></td></tr>");

		out.println("  </tbody></table>");
		out.println("  <input type=\"submit\" value=\"Sign in\">");
		out.println("</form>");
		addCommonFooter(out);
	}

	private void showSetSettingsPage(PrintWriter out, String errorMessage) {
		Settings settings = Common.getSettings();
		addCommonHeader(out);

		out.print("  <script type=\"text/javascript\">\n" +
				"    function refreshParameter(parameter) {\n" +
				"      var disabled = !document.getElementById(parameter + \"-checkbox\").checked;\n" +
				"      document.getElementById(parameter + \"-label\").disabled = disabled;\n" +
				"      document.getElementById(parameter + \"-text\").disabled = disabled;\n" +
				"    }\n" +
				"  </script>");
		if (errorMessage != null) {
			out.println("<p style=\"color: Red\">" + errorMessage + "</p>");
		}

		// Show server version
		String ver = "local";
		InputStream is = getServletContext().getResourceAsStream("/META-INF/MANIFEST.MF");
		if (is != null)
			try {
				ver = new Manifest(is).getMainAttributes().getValue("Implementation-Version");
				if (ver == null) ver = "unknown";
			} catch (IOException e) {
				e.printStackTrace();
			}
		out.println("<div style=\"margin-bottom: 10px\">version: " + ver + "</div>");
		//out.println("<div>version: " + getClass().getPackage().getImplementationVersion() + "</div>");

		// Show links to get logs
		out.println("<div style=\"margin-bottom: 10px\"><a href=\"manager?show=untillExceptLog&maxLength=10240\">Show unTill except log</a></div>");
		out.println("<div style=\"margin-bottom: 10px\"><a href=\"manager?show=serverLog&maxCount=20\">Show server log</a></div>");
		out.println("<div style=\"margin-bottom: 20px; margin-top: 20px\"><a href=\"manager?generateNewKeys=1\">Generate new keys and download public key</a></div>");

		out.println("<h2>Settings</h2>");

		out.println("<form method=\"post\">");
		out.println("  <input type=\"hidden\" name=\"form-name\" value=\"settings\">");
		out.println("  <table><tbody>");

		// Generate Settings
		for (String parName : settings.getParameterNames()) {
			String parDesc = settings.getDescription(parName);
			boolean parReq = settings.getRequiredFlag(parName);
			String parVal = settings.get(parName);
			if (parVal == null) parVal = "";
			boolean parNull = settings.getUserDefinedValue(parName) == null;
			boolean parDisabled = false; // now is not used

			if (parName.equals("AdminPasswordHash")) {
				parVal = "********";
			}

			if (parName.equals("NextInvoiceNumber")
					|| parName.equals("NextNegativeInvoiceNumber")
					|| parName.equals("EventsStorageLastSyncData")
					|| parName.equals("HwmNextMonthForInvoiceGeneration")) {
				parNull = true;
			}

			out.print("    <tr>");
			out.print("<td><input id=\"" + parName + "-checkbox\" " + "type=\"checkbox\" " +
					(parNull ? "" : "checked=\"checked\" ") +
//					"style=\"width: 200px\" " +
					(parDisabled ? " disabled=\"disabled\"" : "") +
					"onchange='refreshParameter(\"" + parName + "\");'" +
					"onclick='refreshParameter(\"" + parName + "\");'></td>");
			out.print("<td><span id=\"" + parName + "-label\"" +
					(parDisabled || parNull ? " disabled=\"disabled\"" : "") + ">" + parDesc + ": </span></td>");
			out.print("<td>");
			if (parName.toLowerCase().contains("formula")) {
				out.print("<textarea id=\"" + parName + "-text\" rows=\"5\" cols=\"100\" name=\"" + parName + "\"" +
						(parDisabled || parNull ? " disabled=\"disabled\"" : "") + ">" +
						Common.htmlFilter(parVal) + "</textarea>");
			} else {
				out.print("<input id=\"" + parName + "-text\" type=\"" +
						(parName.toLowerCase().contains("password") ? "password" : 
							parName.toLowerCase().contains("formula") ? "textarea" : "text") + "\"" +
						"name=\"" + parName + "\" value=\"" + Common.htmlFilter(parVal) + "\"" +
						(parDisabled || parNull ? " disabled=\"disabled\"" : "") + ">");
			}
			if (parReq) {
				out.print("<span style=\"color: Red\"> *</span>");
			}
			out.print("</td>");
			out.println("</tr>");
		}

		out.println("  </tbody></table>");
		out.println("  <input type=\"submit\" value=\"Save\">");
		out.println("</form>");
		addCommonFooter(out);
	}

	private void showGenerateNewKeysPage(PrintWriter out) {
		addCommonHeader(out);

		out.println("<h2>Generate new keys</h2>");

		out.println("<form method=\"post\" action=\"manager\" onsubmit=\"return confirm('Do you really want to create a new key pair?');\">");
		out.println("  <input type=\"hidden\" name=\"form-name\" value=\"generateNewKeys\">");
		out.println("  <table><tbody>");

		out.print("    <tr>");
		out.print("<td><span id=\"productName-label\">Product name: </span></td>");
		out.print("<td><input id=\"productName-text\" type=\"text\" name=\"productName\" value=\"unTill\"></td>");
		out.print("<td align=\"right\"><span style=\"color: Red\">*</span></td>");
		out.println("</tr>");
		out.print("    <tr>");
		out.print("<td><span id=\"productVersion-label\">From version: </span></td>");
		out.print("<td><input id=\"productVersion-text\" type=\"text\" name=\"productVersion\" value=\"113\"></td>");
		out.print("<td align=\"right\"><span style=\"color: Red\">*</span></td>");
		out.println("</tr>");

		out.println("  </tbody></table>");
		out.println("  <input type=\"submit\" value=\"Save and download public key\">");
		out.println("</form>");

		out.println("<p><a href=\"UntillLicenseServer.html\">unTill License Server</a></p>");
		out.println("<p><a href=\"manager\">unTill License Server Manager</a></p>");

		addCommonFooter(out);
	}

	private void showUntillExceptLog(PrintWriter out, long maxLength) {
		addCommonHeader(out);
		out.println("<h2>unTill except.log</h2>");
		Settings settings = Common.getSettings();
		File untillLogsPath = new File(new File(settings.getUntillPath()).getParent(), "logs");
		File untillExceptLogFile = new File(untillLogsPath,
				"except" + new SimpleDateFormat(".yyyy-MM-dd").format(new Date()) + ".log");
		if (!untillExceptLogFile.exists())
			untillExceptLogFile = new File(untillLogsPath, "except.log");
		if (!untillExceptLogFile.exists()) {
			out.println("<p style=\"color: Red\">File (" + untillExceptLogFile.getPath() + ") is not exist</p>");
		} else {
			out.print("<pre>");
			try {
				FileInputStream inputStream = new FileInputStream(untillExceptLogFile);
				if (untillExceptLogFile.length() > maxLength)
					inputStream.skip(untillExceptLogFile.length() - maxLength);
				InputStreamReader reader = new InputStreamReader(inputStream);
				char[] buf = new char[4096];
				int len;
				while ((len = reader.read(buf)) != -1)
					out.write(buf, 0, len);
				reader.close();
				out.print("</pre>");
			} catch (FileNotFoundException e) {
				// TODO Auto-generated catch block
				out.print("</pre>");
				out.println("<p style=\"color: Red\">FileNotFoundException: (" + e.toString() + ")</p>");
				e.printStackTrace();
			} catch (IOException e) {
				out.print("</pre>");
				out.println("<p style=\"color: Red\">IOException: (" + e.toString() + ")</p>");
				e.printStackTrace();
			}
		}
		addCommonFooter(out);
	}

	private void showServerLog(PrintWriter out, int maxCount) {
		addCommonHeader(out);
		out.println("<h2>Server log</h2>");

		LogBufferAppender logBufferAppender = Common.getLogBufferAppender();
		if (logBufferAppender == null) {
			out.println("<p style=\"color: Red\">logBufferAppender == null</p>");
		} else {
			out.print("<pre>");
			out.print(logBufferAppender.getLog(maxCount));
			out.print("</pre>");
		}
		addCommonFooter(out);
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException
	{
		this.doGet(req, resp);
	}
}
