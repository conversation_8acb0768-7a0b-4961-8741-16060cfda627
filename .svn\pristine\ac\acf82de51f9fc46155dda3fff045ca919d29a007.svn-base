package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.json.client.JSONNumber;
import com.google.gwt.json.client.JSONObject;
import com.google.gwt.json.client.JSONParser;
import com.google.gwt.json.client.JSONString;
import com.google.gwt.json.client.JSONValue;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.SuSubTask;

public class SuSubtaskDialog extends DialogBox implements ClickHandler {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {

		@DefaultStringValue("View target")
		String viewSuSubTaskHeader();

		@DefaultStringValue("Target")
		String targetPanel();

		String clientNameLabel();
		String licenseUidLabel();
		String chainLabel();
		@DefaultStringValue("Computer")
		String hostLabel();
		@DefaultStringValue("Status")
		String statusLabel();
		@DefaultStringValue("Exit code")
		String exitCodeLabel();

		@DefaultStringValue("Result")
		String resultLabel();

		String okButton();
		String cancelButton();
		@DefaultStringValue("Close")
		String closeButton();

		@DefaultStringValue("Created")
		String suSubTaskCreatedStatus();
		@DefaultStringValue("Preparing")
		String suSubTaskPreparingStatus();
		@DefaultStringValue("Prepared")
		String suSubTaskPreparedStatus();
		@DefaultStringValue("WaitForExecuting")
		String suSubTaskWaitForExecutingStatus();
		@DefaultStringValue("Executing")
		String suSubTaskExecutingStatus();
		@DefaultStringValue("Success")
		String suSubTaskSuccessStatus();
		@DefaultStringValue("Failure")
		String suSubTaskFailureStatus();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	  /////////////
	 // Members //
	/////////////

	private SuSubTask subTask;

	  //////////////////
	 // Constructors //
	//////////////////

	public SuSubtaskDialog(SuSubTask subTask) {
		this.subTask = subTask;

		generateUI();

		this.setText(constants.viewSuSubTaskHeader());

		txtChain.setText(subTask.host.license.chain);
		txtClientName.setText(subTask.host.license.clientName);
		txtLicenseUid.setText(HardCodeHelper.formatHardCode(subTask.host.license.licenseUid));
		txtHost.setText(subTask.host.name);
		String status = subTask.status == SuSubTask.Status.CREATED ? constants.suSubTaskCreatedStatus()
				: subTask.status == SuSubTask.Status.PREPARING ? constants.suSubTaskPreparingStatus()
				: subTask.status == SuSubTask.Status.PREPARED ? constants.suSubTaskPreparedStatus()
				: subTask.status == SuSubTask.Status.WAIT_FOR_EXECUTING ? constants.suSubTaskWaitForExecutingStatus()
				: subTask.status == SuSubTask.Status.EXECUTING ? constants.suSubTaskExecutingStatus()
				: subTask.status == SuSubTask.Status.SUCCESS ? constants.suSubTaskSuccessStatus()
				: subTask.status == SuSubTask.Status.FAILURE ? constants.suSubTaskFailureStatus()
				: subTask.status != null ? subTask.status.name() : "";
		txtStatus.setText(status);

		fillResult();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnTarget = new EntitledDecoratorPanel(constants.targetPanel());
	private FlexTable tblTarget = new FlexTable();
	private Label lblChain = new Label(constants.chainLabel() + ":", false);
	private TextBox txtChain = new TextBox();
	private Label lblClientName = new Label(constants.clientNameLabel() + ":", false);
	private TextBox txtClientName = new TextBox();
	private Label lblLicenseUid = new Label(constants.licenseUidLabel() + ":", false);
	private TextBox txtLicenseUid = new TextBox();
	private Label lblHost = new Label(constants.hostLabel() + ":", false);
	private TextBox txtHost = new TextBox();
	private Label lblStatus = new Label(constants.statusLabel() + ":", false);
	private TextBox txtStatus = new TextBox();
	private Label lblExitCode = new Label(constants.exitCodeLabel() + ":", false);
	private TextBox txtExitCode = new TextBox();

	private EntitledDecoratorPanel dpnResult = new EntitledDecoratorPanel(constants.resultLabel());
	private HTML htmResult = new HTML();
	private ScrollPanel scrResults = new ScrollPanel(htmResult);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnClose = new Button(constants.closeButton(), this);

	private void generateUI() {

		// Assemble target panel
		txtChain.setReadOnly(true);
		lblClientName.setWidth("100px");
		txtClientName.setWidth("680px");
		txtClientName.setReadOnly(true);
		txtLicenseUid.setWidth("680px");
		txtLicenseUid.setReadOnly(true);
		txtHost.setReadOnly(true);
		txtStatus.setWidth("200px");
		txtStatus.setReadOnly(true);
		txtExitCode.setReadOnly(true);
		tblTarget.setWidth("100%");
		int row = 0;
		FlexCellFormatter tblTagetCF = tblTarget.getFlexCellFormatter();
		tblTarget.setWidget(row, 0, lblChain);
		tblTagetCF.setColSpan(row, 1, 2);
		tblTarget.setWidget(row++, 1, txtChain);
		tblTarget.setWidget(row, 0, lblClientName);
		tblTagetCF.setColSpan(row, 1, 2);
		tblTarget.setWidget(row++, 1, txtClientName);
		tblTarget.setWidget(row, 0, lblLicenseUid);
		tblTagetCF.setColSpan(row, 1, 2);
		tblTarget.setWidget(row++, 1, txtLicenseUid);
		tblTarget.setWidget(row, 0, lblHost);
		tblTagetCF.setColSpan(row, 1, 2);
		tblTarget.setWidget(row++, 1, txtHost);
		tblTarget.setWidget(row, 0, lblStatus);
		tblTagetCF.setColSpan(row, 1, 2);
		tblTarget.setWidget(row++, 1, txtStatus);
		tblTarget.setWidget(row, 0, lblExitCode);
		tblTagetCF.setColSpan(row, 1, 2);
		tblTarget.setWidget(row++, 1, txtExitCode);
		dpnTarget.setWidget(tblTarget);

		// Assemble targets panel with scroll
		scrResults.setSize("800px", "200px");
		dpnResult.setWidget(scrResults);

		// Assemble button panel
		pnlButtons.setWidth("100%");
		btnClose.setWidth("80px");
		Label lblSplitter = new Label(" ");
		pnlButtons.add(lblSplitter);
		pnlButtons.setCellWidth(lblSplitter, "100%");
		pnlButtons.add(btnClose);
		pnlButtons.setCellHorizontalAlignment(btnClose, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnTarget);
		mainPanel.add(dpnResult);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnClose) {

			this.hide();

		}

	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void fillResult() {

		if (subTask.result == null || subTask.result.isEmpty())
			return;

		try {
			JSONValue jsonValue = JSONParser.parseStrict(subTask.result);
			JSONObject jsonObjectResult = jsonValue.isObject();
			if (jsonObjectResult == null) throw new Exception("result == null");
			JSONNumber jsonNumberExitCode = jsonObjectResult.get("exitCode").isNumber();
			if (jsonNumberExitCode == null) throw new Exception("result.exitCode == null");
			JSONString jsonStringStdOut = jsonObjectResult.get("stdOut").isString();
			JSONString jsonStringStdErr = jsonObjectResult.get("stdErr").isString();

			txtExitCode.setText(Double.toString(jsonNumberExitCode.doubleValue()));

			htmResult.setHTML(stdOutStdErrAsHtml(
					jsonStringStdOut == null ? null : jsonStringStdOut.stringValue(),
					jsonStringStdErr == null ? null : jsonStringStdErr.stringValue()
			));
		} catch (Exception e) {
			ErrorDialog.show("Parsing result error", e);
		}

	}

	private SafeHtml stdOutStdErrAsHtml(String stdOut, String stdErr) {
		SafeHtmlBuilder builder = new SafeHtmlBuilder();
		if (stdErr != null) {
			builder.appendHtmlConstant("<span style=\"color:red;\">");
			builder.appendEscapedLines(stdErr);
			builder.appendHtmlConstant("</span>");
		}
		if (stdOut != null) {
			builder.appendEscapedLines(stdOut);
		}
		return builder.toSafeHtml();
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
	}

}
