package eu.untill.license.server;

import java.sql.Connection;
import java.util.TimerTask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LicenseTimerTask extends TimerTask {

	static final Logger LOG = LoggerFactory.getLogger(LicenseTimerTask.class);

	@Override
	public void run() {
		LOG.debug("ENTRY");

		try (Connection conn = Common.getConnection()) {

			// Generate and send license invoices
			try {
				Common.getInvoiceHandler().generateAndSend(conn);
			} catch (Exception e) {
				LOG.warn("invoice: generateAndSend", e);
			}

			// Auto invoicing (approving)
			try {
				Common.getLicenseHandler().autoApproving(conn);
			} catch (Exception e) {
				LOG.warn("license: autoApproving", e);
			}

			// Auto prolong (renewal)
			try {
				Common.getLicenseHandler().autoProlong(conn);
			} catch (Exception e) {
				LOG.warn("license: autoProlong", e);
			}

			// HWM invoice: prepare
			if (Common.getSettings().getHwmEnabledTasks().toLowerCase().contains("prepare")) {
				try {
					Common.getHwmInvoiceHandler().prepare(conn);
				} catch (Exception e) {
					LOG.warn("HWM invoice: prepare", e);
				}
			}

			// HWM invoice: order
			if (Common.getSettings().getHwmEnabledTasks().toLowerCase().contains("order")) {
				try {
					Common.getHwmInvoiceHandler().order(conn);
				} catch (Exception e) {
					LOG.warn("HWM invoice: order", e);
				}
			}

			// HWM invoice: generate and send
			if (Common.getSettings().getHwmEnabledTasks().toLowerCase().contains("send")) {
				try {
					Common.getHwmInvoiceHandler().generateAndSend(conn);
				} catch (Exception e) {
					LOG.warn("HWM invoice: generateAndSend", e);
				}
			}

		} catch (Exception e) {
			LOG.error("unexpected error", e);
		}

		LOG.debug("RETURN");
	}

}
