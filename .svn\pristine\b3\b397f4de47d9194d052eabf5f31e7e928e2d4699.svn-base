/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.UntillLicenseServerConstants;
import eu.untill.license.client.UntillLicenseServerImages;
import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.LicenseService.DbErrorException;
import eu.untill.license.client.LicenseService.InvalidArgumentException;
import eu.untill.license.client.LicenseService.ServerErrorException;

/**
 * <AUTHOR>
 *
 */
public class ErrorDialog {

	public static interface UlsMessages extends Messages {
		String authFailedHtmlMessage();
		String invalidArgumentHtmlMessage();
		String dbErrorHtmlMessage();
		String serverErrorHtmlMessage();
		String unknownErrorHtmlMessage();
	}

//	public interface UlsImages extends ImageBundle {
//		AbstractImagePrototype error();
//	}

	private static UntillLicenseServerConstants constants = UntillLicenseServer.getConstants();
	private static UlsMessages messages = UntillLicenseServer.getMessages();
	private static UntillLicenseServerImages images = UntillLicenseServer.getImages();

	private static DialogBox dialog = null;
	private static FlexTable table = null;

	private static Image imgPicture = null;
	private static HTML htmMessage = null;
	private static Button btnOk = null;

	public static void show(Throwable e) {
		show(null, e);
	}

	public static void show(String htmlMessage) {
		show(htmlMessage, null);
	}

	public static void show(String htmlMessage, Throwable e) {

		htmlMessage = prepareMessage(htmlMessage, e);

		if (dialog == null) {
			dialog = new DialogBox();
			table = new FlexTable();

			imgPicture = new Image(images.error());
			htmMessage = new HTML();
			btnOk = new Button(constants.okButton(), new ClickHandler() {
				//@Override
				public void onClick(ClickEvent event) {
					dialog.hide();
					dialog = null;
				}
			});

			dialog.setText("Error - " + constants.mainTitle());
			htmMessage.setHTML(htmlMessage);

			FlexCellFormatter formatter = table.getFlexCellFormatter();
			table.setWidget(0, 0, imgPicture);
			formatter.setVerticalAlignment(0, 0, HasVerticalAlignment.ALIGN_MIDDLE);
			table.setCellSpacing(5);
			table.setWidget(0, 1, htmMessage);
			formatter.setVerticalAlignment(0, 1, HasVerticalAlignment.ALIGN_MIDDLE);
			table.setWidget(1, 0, btnOk);
			table.getFlexCellFormatter().setColSpan(1, 0, 2);
			formatter.setHorizontalAlignment(1, 0, HasHorizontalAlignment.ALIGN_CENTER);

			dialog.setWidget(table);
			dialog.show();
			dialog.center();
		} else {
			// TODO add other errors into existing dialog
		}
	}

	private static String prepareMessage(String htmlMessage, Throwable e) {
		boolean showClass = true;

		if (htmlMessage == null) {
			if (e != null && e instanceof InvalidArgumentException) {
				if (e.getClass() == InvalidArgumentException.class) showClass = false;
				htmlMessage = messages.invalidArgumentHtmlMessage();
			} else if (e != null && e instanceof AuthFailedException) {
				if (e.getClass() == AuthFailedException.class) showClass = false;
				htmlMessage = messages.authFailedHtmlMessage();
			} else if (e != null && e instanceof ServerErrorException){
				if (e.getClass() == ServerErrorException.class) showClass = false;
				htmlMessage = messages.serverErrorHtmlMessage();
			} else if (e != null && e instanceof DbErrorException){
				if (e.getClass() == DbErrorException.class) showClass = false;
				htmlMessage = messages.dbErrorHtmlMessage();
			} else {
				htmlMessage = messages.unknownErrorHtmlMessage();
			}
			htmlMessage = "<h4>" + htmlMessage + "</h4>";
		} else {
			htmlMessage = "<div>" + htmlMessage + "</div>";
		}

		if (e != null) {
			if (showClass)
				htmlMessage += "<b>" + textToHtml(e.getClass().toString()) + "</b>";
			if (e.getMessage() != null) {
				if (showClass) htmlMessage += ": ";
				htmlMessage += textToHtml(e.getMessage());
			}
		}

		return htmlMessage;
	}

	private static String textToHtml(String text) {
		HTML html = new HTML();
		html.setText(text);
		return html.getHTML();
	}

}
