/**
 *
 */
package eu.untill.license.client.ui;

import java.util.Date;
import java.util.List;

import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.DecoratorPanel;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.Cell;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.LicenseService.RrDealer;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.UntillLicenseServerImages;
import eu.untill.license.client.ui.PageNavigator.PageChangeListener;
import eu.untill.license.shared.SuSubTask;
import eu.untill.license.shared.SuTask;
import eu.untill.license.shared.SuTaskBundle;
import eu.untill.license.shared.SuTaskListOrder;

/**
 * <AUTHOR>
 *
 */
public class SuTaskListWidget extends Composite implements ClickHandler, PageChangeListener
{

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String dealerLabel();

		@DefaultStringValue("New task")
		String createNewTaskButton();
		String refreshButton();

		String statusColumnHeader();
		@DefaultStringValue("Creation date/time")
		String createDatetimeColumnHeader();
		@DefaultStringValue("Title")
		String taskTitleColumnHeader();
		@DefaultStringValue("Start date/time")
		String startDatetimeColumnHeader();
		
		@DefaultStringValue("No tasks found")
		String tasksNotFound();

		String allDealersListItem();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UntillLicenseServerImages images = UntillLicenseServer.getUntillLicenseServerImages();

	  //////////////////
	 // Members (UI) //
	//////////////////

	private DecoratorPanel decoratorPanel = new DecoratorPanel();
	private VerticalPanel panel = new VerticalPanel();

	private HorizontalPanel toolBar = new HorizontalPanel();
	private Button btnCreateNewTask = new Button(new Image(images.blank()).toString()
			+ new Label(constants.createNewTaskButton()).toString(), this);
	private Button btnRefresh = new Button(new Image(images.refresh()).toString()
			+ new Label(constants.refreshButton()).toString(), this);

	private FlexTable grdFilter3 = new FlexTable();
	private Label lblDealer = new Label(constants.dealerLabel() + ": ", false);
	private ListBox lstDealers = new ListBox();

	List<SuTaskBundle> taskBundleList = null;

	private PageNavigator pageNavigator = new PageNavigator(20, this);

	private SortingListButtonGroup<SuTaskListOrder> tableSortingButtonGroup = new SortingListButtonGroup<>(
			() -> refresh());

	private FlexTable table = new FlexTable();

	private Date lastUpdateDealerList = null;

	  //////////////////
	 // Constructors //
	//////////////////

	public SuTaskListWidget() {

		setDealerList(null);

		lstDealers.addChangeHandler(new ChangeHandler() {
			@Override
			public void onChange(ChangeEvent event) {
				refresh();
			}
		});

		btnCreateNewTask.addStyleName("ToolBar-Button");
		btnRefresh.addStyleName("ToolBar-Button");

		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			grdFilter3.addStyleName("ToolBar-Object");
//			grdFilter3.setHeight("100%");
			grdFilter3.setWidget(0, 0, lblDealer);
			grdFilter3.setWidget(0, 1, lstDealers);
		}

		toolBar.setWidth("100%");
//		toolBar.setHeight("100%");
		toolBar.setVerticalAlignment(HasVerticalAlignment.ALIGN_MIDDLE);
		toolBar.setSpacing(2);
		if (!UntillLicenseServer.getLogonDealer().onlyProlong ||
				UntillLicenseServer.getLogonDealer().isSuperDealer) 
			toolBar.add(btnCreateNewTask);
		toolBar.add(btnRefresh);
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			toolBar.add(grdFilter3);
		}

		toolBar.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		Label splitter = new Label(" ");
		toolBar.add(splitter);
		toolBar.setCellWidth(splitter, "100%");
		toolBar.setVerticalAlignment(HasVerticalAlignment.ALIGN_BOTTOM);
		toolBar.add(pageNavigator);

		prepareTaskTable();

		panel.setWidth("100%");
		//panel.add(dpnDealer);
		panel.add(toolBar);
		panel.add(table);

		decoratorPanel.setWidget(panel);

		this.initWidget(decoratorPanel);
	}

	  ////////////////////
	 // Public methods //
	////////////////////

	public void refresh() {
		refresh(false);
	}

	public void refresh(boolean holdCurrentPage) {

		// Update dealer list (no more frequently than once every 5 minutes)
		if (UntillLicenseServer.getLogonDealer().isSuperDealer &&
				(lastUpdateDealerList == null ||
				new Date().getTime() - lastUpdateDealerList.getTime() > 5 * (1000*60))) {
			lastUpdateDealerList = new Date();

			// Call RPC
			UntillLicenseServer.getLicenseServiceAsync().getAllDealers(UntillLicenseServer.getAuthScheme(),
					new AsyncCallback<List<RrDealer>>() {
						@Override
						public void onFailure(Throwable caught) {
							ErrorDialog.show("Error get all dealer list", caught);
						}
						@Override
						public void onSuccess(List<RrDealer> result) {
							setDealerList(result);
						}
					});
		}

		// Call RPC LicenseService.getSuTaskBundleCount / getSuTaskBundleList
		final long dealerIdFilter;
		if (UntillLicenseServer.getLogonDealer().isSuperDealer) {
			if (lstDealers.getSelectedIndex() < 0)
				dealerIdFilter = UntillLicenseServer.getLogonDealer().id;
			else if (lstDealers.getSelectedIndex() == 0)
				dealerIdFilter = 0;
			else
				dealerIdFilter = getSelectedDealer().id;
		} else {
			dealerIdFilter = 0;
		}
		UntillLicenseServer.getLicenseServiceAsync().getSuTaskBundleCount(
				UntillLicenseServer.getAuthScheme(), dealerIdFilter, new AsyncCallback<Integer>() {
					@Override
					public void onFailure(Throwable caught) {
						WaitDialog.hideAll();
						ErrorDialog.show(caught);
					}
					@Override
					public void onSuccess(Integer result) {
						if (!holdCurrentPage) pageNavigator.setCurrentPage(0);
						pageNavigator.setItemCount(result);
						int from = pageNavigator.getBeginItemIndex() + 1;
						int to = pageNavigator.getEndItemIndex();
						UntillLicenseServer.getLicenseServiceAsync().getSuTaskBundleList(
								UntillLicenseServer.getAuthScheme(), dealerIdFilter,
								tableSortingButtonGroup.getSortingType(), from, to,
								new AsyncCallback<List<SuTaskBundle>>() {
									@Override
									public void onFailure(Throwable caught) {
										WaitDialog.hideAll();
										ErrorDialog.show(caught);
									}
									@Override
									public void onSuccess(List<SuTaskBundle> result) {
										try {
											taskBundleList = result;
											refreshTaskTable();
										} finally {
											WaitDialog.hide();
										}
									}
								});
					}
				});
		WaitDialog.show();
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	// ClickListener implementation
	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnRefresh) {

			this.refresh(true);

		} else if (sender == btnCreateNewTask) {

			// Create new task

			RrDealer curDealer;
			if (UntillLicenseServer.getLogonDealer().isSuperDealer
					&& lstDealers.getSelectedIndex() > 0)
				curDealer = getSelectedDealer();
			else
				curDealer = UntillLicenseServer.getLogonDealer();

			new SuTaskDialog(curDealer.id).show();

		} else if (sender == table) {

			Cell cell = table.getCellForEvent(event);
			if (cell != null
					&& cell.getRowIndex() > 0
					&& table.getFlexCellFormatter().getStyleName(cell.getRowIndex(),
							cell.getCellIndex()).equals("ll-CommonCell")) {
				SuTaskBundle taskBundle = ((StatusWidget) table.getWidget(cell.getRowIndex(), 0)).getTaskBundle();
				new SuTaskDialog(taskBundle).show();

			}
		}
	}

	@Override
	public void onChangePage() {
		refresh(true);
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private RrDealer[] arrDealers;

	private RrDealer getSelectedDealer() {
		if (lstDealers.getSelectedIndex() <= 0)
			return null;
		return arrDealers[Integer.parseInt(lstDealers.getValue(lstDealers.getSelectedIndex()))];
	}

	private void setDealerList(List<RrDealer> dealers) {
		if (dealers == null) {
			lstDealers.clear();
			dealers = null;
		} else {
			long selectedDealerId;
			if (lstDealers.getSelectedIndex() < 0) {
				selectedDealerId = UntillLicenseServer.getLogonDealer().id;
			} else {
				if (lstDealers.getSelectedIndex() == 0) selectedDealerId = 0;
				else selectedDealerId = getSelectedDealer().id;
			}
			lstDealers.clear();
			lstDealers.addItem(constants.allDealersListItem());
			if (selectedDealerId == 0) lstDealers.setSelectedIndex(0);
			arrDealers = new RrDealer[dealers.size()];
			int i = 0;
			boolean found = false;
			for (RrDealer dealer : dealers) {
				lstDealers.addItem(dealer.name, Integer.toString(i));
				arrDealers[i++] = dealer;
				if (selectedDealerId != 0 && selectedDealerId == dealer.id) {
					lstDealers.setSelectedIndex(lstDealers.getItemCount() - 1);
					found = true;
				}
			}
			lstDealers.setVisible(true);
			if (!found)
				refresh();
		}
	}

	private void prepareTaskTable() {
		table.addClickHandler(this);
		table.addStyleName("ll-Table");

		// Prepare table header
		table.getRowFormatter().addStyleName(0, "ll-Header");
		FlexCellFormatter tableCF = table.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "24px");
		table.setText(0, col++, constants.statusColumnHeader());
		tableCF.setWidth(0, col, "250");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(constants.createDatetimeColumnHeader(),
				SuTaskListOrder.BY_CREATE_DATETIME, SuTaskListOrder.BY_CREATE_DATETIME_DESC, true, true));
		tableCF.setWidth(0, col, "400");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(constants.taskTitleColumnHeader(),
				SuTaskListOrder.BY_TITLE, SuTaskListOrder.BY_TITLE_DESC));
		tableCF.setWidth(0, col, "150");
		table.setWidget(0, col++, tableSortingButtonGroup.createButton(constants.startDatetimeColumnHeader(),
				SuTaskListOrder.BY_START_DATETIME, SuTaskListOrder.BY_START_DATETIME_DESC));
	}

	private void refreshTaskTable() {
		panel.remove(table);
		try {
			clearTaskTable();
			fillTaskTable();
		} finally {
			panel.add(table);
		}
	}

	private void clearTaskTable() {
		for (int r = table.getRowCount() - 1; r > 0; --r)
			table.removeRow(r);
	}

	private void fillTaskTable() {
		RowFormatter tableRowFormatter = table.getRowFormatter();
		FlexCellFormatter tableCellFormatter = table.getFlexCellFormatter();

		// Add licenses into table
		if (taskBundleList.isEmpty()) {
			tableCellFormatter.setColSpan(1, 0, table.getCellCount(0));
			table.setText(1, 0, constants.tasksNotFound());
			tableCellFormatter.setAlignment(1, 0, HasHorizontalAlignment.ALIGN_CENTER,
					HasVerticalAlignment.ALIGN_MIDDLE);
		} else {
			int row = 1;

			for (SuTaskBundle taskBundle : taskBundleList) {

				//boolean superDealer = UntillLicenseServer.getLogonDealer().isSuperDealer;

				StatusWidget statusWidget = new StatusWidget(taskBundle);

				// set row style classes
				tableRowFormatter.addStyleName(row, "ll-Row");

				// TODO merge with tableCellFormatter
				FlexCellFormatter tableCF = table.getFlexCellFormatter();

				// fill row
				int col = 0;
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				table.setWidget(row, col++, statusWidget);
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				table.setText(row, col++, DateTimeFormat.getFormat(PredefinedFormat.DATE_TIME_SHORT).format(taskBundle.createDatetime));
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				table.setText(row, col++, taskBundle.title);
				tableCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				if (taskBundle.startDatetime == null)
					table.setText(row, col++, " ");
				else
					table.setText(row, col++, DateTimeFormat.getFormat(PredefinedFormat.DATE_TIME_MEDIUM).format(taskBundle.startDatetime));
				
				for (int i = 0; i < col; ++i)
					tableCF.setStyleName(row, i, "ll-CommonCell");

				++row;
			}
		}
	}

	  /////////////////////
	 // Private classes //
	/////////////////////

	private class StatusWidget extends Composite {
		SuTaskBundle taskBundle;
//		Image img;

		public StatusWidget(SuTaskBundle taskBundle) {
			this.taskBundle = taskBundle;

			int failureCount = 0, createdAndPreparingAndPreparedCount = 0, successCount = 0, otherCount = 0;
			for (SuTask task : taskBundle.tasks) {
				for (SuSubTask subTask : task.subTasks) {
					switch (subTask.status) {
					case FAILURE: failureCount++; break;
					case SUCCESS: successCount++; break;
					case CREATED: case PREPARING: case PREPARED: createdAndPreparingAndPreparedCount++; break;
					default: otherCount++;
					}
				}
			}

			String icon = "work";
			if (failureCount > 0)
				if (successCount == 0 && createdAndPreparingAndPreparedCount == 0 && otherCount == 0)
					icon = "error";
				else
					icon = "error_outline"; 
			else if (successCount > 0 && createdAndPreparingAndPreparedCount == 0 && otherCount == 0)
				icon = "done";
			else if (createdAndPreparingAndPreparedCount > 0 && successCount == 0 && otherCount == 0
					&& taskBundle.startDatetime != null)
				icon = "watch_later";
					
			this.initWidget(new HTML("<i class=\"material-icons\">" + icon + "</i>"));
		}

		public SuTaskBundle getTaskBundle() {
			return taskBundle;
		}
	}

}
