/**
 * 
 */
package eu.untill.license.server;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.TimeZone;
import java.util.Timer;

import javax.servlet.ServletContext;
import javax.xml.rpc.ServiceException;

import org.apache.commons.dbcp.ConnectionFactory;
import org.apache.commons.dbcp.DriverManagerConnectionFactory;
import org.apache.commons.dbcp.PoolableConnectionFactory;
import org.apache.commons.dbcp.PoolingDataSource;
import org.apache.commons.pool.impl.GenericObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.Gson;
import com.tpapipos.ITPAPIPOS;
import com.tpapipos.ITPAPIPOSserviceLocator;
import com.untill.su.server.TaskDispatcher;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.Context;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.DbLicenseOnline;
import eu.untill.license.shared.License;
import eu.untill.license.shared.ProlongParams;

/**
 * <AUTHOR>
 *
 */
public final class Common {
	private Common() { }

	static final Logger LOG = LoggerFactory.getLogger(Common.class);

	private static LogBufferAppender logBufferAppender;

	private static ServletContext servletContext = null;

	private static PoolingDataSource dataSource = null;

	private static LicenseHandler licenseHandler = null;
	private static OnlineLicenseHandler onlineLicenseHandler = null;
	private static DeployerEngineHandler deployerEngineHandler = null;
	private static SuTaskHandler suTaskHandler = null;
	private static SuHandler suHandler = null;
	private static TaskDispatcher taskDispatcher = null;

	private static InvoiceHandler invoiceHandler = null;
	private static HwmInvoiceHandler hwmInvoiceHandler = null;

	private static FiscalHandler fiscalHandler = null;

	private static Timer invoiceGeneratorTimer = null;

	private static Timer eventsStorageSyncTimer = null;

	private static Timer suServerTimer = null;

	private static Object settingsLock = new Object();

	private static ThreadLocal<String> requestUrl = new ThreadLocal<String>();


	  ////////////
	 // Public //
	////////////

	/**
	 * Common initialization
	 * Must be executed before any other
	 * @param servletContext
	 */
	public static void init(ServletContext servletContext) {
		// initialize logger
		initLogger();

		// Logging entering
		LOG.debug("ENTRY");
		LOG.info("Starting unTill License Server: {}", servletContext.getServerInfo());

		Common.servletContext = servletContext;

		// load settings
		Settings settings;
		synchronized (getSettingsLock()) {
			settings = new Settings(servletContext);
			setSettings(settings);
		}

		// initialize data source
		initDataSource();

		// Temporary fix articles names
		fixArticlesNames();

		CommonHelper commonHelper = new CommonHelperImpl();
		EmailHelper emailHelper = new EmailHelperImpl();
		InvoiceHelper invoiceHelper = new InvoiceHelperImpl();
		UntillLicensesPluginHelper untillLicensesPluginHelper = new UntillLicensesPluginHelperImpl();
		UntillTpapiHelper untillTpapiHelper = new UntillTpapiHelperImpl(commonHelper, untillLicensesPluginHelper);
		FiscalHelper fiscalHelper = new FiscalHelperImpl(commonHelper);

		// create business logic objects
		licenseHandler = new LicenseHandler(
				commonHelper,
				emailHelper,
				untillLicensesPluginHelper,
				invoiceHelper,
				untillTpapiHelper,
				fiscalHelper
		);
		onlineLicenseHandler = new OnlineLicenseHandler(
				commonHelper
		);
		deployerEngineHandler = new DeployerEngineHandler(
		);
		suTaskHandler = new SuTaskHandler(
				commonHelper
		);
		suHandler = new SuHandler(
				commonHelper
		);
		taskDispatcher = new TaskDispatcher(
				suTaskHandler
		);
		invoiceHandler = new InvoiceHandler(
				commonHelper,
				emailHelper,
				invoiceHelper
		);
		hwmInvoiceHandler = new HwmInvoiceHandler(
				commonHelper,
				emailHelper,
				invoiceHelper,
				untillTpapiHelper
		);
		fiscalHandler = new FiscalHandler();

		// create invoice generator timer
		invoiceGeneratorTimer = new Timer();
		invoiceGeneratorTimer.schedule(new LicenseTimerTask(),
				settings.getInvoiceGeneratorPeriod() * 1000 / 4,
				settings.getInvoiceGeneratorPeriod() * 1000);

		// load JDBC drivers for Events Storage and create synchronization timer
		try {
			Class.forName(settings.getEventsStorageJdbcDriver());
			eventsStorageSyncTimer = new Timer();
			eventsStorageSyncTimer.schedule(new EventsStorageSyncTimerTask(),
					Math.round(settings.getEventsStorageSyncPeriod() * 1000 * 60 * 60 / 4),
					Math.round(settings.getEventsStorageSyncPeriod() * 1000 * 60 * 60));
		} catch (ClassNotFoundException e) {
			LOG.error("Error loading Events Storage JDBC drivers", e);
		}

		// create SU server timer
		suServerTimer = new Timer();
		suServerTimer.schedule(new SuServerTimerTask(),
				15 * 1000 / 4,
				15 * 1000); // TODO change to 60 seconds

		// Logging exiting
		LOG.debug("RETURN");
	}

	private static void fixArticlesNames() {
		try (Connection conn = Common.getConnection()) {
			try (PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM ARTICLES\n"
					+ "WHERE (NAME LIKE '% one year%' OR NAME LIKE '% for 3 years%')\n"
					+ "AND IS_ACTIVE = 1\n"
			)) {
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next() || rs.getInt(1) < 100)
						return;
				}
			}
			try (PreparedStatement stmt = conn.prepareStatement("UPDATE ARTICLES\n"
					+ "SET NAME = REPLACE(REPLACE(NAME, ' one year', ''), ' for 3 years', ''),\n"
					+ "  INTERNAL_NAME = REPLACE(REPLACE(INTERNAL_NAME, ' one year', ''), ' for 3 years', ''),\n"
					+ "  PC_TEXT = REPLACE(REPLACE(PC_TEXT, ' one year', ''), ' for 3 years', ''),\n"
					+ "  OMAN_TEXT = REPLACE(REPLACE(OMAN_TEXT, ' one year', ''), ' for 3 years', '')\n"
					+ "WHERE (NAME LIKE '% one year%' OR NAME LIKE '% for 3 years%')\n"
					+ "AND IS_ACTIVE = 1\n"
			)) {
				stmt.execute();
			}
			conn.commit();
		} catch (SQLException e) {
			LOG.error("Error fixing articles names", e);
		}
	}

	/**
	 * Common destroy
	 */
	public static void destroy() {
		LOG.debug("ENTRY");

		
		// destroy timers
		suServerTimer.cancel();
		suServerTimer = null;
		if (eventsStorageSyncTimer != null) {
			eventsStorageSyncTimer.cancel();
			eventsStorageSyncTimer = null;
		}
		invoiceGeneratorTimer.cancel();
		invoiceGeneratorTimer = null;

//		dataSource = null;

//		servletContext.removeAttribute("settings");

		LOG.debug("RETURN");
	}

	public static Object getSettingsLock() {
		return settingsLock;
	}
	/**
	 * @return the actual context settings
	 */
	public static Settings getSettings() {
//		if (servletContext == null) {
//			if (logger.isLoggable(Level.SEVERE))
//				LOG.error("ServletContext is not initialized");
//			throw new RuntimeException("ServletContext is not initialized");
//		}
		synchronized (servletContext) {
			return (Settings) servletContext.getAttribute("settings");
		}
	}

	public static void setSettings(Settings settings) {
		synchronized (servletContext) {
			servletContext.setAttribute("settings", settings);
		}
	}

	/**
	 * @return the connection from pool
	 * @throws SQLException
	 */
	public static Connection getConnection() throws SQLException {
		//DriverManager.setLogStream(System.out);
		synchronized (dataSource) {
			return dataSource.getConnection();
		}
		//return DriverManager.getConnection("***************************************");
	}

	/**
	 * @return business logic object LicenseHandler
	 */
	public static LicenseHandler getLicenseHandler() {
		return licenseHandler;
	}

	/**
	 * @return business logic object OnlineLicenseHandler
	 */
	public static OnlineLicenseHandler getOnlineLicenseHandler() {
		return onlineLicenseHandler;
	}

	/**
	 * @return business logic object AgentCommandHandler
	 */
	public static DeployerEngineHandler getAgentCommandHandler() {
		return deployerEngineHandler;
	}

	/**
	 * @return business logic object SuTaskHandler
	 */
	public static SuTaskHandler getSuTaskHandler() {
		return suTaskHandler;
	}

	/**
	 * @return business logic object SuHandler
	 */
	public static SuHandler getSuHandler() {
		return suHandler;
	}

	/**
	 * @return business logic object TaskDispatcher
	 */
	public static TaskDispatcher getTaskDispatcher() {
		return taskDispatcher;
	}

	/**
	 * @return business logic object InvoiceHandler
	 */
	public static InvoiceHandler getInvoiceHandler() {
		return invoiceHandler;
	}

	/**
	 * @return business logic object HwmInvoiceHandler
	 */
	public static HwmInvoiceHandler getHwmInvoiceHandler() {
		return hwmInvoiceHandler;
	}

	/**
	 * @return business logic object FiscalHandler
	 */
	public static FiscalHandler getFiscalHandler() {
		return fiscalHandler;
	}

	/**
	 * 
	 * @return LogBufferAppender object
	 */
	public static LogBufferAppender getLogBufferAppender() {
		return logBufferAppender;
	}

//	public static DataSource getDataSource() {
//		return dataSource;
//	}

	public static ITPAPIPOS getTPTPIPOS() throws ServiceException {
		Settings settings = Common.getSettings();
		Integer tpapiSoapPort;
		try {
			tpapiSoapPort = settings.getTpapiSoapPort();
			if (tpapiSoapPort == null)
				return null;
		} catch (IOException e) {
			LOG.error("Error reading unTill port number", e);
			return null;
		}
		ITPAPIPOSserviceLocator locator = new ITPAPIPOSserviceLocator();
		locator.setITPAPIPOSPortEndpointAddress("http://localhost:" +
				Integer.toString(tpapiSoapPort) + "/soap/ITPAPIPOS");
		return locator.getITPAPIPOSPort();
	}

	public static ServletContext getServletContext() {
		return servletContext; 
	}

	public static void setServletRequestUrl(String url) {
		requestUrl.set(url);
	}

	public static String getServletRequestUrl() {
		return requestUrl.get();
	}



	  /////////////
	 // Private //
	/////////////

	private static void initLogger() {
		LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
		for (ch.qos.logback.classic.Logger logbackLogger: context.getLoggerList()) {
			for (Iterator<Appender<ILoggingEvent>> index = logbackLogger.iteratorForAppenders(); index.hasNext();) {
				Appender<ILoggingEvent> enumElement = index.next();
				if (enumElement instanceof LogBufferAppender) {
					logBufferAppender = (LogBufferAppender) enumElement;
				}
			}
		}
		if (logBufferAppender == null) {
			logBufferAppender = new LogBufferAppender();
			logBufferAppender.setContext((Context) LoggerFactory.getILoggerFactory());
			logBufferAppender.start();
			ch.qos.logback.classic.Logger logbackLogger = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger("eu.untill.license");
			logbackLogger.addAppender(logBufferAppender);
			logbackLogger.setLevel(ch.qos.logback.classic.Level.ALL);
		}
	}

	private static void initDataSource() {
		// Get settings
		Settings settings = Common.getSettings();

		// Loading JDBC drivers
		try {
			// Loading underlying JDBC driver
			Class.forName(settings.getJDBCDriverClassName());
			// Loading DBCP JDBC driver
			Class.forName("org.apache.commons.dbcp.PoolingDriver");
		} catch (ClassNotFoundException e) {
			LOG.warn("Error loading JDBC drivers", e);
		}

		// Set up data source
		GenericObjectPool<Connection> connectionPool = new GenericObjectPool<Connection>();
		//ConnectionFactory connectionFactory = new DriverManagerConnectionFactory(JDBCUrl, JDBCUsername, JDBCPassword);
		Properties connectionProperties = new Properties();
		connectionProperties.put("user", settings.getJDBCUsername());
		connectionProperties.put("password", settings.getJDBCPassword());
		connectionProperties.put("encoding", "UNICODE_FSS");
		ConnectionFactory connectionFactory = new DriverManagerConnectionFactory(settings.getJDBCUrl(), connectionProperties);
		//PoolableConnectionFactory poolableConnectionFactory =
		//new PoolableConnectionFactory(connectionFactory, connectionPool, null, null, false, true);
		new PoolableConnectionFactory(connectionFactory, connectionPool,
				null, // KeyedObjectPoolFactory stmtPoolFactory
				"SELECT COUNT(*) FROM LICENSES", // validationQuery
				Arrays.asList("EXECUTE PROCEDURE SET_LOGGING_ON"), // connectionInitSqls
				false, // defaultReadOnly
				false // defaultAutoCommit
		);
		dataSource = new PoolingDataSource(connectionPool);
	}

	public static DbLicense getDbLicenseFromResultSet(ResultSet rs) throws SQLException {
		DbLicense result = new DbLicense();
		result.id = rs.getLong("ID");
		result.hardCode = rs.getString("HARD_CODE");
		result.hardCode = result.hardCode == null ? "" : result.hardCode.trim();

		fillLicenseFromResultSet(rs, result);

		result.dealerComments = rs.getString("DEALER_COMMENTS");
		result.dealerComments = result.dealerComments == null ? "" : result.dealerComments.trim();
		result.idDealers = rs.getLong("ID_DEALERS");
		result.requestType = rs.getString("REQUEST_TYPE");
		result.requestType = result.requestType == null ? "" : result.requestType.trim();
		result.nonProlongable = rs.getShort("NON_PROLONGABLE") != 0;
		result.unconfirmed = rs.getShort("REQUEST_ACTIVE") != 0 && rs.getShort("IS_ACTIVE") == 0;
		result.emergencyAvailable = rs.getShort("EMERGENCY_AVAILABLE");
		result.nonApproved = rs.getShort("NON_APPROVED");
		result.invoiceRequired = rs.getShort("INVOICE_REQUIRED") != 0;
		result.invoiceDate = rs.getTimestamp("INVOICE_DATE");
		result.invoiceNumber = rs.getLong("INVOICE_NUMBER");
		result.tempExpiredDate = Common.toUtcDateOnly(rs.getDate("TEMP_EXPIRED_DATE"));

		result.availableReinstallations = rs.getInt("AVAILABLE_REINSTALLATIONS");
		result.installationTime = rs.getTimestamp("INSTALLATION_TIME");

		result.chain = rs.getString("CHAIN");

		result.autoProlongable = rs.getShort("AUTO_PROLONGABLE") != 0;

		String prolongParamsJson = rs.getString("PROLONG_PARAMS");
		if (prolongParamsJson == null) {
			result.prolongParams = null;
		} else {
			Gson gson = new Gson();
			result.prolongParams = gson.fromJson(prolongParamsJson, ProlongParams.class);
		}

		result.minVersion = rs.getInt("MIN_VERSION");

		result.requestLargeAccount = rs.getShort("REQUEST_LARGE_ACCOUNT") != 0;
		result.largeAccount = rs.getShort("LARGE_ACCOUNT") != 0;

		result.hwmYearly = rs.getShort("HWM_YEARLY") != 0;
		result.hwmFirst = rs.getShort("HWM_FIRST") != 0;

		return result;
	}

	public static void fillLicenseFromResultSet(ResultSet rs, License license) throws SQLException {
		license.setClientName(rs.getString("CLIENT_NAME"));
		license.setClientName(license.getClientName() == null ? "" : license.getClientName().trim());
		license.setClientDisplayName(rs.getString("CLIENT_DISPLAY_NAME"));
		license.setClientAddress(rs.getString("CLIENT_ADDRESS"));
		license.setClientAddress(license.getClientAddress() == null ? "" : license.getClientAddress().trim());
		license.setClientEmail(rs.getString("CLIENT_EMAIL"));
		license.setClientEmail(license.getClientEmail() == null ? "" : license.getClientEmail().trim());
		license.setClientPhone(rs.getString("CLIENT_PHONE"));
		license.setClientPhone(license.getClientPhone() == null ? "" : license.getClientPhone().trim());
		license.setLbConnections(rs.getShort("LB_CONNECTIONS"));
		license.setRemConnections((short) (rs.getShort("REM_CONNECTIONS")
				+ rs.getShort("BO_CONNECTIONS") + rs.getShort("POS_CONNECTIONS")));
		license.setOmConnections(rs.getShort("OM_CONNECTIONS"));
//		license.setHqConnections(rs.getShort("HQ_CONNECTIONS"));
		license.setPsConnections(rs.getShort("PS_CONNECTIONS"));
		license.setHeConnections(rs.getShort("HE_CONNECTIONS"));
		license.setStartDate(Common.toUtcDateOnly(rs.getDate("START_DATE")));
		license.setIssueDate(Common.toUtcDateOnly(rs.getDate("ISSUE_DATE")));
		license.setExpiredDate(Common.toUtcDateOnly(rs.getDate("EXPIRED_DATE")));
		license.setFuncLimited(rs.getLong("FUNC_LIMITED"));
		// Special behavior for BEVERAGE_CONTROL (BERG, BECONET, FRIJADO)
		if ((1l << License.FUNC_BEVERAGE_CONTROL & license.getFuncLimited()) != 0
				|| (1l << License.FUNC_BERG & license.getFuncLimited()) != 0
				|| (1l << License.FUNC_BECONET & license.getFuncLimited()) != 0
				|| (1l << License.FUNC_FRIJADO & license.getFuncLimited()) != 0) {
			license.setFuncLimited(license.getFuncLimited() | 1l << License.FUNC_BEVERAGE_CONTROL);
			license.setFuncLimited(license.getFuncLimited() | 1l << License.FUNC_BERG);
			license.setFuncLimited(license.getFuncLimited() | 1l << License.FUNC_BECONET);
			license.setFuncLimited(license.getFuncLimited() | 1l << License.FUNC_FRIJADO);
		}
		license.setStatus(rs.getShort("STATUS"));
		license.setDefinitiveMaxVersion(rs.getInt("DEFINITIVE_MAX_VERSION"));

		
		ExtraData extraData = ExtraData.loadFromString(rs.getString("EXTRA_DATA"));
		license.setFiscal(extraData == null ? null : extraData.getFiscal());
	}

	public static String getExtraDataJsonFromLicense(License license) {
		if (license.getFiscal() == null)
			return null;
		ExtraData extraData = new ExtraData();
		extraData.setFiscal(license.getFiscal());
		return ExtraData.saveToString(extraData);
	}

	public static DbLicenseOnline getDbLicenseOnlineFromResultSet(ResultSet rs) throws SQLException {
		DbLicenseOnline result = new DbLicenseOnline();
		result.setId(rs.getLong("ID"));
		result.setIdLicenses(rs.getLong("ID_LICENSES"));
		result.setPartialActiveSignature(reduceString(rs.getString("ACTIVE_SIGNATURE")));
		result.setActiveProductName(rs.getString("ACTIVE_PRODUCT_NAME"));
		result.setActiveProductVersion(rs.getInt("ACTIVE_PRODUCT_VERSION"));
		result.setActiveDatabaseInfo(rs.getString("ACTIVE_DATABASE_INFO"));
		result.setActivationTime(rs.getTimestamp("ACTIVATION_TIME"));
		result.setActivationCount(rs.getInt("ACTIVATION_COUNT"));
		result.setActivationPeriodBegin(rs.getTimestamp("ACTIVATION_PERIOD_BEGIN"));
		result.setActivationPeriodEnd(rs.getTimestamp("ACTIVATION_PERIOD_END"));
		result.setFailureCount(rs.getInt("FAILURE_COUNT"));
		result.setLastFailureTime(rs.getTimestamp("LAST_FAILURE_TIME"));
		result.setLastFailureCount(rs.getInt("LAST_FAILURE_COUNT"));
		result.setLastFailureField(rs.getString("LAST_FAILURE_FIELD"));
		result.setLastFailureData(rs.getString("LAST_FAILURE_DATA"));
		return result;
	}

	// TODO Move to Order constructor
	public static Order getOrderByBillId(Connection conn, long billId) throws SQLException {
		Order result;

		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {

			// Get VAT type
			boolean priceExcludesVat = false;
			try (
				PreparedStatement stmt1 = conn.prepareStatement("SELECT VAT_TYPE FROM RESTAURANT_VARS");
				ResultSet rs1 = stmt1.executeQuery();
			) {
				if (rs1.next())
					priceExcludesVat = rs1.getInt("VAT_TYPE") != 0;
			}

			result = new Order();
			
			// Get license dealer base parameters
			stmt = conn.prepareStatement("SELECT I.ID, I.KIND, I.QUANTITY, I.TEXT, "
					+ "A.ID ARTICLE_ID, A.ARTICLE_NUMBER, A.NAME, I.PRICE, I.ORIGINAL_PRICE, "
					+ "I.VAT, I.NEGATIVE, I.VAT_PERCENT\n"
					+ "FROM BILL B\n"
					+ "INNER JOIN ORDERS O ON O.ID_BILL = B.ID\n"
					+ "INNER JOIN ORDER_ITEM I ON I.ID_ORDERS = O.ID\n"
					+ "LEFT OUTER JOIN ARTICLES A ON I.ID_ARTICLES = A.ID\n"
					+ "WHERE B.ID = ?\n"
					+ "ORDER BY I.ID\n");
			stmt.setLong(1, billId);
			rs = stmt.executeQuery();

			Order.Item orderItem = null;

			while (rs.next()) {
				int kind = rs.getInt("KIND");
				if (kind == 1 || orderItem == null) {

					if (orderItem != null)
						addOrderItemToOrder(result, orderItem);

					orderItem = new Order.Item();
					orderItem.quantity = rs.getInt("QUANTITY");
					orderItem.articleId = rs.getLong("ARTICLE_ID");
					orderItem.articleNumber = rs.getInt("ARTICLE_NUMBER");
					orderItem.articleName = rs.getString("NAME");
					orderItem.price = rs.getBigDecimal("PRICE");
					orderItem.originalPrice = rs.getBigDecimal("ORIGINAL_PRICE");
					orderItem.vat = rs.getDouble("VAT");
					orderItem.negative = rs.getShort("NEGATIVE") != 0;
					orderItem.vatPercent = rs.getBigDecimal("VAT_PERCENT");

					double vatPercentAsDecimal = orderItem.vatPercent.doubleValue() / 100.0;
					double vatIfPriceExcludesVat = orderItem.price.doubleValue() * vatPercentAsDecimal;
					double vatIfPriceIncludesVat = vatIfPriceExcludesVat / (vatPercentAsDecimal + 1.0);
					double vatDeltaIfPriceExcludesVat = Math.abs(orderItem.vat - vatIfPriceExcludesVat);
					double vatDeltaIfPriceIncludesVat = Math.abs(orderItem.vat - vatIfPriceIncludesVat);
					orderItem.priceExcludesVat = vatDeltaIfPriceExcludesVat < vatDeltaIfPriceIncludesVat;
					double vatUlp = Math.ulp(orderItem.vat.doubleValue());
					if (vatDeltaIfPriceExcludesVat <= vatUlp && vatDeltaIfPriceIncludesVat <= vatUlp)
						orderItem.priceExcludesVat = priceExcludesVat;
					else {
						orderItem.priceExcludesVat = vatDeltaIfPriceExcludesVat < vatDeltaIfPriceIncludesVat;
						if (vatDeltaIfPriceExcludesVat > vatUlp && vatDeltaIfPriceIncludesVat > vatUlp)
							LOG.warn("Impossible to determine VAT type for order item (id={}, price={}, vatPercent={}, vat={},"
									+ " vatIfPriceExcludesVat={}, vatIfPriceIncludesVat={})", 
									rs.getLong("ID"), orderItem.price, orderItem.vatPercent, orderItem.vat,
									vatIfPriceExcludesVat, vatIfPriceIncludesVat);
					}
					if (orderItem.priceExcludesVat != priceExcludesVat) {
						LOG.info("The VAT type of order item does not match Restaurant setting (id={}, price={}, "
								+ "vatPercent={}, vat={})", rs.getLong("ID"), orderItem.price, orderItem.vatPercent, orderItem.vat);
					}

				} else if (kind == 3) { // Message

					// if a option exists in current the order item
					if (orderItem.optionList != null && !orderItem.optionList.isEmpty()) {
						// then add text into this option
						Order.Item.Option opt = 
							orderItem.optionList.get(orderItem.optionList.size() - 1);
						if (opt.text == null || opt.text.isEmpty())
							opt.text = rs.getString("TEXT");
						else
							opt.text += "\r\n" + rs.getString("TEXT");
					} else {
						// else, add text into current the order item
						if (orderItem.text == null || orderItem.text.isEmpty())
							orderItem.text = rs.getString("TEXT");
						else
							orderItem.text += "\r\n" + rs.getString("TEXT");
					}

				} else { // Option (and other)

					Order.Item.Option option = new Order.Item.Option();
					option.quantity = rs.getInt("QUANTITY");
					option.articleId = rs.getLong("ARTICLE_ID");
					option.articleNumber = rs.getInt("ARTICLE_NUMBER");
					option.articleName = rs.getString("NAME");
					option.price = rs.getBigDecimal("PRICE");
					option.originalPrice = rs.getBigDecimal("ORIGINAL_PRICE");
					option.vat = rs.getDouble("VAT");
					option.negative = rs.getShort("NEGATIVE") != 0;
					option.vatPercent = rs.getBigDecimal("VAT_PERCENT");

//					// Consolidate options
//					Order.Item.Option equalOption = null;
//					if (orderItem.optionList == null) {
//						orderItem.optionList = new ArrayList<Order.Item.Option>();
//					} else {
//						for (Order.Item.Option curOption : orderItem.optionList) {
//							if (curOption.articleId == option.articleId
//							//		&& option.articleNumber == curOption.articleNumber
//							//		&& option.articleName == curOption.articleName
//									&& curOption.price.compareTo(option.price) == 0
//									&& curOption.originalPrice.compareTo(option.originalPrice) == 0
//							//		&& curOption.vat == option.vat
//									&& curOption.negative == option.negative
//									&& curOption.vatPercent.compareTo(option.vatPercent) == 0
//									&& compareStrings(curOption.text, option.text) == 0) {
//								equalOption = curOption;
//								break;
//							}
//						}
//					}
//					if (equalOption != null) {
//						equalOption.quantity += option.quantity;
//					} else {
					orderItem.optionList.add(option);
//					}

				} // if (kind == 1 ... else if ... else
			} // while (rs.next())
			if (orderItem != null)
				addOrderItemToOrder(result, orderItem);

		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}

		return result;
	}

	private static void addOrderItemToOrder(Order order, Order.Item orderItem) {
		Order.Item equalOrderItem = null;
		if (order.itemList == null) {
			order.itemList = new ArrayList<Order.Item>();
		} else {
			// Find equal order item
			for (Order.Item curOrderItem : order.itemList) {
				if (curOrderItem.articleId != orderItem.articleId) continue;
				//if (curOrderItem.articleNumber != orderItem.articleNumber) continue;
				//if (curOrderItem.articleName != orderItem.articleName) continue;
				if (curOrderItem.price.compareTo(orderItem.price) != 0) continue;
				if (curOrderItem.originalPrice.compareTo(orderItem.originalPrice) != 0) continue;
				//if (curOrderItem.vat != orderItem.vat) continue;
				if (curOrderItem.negative != orderItem.negative) continue;
				if (curOrderItem.vatPercent.compareTo(orderItem.vatPercent) != 0) continue;
				if (compareStrings(curOrderItem.text, orderItem.text) != 0) continue;
				if ((curOrderItem.optionList != null) != (orderItem.optionList != null)) continue;
				if (curOrderItem.optionList == null) {
					equalOrderItem = curOrderItem;
					break;
				}
				if (curOrderItem.optionList.size() != orderItem.optionList.size()) continue;
				int i;
				for (i = 0; i < curOrderItem.optionList.size(); ++i) {
					Order.Item.Option option = curOrderItem.optionList.get(i);
					Order.Item.Option curOption = orderItem.optionList.get(i);
					if (option.articleId != curOption.articleId
					//		|| option.articleNumber != curOption.articleNumber
					//		|| option.articleName != curOption.articleName
							|| option.price.compareTo(curOption.price) != 0
							|| option.originalPrice.compareTo(curOption.originalPrice) != 0
					//		|| option.vat != curOption.vat
							|| option.negative != curOption.negative
							|| option.vatPercent.compareTo(curOption.vatPercent) != 0
							|| compareStrings(option.text, curOption.text) != 0) {
						break;
					}
				}
				if (i >= curOrderItem.optionList.size()) {
					equalOrderItem = curOrderItem;
					break;
				}
			}
		} // if (result.orderItemList == null) ... else ...

		if (equalOrderItem != null) {
			equalOrderItem.quantity += orderItem.quantity;
			if (equalOrderItem.optionList != null) {
				for (int i = 0; i < equalOrderItem.optionList.size(); ++i) {
					equalOrderItem.optionList.get(i).quantity +=
						orderItem.optionList.get(i).quantity;
				}
			}
		} else {
			order.itemList.add(orderItem);
		}

	}

	public static long generateTableId(Connection conn) throws SQLException {
		try (PreparedStatement stmt = conn.prepareStatement("SELECT * FROM GENERATE_TABLE_ID\n");
				ResultSet rs = stmt.executeQuery()) {
			if (!rs.next()) throw new RuntimeException();
			return rs.getLong(1);
		}
	}

	  ///////////////////////////////////////
	 // Static independent common methods //
	///////////////////////////////////////

	public static int compareStrings(String str1, String str2) {
		if (str1 == null) {
			if (str2 == null) return 0;
			return Integer.MIN_VALUE;
		} else {
			if (str2 == null) return Integer.MAX_VALUE;
			return str1.compareTo(str2);
		}
	}

	public static Date getOnlyDate(Date date) {
		Calendar result = Calendar.getInstance();
		result.setTime(date);
		result.set(Calendar.MILLISECOND, 0);
		result.set(Calendar.SECOND, 0);
		result.set(Calendar.MINUTE, 0);
		result.set(Calendar.HOUR_OF_DAY, 0);
		return result.getTime();
	}

	public static Date toUtcDateOnly(Date localDate) {
		if (localDate == null)
			return null;

		Calendar localCalendar = Calendar.getInstance();
		localCalendar.setTime(localDate);

		Calendar utcCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		utcCalendar.set(localCalendar.get(Calendar.YEAR), localCalendar.get(Calendar.MONTH),
				localCalendar.get(Calendar.DATE), 0, 0, 0);
		utcCalendar.set(Calendar.MILLISECOND, 0);

		return utcCalendar.getTime();
	}

	public static Date toLocalDateOnly(Date utcDate) {
		if (utcDate == null)
			return null;

		Calendar utcCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		utcCalendar.setTime(utcDate);

		Calendar localCalendar = Calendar.getInstance();
		localCalendar.set(utcCalendar.get(Calendar.YEAR), utcCalendar.get(Calendar.MONTH),
				utcCalendar.get(Calendar.DATE), 0, 0, 0);
		localCalendar.set(Calendar.MILLISECOND, 0);

		return localCalendar.getTime();
	}

	public static Date dateAddDays(Date date, int days) {
		Calendar utcCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		utcCalendar.setTime(date);
		utcCalendar.add(Calendar.DATE, days);
		return utcCalendar.getTime();
	}

	public static LocalDate convDateToLocalDate(Date date) {
		return date.toInstant().atZone(ZoneOffset.UTC).toLocalDate();
	}

//	// !!! unused, untried !!!
//	public static int compareDouble(double dbl1, double dbl2) {
//		if (dbl1 == dbl2) return 0;
//		if (Double.isNaN(dbl1) || dbl1 == Double.POSITIVE_INFINITY || dbl2 == Double.NEGATIVE_INFINITY) 
//			return +1;
//		if (Double.isNaN(dbl2) || dbl2 == Double.POSITIVE_INFINITY || dbl1 == Double.NEGATIVE_INFINITY) 
//			return -1;
//		Double dif = dbl1 - dbl2;
//		//Double eps = Math.ulp(Math.max(Math.abs(dbl1), Math.abs(dbl2)));
//		Double eps1 = Math.ulp(1.0);
//		if (dif > eps) return +1;
//		if (dif < -eps) return -1;
//		return 0;
//	}

	/**
	 * HTML filter utility. (Based on http://svn.apache.org/repos/asf/tomcat/trunk/webapps/examples/WEB-INF/classes/util/HTMLFilter.java)
	 * 
	 * Filter the specified message string for characters that are sensitive
	 * in HTML. This avoids potential attacks caused by including JavaScript
	 * codes in the request URL that is often reported in error messages.
	 *
	 * @param message The message string to be filtered
	 */
	public static String htmlFilter(String message) {

		if (message == null)
			return (null);

		char content[] = new char[message.length()];
		message.getChars(0, message.length(), content, 0);
		StringBuffer result = new StringBuffer((int) (content.length * 1.5));
		for (int i = 0; i < content.length; i++) {
			switch (content[i]) {
			case '<':
				result.append("&lt;");
				break;
			case '>':
				result.append("&gt;");
				break;
			case '&':
				result.append("&amp;");
				break;
			case '"':
				result.append("&quot;");
				break;
			default:
				result.append(content[i]);
			}
		}
		return result.toString();

	}

	public static String getStringFromFile(String fileName) throws IOException {
		return getStringFromFile(new File(fileName));
	}
	public static String getStringFromFile(File file) throws IOException {
		StringBuffer sb = new StringBuffer();
		BufferedReader in = null;
		String s = null;
		in = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"));
		try {
			while ((s = in.readLine()) != null) {
				sb.append(s);
				sb.append("\n");
			}
		} finally {
			in.close();
		}
		return sb.toString();
	}

	public static boolean checkPassword(String password, String passHash) {

		if (password == null || passHash == null)
			return false;

		// Generate password hash (MD5)
		String verifiable_pass_hash;
		try {
			verifiable_pass_hash = Common.getPasswordHash(password);
		} catch (NoSuchAlgorithmException e) {
			LOG.error("getPasswordHash failed", e);
			return false;
		}

		return verifiable_pass_hash.equalsIgnoreCase(passHash);
	}

	public static String getPasswordHash(String password) throws NoSuchAlgorithmException {
		// Generate password hash (MD5)
		MessageDigest md5 = MessageDigest.getInstance("MD5");
		md5.update(password.getBytes());
		return BytesToHexString(md5.digest());
	}

	private static String BytesToHexString(byte[] bytes) {
		StringBuilder result = new StringBuilder();
		for (Byte b : bytes) {
			int ib = (b + 0x100) & 0xff;
			if (ib < 0x10) result.append("0");
			result.append(Integer.toHexString(ib));
		}
		return result.toString();
	}

	public static class ExtraField {
		public String name;
		public short dataType;
		public String valAsString;
	}

	public static Set<ExtraField> getExtraFileds(Connection conn, long idExtraFieldValues) throws SQLException {
		Set<ExtraField> result = new HashSet<ExtraField>();
		if (idExtraFieldValues == 0)
			return result;

		PreparedStatement stmt = null;
		ResultSet rs = null;
		try {
			stmt = conn.prepareStatement("SELECT D.NAME, D.DATATYPE, I.VAL\n"
					+ "FROM EXTRA_FIELD_VALUES V\n"
					+ "INNER JOIN EXTRA_FIELD_ITEMS I ON I.ID_EXTRA_FIELD_VALUES = V.ID\n"
					+ "INNER JOIN EXTRA_FIELD_DEFS D ON I.ID_EXTRA_FIELD_DEFS = D.ID\n"
					+ "WHERE V.ID = ?\n");
			stmt.setLong(1, idExtraFieldValues);
			rs = stmt.executeQuery();
			while (rs.next()) {
				ExtraField extraField = new ExtraField();
				extraField.name = rs.getString("NAME").trim();
				extraField.valAsString = getExtraFieldValue(rs.getBlob("VAL"), rs.getShort("DATATYPE"));
				result.add(extraField);
			}
		} finally {
			if (rs != null) { try { rs.close(); } catch (SQLException e) { } rs = null; }
			if (stmt != null) { try { stmt.close(); } catch (SQLException e) { } stmt = null; }
		}
		return result;
	}
	
	private static String getExtraFieldValue(Blob val, short datatype) throws SQLException {
		if (val == null)
			return null;
		switch (datatype) {
		case 0: // String
			String result = "";
			if (val.length() > 4) {
				int strSize = byteArrayToInt(val.getBytes(1, 4));
				try {
					result = new String(val.getBytes(1, strSize + 4), 4, strSize, "UTF-8");
				} catch (UnsupportedEncodingException e) {
					throw new RuntimeException(e);
				}
			}
			return result;
		default:
			return null;
		}
	}

	private static int byteArrayToInt(byte[] b) {
		return ByteBuffer.wrap(b).order(ByteOrder.LITTLE_ENDIAN).getInt();
	}

	public static List<String> parseEmailList(String emailList) {
		ArrayList<String> result = new ArrayList<String>();
		String[] emailArray = emailList.split("\\,");
		for (String email : emailArray) {
			email = email.trim();
			if (email.isEmpty()) continue;
			result.add(email);
		}
		return result;
	}

	public static String reduceString(String s) {
		if (s == null || s.length() < 20)
			return s;
		return s.substring(0, 8) + "..." + s.substring(s.length() - 8);
	}

	public static String fixFileName(String fileName) {
		if (fileName == null)
			return null;
		String result = fileName.trim().replaceAll("[\\\\/:*?\"<>|]", "_");
		if (result.matches("(?i)\\.|\\.\\.|CON|PRN|AUX|CLOCK\\$|NUL|COM\\d|LPT\\d"))
			result = "_" + result;
		return result;
	}
}
