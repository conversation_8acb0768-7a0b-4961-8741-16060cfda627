<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="eu.untill.license.server.UntillTpapiHelperImplTest" tests="23" skipped="0" failures="0" errors="0" timestamp="2025-08-06T13:09:01.462Z" hostname="UNP-WS2" time="0.032">
  <properties/>
  <testcase name="testLAddLicenseArticles_AllConnectionTypes" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.019"/>
  <testcase name="testLAddLicenseArticles_PaymentIntegrations" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_ComplexLicense" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_NeverExpiredStatus" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_IntegrationFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_SkippedFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_BasicLicense" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_LatestIntegrations" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_ModuledLicense" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testCalcMonthsAndDaysBetween" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_NewerIntegrations" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_ProlongMode" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_UnlimitedStatus" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_BeverageFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_ReserveFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_TPAPIFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_WarehouseManagement_NonHosted" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_ApostillNoEFTInterface" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_AnnouncerKS" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_ApostillLicense" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <testcase name="testLAddLicenseArticles_CoreFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_WarehouseManagementFunctions" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.0"/>
  <testcase name="testLAddLicenseArticles_UnknownFunction" classname="eu.untill.license.server.UntillTpapiHelperImplTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
