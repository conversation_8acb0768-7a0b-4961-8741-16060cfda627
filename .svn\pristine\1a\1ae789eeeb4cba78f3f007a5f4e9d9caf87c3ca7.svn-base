package eu.untill.license.client.ui;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

public class SortingListButtonGroup<T> {
	private SortingListButton activeSortingListButton;
	private Runnable refreshCallback;

	private T sortingType;

	public SortingListButtonGroup(Runnable refreshCallback) {
		this.refreshCallback = refreshCallback;
	}

	public Widget createButton(String columnName, T ascSortingType, T descSortingType) {
		return createButton(columnName, "", ascSortingType, descSortingType, false, false);
	}

	public Widget createButton(String columnName, T ascSortingType, T descSortingType, boolean descent, boolean active) {
		return createButton(columnName, "", ascSortingType, descSortingType, descent, active);
	}

	public Widget createButton(String columnName, String columnHint, T ascSortingType, T descSortingType) {
		return createButton(columnName, columnHint, ascSortingType, descSortingType, false, false);
	}

	public Widget createButton(String columnName, String columnHint, T ascSortingType, T descSortingType,
			boolean descent, boolean active) {
		return new SortingListButton(columnName, columnHint, ascSortingType, descSortingType, descent, active);
	}

	public T getSortingType() {
		return sortingType;
	}

	private class SortingListButton extends Label implements ClickHandler {
		private T ascSortingType, descSortingType;
		private boolean descent = false;

		public SortingListButton(String columnName, String columnHint, T ascSortingType, T descSortingType,
				boolean descent, boolean active) {
			super(columnName);
			if (!columnHint.isEmpty()) this.setTitle(columnHint);
			this.ascSortingType = ascSortingType;
			this.descSortingType = descSortingType;
			this.descent = descent;
			if (active)
				activate();
			addClickHandler(this);
			addStyleName("ll-HeaderButton");
		}

		private void activate() {
			this.setText(this.getText() + (descent ? " \u25BC" : " \u25B2")); // add arrow to caption
			activeSortingListButton = this;
			sortingType = descent ? descSortingType : ascSortingType;
		}

		private void deactivate() {
			this.setText(this.getText().substring(0, this.getText().length() - 2)); // remove arrow from caption
			activeSortingListButton = null;
			sortingType = null;
		}

		@Override
		public void onClick(ClickEvent event) {
			if (activeSortingListButton != null) {
				if (activeSortingListButton == this) {
					this.deactivate();
					descent = !descent;
				} else {
					activeSortingListButton.deactivate();
				}
			}

			this.activate();

			if (refreshCallback != null)
				refreshCallback.run();
		}
	}
}
