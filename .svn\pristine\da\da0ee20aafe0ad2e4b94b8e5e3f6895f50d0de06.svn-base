/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.user.client.ui.DockPanel;
import com.google.gwt.user.client.ui.Frame;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.gwt.user.client.ui.VerticalPanel;

/**
 * <AUTHOR>
 *
 */
public class LoginPage {

	private DockPanel pnlBase = new DockPanel();
	VerticalPanel titlePanel = new VerticalPanel();
	private Frame rssFrame = new Frame("https://feed.mikle.com/widget/v2/79086/");
	private TitleWidget titleWidget = new TitleWidget();
	private LoginWidget loginWidget = new LoginWidget();
	private Label footerWidget = new Label();
	
	  //////////////////
	 // Constructors //
	//////////////////

	private LoginPage() {
		rssFrame.getElement().setPropertyString("height", "207px");
		rssFrame.getElement().setPropertyString("width", "100%");
		rssFrame.getElement().setClassName("fw-iframe");
		rssFrame.getElement().setPropertyString("scrolling", "no");
		rssFrame.getElement().setPropertyString("style", "border:none;");
		titlePanel.setWidth("100%");
		titlePanel.add(titleWidget);
		titlePanel.add(rssFrame);
		pnlBase.setWidth("100%");
		pnlBase.setHeight("100%");
		pnlBase.add(titlePanel, DockPanel.NORTH);
		pnlBase.add(loginWidget, DockPanel.CENTER);
		pnlBase.add(footerWidget, DockPanel.SOUTH);
		pnlBase.setCellHorizontalAlignment(loginWidget, HasHorizontalAlignment.ALIGN_CENTER);
		pnlBase.setCellVerticalAlignment(loginWidget, HasVerticalAlignment.ALIGN_MIDDLE);
		pnlBase.setCellWidth(loginWidget, "100%");
		pnlBase.setCellHeight(titlePanel, "33%");
		pnlBase.setCellHeight(loginWidget, "33%");
		pnlBase.setCellHeight(footerWidget, "33%");
	}

	  ////////////////////
	 // Public methods //
	////////////////////

	public void show() {
		titleWidget.refresh();
		loginWidget.refresh();

		RootPanel rootPanel = RootPanel.get();
		rootPanel.clear();
		rootPanel.setHeight("100%");
		rootPanel.add(pnlBase);
	}

	public void refresh() {
		titleWidget.refresh();
		loginWidget.refresh();
	}

	  //////////////////////////////
	 // Singleton implementation //
	//////////////////////////////

	private static LoginPage ref = null;

	public static LoginPage get() {
		if (ref == null)
			ref = new LoginPage();
		return ref;
	}

	public static void clear() {
		if (ref != null)
			ref = null;
	}

//	public Object clone() throws CloneNotSupportedException	{
//		throw new CloneNotSupportedException();
//	}
}
