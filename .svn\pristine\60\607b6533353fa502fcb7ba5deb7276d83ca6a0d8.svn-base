<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee 
              http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         version="2.5"
         xmlns="http://java.sun.com/xml/ns/javaee">

  <!-- Default page to serve -->
  <welcome-file-list>
    <welcome-file>UntillLicenseServer.html</welcome-file>
  </welcome-file-list>

  <!-- Servlets -->
  <servlet>
    <servlet-name>licenseServlet</servlet-name>
    <servlet-class>eu.untill.license.server.LicenseServiceImpl</servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet-mapping>
    <servlet-name>licenseServlet</servlet-name>
    <url-pattern>/untilllicenseserver/license</url-pattern>
  </servlet-mapping>

  <!-- additional mapping to support requests Apostill -->
  <servlet-mapping>
    <servlet-name>licenseServlet</servlet-name>
    <url-pattern>/license</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>managerServlet</servlet-name>
    <servlet-class>eu.untill.license.server.Manager</servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet-mapping>
    <servlet-name>managerServlet</servlet-name>
    <url-pattern>/manager</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>suServlet</servlet-name>
    <servlet-class>eu.untill.license.server.SuServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>suServlet</servlet-name>
    <url-pattern>/su/*</url-pattern>
  </servlet-mapping>

</web-app>
