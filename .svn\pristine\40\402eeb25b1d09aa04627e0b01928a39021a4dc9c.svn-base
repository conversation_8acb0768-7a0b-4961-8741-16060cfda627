/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.i18n.client.Constants;
import com.google.gwt.user.client.ui.Widget;

/**
 * <AUTHOR>
 *
 */
public class SuperDealerWidget extends Widget {

	public static interface UlsConstants extends Constants {
	}

//	public static interface UlsMessages extends Messages {
//	}

//	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
//	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

}
