package eu.untill.license.server;

import static org.junit.Assert.*;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.Month;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CyclicBarrier;

import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.runners.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import TPAPIPosTypesU.TOrderItem;
import eu.untill.license.server.InvoiceHelper.Invoice;
import eu.untill.license.server.TestDb.TestDbParams;

@RunWith(MockitoJUnitRunner.class)
public class HwmInvoiceHandlerTest {

	@Mock private CommonHelper commonHelperMock;
	@Mock private EmailHelper emailHelperMock;
	@Mock private Settings settingsMock;
	@Mock private InvoiceHelper invoiceHelper;
	@Mock private UntillTpapiHelper untillTpapiHelper;
	@InjectMocks private HwmInvoiceHandler hwmInvoiceHandler;

	@Rule public final TestDb testDb;
	public HwmInvoiceHandlerTest() throws Exception {
		testDb = TestDb.getInstance();
	}

	@Test
	public void prepare_noWork() throws Exception {
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(commonHelperMock.getCurrentLocalDate()).thenReturn(LocalDate.of(2017, Month.JANUARY, 11));
		when(settingsMock.getHwmNextMonthForInvoiceGeneration()).thenReturn("2017-01");

		hwmInvoiceHandler.prepare(null);

		verify(commonHelperMock).getSettings();
		verify(settingsMock).getHwmNextMonthForInvoiceGeneration();
		verify(commonHelperMock).getCurrentLocalDate();
		verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock);
	}

	@Test
	@TestDbParams(
			init = {"base", "common", "hwm-1-2-licenses"},
			check = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016"})
	public void prepare_test1() throws Exception {
		when(commonHelperMock.getCurrentLocalDate()).thenReturn(LocalDate.of(2017, Month.JANUARY, 1));
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(settingsMock.getHwmNextMonthForInvoiceGeneration()).thenReturn("2016-01");

		hwmInvoiceHandler.prepare(testDb.getConnection());

		verify(commonHelperMock, atLeastOnce()).getSettings();
		verify(settingsMock).getHwmNextMonthForInvoiceGeneration();
		verify(settingsMock).setHwmNextMonthForInvoiceGeneration("2017-01");
		verify(commonHelperMock).getCurrentLocalDate();
		verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock);
	}

	@Test//(timeout = 1000)
	@TestDbParams(
			init = {"base", "common", "hwm-1-2-licenses", },
			check = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016"},
			nextId = 5000000001L)
	public void prepare_concurrency() throws Exception {
		when(commonHelperMock.getCurrentLocalDate()).thenReturn(LocalDate.of(2017, Month.JANUARY, 1));
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(settingsMock.getHwmNextMonthForInvoiceGeneration()).thenReturn("2016-06");

		Thread threads[] = new Thread[3];
		final CyclicBarrier gate = new CyclicBarrier(threads.length + 1);
		for (int i = 0; i < threads.length; ++i) {
			threads[i] = new Thread() {
				public void run() {
					try (Connection conn = testDb.getNewConnection()) {
						gate.await();
						hwmInvoiceHandler.prepare(conn);
					} catch (InterruptedException | BrokenBarrierException | SQLException e) {
						e.printStackTrace();
					}
				};
			};
			threads[i].start();
		}
		gate.await(); // start prepare in all threads
		for (Thread thread: threads)
			thread.join(0);

		verify(commonHelperMock, atLeastOnce()).getSettings();
		verify(settingsMock, atLeastOnce()).getHwmNextMonthForInvoiceGeneration();
		verify(settingsMock, atLeastOnce()).setHwmNextMonthForInvoiceGeneration("2017-01");
		verify(commonHelperMock, atLeastOnce()).getCurrentLocalDate();
		verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock);
	}

	@Test
	@TestDbParams(
			init = {"base", "common", "hwm-1-2-licenses", "hwm-3-licenses", "hwm-1-2-invoices-2016"},
			check = {"base", "common", "hwm-1-2-licenses", "hwm-3-licenses", "hwm-1-2-invoices-2016", "hwm-3-invoices-2016"},
			nextId = 6000000001L)
	public void prepare_test2() throws Exception {
		when(commonHelperMock.getCurrentLocalDate()).thenReturn(LocalDate.of(2017, Month.JANUARY, 1));
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(settingsMock.getHwmNextMonthForInvoiceGeneration()).thenReturn("2016-01");

		hwmInvoiceHandler.prepare(testDb.getConnection());

		verify(commonHelperMock, atLeastOnce()).getSettings();
		verify(settingsMock, atLeastOnce()).getHwmNextMonthForInvoiceGeneration();
		verify(settingsMock).setHwmNextMonthForInvoiceGeneration("2017-01");
		verify(commonHelperMock, atLeastOnce()).getCurrentLocalDate();
		verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock);
	}

	@Test(expected = SQLException.class)
	public void prepare_wrongDb() throws Exception {
		when(commonHelperMock.getCurrentLocalDate()).thenReturn(LocalDate.of(2017, Month.JANUARY, 1));
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(settingsMock.getHwmNextMonthForInvoiceGeneration()).thenReturn("2016-HZ");
		try (Connection wrongDbConn = DriverManager.getConnection("jdbc:h2:mem:")) {
			hwmInvoiceHandler.prepare(wrongDbConn);
		} finally {
			verify(commonHelperMock, atLeastOnce()).getSettings();
			verify(settingsMock).getHwmNextMonthForInvoiceGeneration();
			verify(commonHelperMock, times(2)).getCurrentLocalDate();
			verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock);
		}
	}

	@Test
	@TestDbParams(init = {"base", "common", "hwm-1-2-licenses"})
	public void order_noWork() throws Exception {
		hwmInvoiceHandler.order(testDb.getConnection());
		verifyZeroInteractions(commonHelperMock, emailHelperMock, settingsMock);
	}

	@Test
	@TestDbParams(
			init = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016"},
			check = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016-ordered"})
	public void order_test1() throws Exception {
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(settingsMock.getHwmHqPriceFormula()).thenReturn("20 - Math.max((quantity - 1) / 2, 10)");
		when(settingsMock.getHwmCentralBoPriceFormula()).thenReturn("10 - Math.max((quantity - 1) / 4, 5)");
		when(untillTpapiHelper.orderAndPay(any(), any(), anyLong())).thenReturn(1L);

		hwmInvoiceHandler.order(testDb.getConnection());

		verify(commonHelperMock, atLeastOnce()).getSettings();
		verify(settingsMock, atLeastOnce()).getHwmHqPriceFormula();
		verify(settingsMock, atLeastOnce()).getHwmCentralBoPriceFormula();
		verify(untillTpapiHelper, atLeastOnce()).getArticleIdByBarcode(eq(testDb.getConnection()), eq("HWMCBO1M"));
		verify(untillTpapiHelper, atLeastOnce()).getArticleIdByBarcode(eq(testDb.getConnection()), eq("HWMHQ1M"));
		verify(untillTpapiHelper, atLeastOnce()).orderAndPay(eq(testDb.getConnection()), anyListOf(TOrderItem.class), eq(1L));
		verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock, untillTpapiHelper);
	}

	@Test
	@TestDbParams(init = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016"})
	public void generateAndSend_noWork1() throws Exception {
		hwmInvoiceHandler.generateAndSend(testDb.getConnection());
		verifyZeroInteractions(commonHelperMock, emailHelperMock, settingsMock, untillTpapiHelper);
	}

	@Test
	@TestDbParams(init = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016-sent"})
	public void generateAndSend_noWork2() throws Exception {
		hwmInvoiceHandler.generateAndSend(testDb.getConnection());
		verifyZeroInteractions(commonHelperMock, emailHelperMock, settingsMock, untillTpapiHelper);
	}

	@Test
	@TestDbParams(
			init = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016-ordered"},
			check = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016-sent"})
	public void generateAndSend_test1() throws Exception {
		final File invoiceFile = File.createTempFile("prefix", "suffix");
		invoiceFile.deleteOnExit();
		when(commonHelperMock.getCurrentDate()).thenReturn(new GregorianCalendar(2017, Calendar.JANUARY, 1).getTime());
		when(commonHelperMock.getSettingsLock()).thenReturn(new Object());
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(invoiceHelper.generateInvoiceForDealer(any(), any(), any(), anyLong(), anyLong(), any())).thenAnswer(
				new Answer<Invoice>() {
					long nextInvoiceNumber = 1L;
					@Override public Invoice answer(InvocationOnMock invocation) throws Throwable {
						Invoice result = new Invoice((Date) invocation.getArguments()[2]);
						result.file = invoiceFile;
						result.positive = true;
						result.number = nextInvoiceNumber++;
						return result;
					}
				});

		hwmInvoiceHandler.generateAndSend(testDb.getConnection());

		assertTrue(invoiceFile.exists());
		verify(commonHelperMock, atLeastOnce()).getSettingsLock();
		verify(commonHelperMock, atLeastOnce()).getSettings();
		verify(commonHelperMock, atLeastOnce()).getCurrentDate();
		verify(settingsMock, times(8)).setNextInvoiceNumber(anyLong());
		verify(settingsMock, times(8)).save();
		verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock, untillTpapiHelper);
	}

	@Test(expected = RuntimeException.class)
	@TestDbParams(
			init = {"base", "common", "hwm-1-2-licenses", "hwm-1-2-invoices-2016-ordered"})
	public void generateAndSend_error1() throws Exception {
		final File invoiceFile = File.createTempFile("prefix", "suffix");
		invoiceFile.deleteOnExit();
		when(commonHelperMock.getCurrentDate()).thenReturn(new GregorianCalendar(2017, Calendar.JANUARY, 1).getTime());
		when(commonHelperMock.getSettingsLock()).thenReturn(new Object());
		when(commonHelperMock.getSettings()).thenReturn(settingsMock);
		when(invoiceHelper.generateInvoiceForDealer(any(), any(), any(), anyLong(), anyLong(), any())).thenAnswer(
				new Answer<Invoice>() {
					@Override public Invoice answer(InvocationOnMock invocation) throws Throwable {
						Invoice invoice = new Invoice((Date) invocation.getArguments()[2]);
						invoice.file = invoiceFile;
						invoice.emails = "some@email";
						return invoice;
					}
				});
		when(emailHelperMock.emailInvoice(any(), any(), any(), any(), any(), any())).thenReturn(false);

		try {
			hwmInvoiceHandler.generateAndSend(testDb.getConnection());
		} finally {
			assertFalse(invoiceFile.exists());
			verify(commonHelperMock, atLeastOnce()).getSettingsLock();
			verify(commonHelperMock, atLeastOnce()).getSettings();
			verify(commonHelperMock, atLeastOnce()).getCurrentDate();
			verify(emailHelperMock).emailInvoice(any(), any(), any(), any(), any(), any());
			verifyNoMoreInteractions(commonHelperMock, emailHelperMock, settingsMock, untillTpapiHelper);
		}
	}

}
