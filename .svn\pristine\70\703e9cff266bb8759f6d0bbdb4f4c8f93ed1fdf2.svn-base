package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.EventListener;
import java.util.List;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyCodes;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.Event.NativePreviewEvent;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.MultiWordSuggestOracle;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.shared.HardCodeHelper;
import eu.untill.license.shared.SuHost;

public class SuAddHostsDialog extends DialogBox implements ClickHandler {

	public static interface DialogListener extends EventListener {
		void onOk(List<SuHost> hosts);
	}

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Add recipients")
		String addHostsDialogHeader();

		@DefaultStringValue("Filter")
		String filterPanel();

		@DefaultStringValue("By client name")
		String byClientNameLabel();
		@DefaultStringValue("By license UID")
		String byLicenseUidLabel();
		@DefaultStringValue("By chain")
		String byChainLabel();
		
		String clientNameColumnHeader();
		String licenseUidColumnHeader();
		String chainColumnHeader();
		String hostColumnHeader();
		@DefaultStringValue("State")
		String stateColumnHeader();

		String okButton();
		String cancelButton();

		@DefaultStringValue("online")
		String online();
		@DefaultStringValue("offline")
		String offline();

	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	  /////////////
	 // Members //
	/////////////

	private DialogListener dialogListener;
	private long dealerId;
	private List<SuHost> hosts;

	  //////////////////
	 // Constructors //
	//////////////////

	public SuAddHostsDialog(long dealerId, DialogListener listener) {
		this.dealerId = dealerId;
		this.dialogListener = listener;

		generateUI();

		this.setText(constants.addHostsDialogHeader());

		requestHosts();

		show();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnConditionType = new EntitledDecoratorPanel(constants.filterPanel());
	private FlexTable tblConditionType = new FlexTable();

	private RadioButton rdbByClientName = new RadioButton("type", constants.byClientNameLabel());
	private RadioButton rdbByLicenseUid = new RadioButton("type", constants.byLicenseUidLabel());
	private RadioButton rdbByChain = new RadioButton("type", constants.byChainLabel());

	private MultiWordSuggestOracle conditionOracle = new MultiWordSuggestOracle();
	private SuggestBox txtCondition = new SuggestBox(conditionOracle);

	private EntitledDecoratorPanel dpnHosts = new EntitledDecoratorPanel(/*"Recipients"*/);
	private CheckBox chkSelectAll = new CheckBox();
	private FlexTable tblHosts = new FlexTable();
	private ScrollPanel scrHosts = new ScrollPanel(tblHosts);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		txtCondition.setWidth("300px");

		ValueChangeHandler<Boolean> filterChangeHandler = (event) -> {
			if (event.getValue()) {
				refreshConditionOracle();
				refreshHosts();
			}
		};
		rdbByClientName.addValueChangeHandler(filterChangeHandler);
		rdbByLicenseUid.addValueChangeHandler(filterChangeHandler);
		rdbByChain.addValueChangeHandler(filterChangeHandler);
		rdbByClientName.setValue(true);

//		txtCondition.addKeyPressHandler((event) -> {
//			if (event.getCharCode() == '\r') {
//				btnOk.click();
//			}
//		});
		txtCondition.addValueChangeHandler((event) -> {
			refreshHosts();
		});
		txtCondition.addSelectionHandler((event) -> {
			refreshHosts();
		});

		int row = 0;
		tblConditionType.setWidget(row++, 0, rdbByClientName);
		tblConditionType.setWidget(row++, 0, rdbByLicenseUid);
		tblConditionType.setWidget(row++, 0, rdbByChain);
		tblConditionType.setWidget(row++, 0, txtCondition);
		dpnConditionType.setWidget(tblConditionType);

		chkSelectAll.setValue(true);
		chkSelectAll.addClickHandler((event) -> {
			for (int i = 1; i < tblHosts.getRowCount(); ++i) {
				CheckBox checkBox = (CheckBox) tblHosts.getWidget(i, 0);
				checkBox.setValue(chkSelectAll.getValue());
			}
		});

		// Create hosts table
		tblHosts.addStyleName("al-Table"); 
		tblHosts.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblHosts.getFlexCellFormatter();
		int col = 0;
		tblHosts.setWidget(0, col++, chkSelectAll);
		tableCF.setWidth(0, col, "1600");
		tblHosts.setText(0, col++, constants.clientNameColumnHeader());
		tableCF.setWidth(0, col, "1600");
		tblHosts.setText(0, col++, constants.licenseUidColumnHeader());
		tableCF.setWidth(0, col, "1000");
		tblHosts.setText(0, col++, constants.chainColumnHeader());
		tableCF.setWidth(0, col, "800");
		tblHosts.setText(0, col++, constants.hostColumnHeader());
		tableCF.setWidth(0, col, "1000");
		tblHosts.setText(0, col++, constants.stateColumnHeader());
		scrHosts.setSize("800px", "300px");
		dpnHosts.setWidget(scrHosts);

		// Assemble button panel
		btnOk.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.add(btnOk);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnConditionType);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(dpnHosts);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnOk) {

			this.hide();

			if (dialogListener != null)
				dialogListener.onOk(getSelectedHosts());
		}

	}

	@Override
	protected void onPreviewNativeEvent(NativePreviewEvent event) {
		super.onPreviewNativeEvent(event);
		switch (event.getTypeInt()) {
		case Event.ONKEYDOWN:
			if (event.getNativeEvent().getKeyCode() == KeyCodes.KEY_ESCAPE) {
				hide();
			}
			break;
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void refreshConditionOracle() {
		conditionOracle.clear();
		if (hosts == null)
			return;
		if (rdbByClientName.getValue()) {
			for (SuHost host : hosts)
				if (host.license.clientName != null)
					conditionOracle.add(host.license.clientName);
		} else if (rdbByLicenseUid.getValue()) {
			for (SuHost host : hosts)
				if (host.license.licenseUid != null)
					conditionOracle.add(HardCodeHelper.formatHardCode(host.license.licenseUid));
		} else if (rdbByChain.getValue()) {
			for (SuHost host : hosts)
				if (host.license.chain != null)
					conditionOracle.add(HardCodeHelper.formatHardCode(host.license.chain));
		}
	}

	private void requestHosts() {
		UntillLicenseServer.getLicenseServiceAsync().getSuHostsByDealerId(UntillLicenseServer.getAuthScheme(), dealerId,
				new AsyncCallback<List<SuHost>>() {
					@Override
					public void onSuccess(List<SuHost> result) {
						hosts = result;
						refreshConditionOracle();
						refreshHosts();
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error", caught);
					}
				});
	}

	private void refreshHosts() {
		scrHosts.remove(tblHosts);
		try {
			for (int r = tblHosts.getRowCount() - 1; r > 0; --r)
				tblHosts.removeRow(r);
			fillHosts();
		} finally {
			scrHosts.add(tblHosts);
		}
	}

	private void fillHosts() {

		if (hosts == null)
			return;

		RowFormatter tblHostsRF = tblHosts.getRowFormatter();
		FlexCellFormatter tblHostsCF = tblHosts.getFlexCellFormatter();

		int row = 1;
		String condition = txtCondition.getText();
		chkSelectAll.setValue(!condition.isEmpty());
		for (SuHost host : hosts) {

			// filter
			if (!condition.isEmpty()) {
				if (rdbByClientName.getValue()) {
					if (!condition.equals(host.license.clientName))
						continue;
				} else if (rdbByLicenseUid.getValue()) {
					if (!HardCodeHelper.unformatHardCode(condition).equals(host.license.licenseUid))
						continue;
				} else if (rdbByChain.getValue()) {
					if (!condition.equals(host.license.chain))
						continue;
				}
			}

			tblHostsRF.addStyleName(row, "ll-Row");

			String state = host.offlineDuration == -1 ? constants.offline() 
					: (host.offlineDuration < SuHost.HOST_OFFLINE_TRESHOLD_MS ? constants.online()
							: constants.offline() + " (" + host.offlineDuration / 1000 + ")");

			HostCheckBox checkBox = new HostCheckBox(host);
			checkBox.setValue(chkSelectAll.getValue());
			
			int col = 0;
			tblHosts.setWidget(row, col++, checkBox);
			tblHosts.setText(row, col++, host.license.clientName);
			tblHostsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
			Label licenseUidLabel = new Label(HardCodeHelper.formatHardCode(host.license.licenseUid), false);
			licenseUidLabel.addStyleName("HardCodeLabel");
			tblHosts.setWidget(row, col++, licenseUidLabel);
			tblHosts.setText(row, col++, host.license.chain);
			tblHosts.setText(row, col++, host.name);
			tblHosts.setText(row, col++, state);

			++row;
		}

	}

	private List<SuHost> getSelectedHosts() {
		List<SuHost> result = new ArrayList<>();
		for (int i = 1; i < tblHosts.getRowCount(); ++i) {
			HostCheckBox checkBox = (HostCheckBox) tblHosts.getWidget(i, 0);
			if (checkBox.getValue())
				result.add(checkBox.getHost());
		}
		return result;
	}

	  /////////////////////
	 // Private classes //
	/////////////////////

	private class HostCheckBox extends CheckBox {
		SuHost host;

		public HostCheckBox(SuHost host) {
			this.host = host;
		}

		public SuHost getHost() {
			return host;
		}
	}
	
	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtCondition.setFocus(true);
	}

}
