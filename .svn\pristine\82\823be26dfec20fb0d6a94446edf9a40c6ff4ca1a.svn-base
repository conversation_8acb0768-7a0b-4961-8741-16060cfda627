package eu.untill.license.shared;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

public class ClientNameChange implements IsSerializable {

//	public long licenseId;
//	public long dealerId;

	public String dealerName;
	public String clientName;
	public String hardCode;
	public Date issueDate;
	public String clientNameBefore;
	public String clientNameAfter;

	@Override
	public String toString() {
		return "[" + getClass().getSimpleName() + ": \"" + dealerName + "\", \"" + clientName +"\", \"" + hardCode + "\"]";
	}

}
