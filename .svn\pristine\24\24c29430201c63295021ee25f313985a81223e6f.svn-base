package eu.untill.license.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

public class ProlongParams implements IsSerializable {
	private Short lbConnections = null;
	private Short remConnections = null;
	private Short omConnections = null;
//	private Short hqConnections = null;
	private Short psConnections = null;
	private Short heConnections = null;
	
	public ProlongParams() {
	}

	public Short getLbConnections() {
		return lbConnections;
	}

	public void setLbConnections(Short lbConnections) {
		this.lbConnections = lbConnections;
	}

	public Short getRemConnections() {
		return remConnections;
	}

	public void setRemConnections(Short remConnections) {
		this.remConnections = remConnections;
	}

	public Short getOmConnections() {
		return omConnections;
	}

	public void setOmConnections(Short omConnections) {
		this.omConnections = omConnections;
	}

//	public Short getHqConnections() {
//		return hqConnections;
//	}
//
//	public void setHqConnections(Short hqConnections) {
//		this.hqConnections = hqConnections;
//	}

	public Short getPsConnections() {
		return psConnections;
	}

	public void setPsConnections(Short psConnections) {
		this.psConnections = psConnections;
	}

	public Short getHeConnections() {
		return heConnections;
	}

	public void setHeConnections(Short heConnections) {
		this.heConnections = heConnections;
	}

}
