/**
 *
 */
package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.EventListener;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Style;

/**
 * <AUTHOR>
 *
 */
public class AddHandlingFeeDialog extends DialogBox implements ClickHandler {

	  ///////////
	 // Event //
	///////////

	public static interface AddHandlingFeeDialogListener extends EventListener {
		void onAddHandlingFee(double price, String description);
	}

	ArrayList<AddHandlingFeeDialogListener> addHandlingFeeDialogListenerList = null;

	public void addAddHandlingFeeDialogListener(AddHandlingFeeDialogListener listener) {
		if (addHandlingFeeDialogListenerList == null)
			addHandlingFeeDialogListenerList = new ArrayList<AddHandlingFeeDialogListener>();
		addHandlingFeeDialogListenerList.add(listener);
	}

	public void removeAddHandlingFeeDialogListener(AddHandlingFeeDialogListener listener) {
		if (addHandlingFeeDialogListenerList != null)
			addHandlingFeeDialogListenerList.remove(listener);
	}

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Add handling fee...")
		String addHandlingFeeHeader();
		@DefaultStringValue("Price")
		String priceLabel();
		@DefaultStringValue("Description")
		String descriptionLabel();
		String okButton();
		String cancelButton();
	}

	public static interface UlsMessages extends Messages {
		String unknownNumberFormatOfPrice();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  //////////////////
	 // Constructors //
	//////////////////

	public AddHandlingFeeDialog(AddHandlingFeeDialogListener listener) {
		// Generate base interface
		generateUI();

		this.addAddHandlingFeeDialogListener(listener);

		this.setText(constants.addHandlingFeeHeader());
		txtPrice.setText("50");
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private FlexTable tblParameters = new FlexTable();
	private Label lblPrice = new Label(constants.priceLabel() + ":", false);
	private TextBox txtPrice = new TextBox();
	private Label lblDescription = new Label(constants.descriptionLabel() + ":", false);
	private TextBox txtDescription = new TextBox();

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		// Assemble parameters table
		txtPrice.setVisibleLength(20);
		txtDescription.setMaxLength(50);
		txtDescription.setVisibleLength(60);
		tblParameters.setWidget(0, 0, lblPrice);
		tblParameters.setWidget(0, 1, txtPrice);
		tblParameters.setWidget(1, 0, lblDescription);
		tblParameters.setWidget(1, 1, txtDescription);

		// Assemble button panel
		btnOk.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.add(btnOk);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(tblParameters);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnOk){

			Double price;
			try {
				price = Double.valueOf(txtPrice.getText());
			} catch (NumberFormatException e) {
				new MessageDialog(messages.unknownNumberFormatOfPrice(), Style.WARNING, txtPrice).show();
				return;
			}

			if (addHandlingFeeDialogListenerList != null)
				for (AddHandlingFeeDialogListener addHandlingFeeDialogListener : addHandlingFeeDialogListenerList) {
					addHandlingFeeDialogListener.onAddHandlingFee(price, txtDescription.getText());
				}

			this.hide();
		}

	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtPrice.setFocus(true);
	}

}
