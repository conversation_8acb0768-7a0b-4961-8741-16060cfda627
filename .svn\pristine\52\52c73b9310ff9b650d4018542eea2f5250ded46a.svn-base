package eu.untill.license.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.api.AuthenticationApi;
import com.untill.retailforce.api.MasterDataOrganisationsApi;
import com.untill.retailforce.api.MasterDataStoresApi;
import com.untill.retailforce.api.MasterDataTerminalsApi;
import com.untill.retailforce.api.SecurityApi;
import com.untill.retailforce.auth.ApiKeyAuth;
import com.untill.retailforce.model.ApiKey;
import com.untill.retailforce.model.CompanyIdentification;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.IdentificationType;
import com.untill.retailforce.model.Organisation;
import com.untill.retailforce.model.OrganisationModel;
import com.untill.retailforce.model.PlatformType;
import com.untill.retailforce.model.Store;
import com.untill.retailforce.model.StoreModel;
import com.untill.retailforce.model.Terminal;
import com.untill.retailforce.model.TerminalModel;

import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.Fiscal;
import eu.untill.license.shared.Fiscal.Address;

public class FiscalHelperImpl implements FiscalHelper {
	static final Logger LOG = LoggerFactory.getLogger(FiscalHelperImpl.class);

	private CommonHelper commonHelper;

	public FiscalHelperImpl(CommonHelper commonHelper) {
		this.commonHelper = commonHelper;
	}

	@Override
	public void doFiscalization(Connection conn, DbLicense license) throws Exception {
		LOG.trace("ENTRY ({})", license);
		try {
			doFiscalizationImpl(conn, license);
		} catch (Exception e) {
			LOG.error("Fiscalization error", e);
			throw e;
		}
		LOG.trace("RETURN");
	}

	public void doFiscalizationImpl(Connection conn, DbLicense license) throws Exception {
		Settings settings = commonHelper.getSettings();
		ApiClient client = getApiClient(settings.getRetailForceApiBasePath(),
				settings.getRetailForceApiKey(), settings.getRetailForceApiSecret());

		UUID distributorId = UUID.fromString(settings.getRetailForceDistributorId());
		UUID clientConfigurationId = UUID.fromString(settings.getRetailForceConfigurationId());
		Boolean testTerminals = settings.getRetailForceTest();

		// TODO: update license.getFiscal() from DB (sync)
		// TODO: check license.getFiscal()
		Fiscal fiscal = license.getFiscal();

		// Create / update organization
		UUID organisationId = fiscal.getOrganisationId() == null ? null : UUID.fromString(fiscal.getOrganisationId());
		if (organisationId == null || Boolean.parseBoolean(fiscal.getRequiresUpdateOrganisation())) {
			Address address = fiscal.getOrganisationAddress();
			if (address == null)
				address = fiscal.getStoreAddress();
			if (address == null)
				address = new Address();
			Integer fiscalYearStartMonth = fiscal.getFiscalYearStartMonth() == null ? null
					: Integer.parseInt(fiscal.getFiscalYearStartMonth());
			Organisation organisation = new Organisation()
					.city(address.getCity())
					.street(address.getStreet())
					.postalCode(address.getPostalCode())
					.countryCode(address.getCountryCode())
					.streetNumber(address.getStreetNumber())
					.community("")
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._0_VATNUMBER)
							.identification(fiscal.getVatNumber())
					)
					.addCompanyIdentificationItem(new CompanyIdentification()
							.type(IdentificationType._3_BUSINESSIDENTIFICATIONNUMBER)
							.identification(fiscal.getBusinessIdentificationNumber())
					)
					.caption(fiscal.getOrganisationCaption())
					.fiscalCountry(getFiscalCountryfromValue(fiscal.getFiscalCountry()))
					.fiscalYearStartMonth(fiscalYearStartMonth)
					.distributorId(distributorId)
					.clientConfigurationId(clientConfigurationId)
					.organisationId(organisationId)
					;
			MasterDataOrganisationsApi mdOrganisations = new MasterDataOrganisationsApi(client);
			if (organisationId == null) {
				OrganisationModel organisationModel = mdOrganisations.apiV10MasterdataOrganisationsPost(organisation);
				organisationId = organisationModel.getOrganisationId();
				fiscal.setOrganisationId(organisationId.toString());
			} else {
				mdOrganisations.apiV10MasterdataOrganisationsOrganisationIdPut(organisationId, organisation);
				fiscal.setRequiresUpdateOrganisation(null);
			}
			upgradeLicenseExtraData(conn, license);
		}

		// Create / update store
		UUID storeId = fiscal.getStoreId() == null ? null
				: UUID.fromString(fiscal.getStoreId());
		if (storeId == null || Boolean.parseBoolean(fiscal.getRequiresUpdateStore())) {
			Address address = fiscal.getStoreAddress();
			if (address == null)
				address = fiscal.getOrganisationAddress();
			if (address == null)
				address = new Address();
			Store store = new Store()
					.city(address.getCity())
					.street(address.getStreet())
					.postalCode(address.getPostalCode())
					.countryCode(address.getCountryCode())
					.streetNumber(address.getStreetNumber())
					.community("")
					.storeNumber(fiscal.getStoreNumber())
					.caption(fiscal.getStoreCaption())
					.organisationId(organisationId)
					.storeId(storeId)
					;
			MasterDataStoresApi mdStores = new MasterDataStoresApi(client);
			if (storeId == null) {
				StoreModel storeModel = mdStores.apiV10MasterdataStoresPost(store);
				storeId = storeModel.getStoreId();
				fiscal.setStoreId(storeId.toString());
			} else {
				mdStores.apiV10MasterdataStoresStoreIdPut(storeId, store);
				fiscal.setRequiresUpdateStore(null);
			}
			upgradeLicenseExtraData(conn, license);
		}

		// Create ApiKey
		if (fiscal.getApiKey() == null) {
			SecurityApi securityApi = new SecurityApi(client);
			ApiKey apiKey = securityApi.apiV10SecurityApikeyEntityIdPost(organisationId);
			fiscal.setApiKey(apiKey.getKey());
			fiscal.setApiSecret(apiKey.getSecret());
			upgradeLicenseExtraData(conn, license);
		}

		// Delete terminals first
		MasterDataTerminalsApi mdTerminals = new MasterDataTerminalsApi(client);
		for (Iterator<Fiscal.Terminal> it = fiscal.getTerminals().iterator(); it.hasNext();) {
			Fiscal.Terminal itTerminal = it.next();
			UUID terminalId = itTerminal.getTerminalId() == null ? null : UUID.fromString(itTerminal.getTerminalId());
			if (terminalId != null && Boolean.parseBoolean(itTerminal.getRequiresDelete())) {
				mdTerminals.apiV10MasterdataTerminalsTerminalIdDelete(terminalId);
				it.remove();
				upgradeLicenseExtraData(conn, license);
			}
		}		
		for (Fiscal.Terminal itTerminal : fiscal.getTerminals()) {
			UUID terminalId = itTerminal.getTerminalId() == null ? null : UUID.fromString(itTerminal.getTerminalId());
			if (itTerminal.getTerminalId() == null || Boolean.parseBoolean(itTerminal.getRequiresUpdate())) {
				// Create / update terminal
				Terminal terminal = new Terminal()
						.terminalNumber(itTerminal.getTerminalNumber())
						.caption(itTerminal.getCaption())
						.storeId(storeId)
						.terminalId(terminalId)
						.isTest(testTerminals)
						.platformType(PlatformType.WINDOWS) // XXX: hardcoded
						;
				if (terminalId == null) {
					TerminalModel terminalModel = mdTerminals.apiV10MasterdataTerminalsPost(terminal);
					terminalId = terminalModel.getTerminalId();
					itTerminal.setTerminalId(terminalId.toString());
				} else {
					mdTerminals.apiV10MasterdataTerminalsTerminalIdPut(terminalId, terminal);
					itTerminal.setRequiresUpdate(null);
				}
				upgradeLicenseExtraData(conn, license);
			}
		}

		logout(client);
	}

	private FiscalCountry getFiscalCountryfromValue(String fiscalCountry) {
		if (fiscalCountry == null || fiscalCountry.equals("4"))
			return FiscalCountry._4_DENMARK;
		return FiscalCountry.fromValue(fiscalCountry);
	}

	private void upgradeLicenseExtraData(Connection conn, DbLicense license) throws SQLException {
		// update license extra data in DB
		try (PreparedStatement stmt = conn.prepareStatement("UPDATE LICENSES SET EXTRA_DATA = ? WHERE ID = ?")) {
			stmt.setString(1, Common.getExtraDataJsonFromLicense(license));
			stmt.setLong(2, license.id);
			if (stmt.executeUpdate() == 0) throw new RuntimeException();
		}
		conn.commit(); // XXX Just in case (if autocommit is not enabled)
	}

	private static ApiClient getApiClient(String basePath, String key, String secret) throws ApiException {
		ApiClient apiClient = Configuration.getDefaultApiClient();
		apiClient.setBasePath(basePath);

		AuthenticationApi apiInstance = new AuthenticationApi(apiClient);
		String apiKey = apiInstance.apiV10AuthenticateLogon2Post(key, secret);

		// Configure API key authorization: Bearer
		ApiKeyAuth Bearer = (ApiKeyAuth) apiClient.getAuthentication("Bearer");
		Bearer.setApiKey(apiKey);
		Bearer.setApiKeyPrefix("Bearer");

		return apiClient;
	}

	private static void logout(ApiClient apiClient) throws ApiException {
		AuthenticationApi apiInstance = new AuthenticationApi(apiClient);
		apiInstance.apiV10AuthenticateLogoutPost();
	}

}
