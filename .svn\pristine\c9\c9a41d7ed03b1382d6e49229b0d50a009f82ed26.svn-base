/**
 * 
 */
package eu.untill.license.server;

import java.time.LocalDate;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */

public interface CommonHelper {

	String generateGuid();

	Date getCurrentDate();
	default LocalDate getCurrentLocalDate() {
		return new java.sql.Date(getCurrentDate().getTime()).toLocalDate();
	}

	String getPrivateKey(String productName, int productVersion);

	String getServletUrl();

	Object getSettingsLock();
	Settings getSettings();
	void setSettings(Settings settings);

}
