package eu.untill.license.server;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import TPAPIPosTypesU.TExtraInfo;
import TPAPIPosTypesU.TOrderItem;
import eu.untill.license.server.Dealer.RequiredDealerIsNotFoundException;
import eu.untill.license.server.InvoiceHelper.Invoice;
import eu.untill.license.shared.License;

public class HwmInvoiceHandler {

	static final Logger LOG = LoggerFactory.getLogger(HwmInvoiceHandler.class);

	private CommonHelper commonHelper;
	private EmailHelper emailHelper;
	private InvoiceHelper invoiceHelper;
	private UntillTpapiHelper untillTpapiHelper;

	public HwmInvoiceHandler(CommonHelper commonHelper, EmailHelper emailHelper, InvoiceHelper invoiceHelper,
			UntillTpapiHelper untillTpapiHelper) {
		this.commonHelper = commonHelper;
		this.emailHelper = emailHelper;
		this.invoiceHelper = invoiceHelper;
		this.untillTpapiHelper = untillTpapiHelper;
	}

	/**
	 * Adding records to HWM_INVOICE and HWM_INVOICE_ITEM tables for all past accounting months
	 * starting with following for HwmNextMonthForInvoiceGeneration
	 * @param conn
	 * @throws SQLException
	 */
	public void prepare(Connection conn) throws SQLException {
		LocalDate invoiceMonth = getNextMonthForInvoiceGeneration();
		LocalDate currentDate = commonHelper.getCurrentLocalDate();

		if (invoiceMonth.plusMonths(1).isAfter(currentDate))
			return;

		// begin transaction
		boolean oldAutoCommit = conn.getAutoCommit();
		conn.setAutoCommit(false);
		try {
			// Enumeration completed months
			while (!invoiceMonth.plusMonths(1).isAfter(currentDate)) {
				prepareInvoicesForOneMonth(conn, invoiceMonth);
				invoiceMonth = invoiceMonth.plusMonths(1);
				if (invoiceMonth.getMonth() == Month.JANUARY)
					prepareInvoicesForOneYear(conn, invoiceMonth);
			}
			conn.commit();
		} catch (SQLException e) {
			conn.rollback();
			throw e;
		} finally {
			conn.setAutoCommit(oldAutoCommit);
		}
		setNextMonthForInvoiceGeneration(invoiceMonth);
	}

	private class HwmInvoice {
		private long dealerId;
		private String chain;
		private List<Long> licenseIds = new ArrayList<>();
		public HwmInvoice(long dealerId, String chain) {
			this.dealerId = dealerId;
			this.chain = chain;
		}
		@Override
		public String toString() {
			return String.format("{dealerId: %d, chain: %s, licenseIds: %s}", dealerId, chain, licenseIds);
		}
	}

	private void prepareInvoicesForOneMonth(Connection conn, LocalDate month) throws SQLException {
		// Select all active licenses with FUNC_WM_HOSTED and not HWM_YEARLY
		LocalDate nextMonth = month.plusMonths(1);
		String sql = "SELECT ID, ISSUE_DATE, ID_DEALERS, ID_LICENSES_PREV, CHAIN FROM LICENSES\n"
				+ "WHERE IS_ACTIVE = 1 AND ID_DEALERS IS NOT NULL\n"
				+ "  AND START_DATE < ?\n" // 1
				+ "  AND (EXPIRED_DATE >= ? OR BIN_AND(STATUS, ?) > 0)\n" // 2, 3
				+ "  AND BIN_AND(FUNC_LIMITED, BIN_SHL(1, ?)) > 0\n" // 4
				+ "  AND COALESCE(HWM_YEARLY, 0) = 0\n"
				+ "  AND NOT EXISTS (\n"
				+ "    SELECT * FROM HWM_INVOICE_ITEM\n"
				+ "    INNER JOIN HWM_INVOICE ON HWM_INVOICE.ID = HWM_INVOICE_ITEM.ID_HWM_INVOICE\n"
				+ "    WHERE ID_LICENSES = LICENSES.ID\n"
				+ "    AND COALESCE(HWM_INVOICE.YEARLY, 0) = 0\n"
				+ "    AND DATEDIFF(MONTH, HWM_INVOICE.CALC_MONTH, ?) = 0\n" // 5
				+ "  )\n"
				+ "ORDER BY ID_DEALERS, UPPER(CHAIN)\n"
				+ "FOR UPDATE\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setDate(1, java.sql.Date.valueOf(nextMonth));
			stmt.setDate(2, java.sql.Date.valueOf(month));
			stmt.setShort(3, License.NO_EXPIRED_DATE_BIT);
			stmt.setInt(4, License.FUNC_WM_HOSTED);
			stmt.setDate(5, java.sql.Date.valueOf(month));
			try (ResultSet rs = stmt.executeQuery()) {
				HwmInvoice invoice = null; 
				while (rs.next()) {
					long dealerId = rs.getLong("ID_DEALERS");
					String chain = rs.getString("CHAIN");
					if (invoice != null && (dealerId != invoice.dealerId || chain == null
							|| !chain.equalsIgnoreCase(invoice.chain))) {
						insertNewInvoice(conn, invoice, false, false, month);
						invoice = null;
					}
					long licenseId = rs.getLong("ID");
					long prevLicenseId = rs.getLong("ID_LICENSES_PREV");
					LocalDate issueDate = rs.getDate("ISSUE_DATE").toLocalDate();
					if (!issueDate.isBefore(month) && isLicenseAlreadyInvoicedForMonth(conn, prevLicenseId, month))
						continue;
					if (!issueDate.isBefore(nextMonth)) {
						licenseId = getPrevLicenseIdIssuedBeforeSpecMonth(conn, prevLicenseId, nextMonth);
						if (licenseId == 0)
							continue;
					}
					if (invoice == null)
						invoice = new HwmInvoice(dealerId, chain);
					invoice.licenseIds.add(licenseId);
				}
				if (invoice != null) {
					insertNewInvoice(conn, invoice, false, false, month);
					//invoice = null;
				}
			}
		}
	}

	private void prepareInvoicesForOneYear(Connection conn, LocalDate year) throws SQLException {
		// Select all active licenses with FUNC_WM_HOSTED and HWM_YEARLY
		String sql = "SELECT ID, ISSUE_DATE, ID_DEALERS, ID_LICENSES_PREV, CHAIN, HWM_FIRST FROM LICENSES\n"
				+ "WHERE IS_ACTIVE = 1 AND ID_DEALERS IS NOT NULL\n"
				+ "  AND START_DATE < ?\n" // 1
				+ "  AND BIN_AND(FUNC_LIMITED, BIN_SHL(1, ?)) > 0\n" // 2
				+ "  AND HWM_YEARLY = 1\n"
				+ "  AND NOT EXISTS (\n"
				+ "    SELECT * FROM HWM_INVOICE_ITEM\n"
				+ "    INNER JOIN HWM_INVOICE ON HWM_INVOICE.ID = HWM_INVOICE_ITEM.ID_HWM_INVOICE\n"
				+ "    WHERE ID_LICENSES = LICENSES.ID\n"
				+ "      AND HWM_INVOICE.YEARLY = 1\n"
				+ "      AND EXTRACT(YEAR FROM HWM_INVOICE.CALC_MONTH) = ?\n" // 3
				+ "  )\n"
				+ "ORDER BY ID_DEALERS, UPPER(CHAIN)\n"
				+ "FOR UPDATE\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setDate(1, java.sql.Date.valueOf(year.plusYears(1)));
			stmt.setInt(2, License.FUNC_WM_HOSTED);
			stmt.setShort(3, (short) year.getYear());
			try (ResultSet rs = stmt.executeQuery()) {
				HwmInvoice invoice = null; 
				while (rs.next()) {
					long dealerId = rs.getLong("ID_DEALERS");
					String chain = rs.getString("CHAIN");
					if (invoice != null && (dealerId != invoice.dealerId || chain == null
							|| !chain.equalsIgnoreCase(invoice.chain))) {
						insertNewInvoice(conn, invoice, true, false, year);
						invoice = null;
					}
					long licenseId = rs.getLong("ID");
					long prevLicenseId = rs.getLong("ID_LICENSES_PREV");
					LocalDate issueDate = rs.getDate("ISSUE_DATE").toLocalDate();
					if (issueDate.getYear() >= year.getYear() && rs.getShort("HWM_FIRST") == 1)
						continue; // impossible (hwm_invoice must already exist)
					if (issueDate.getYear() >= year.getYear() && isLicenseAlreadyInvoicedForYear(conn, prevLicenseId, year.getYear()))
						continue;
					if (issueDate.getYear() > year.getYear()) {
						licenseId = getPrevLicenseIdIssuedOnOrBeforeSpecYear(conn, prevLicenseId, year.getYear());
						if (licenseId == 0)
							continue;
					}
					if (invoice == null)
						invoice = new HwmInvoice(dealerId, chain);
					invoice.licenseIds.add(licenseId);
				}
				if (invoice != null) {
					insertNewInvoice(conn, invoice, true, false, year);
					//invoice = null;
				}
			}
		}
	}

	public void prepareInvoicesForRestOfYear(Connection conn, long dealerId, String chain, long licenseId,
			LocalDate year) throws SQLException {
		HwmInvoice invoice = new HwmInvoice(dealerId, chain);
		invoice.licenseIds.add(licenseId);
		insertNewInvoice(conn, invoice, true, true, year);
	}

	/**
	 * Verifying that any previous license has already been used in invoice for specified month
	 * @throws SQLException 
	 */
	private boolean isLicenseAlreadyInvoicedForMonth(Connection conn, long licenseId, LocalDate month) throws SQLException {
		String sql = "SELECT LICENSES.ID_LICENSES_PREV, (\n"
				+ "  SELECT COUNT(*) FROM HWM_INVOICE\n"
				+ "  INNER JOIN HWM_INVOICE_ITEM ON HWM_INVOICE_ITEM.ID_HWM_INVOICE = HWM_INVOICE.ID\n"
				+ "  WHERE HWM_INVOICE_ITEM.ID_LICENSES = LICENSES.ID\n"
				+ "  AND COALESCE(HWM_INVOICE.YEARLY, 0) = 0\n"
				+ "  AND DATEDIFF(MONTH, HWM_INVOICE.CALC_MONTH, ?) = 0\n" // 1
				+ ") HWM_INVOICE_COUNT\n"
				+ "FROM LICENSES\n"
				+ "WHERE LICENSES.ID = ?\n"; //2
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setDate(1, java.sql.Date.valueOf(month));
			while (licenseId != 0) {
				stmt.setLong(2, licenseId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next()) throw new RuntimeException("License with ID = " + licenseId + " not found");
					if (rs.getLong("HWM_INVOICE_COUNT") != 0)
						return true;
					licenseId = rs.getLong("ID_LICENSES_PREV");
				}
			}
			return false;
		}
	}

	/**
	 * Verifying that any previous license has already been used in invoice for specified year
	 * @throws SQLException 
	 */
	private boolean isLicenseAlreadyInvoicedForYear(Connection conn, long licenseId, int year) throws SQLException {
		String sql = "SELECT LICENSES.ID_LICENSES_PREV, (\n"
				+ "  SELECT COUNT(*) FROM HWM_INVOICE\n"
				+ "  INNER JOIN HWM_INVOICE_ITEM ON HWM_INVOICE_ITEM.ID_HWM_INVOICE = HWM_INVOICE.ID\n"
				+ "  WHERE HWM_INVOICE_ITEM.ID_LICENSES = LICENSES.ID\n"
				+ "  AND HWM_INVOICE.YEARLY = 1"
				+ "  AND EXTRACT(YEAR FROM HWM_INVOICE.CALC_MONTH) = ?\n" // 1
				+ ") HWM_INVOICE_COUNT\n"
				+ "FROM LICENSES\n"
				+ "WHERE LICENSES.ID = ?\n"; //2
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setShort(1, (short) year);
			while (licenseId != 0) {
				stmt.setLong(2, licenseId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next()) throw new RuntimeException("License with ID = " + licenseId + " not found");
					if (rs.getLong("HWM_INVOICE_COUNT") != 0)
						return true;
					licenseId = rs.getLong("ID_LICENSES_PREV");
				}
			}
			return false;
		}
	}

	/**
	 * Find nearest previous license issued before the specified month, and checking for presence FUNC_WM_HOSTED
	 * @return license ID - if such license is found, otherwise 0
	 * @throws SQLException 
	 */
	private long getPrevLicenseIdIssuedBeforeSpecMonth(Connection conn, long licenseId, LocalDate month) throws SQLException {
		String sql = "SELECT ID, ISSUE_DATE, FUNC_LIMITED, ID_LICENSES_PREV, HWM_YEARLY\n"
				+ "FROM LICENSES\n"
				+ "WHERE ID = ?\n" // 1
				+ "FOR UPDATE\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			while (licenseId != 0) {
				stmt.setLong(1, licenseId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next()) throw new RuntimeException("License with ID = " + licenseId + " not found");
					if (rs.getDate("ISSUE_DATE").toLocalDate().isBefore(month)) {
						if ((rs.getLong("FUNC_LIMITED") & (1L << License.FUNC_WM_HOSTED)) != 0
								&& rs.getShort("HWM_YEARLY") == 0)
							return licenseId;
						else
							return 0;
					}
					licenseId = rs.getLong("ID_LICENSES_PREV");
				}
			}
			return 0;
		}
	}

	/**
	 * Find nearest previous license issued on or before the specified year, and
	 * checking for presence FUNC_WM_HOSTED and HWM_YEARLY
	 * 
	 * @return license ID - if such license is found, otherwise 0
	 * @throws SQLException
	 */
	private long getPrevLicenseIdIssuedOnOrBeforeSpecYear(Connection conn, long licenseId, int year) throws SQLException {
		String sql = "SELECT ID, ISSUE_DATE, FUNC_LIMITED, ID_LICENSES_PREV, HWM_YEARLY\n"
				+ "FROM LICENSES\n"
				+ "WHERE ID = ?\n" // 1
				+ "FOR UPDATE\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			while (licenseId != 0) {
				stmt.setLong(1, licenseId);
				try (ResultSet rs = stmt.executeQuery()) {
					if (!rs.next()) throw new RuntimeException("License with ID = " + licenseId + " not found");
					if (rs.getDate("ISSUE_DATE").toLocalDate().getYear() <= year) {
						if ((rs.getLong("FUNC_LIMITED") & (1L << License.FUNC_WM_HOSTED)) != 0
								&& rs.getShort("HWM_YEARLY") == 1)
							return licenseId;
						else
							return 0;
					}
					licenseId = rs.getLong("ID_LICENSES_PREV");
				}
			}
			return 0;
		}
	}


	private void insertNewInvoice(Connection conn, HwmInvoice invoice, boolean yearly, boolean firstYear, LocalDate monthYearOrDay) throws SQLException {
		// Skip for Demo Dealer
		try {
			Dealer dealer = new Dealer(conn, invoice.dealerId);
			if (dealer.demoDealer) {
				if (yearly)
					LOG.info(String.format("Skip HWM-invoice creation for Demo Dealer: {year: %tY, invoice: %s}",
							monthYearOrDay, invoice));
				else
					LOG.info(String.format("Skip HWM-invoice creation for Demo Dealer: {month: %tY-%<tm, invoice: %s}",
							monthYearOrDay, invoice));
				return;
			}
		} catch (RequiredDealerIsNotFoundException e) {
			throw new RuntimeException(e);
		}

		// Generate id for new invoice
		long invoiceId;
		try (
			PreparedStatement stmt = conn.prepareStatement("SELECT * FROM GENERATE_TABLE_ID\n");
			ResultSet rs = stmt.executeQuery();
		) {
			if (!rs.next()) throw new RuntimeException();
			invoiceId = rs.getLong(1);
		}

		if (yearly)
			if (firstYear)
				LOG.info(String.format("New HWM-invoice: {id: %d, first-year: %tY-%<tm-%<td, invoice: %s}", invoiceId, monthYearOrDay, invoice));
			else
				LOG.info(String.format("New HWM-invoice: {id: %d, year: %tY, invoice: %s}", invoiceId, monthYearOrDay, invoice));
		else
			LOG.info(String.format("New HWM-invoice: {id: %d, month: %tY-%<tm, invoice: %s}", invoiceId, monthYearOrDay, invoice));

		//                                     1   2           3      4       5           6           -
		String sql = "INSERT INTO HWM_INVOICE (ID, ID_DEALERS, CHAIN, YEARLY, FIRST_YEAR, CALC_MONTH, ORDER_REQUIRED)\n"
				+ "VALUES (?, ?, ?, ?, ?, ?, 1)\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, invoiceId);
			stmt.setLong(2, invoice.dealerId);
			stmt.setString(3, invoice.chain);
			stmt.setShort(4, (short) (yearly ? 1 : 0));
			stmt.setShort(5, (short) (firstYear ? 1 : 0));
			stmt.setDate(6, java.sql.Date.valueOf(monthYearOrDay));
			if (stmt.executeUpdate() != 1) throw new RuntimeException();
		}
		//                                   1               2
		sql = "INSERT INTO HWM_INVOICE_ITEM (ID_HWM_INVOICE, ID_LICENSES)\n"
				+ "VALUES (?, ?)\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			for(Long licenseId: invoice.licenseIds) {
				stmt.setLong(1, invoiceId);
				stmt.setLong(2, licenseId);
				if (stmt.executeUpdate() != 1) throw new RuntimeException();
			}
		}
	}

	private LocalDate getNextMonthForInvoiceGeneration() {
		Settings settings = commonHelper.getSettings();
		String nextMonthStr = settings.getHwmNextMonthForInvoiceGeneration();
		LocalDate result;
		try {
			result = YearMonth.parse(nextMonthStr).atDay(1);
		} catch (DateTimeParseException e) {
			LOG.warn(String.format("Invalid format for HwmNextMonthForInvoiceGeneration parameter: '%s'", nextMonthStr));
			result = commonHelper.getCurrentLocalDate().withDayOfMonth(1).minusYears(1);
		}
		return result;
	}

	private void setNextMonthForInvoiceGeneration(LocalDate invoiceMonth) {
		Settings settings = commonHelper.getSettings();
		settings.setHwmNextMonthForInvoiceGeneration(YearMonth.from(invoiceMonth).toString());
	}

	/**
	 * calculate, order and pay, write billId into DB
	 * @param conn
	 * @throws Exception 
	 */
	public void order(Connection conn) throws Exception {
		while (true) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {
				long invoiceId = 0, dealerId = 0;
				String chain = null;
				boolean yearly = false;
				boolean firstYear = false;
				LocalDate calcMonth = null;
				boolean nothingFound = true;
				String sql = "SELECT ID, ID_DEALERS, CHAIN, YEARLY, FIRST_YEAR, CALC_MONTH\n"
						+ "FROM HWM_INVOICE\n"
						+ "WHERE ORDER_REQUIRED = 1\n"
						+ "ORDER BY CALC_MONTH, ID\n"
						// TODO: + "ROWS 1\n"
						+ "FOR UPDATE\n";
				try (
					PreparedStatement stmt = conn.prepareStatement(sql);
					ResultSet rs = stmt.executeQuery();
				) {
					if (rs.next()) {
						invoiceId = rs.getLong("ID");
						dealerId = rs.getLong("ID_DEALERS");
						chain = rs.getString("CHAIN");
						yearly = rs.getShort("YEARLY") == 1;
						firstYear = rs.getShort("FIRST_YEAR") == 1;
						calcMonth = rs.getDate("CALC_MONTH").toLocalDate();
						nothingFound = false;
					}
				}
				if (nothingFound) {
					conn.commit(); // or rollback()
					break;
				}

				List<TOrderItem> order = createOrder(conn, invoiceId, yearly, firstYear, calcMonth, chain);
				if (order == null) {
					try (PreparedStatement stmt = conn.prepareStatement(
							"UPDATE HWM_INVOICE SET\n"
							+ "ORDER_REQUIRED = 0,\n"
							+ "ID_BILL = NULL\n"
							+ "WHERE ID = ?\n")) {
						stmt.setLong(1, invoiceId);
						if (stmt.executeUpdate() == 0) throw new RuntimeException();
					}
					conn.commit();
					LOG.warn(String.format("Empty order for HWM-invoice: {id: %d}", invoiceId));
					continue;
				}

				// TODO case: total price == 0
				long billId = untillTpapiHelper.orderAndPay(conn, order, dealerId);

				try (PreparedStatement stmt = conn.prepareStatement(
						"UPDATE HWM_INVOICE SET\n"
						+ "ORDER_REQUIRED = 0,\n"
						+ "ID_BILL = ?,\n"
						+ "INVOICE_REQUIRED = 1\n"
						+ "WHERE ID = ?\n")) {
					stmt.setLong(1, billId);
					stmt.setLong(2, invoiceId);
					if (stmt.executeUpdate() == 0) throw new RuntimeException();
				}
				conn.commit();
				LOG.info(String.format("Made order for HWM-invoice: {id: %d, billId: %d}", invoiceId, billId));
			} catch (SQLException e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} // while (true)
	}

	private List<TOrderItem> createOrder(Connection conn, long invoiceId, boolean yearly, boolean firstYear,
			LocalDate calcMonth, String chain) throws Exception {

		int wmHqQuantity = 0;
		int wmCentralBoQuantity = 0;
		String sql = "SELECT LICENSES.FUNC_LIMITED\n"
				+ "FROM HWM_INVOICE_ITEM\n"
				+ "INNER JOIN LICENSES ON LICENSES.ID = HWM_INVOICE_ITEM.ID_LICENSES\n"
				+ "WHERE HWM_INVOICE_ITEM.ID_HWM_INVOICE = ?\n";
		try (PreparedStatement stmt = conn.prepareStatement(sql)) {
			stmt.setLong(1, invoiceId);
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					long funcLimited = rs.getLong("FUNC_LIMITED");
					if ((funcLimited & (1L << License.FUNC_WM_HQ)) != 0)
						++wmHqQuantity;
					if ((funcLimited & (1L << License.FUNC_WM_CENTRAL_BO)) != 0)
						++wmCentralBoQuantity;
				}
			}
		}

		if (wmHqQuantity + wmCentralBoQuantity == 0)
			return null;

		ScriptEngineManager manager = new ScriptEngineManager();
		ScriptEngine engine = manager.getEngineByName("js");
		String jsHwmHqPriceFormula = commonHelper.getSettings().getHwmHqPriceFormula();
		String jsHwmCentralBoPriceFormula = commonHelper.getSettings().getHwmCentralBoPriceFormula();

		List<TOrderItem> result = new ArrayList<>();
		int itemNumber = 0;

		if (!yearly) {

			if (wmHqQuantity > 0) {
				long articleId = untillTpapiHelper.getArticleIdByBarcode(conn, "HWMHQ1M");
				double price = roundPrice(invokeJsPriceFormula(engine, jsHwmHqPriceFormula, wmHqQuantity));
				result.add(new TOrderItem(itemNumber++, articleId, 0, "", price, wmHqQuantity, new TExtraInfo[0]));
			}
			if (wmCentralBoQuantity > 0) {
				long articleId = untillTpapiHelper.getArticleIdByBarcode(conn, "HWMCBO1M");
				double price = roundPrice(invokeJsPriceFormula(engine, jsHwmCentralBoPriceFormula, wmCentralBoQuantity));
				result.add(new TOrderItem(itemNumber++, articleId, 0, "", price, wmCentralBoQuantity, new TExtraInfo[0]));
			}

		} else { // yearly

			if (firstYear) {
				assert wmHqQuantity + wmCentralBoQuantity == 1;

				// get quantity of first year licenses for same chain
				int prevWmHqQuantity = 0;
				int prevWmCentralBoQuantity = 0;
				if (chain != null) {
					try (PreparedStatement stmt = conn.prepareStatement(
							"SELECT LICENSES.FUNC_LIMITED\n"
							+ "FROM HWM_INVOICE_ITEM\n"
							+ "INNER JOIN HWM_INVOICE ON HWM_INVOICE.ID = HWM_INVOICE_ITEM.ID_HWM_INVOICE\n"
							+ "INNER JOIN LICENSES ON LICENSES.ID = HWM_INVOICE_ITEM.ID_LICENSES\n"
							+ "WHERE HWM_INVOICE.ID <> ? AND UPPER(HWM_INVOICE.CHAIN) = UPPER(?)\n"
							+ "AND HWM_INVOICE.YEARLY = 1 AND HWM_INVOICE.FIRST_YEAR = 1\n"
							+ "AND EXTRACT(YEAR FROM HWM_INVOICE.CALC_MONTH) = ?\n"
							+ "AND (ORDER_REQUIRED IS NULL OR ORDER_REQUIRED = 0)\n"
					)) {
						stmt.setLong(1, invoiceId);
						stmt.setString(2, chain);
						stmt.setShort(3, (short) calcMonth.getYear());
						try (ResultSet rs = stmt.executeQuery()) {
							while (rs.next()) {
								long funcLimited = rs.getLong("FUNC_LIMITED");
								if ((funcLimited & (1L << License.FUNC_WM_HQ)) != 0)
									++prevWmHqQuantity;
								if ((funcLimited & (1L << License.FUNC_WM_CENTRAL_BO)) != 0)
									++prevWmCentralBoQuantity;
							}
						}
					}
				}

				long daysToEndOfYear = calcMonth.until(LocalDate.of(calcMonth.getYear() + 1, Month.JANUARY, 1), ChronoUnit.DAYS);
				double prevYearlyPrice = prevWmHqQuantity == 0 ? 0 : invokeJsPriceFormula(engine, jsHwmHqPriceFormula, prevWmHqQuantity) * 12;
				for (int i = 0; i < wmHqQuantity; i++) {
					long articleId = untillTpapiHelper.getArticleIdByBarcode(conn, "HWMHQ1D");
					double yearlyPrice = invokeJsPriceFormula(engine, jsHwmHqPriceFormula, prevWmHqQuantity + i + 1) * 12;
					double price = roundPrice((yearlyPrice * (prevWmHqQuantity + i + 1) - prevYearlyPrice * (prevWmHqQuantity + i)) / 365);
					result.add(new TOrderItem(itemNumber++, articleId, 0, "", price, (int) daysToEndOfYear, new TExtraInfo[0]));
					prevYearlyPrice = yearlyPrice;
				}
				prevYearlyPrice = prevWmCentralBoQuantity == 0 ? 0 : invokeJsPriceFormula(engine, jsHwmCentralBoPriceFormula, prevWmCentralBoQuantity) * 12;
				for (int i = 0; i < wmCentralBoQuantity; i++) {
					long articleId = untillTpapiHelper.getArticleIdByBarcode(conn, "HWMCBO1D");
					double yearlyPrice = invokeJsPriceFormula(engine, jsHwmCentralBoPriceFormula, prevWmCentralBoQuantity + i + 1) * 12;
					double price = roundPrice((yearlyPrice * (prevWmCentralBoQuantity + i + 1) - prevYearlyPrice * (prevWmCentralBoQuantity + i)) / 365);
					result.add(new TOrderItem(itemNumber++, articleId, 0, "", price, (int) daysToEndOfYear, new TExtraInfo[0]));
					prevYearlyPrice = yearlyPrice;
				}

			} else { // firstYear == false
				assert calcMonth.getMonth() == Month.JANUARY && calcMonth.getDayOfMonth() == 1;

				if (wmHqQuantity > 0) {
					long articleId = untillTpapiHelper.getArticleIdByBarcode(conn, "HWMHQ1Y");
					double price = roundPrice(invokeJsPriceFormula(engine, jsHwmHqPriceFormula, wmHqQuantity) * 12);
					result.add(new TOrderItem(itemNumber++, articleId, 0, "", price, wmHqQuantity, new TExtraInfo[0]));
				}
				if (wmCentralBoQuantity > 0) {
					long articleId = untillTpapiHelper.getArticleIdByBarcode(conn, "HWMCBO1Y");
					double price = roundPrice(invokeJsPriceFormula(engine, jsHwmCentralBoPriceFormula, wmCentralBoQuantity) * 12);
					result.add(new TOrderItem(itemNumber++, articleId, 0, "", price, wmCentralBoQuantity, new TExtraInfo[0]));
				}

			}

		}

		return result;
	}

	private double invokeJsPriceFormula(ScriptEngine engine, String priceFormula, int quantity) throws ScriptException {
		engine.put("quantity", quantity);
		return (double) engine.eval(priceFormula);
	}

	private double roundPrice(double price) {
		return (double) Math.round(price * 100.0) / 100.0;
	}

	/**
	 * Generate pdf-file and send invoice
	 * @param conn
	 * @throws SQLException
	 */
	public void generateAndSend(Connection conn) throws SQLException {
		while (true) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {
				long invoiceId = 0, dealerId = 0, billId = 0;
				boolean yearly = false;
				LocalDate calcMonth = null;
				String clientNameOrChain = null;
				boolean nothingFound = true;
				String sql = "SELECT ID, ID_DEALERS, ID_BILL, YEARLY, CALC_MONTH, (\n"
						+ "  SELECT CASE WHEN COUNT(*) = 1 THEN MAX(LICENSES.CLIENT_NAME) ELSE MAX(LICENSES.CHAIN) END\n"
						+ "  FROM LICENSES\n"
						+ "  INNER JOIN HWM_INVOICE_ITEM ON HWM_INVOICE_ITEM.ID_LICENSES = LICENSES.ID\n"
						+ "  WHERE HWM_INVOICE_ITEM.ID_HWM_INVOICE = HWM_INVOICE.ID\n"
						+ ") CLIENT_NAME_OR_CHAIN\n"
						+ "FROM HWM_INVOICE\n"
						+ "WHERE INVOICE_REQUIRED > 0\n"
						+ "  AND ID_BILL IS NOT NULL\n" // additional check
						+ "ORDER BY CALC_MONTH, ID\n"
						// TODO: + "ROWS 1\n"
						+ "FOR UPDATE\n";
				try (
					PreparedStatement stmt = conn.prepareStatement(sql);
					ResultSet rs = stmt.executeQuery();
				) {
					if (rs.next()) {
						invoiceId = rs.getLong("ID");
						dealerId = rs.getLong("ID_DEALERS");
						billId = rs.getLong("ID_BILL");
						yearly = rs.getShort("YEARLY") != 0;
						calcMonth = rs.getDate("CALC_MONTH").toLocalDate();
						clientNameOrChain = rs.getString("CLIENT_NAME_OR_CHAIN");
						nothingFound = false;
					}
				}
				if (nothingFound) {
					conn.commit(); // or rollback()
					break;
				}

				synchronized (commonHelper.getSettingsLock()) {
					Settings settings = commonHelper.getSettings();

					LocalDate calcMonthPlusOne = calcMonth.plusMonths(1);
					String clientNameOrChainWithMonthPlusOne = String.format("%s (%s)", clientNameOrChain,
							yearly ? calcMonth.format(DateTimeFormatter.ofPattern("yyyy")) :
							calcMonthPlusOne.format(DateTimeFormatter.ofPattern("MMM yyyy", Locale.ENGLISH)));
					
					// Generate invoice pdf
					Invoice invoice = invoiceHelper.generateInvoiceForDealer(conn, settings,
							commonHelper.getCurrentDate(), billId, dealerId, clientNameOrChainWithMonthPlusOne);
					if (invoice == null) // TODO rewrite error handling
						throw new RuntimeException("invoiceHelper.generateInvoiceForDealer() error");
					try {

						try (PreparedStatement stmt = conn.prepareStatement(
								"UPDATE HWM_INVOICE SET\n"
								+ "INVOICE_REQUIRED = 0,\n"
								+ "INVOICE_DATE = ?,\n"
								+ "INVOICE_NUMBER = ?\n"
								+ "WHERE ID = ?\n")) {
							stmt.setDate(1, new java.sql.Date(invoice.getDate().getTime()));
							stmt.setLong(2, (invoice.isPositive() ? +1 : -1) * invoice.getNumber());
							stmt.setLong(3, invoiceId);
							if (stmt.executeUpdate() == 0) throw new RuntimeException();
						}

						if (invoice.getEmails() != null) {
							List<String> emailList = Common.parseEmailList(invoice.getEmails());
							if (!emailList.isEmpty()) {
								if (!emailHelper.emailInvoice(invoice.getFile(), invoice.getDate(),
										invoice.getFullNumber(), emailList, null, clientNameOrChainWithMonthPlusOne))
									throw new RuntimeException("emailHelper.emailInvoice() error"); // TODO rewrite error handling
							} else {
								LOG.warn("Dealer has no emails for invoices: " + invoice.getFile().getName());
							}
						} else {
							LOG.warn("Dealer has no invoice emails: " + invoice.getFile().getName());
						}

						// Set next invoice number and save
						if (invoice.isPositive()) {
							settings.setNextInvoiceNumber(invoice.getNumber() + 1);
						} else {
							settings.setNextNegativeInvoiceNumber(invoice.getNumber() + 1);
						}
						settings.save();
						LOG.info(String.format("Generate and send HWM-invoice: {id: %d, number: %d, emails: %s}", invoiceId, invoice.getNumber(), invoice.getEmails()));
					} catch (Exception e) {
						invoice.getFile().delete();
						throw e;
					}
				}
				conn.commit();
			} catch (SQLException e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}

		} // while (true)
	}

}
