/**
 * 
 */
package eu.untill.license.server;

import java.io.InputStreamReader;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.sql.Array;
import java.sql.Blob;
import java.sql.CallableStatement;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.DriverManager;
import java.sql.NClob;
import java.sql.PreparedStatement;
import java.sql.SQLClientInfoException;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.sql.SQLXML;
import java.sql.Savepoint;
import java.sql.Struct;
import java.time.Instant;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.Executor;

import org.dbunit.Assertion;
import org.dbunit.database.DatabaseConnection;
import org.dbunit.database.IDatabaseConnection;
import org.dbunit.dataset.CompositeDataSet;
import org.dbunit.dataset.DataSetException;
import org.dbunit.dataset.IDataSet;
import org.dbunit.dataset.ReplacementDataSet;
import org.dbunit.dataset.xml.FlatXmlDataSetBuilder;
import org.dbunit.operation.DatabaseOperation;
import org.h2.tools.RunScript;
import org.junit.rules.TestRule;
import org.junit.runner.Description;
import org.junit.runners.model.Statement;

/**
 * Usage like this:
 * 
 *	class SomeTestClass {
 *		@Rule
 *		public final TestDb testDb;
 * 		
 * 		public SomeTestClass() throws Exception {
 * 			testDb = TestDb.getInstance();
 * 		}
 * 
 * 		@Test
 * 		@TestDbParams(
 * 			init = {"TestDb-base", "TestDb-common"},
 * 			check = {"TestDb-base", "TestDb-common", "TestDb-licenseRequest"},
 * 			nextId = 123
 * 		)
 * 		public testSomeTest() throws Exception {
 * 			// TODO ...
 * 		}
 * 	}
 * 
 * <AUTHOR>
 * 
 */
public class TestDb implements TestRule {

	private static long nextId = 5000000001L;
	public static long generateTableId() {
		return nextId++;
	}
	public static long binAnd(long a, long b) {
		return a & b;
	}
	public static long binShl(long a, int b) {
		return a << b;
	}

	@Retention(RetentionPolicy.RUNTIME)
	@Target(ElementType.METHOD)
	public static @interface TestDbParams {
		String[] init() default {};
		String[] check() default {};
		long nextId() default 5000000001L;
		String currentDate() default "";
	}

	private static TestDb instance = null;

	public static TestDb getInstance() throws Exception {
		if (instance == null) {
			instance = new TestDb();
		}
		return instance;
	}

	private final String dbUniqueName;
	private final Date currentDate;
	private final IDatabaseConnection databaseConnection;
	private final DatabaseOperation setUpOperation;
	private final DatabaseOperation tearDownOperation;
	private final IDataSet defaultDataSet;
	private final CorrectiveConnection connection;

	private TestDbParams params;

	private IDataSet initDataSet;
	
	public TestDb() throws Exception {

		dbUniqueName = UUID.randomUUID().toString();

		// create new in-memory DB
		Connection conn = getNewConnection();
		conn.setAutoCommit(false);

		// create DB structure
		RunScript.execute(conn, new InputStreamReader(this.getClass().getResourceAsStream(
				"testdb/structure.sql")));

		this.currentDate = new Date();
		this.databaseConnection = new DatabaseConnection(conn);
		this.setUpOperation = DatabaseOperation.CLEAN_INSERT; // TODO ...
		this.tearDownOperation = DatabaseOperation.NONE; // TODO ...
		this.defaultDataSet = loadDataSet("base.xml");

		this.connection = new CorrectiveConnection(databaseConnection.getConnection());
	}

	public Date getCurrentDate() {
		return params != null && params.currentDate() != null && !params.currentDate().isEmpty() ?
				Date.from(Instant.parse(params.currentDate())) : currentDate;
	}

	public Connection getConnection() throws SQLException {
		return connection;
	}

	public Connection getNewConnection() throws SQLException {
		return DriverManager.getConnection("jdbc:h2:mem:" + dbUniqueName);
	}
	
	@Override
	public Statement apply(final Statement base, Description description) {
		this.params = description.getAnnotation(TestDbParams.class);
		return statement(base);
	}

	private Statement statement(final Statement base) {
		return new Statement() {
			@Override
			public void evaluate() throws Throwable {
				before();
				try {
					base.evaluate();
				} finally {
					databaseConnection.getConnection().rollback();
				}
				after();
			}
		};
	}

	protected void before() throws Exception {
		if (params != null) {
			// set up
			initDataSet = params.init().length > 0 ? loadDataSet(params.init()): defaultDataSet; 
			setUpOperation.execute(databaseConnection, initDataSet);
			databaseConnection.getConnection().commit();
			nextId = params.nextId();
		}
	}

	protected void after() throws Exception  {
		if (params != null) {
			// check
			if (params.check().length == 0) {
				Assertion.assertEquals(initDataSet, databaseConnection.createDataSet());
			} else {
				Assertion.assertEquals(loadDataSet(params.check()), databaseConnection.createDataSet());
			}
			// tear down
			initDataSet = null;
			tearDownOperation.execute(databaseConnection, defaultDataSet);
			databaseConnection.getConnection().commit();
		}
	}

	private IDataSet loadDataSet(String[] names) throws DataSetException {
		IDataSet[] dataSets = new IDataSet[names.length];
		for (int i = 0; i < names.length; ++i)
			dataSets[i] = loadDataSet(names[i]);
		CompositeDataSet result = new CompositeDataSet(dataSets, true, false);
		return result;
	}

	private IDataSet loadDataSet(String name) throws DataSetException {
		if (!name.contains(".")) name += ".xml";
		FlatXmlDataSetBuilder builder = new FlatXmlDataSetBuilder();
		//builder.setColumnSensing(true);
		IDataSet dataSet = builder.build(this.getClass().getResourceAsStream("testdb/" + name));
		ReplacementDataSet result = new ReplacementDataSet(dataSet);
		result.addReplacementObject("[NULL]", null);
		result.addReplacementObject("[TIME]", getCurrentDate());
		result.addReplacementObject("[DATE]", Common.getOnlyDate(getCurrentDate()));
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(Common.getOnlyDate(getCurrentDate()));
		calendar.add(Calendar.YEAR, 1);
		calendar.add(Calendar.DATE, -1);
		result.addReplacementObject("[DATE+1Y-1D]", calendar.getTime());
		result.addReplacementObject("[TIME]", getCurrentDate());

		calendar.setTime(getCurrentDate());
		calendar.add(Calendar.DATE, -1);
		result.addReplacementObject("[TIME-1D]", calendar.getTime());
		
		calendar.setTime(getCurrentDate());
		calendar.add(Calendar.WEEK_OF_YEAR, +2);
		result.addReplacementObject("[TIME+2W]", calendar.getTime());

		// ...

		return result;
	}

	public static class CorrectiveConnection implements Connection {
		private Connection conn;

		public CorrectiveConnection(Connection conn) {
			this.conn = conn;
		}

		@Override public <T> T unwrap(Class<T> iface) throws SQLException { return conn.unwrap(iface); }
		@Override public boolean isWrapperFor(Class<?> iface) throws SQLException { return conn.isWrapperFor(iface); }
		@Override public java.sql.Statement createStatement() throws SQLException { return conn.createStatement(); }
		@Override public PreparedStatement prepareStatement(String sql) throws SQLException {
			return conn.prepareStatement(translateSql(sql));
		}
		@Override public CallableStatement prepareCall(String sql) throws SQLException {
			return conn.prepareCall(translateSql(sql));
		}
		@Override public String nativeSQL(String sql) throws SQLException {
			return conn.nativeSQL(translateSql(sql));
		}
		@Override public void setAutoCommit(boolean autoCommit) throws SQLException { conn.setAutoCommit(autoCommit); }
		@Override public boolean getAutoCommit() throws SQLException { return conn.getAutoCommit(); }
		@Override public void commit() throws SQLException { conn.commit(); }
		@Override public void rollback() throws SQLException { conn.rollback(); }
		@Override public void close() throws SQLException { conn.close(); }
		@Override public boolean isClosed() throws SQLException { return conn.isClosed(); }
		@Override public DatabaseMetaData getMetaData() throws SQLException { return conn.getMetaData(); }
		@Override public void setReadOnly(boolean readOnly) throws SQLException { conn.setReadOnly(readOnly); }
		@Override public boolean isReadOnly() throws SQLException { return conn.isReadOnly(); }
		@Override public void setCatalog(String catalog) throws SQLException { conn.setCatalog(catalog); }
		@Override public String getCatalog() throws SQLException { return conn.getCatalog(); }
		@Override public void setTransactionIsolation(int level) throws SQLException { conn.setTransactionIsolation(level); }
		@Override public int getTransactionIsolation() throws SQLException { return conn.getTransactionIsolation(); }
		@Override public SQLWarning getWarnings() throws SQLException { return conn.getWarnings(); }
		@Override public void clearWarnings() throws SQLException { conn.clearWarnings(); }
		@Override public java.sql.Statement createStatement(int resultSetType, int resultSetConcurrency) throws SQLException {
			return conn.createStatement(resultSetType, resultSetConcurrency);
		}
		@Override public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
			return conn.prepareStatement(translateSql(sql), resultSetType, resultSetConcurrency);
		}
		@Override public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency) throws SQLException {
			return conn.prepareCall(translateSql(sql), resultSetType, resultSetConcurrency);
		}
		@Override public Map<String, Class<?>> getTypeMap() throws SQLException { return conn.getTypeMap(); }
		@Override public void setTypeMap(Map<String, Class<?>> map) throws SQLException { conn.setTypeMap(map); }

		@Override public void setHoldability(int holdability) throws SQLException { conn.setHoldability(holdability); }
		@Override public int getHoldability() throws SQLException { return conn.getHoldability(); }
		@Override public Savepoint setSavepoint() throws SQLException { return conn.setSavepoint(); }
		@Override public Savepoint setSavepoint(String name) throws SQLException { return conn.setSavepoint(name); }
		@Override public void rollback(Savepoint savepoint) throws SQLException { conn.rollback(savepoint); }
		@Override public void releaseSavepoint(Savepoint savepoint) throws SQLException { conn.releaseSavepoint(savepoint); }
		@Override public java.sql.Statement createStatement(int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
			return conn.createStatement(resultSetType, resultSetConcurrency, resultSetHoldability);
		}
		@Override public PreparedStatement prepareStatement(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
			return conn.prepareStatement(translateSql(sql), resultSetType, resultSetConcurrency, resultSetHoldability);
		}
		@Override public CallableStatement prepareCall(String sql, int resultSetType, int resultSetConcurrency, int resultSetHoldability) throws SQLException {
			return conn.prepareCall(translateSql(sql), resultSetType, resultSetConcurrency, resultSetHoldability);
		}
		@Override public PreparedStatement prepareStatement(String sql, int autoGeneratedKeys) throws SQLException {
			return conn.prepareStatement(translateSql(sql), autoGeneratedKeys);
		}
		@Override public PreparedStatement prepareStatement(String sql, int[] columnIndexes) throws SQLException {
			return conn.prepareStatement(translateSql(sql), columnIndexes);
		}
		@Override public PreparedStatement prepareStatement(String sql, String[] columnNames) throws SQLException {
			return conn.prepareStatement(translateSql(sql), columnNames);
		}
		@Override public Clob createClob() throws SQLException { return conn.createClob(); }
		@Override public Blob createBlob() throws SQLException { return conn.createBlob(); }
		@Override public NClob createNClob() throws SQLException { return conn.createNClob(); }
		@Override public SQLXML createSQLXML() throws SQLException { return conn.createSQLXML(); }
		@Override public boolean isValid(int timeout) throws SQLException { return conn.isValid(timeout); }
		@Override public void setClientInfo(String name, String value) throws SQLClientInfoException {
			conn.setClientInfo(name, value);
		}
		@Override public void setClientInfo(Properties properties) throws SQLClientInfoException {
			conn.setClientInfo(properties);
		}
		@Override public String getClientInfo(String name) throws SQLException { return conn.getClientInfo(name); }
		@Override public Properties getClientInfo() throws SQLException { return conn.getClientInfo(); }
		@Override public Array createArrayOf(String typeName, Object[] elements) throws SQLException {
			return conn.createArrayOf(typeName, elements);
		}
		@Override public Struct createStruct(String typeName, Object[] attributes) throws SQLException {
			return conn.createStruct(typeName, attributes);
		}
		@Override public void setSchema(String schema) throws SQLException { conn.setSchema(schema); }
		@Override public String getSchema() throws SQLException { return conn.getSchema(); }
		@Override public void abort(Executor executor) throws SQLException { conn.abort(executor); }
		@Override public void setNetworkTimeout(Executor executor, int milliseconds) throws SQLException {
			conn.setNetworkTimeout(executor, milliseconds);
		}
		@Override public int getNetworkTimeout() throws SQLException { return conn.getNetworkTimeout(); }

		private String translateSql(String sql) {
			sql = sql.replaceAll("(?i)^(\\s*SELECT\\s+)FIRST\\b", "$1TOP");
			return sql;
		}

	}
}
