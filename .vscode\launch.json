{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "UntillLicenseServer - GWT DevMode",
            "request": "launch",
            "mainClass": "com.google.gwt.dev.DevMode",
            "projectName": "UntillLicenseServer",
            "args": [
                "-war",
                "war",
                "-startupUrl",
                "UntillLicenseServer.html",
                "eu.untill.license.UntillLicenseServer"
            ],
            "vmArgs": [
                "-Xmx1g"
            ],
            "classPaths": [
                "${workspaceFolder}/src",
                "${workspaceFolder}/build/classes/java/main",
                "${workspaceFolder}/build/resources/main",
				""C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.gwt\gwt-codeserver\2.8.2\46efb7a794d107d297a3ed3f253de0ee8b49f3bd\gwt-codeserver-2.8.2.jar""
            ],
            "modulePaths": [],
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole",
            "stopOnEntry": false,
            "internalConsoleOptions": "openOnSessionStart"
        },
        {
            "type": "java",
            "name": "UntillLicenseServer - Gradle Run",
            "request": "launch",
            "mainClass": "com.google.gwt.dev.DevMode",
            "projectName": "UntillLicenseServer",
            "args": [
                "-war",
                "war",
                "-startupUrl",
                "UntillLicenseServer.html",
                "eu.untill.license.UntillLicenseServer"
            ],
            "vmArgs": [
                "-Xmx1g"
            ],
            "classPaths": [
                "${workspaceFolder}/src"
            ],
            "modulePaths": [],
            "cwd": "${workspaceFolder}",
            "env": {},
            "console": "internalConsole",
            "stopOnEntry": false,
            "internalConsoleOptions": "openOnSessionStart",
            "preLaunchTask": "gradle: build"
        }
    ]
}
