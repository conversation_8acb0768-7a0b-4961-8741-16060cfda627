println """<?xml version="1.0" encoding="UTF-8"?>
		|<data>
		|	<categories>
		|		<category>
		|			<id>1014</id>
		|			<name>Licenses-25</name>
		|		</category>
		|	</categories>
		|	<printers>
		|	</printers>
		|	<groups>
		|		<group>
		|			<id>2014</id>
		|			<name>Licenses-25</name>
		|			<vat></vat>
		|			<idCategory>1014</idCategory>
		|			<preparationArea>Licenses</preparationArea>
		|		</group>
		|	</groups>
		|	<options>
		|	</options>
		|	<departments>
		|		<department>
		|			<id>3014</id>
		|			<number>250</number>
		|			<name>Licenses-25</name>
		|			<idGroup>2014</idGroup>
		|			<minNumber></minNumber>
		|			<maxNumber></maxNumber>
		|			<supplement></supplement>
		|			<condiment></condiment>
		|		</department>
		|	</departments>
		|	<articles>""".stripMargin()

int nextId = 6400;
for (int i = 1; i <= 23; ++i) {
	for (j = 0; j < 2; ++j) {
		println """		<article>
				|			<id>${nextId++}</id>
				|			<name>unTill(R)${(j==1?' Upgrade':'')} $i pcs HHT-EFT license one year</name>
				|			<price></price>
				|			<barcode>${j==1?'U':''}${i}HE1Y</barcode>
				|			<idDepartment>3014</idDepartment>
				|			<idFreeOption></idFreeOption>
				|		</article>""".stripMargin()
	}
}

println """	</articles>
		|	<option_structures>
		|	</option_structures>
		|	<art_options>
		|	</art_options>
		|	<users>
		|	</users>
		|</data>""".stripMargin()
