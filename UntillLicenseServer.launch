<?xml version="1.0" encoding="UTF-8"?>
<launchConfiguration type="org.eclipse.jdt.launching.localJavaApplication">
  <booleanAttribute key="org.eclipse.jdt.launching.DEFAULT_CLASSPATH" value="false" />
  <stringAttribute key="org.eclipse.jdt.launching.MAIN_TYPE" value="com.google.gwt.dev.DevMode" />
  <listAttribute key="org.eclipse.jdt.launching.CLASSPATH">
    <listEntry value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#10;&lt;runtimeClasspathEntry containerPath=&quot;org.eclipse.jdt.launching.JRE_CONTAINER&quot; javaProject=&quot;UntillLicenseServer&quot; path=&quot;1&quot; type=&quot;4&quot;/&gt;&#10;" />
    <listEntry value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#10;&lt;runtimeClasspathEntry externalArchive=&quot;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.gwt\gwt-codeserver\2.8.2\46efb7a794d107d297a3ed3f253de0ee8b49f3bd\gwt-codeserver-2.8.2.jar&quot; path=&quot;3&quot; type=&quot;2&quot;/&gt;&#10;" />
    <listEntry value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#10;&lt;runtimeClasspathEntry internalArchive=&quot;/UntillLicenseServer/src&quot; path=&quot;3&quot; type=&quot;2&quot;/&gt;&#10;" />
    <listEntry value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#10;&lt;runtimeClasspathEntry id=&quot;org.eclipse.jdt.launching.classpathentry.defaultClasspath&quot;&gt;&#10;&lt;memento project=&quot;UntillLicenseServer&quot;/&gt;&#10;&lt;/runtimeClasspathEntry&gt;&#10;" />
  </listAttribute>
  <stringAttribute key="org.eclipse.jdt.launching.PROGRAM_ARGUMENTS" value="-war war -startupUrl UntillLicenseServer.html eu.untill.license.UntillLicenseServer" />
  <stringAttribute key="org.eclipse.jdt.launching.PROJECT_ATTR" value="UntillLicenseServer" />
  <booleanAttribute key="org.eclipse.debug.core.appendEnvironmentVariables" value="true" />
  <stringAttribute key="org.eclipse.jdt.launching.VM_ARGUMENTS" value="-Xmx1g" />
</launchConfiguration>