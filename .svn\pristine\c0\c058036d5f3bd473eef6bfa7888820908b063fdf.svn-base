package eu.untill.license.client;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.google.gwt.user.client.rpc.IsSerializable;
import com.google.gwt.user.client.rpc.RemoteService;
import com.google.gwt.user.client.rpc.RemoteServiceRelativePath;

import eu.untill.license.shared.ArticleWithPrices;
import eu.untill.license.shared.ClientNameChange;
import eu.untill.license.shared.DbHwmInvoice;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.DbLicenseOnline;
import eu.untill.license.shared.FiscalOrganisation;
import eu.untill.license.shared.ShProductInfo;
import eu.untill.license.shared.SuHost;
import eu.untill.license.shared.SuLicense;
import eu.untill.license.shared.SuTaskBundle;
import eu.untill.license.shared.SuTaskListOrder;

/**
 * The client-side stub for the RPC service.
 */
@RemoteServiceRelativePath("license")
public interface LicenseService extends RemoteService {

	  ////////////////////
	 // Server methods //
	////////////////////

	/**
	 * Checks authorization and returns a dealer object
	 *
	 * @param authScheme
	 * @return dealer object or null
	 * @throws DbErrorException
	 * @throws ServerErrorException
	 */
	public RrDealer login(AuthScheme authScheme)
			throws DbErrorException, ServerErrorException;

	/**
	 * Returns a list of dealers (available only for super dealers)
	 *
	 * @param authScheme
	 * @return list of dealers (null, if is not super dealer)
	 * @throws LicenseServiceException
	 */
	public List<RrDealer> getAllDealers(AuthScheme authScheme)
			throws LicenseServiceException;

	public List<RrDealer> getApostillDealers(AuthScheme authScheme)
			throws LicenseServiceException;

	/**
	 * Returns a list of client names (and client display names)
	 *
	 * Auxiliary method. Maximum number of returned elements defines server.
	 * @param authScheme
	 * @param dealerId - only for super dealer (<tt>0</tt> - all dealers)
	 * @return list of client names
	 * @throws LicenseServiceException
	 */
	public List<String> getClientNames(AuthScheme authScheme, long dealerId, boolean onlyOnline) throws LicenseServiceException;

	public List<String> getHardCodes(AuthScheme authScheme, long dealerId, boolean onlyOnline) throws LicenseServiceException;

	/**
	 * Returns a list of licenses matching filters
	 * <tt>clientNameFilter</tt> and <tt>hardCodeFilter</tt>.
	 * <br>
	 * Maximum number of returned elements defines server.
	 *
	 * @param authScheme
	 * @param dealerId - only for super dealer (<tt>0</tt> - all dealers)
	 * @param sortingType - sorting field and direction
	 * @param clientNameFilter <tt>null</tt> - no filter
	 * @param hardCodeFilter <tt>null</tt> - no filter
	 * @param showOnlyTrial - Show also a trial/demo licenses
	 * @param showCanceled - Show also a canceled licenses (only for super dealer)
	 * @param showOnlyNonApproved - Show only non-approved licenses (only for super dealer)
	 * @return list of licenses
	 * @throws LicenseServiceException
	 */
	public List<DbLicense> getLicenseList(AuthScheme authScheme, long dealerId,
			SortingType sortingType, String clientNameFilter, String hardCodeFilter,
			boolean showOnlyTrial, boolean showCanceled, boolean showOnlyNonApproved)
			throws LicenseServiceException;

	public LicensePeriod getNewLicensePeriod(LicenseType licenseType);

	public LicensePeriod getProlongLicensePeriod(LicenseType licenseType,
			LicensePeriod prolongedLicensePeriod);

	public LicensePeriod getEmergencyLicensePeriod(Date baseExpiredDate);

	public boolean validLicenseHardCode(String hardCode);

	/**
	 * Request for <b>create</b>, <b>prolong</b> or <b>upgrade</b> unTill license or create <b>emergency</b> unTill License
	 *
	 * @param authScheme
	 * @param license - requested license [license.id - previous license id (ignored for <b>create</b>);
	 * license.idDealers - used only for <b>create</b>; license.issueDate - ignored; ...]
	 * @param requestType
	 * @return request result object
	 * @throws LicenseServiceException
	 */
	public RequestLicenseResult requestLicense(AuthScheme authScheme, DbLicense license,
			RequestType requestType) throws LicenseServiceException;

	public AddApostillHddCodesResult addApostillHddCodes(AuthScheme authScheme,
			long apostillDealerId, String hddCodes) throws LicenseServiceException;

	// This method is not currently used, but it is in working condition
	public boolean voidRequest(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;

	public boolean resendRequest(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;

	/**
	 * Request for re-sending license
	 *
	 * @param authScheme
	 * @param licenseId
	 * @param tempLicense
	 * @return
	 * @throws LicenseServiceException
	 */
	public boolean resendLicense(AuthScheme authScheme, long licenseId, Date tempLicenseEndDate)
			throws LicenseServiceException;

	/**
	 * 
	 * @param authScheme
	 * @param licenseId
	 * @return null - if order is not exist
	 * @throws LicenseServiceException
	 */
	public RrOrder getLicenseOrder(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;

	/**
	 * Add discount for the license with price and text (only for super dealers)
	 *
	 * @param authScheme
	 * @param licenseId
	 * @param price
	 * @param text
	 * @return
	 * @throws LicenseServiceException
	 */
	public void addDiscount(AuthScheme authScheme, long licenseId, double price,
			String text) throws LicenseServiceException;

	/**
	 * Add negative article for the license with text (only for super dealers)
	 *
	 * @param authScheme
	 * @param licenseId
	 * @param articleId
	 * @param quantity
	 * @param text
	 * @return
	 * @throws LicenseServiceException
	 */
	public void addNegativeArticle(AuthScheme authScheme, long licenseId,
			long articleId, int quantity, String text) throws LicenseServiceException;

	public void addArticle(AuthScheme authScheme, long licenseId, long articleId, int quantity, double manualPrice,
			String text) throws LicenseServiceException;

	/**
	 * Approve the license (only for super dealers)
	 *
	 * @param authScheme 
	 * @param licenseId
	 * @param noInvoice
	 * @return
	 * @throws LicenseServiceException
	 */
	public void approveLicense(AuthScheme authScheme, long licenseId, boolean noInvoice)
			throws LicenseServiceException;

	/**
	 * Order manual license (only for super dealers)
	 *
	 * @param authScheme
	 * @param licenseId
	 * @throws LicenseServiceException
	 */
	public void orderManualLicense(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;

	/**
	 * Recreate invoice (only for super dealers)
	 *
	 * @param authScheme
	 * @param licenseId
	 * @throws LicenseServiceException
	 */
	public void recreateInvoice(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;

	/**
	 * Reorder license (only for super dealers)
	 *
	 * @param authScheme
	 * @param licenseId - license must be non-approved
	 * @return
	 * @throws LicenseServiceException
	 */
	public CommonRpcResult reorderLicense(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;


	/**
	 * Returns the license status non-approved (Approval) (only for super dealers)
	 *
	 * @param authScheme
	 * @param licenseId - license must be approved
	 * @throws LicenseServiceException
	 */
	public void disapproveLicense(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException;

	/**
	 * Returns maximum version for Definitive licenses from settings
	 *
	 * @param authScheme
	 * @return result
	 * @throws LicenseServiceException
	 */
	public int getMaxDefinitiveMaxVersion(AuthScheme authScheme)
			throws LicenseServiceException;

	public CommonRpcResult createCreditInvoice(AuthScheme authScheme, long dealerId,
			List<RrOrderItem> orderItems, String clientName) throws LicenseServiceException;

	public List<RrArticle> getManualArticles(AuthScheme authScheme) throws LicenseServiceException;

	public void reinstallLicense(AuthScheme authScheme, long licenseId) throws LicenseServiceException;

	public DbLicenseOnline getLicenseOnlineDetails(AuthScheme authScheme, long licenseId) throws LicenseServiceException;

	public List<String> getChains(AuthScheme authScheme, long dealerId, boolean onlyOnline) throws LicenseServiceException;


	public Map<String, ShProductInfo> getProductList() throws LicenseServiceException;

	public Map<String, Boolean> getProductVersionList(String product) throws LicenseServiceException;


	public int getSuTaskBundleCount(AuthScheme authScheme, long dealerId) throws LicenseServiceException;

	public List<SuTaskBundle> getSuTaskBundleList(AuthScheme authScheme, long dealerId, SuTaskListOrder order,
			int from, int to) throws LicenseServiceException;

	public void createSuTaskBundle(AuthScheme authScheme, SuTaskBundle taskBundle) throws LicenseServiceException;

	public List<SuHost> getSuHostsByLicenseIds(AuthScheme authScheme, Set<Long> licenseIds)
			throws LicenseServiceException;

	public List<SuHost> getSuHostsByHardCodes(AuthScheme authScheme,Set<String> licenseHardCodes)
			throws LicenseServiceException;

	public List<SuHost> getSuHostsByDealerId(AuthScheme authScheme, long dealerId)
			throws LicenseServiceException;

	SuLicense getSuLicense(AuthScheme authScheme, String hardCode) throws LicenseServiceException;
	List<SuHost> getSuHostsByHardCode(AuthScheme authScheme, String hardCode) throws LicenseServiceException;
	void unregisterSuHost(AuthScheme authScheme, long hostId) throws LicenseServiceException;
	void setSuLicenseHostRegPass(AuthScheme authScheme, String hardCode, String hostRegPass) throws LicenseServiceException;


	/**
	 * Return list of client name changes (only for super dealers)
	 * @param authScheme
	 * @param dealerId
	 * @param filter
	 * @return
	 * @throws LicenseServiceException
	 */
	List<ClientNameChange> getClientNameChanges(AuthScheme authScheme, long dealerId, String filter) throws LicenseServiceException;

	/**
	 * Returns a list of license boosts
	 *
	 * @param authScheme
	 * @param licenseUid
	 * @return list of license boosts
	 * @throws LicenseServiceException
	 */
	public List<DbLicense> getLicenseBoosts(AuthScheme authScheme, String licenseUid) throws LicenseServiceException;

	public RrArticle getCreditArticle(AuthScheme authScheme) throws LicenseServiceException;
	public RrArticle getHandlingFeeArticle(AuthScheme authScheme) throws LicenseServiceException;

	List<DbHwmInvoice> getHwmInvoiceReport(AuthScheme authScheme, Date month) throws LicenseServiceException;

	List<ArticleWithPrices> getDealerPrices(AuthScheme authScheme, long dealerId, String pricesPass) throws LicenseServiceException;

	String getLicenseComment(AuthScheme authScheme, long licenseId) throws LicenseServiceException;
	void setLicenseComment(AuthScheme authScheme, long licenseId, String licenseComment) throws LicenseServiceException;

	List<FiscalOrganisation> getFiscalOrganisations(AuthScheme authScheme, long dealerId) throws LicenseServiceException;

	  ///////////////////////
	 // Exception classes //
	///////////////////////

	public abstract class LicenseServiceException extends Exception {
		private static final long serialVersionUID = -7497496143327230945L;
		public LicenseServiceException() { }
		public LicenseServiceException(String message) { super(message); }
		public LicenseServiceException(Throwable cause) { super(cause); }
		public LicenseServiceException(String message, Throwable cause) { super(message, cause); }
	}

	public class AuthFailedException extends LicenseServiceException {
		private static final long serialVersionUID = -3789654774299408230L;
	}
	public class InvalidArgumentException extends LicenseServiceException {
		private static final long serialVersionUID = -3370414414010431945L;
		public InvalidArgumentException() { super(); }
		public InvalidArgumentException(String string) { super(string); }
	}
	public class ServerErrorException extends LicenseServiceException {
		private static final long serialVersionUID = -4176707450734553378L;
		public ServerErrorException() { super(); }
		public ServerErrorException(String string) { super(string); }
	}
	public class DbErrorException extends LicenseServiceException {
		private static final long serialVersionUID = 6701934271824130669L;
		public DbErrorException() { super(); }
		public DbErrorException(String string) { super(string); }
	}

	  ///////////////////////
	 // Auxiliary classes //
	///////////////////////

	static enum RequestType {
		CREATE { @Override public String toString() { return "create"; } },
		PROLONG { @Override public String toString() { return "prolong"; } },
		UPGRADE { @Override public String toString() { return "upgrade"; } },
		EMERGENCY { @Override public String toString() { return "emergency"; } },
		CREATE_BOOST { @Override public String toString() { return "create_boost"; } },
	}

	static enum LicenseType {
		GENERAL { @Override public String toString() { return "general"; } },
		DEMO { @Override public String toString() { return "demo"; } },
		APOSTILL { @Override public String toString() { return "apostill"; } },
		APOSTILL_TRIAL{ @Override public String toString() { return "apostill_trial"; } },
		SAAS { @Override public String toString() { return "saas"; } },
		BOOST { @Override public String toString() { return "boost"; } },
	}

	static class RequestLicenseResult implements IsSerializable {

		public enum Results {
			FAIL_UNEXPECTED, // message in unexpectedMessage
			FAIL_ILLEGAL_REQUEST, // without messages
			//FAIL_DEALER_IS_NOT_FOUND,
			FAIL_CLIENT_NAME_IS_EMPTY,
			FAIL_CLIENT_DISPLAY_NAME_IS_EMPTY,
			FAIL_HARD_CODE_IS_NO_VALID, // only for CREATE request
			FAIL_LB_CONNECTIONS_IS_NEGATIVE,
			FAIL_LB_CONNECTIONS_IS_DOWNGRADE,
			FAIL_REM_CONNECTIONS_IS_NEGATIVE,
			FAIL_REM_CONNECTIONS_IS_DOWNGRADE,
			FAIL_OM_CONNECTIONS_IS_NEGATIVE,
			FAIL_OM_CONNECTIONS_IS_DOWNGRADE,
//			FAIL_HQ_CONNECTIONS_IS_NEGATIVE,
//			FAIL_HQ_CONNECTIONS_IS_DOWNGRADE,
			FAIL_PS_CONNECTIONS_IS_NEGATIVE,
			FAIL_PS_CONNECTIONS_IS_DOWNGRADE,
			FAIL_HE_CONNECTIONS_IS_NEGATIVE,
			FAIL_HE_CONNECTIONS_IS_DOWNGRADE,
			FAIL_LB_CONNECTIONS_IS_ZERO,
			FAIL_FUNC_LIMITED_DOWNGRADE, // = FAIL_ILLEGAL_REQUEST
			FAIL_PERIOD_IS_INCORRECT,
			FAIL_HARD_CODE_ALREADY_EXISTS,
			FAIL_CLIENT_ALREADY_EXISTS,
//			FAIL_ERROR_SEND_REQUEST_MESSAGE,
			FAIL_SEND_EMERGENCY_LICENSE,
			FAIL_EMERGENCY_IS_NOT_AVAILABLE,
			FAIL_DEFINITIVE_MAX_VERSION_IS_WRONG,
			FAIL_CHAIN_USE_FOR_ANOTHER_HWM_PM,
			SUCCESS,
			SUCCESS_BUT_FAIL_SEND_REQUEST_MESSAGE,
			SUCCESS_NO_UPGRADE,
		}

		Results result;
		String unexpectedMessage;

		public RequestLicenseResult() {
			this.result = null;
			this.unexpectedMessage = null;
		}

		public RequestLicenseResult(Results result) {
			this.result = result;
			this.unexpectedMessage = null;
		}

		public RequestLicenseResult(String unexpectedMessage) {
			this.result = Results.FAIL_UNEXPECTED;
			this.unexpectedMessage = unexpectedMessage;
		}

		public Results getResult() {
			return this.result;
		}

		public String getUnexpectedMessage() {
			return this.unexpectedMessage;
		}
	}

	static class LicensePeriod implements IsSerializable {
		Date startDate;
		Date expiredDate;
		public LicensePeriod() { } // for serialization
		public LicensePeriod(Date startDate, Date expiredDate) {
			super();
			this.startDate = startDate;
			this.expiredDate = expiredDate;
		}
		public Date getStartDate() { return startDate; }
		public Date getExpiredDate() { return expiredDate; }
		@Override public String toString() { return "[" + startDate.toString() + "; " + expiredDate.toString() + "]"; }
	}

	static class CommonRpcResult implements IsSerializable {
		boolean success;
		String errorMessage;
		public CommonRpcResult() {
			this.success = true;
			this.errorMessage = null;
		}
		public CommonRpcResult(String errorMessage) {
			this.success = false;
			this.errorMessage = errorMessage;
		}
		public CommonRpcResult(boolean success, String errorMessage) {
			this.success = success;
			this.errorMessage = errorMessage;
		}
		public boolean isSuccess() { return success; }
		public String getErrorMessage() { return errorMessage; }
		@Override public String toString() { return "[" + (success ? "success" : "fail") + "; " + (errorMessage == null ? "null" : "'" + errorMessage + "'") + "]"; }
	}

	static class AddApostillHddCodesResult extends CommonRpcResult {
		public AddApostillHddCodesResult() {
			this.success = true;
			this.errorMessage = null;
		}
		public AddApostillHddCodesResult(String errorMessage) {
			this.success = false;
			this.errorMessage = errorMessage;
		}
	}

	static enum SortingType {
		BY_CLIENT_NAME,
		BY_CLIENT_NAME_DESC,
		BY_CLIENT_DISPLAY_NAME,
		BY_CLIENT_DISPLAY_DESC,
		BY_CHAIN,
		BY_CHAIN_DESC,
		BY_LB_CONNECTIONS,
		BY_LB_CONNECTIONS_DESC,
		BY_REM_CONNECTIONS,
		BY_REM_CONNECTIONS_DESC,
		BY_OM_CONNECTIONS,
		BY_OM_CONNECTIONS_DESC,
//		BY_HQ_CONNECTIONS,
//		BY_HQ_CONNECTIONS_DESC,
		BY_PS_CONNECTIONS,
		BY_PS_CONNECTIONS_DESC,
		BY_HE_CONNECTIONS,
		BY_HE_CONNECTIONS_DESC,
		BY_START_DATE,
		BY_START_DATE_DESC,
		BY_ISSUE_DATE,
		BY_ISSUE_DATE_DESC,
		BY_EXPIRED_DATE,
		BY_EXPIRED_DATE_DESC,
		BY_TEMP_EXPIRED_DATE,
		BY_TEMP_EXPIRED_DATE_DESC
	}

	static class RrDealer implements IsSerializable {
		public long id;
		public String name;
		public String country;
		public boolean isSuperDealer;
		public boolean emailIsValid;
		public boolean onlyProlong;
		public boolean permanentJLoggingDefault;
		public boolean onlineDefault;
		public boolean autoProlongDef;
		public boolean pricesPassIsNotEmpty;
		public RrDealer(long id, String name, String country, boolean isSuperDealer, boolean emailIsSpecified,
				boolean onlyProlong, boolean permanentJLoggingDefault, boolean onlineDefault, boolean autoProlongDef,
				boolean pricesPassIsNotEmpty
		) {
			this.id = id;
			this.name = name;
			this.country = country;
			this.isSuperDealer = isSuperDealer;
			this.emailIsValid = emailIsSpecified;
			this.onlyProlong = onlyProlong;
			this.permanentJLoggingDefault = permanentJLoggingDefault;
			this.onlineDefault = onlineDefault;
			this.autoProlongDef = autoProlongDef;
			this.pricesPassIsNotEmpty = pricesPassIsNotEmpty;
		}
		public RrDealer() { } // for serialization
		@Override public String toString() { return "[" + name + "]"; }
	}

	static class RrOrder implements IsSerializable {
		public List<RrOrderItem> orderItemList = null;
		public String currencyCode;
		public int currencyRound;
		public float currencyRate;
		public String currencySymbol;
		public short currencySymbolAlignment;
		public double totalWoVat;
		public double totalVat;
		public double total;
		public RrOrder() { } // for serialization
		@Override public String toString() {
			return "[RrOrder: " + (orderItemList != null ? String.valueOf(orderItemList.size()) : "null")
					+ ", " + totalWoVat + ", " + totalVat + ", " + total + "]"; 
		}
	}

	static class RrOrderItem implements IsSerializable {
		public int quantity;
		public long articleId;
		public int articleNumber;
		public String articleName;
		public Double price;
		public boolean negative;
		public String text;
		
		public RrOrderItem(int quantity, long articleId, int articleNumber, String articleName,
				Double price, String text) {
			this.quantity = quantity;
			this.articleId = articleId;
			this.articleNumber = articleNumber;
			this.articleName = articleName;
			this.price = price;
			this.negative = price < 0;
			this.text = text;
		}
		public RrOrderItem(int quantity, long articleId, Double price, String text) {
			this.quantity = quantity;
			this.articleId = articleId;
			this.articleNumber = 0;
			this.articleName = "";
			this.price = price;
			this.negative = price < 0;
			this.text = text;
		}
		public RrOrderItem() { } // for serialization
		@Override public String toString() { return "[RrOrderItem: "
				+ String.valueOf(quantity) + ", " + String.valueOf(articleId) + ", "
				+ String.valueOf(articleNumber) + ", " + String.valueOf(price) + "]";
		}
	}

	static class RrArticle implements IsSerializable {
		public long id;
		public int number;
		public String name;
		public RrArticle(long id, int number, String name) {
			this.id = id;
			this.number = number;
			this.name = name;
		}
		public RrArticle() { } // for serialization
		@Override public String toString() { return "[RrArticle: "
				+ String.valueOf(id) + ", " + String.valueOf(number) + ", "
				+ String.valueOf(name) + "]";
		}
	}
}
