package eu.untill.license.server;

import java.io.IOException;

import javax.websocket.CloseReason;
import javax.websocket.CloseReason.CloseCodes;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ServerEndpoint(value = "/suws")
public class SuWsServlet {
	static final Logger LOG = LoggerFactory.getLogger(SuServlet.class);

	@OnOpen
	public void onOpen(Session session) {
		LOG.info("Connected ... " + session.getId());
	}

	@OnMessage
	public String onMessage(String message, Session session) {
		switch (message) {
		case "quit":
			try {
				session.close(new CloseReason(CloseCodes.NORMAL_CLOSURE, "Game ended"));
			} catch (IOException e) {
				throw new RuntimeException(e);
			}
			break;
		}
		return message;
	}

	@OnClose
	public void onClose(Session session, CloseReason closeReason) {
		LOG.info(String.format("Session %s closed because of %s", session.getId(), closeReason));
	}
}
