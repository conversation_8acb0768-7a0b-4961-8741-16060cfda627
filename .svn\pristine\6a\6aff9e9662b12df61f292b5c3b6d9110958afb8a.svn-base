/**
 *
 */
package eu.untill.license.server;

import java.io.IOException;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.validator.routines.EmailValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gwt.user.server.rpc.RemoteServiceServlet;

import eu.untill.license.client.AuthScheme;
import eu.untill.license.client.LicenseService;
import eu.untill.license.server.Dealer.RequiredDealerIsNotFoundException;
import eu.untill.license.server.Dealer.UnknownAuthSchemeException;
import eu.untill.license.server.LicenseHandler.ConfirmOrderResult;
import eu.untill.license.server.OnlineLicenseHandler.OnlineLicenseHandlerException;
import eu.untill.license.shared.ArticleWithPrices;
import eu.untill.license.shared.ClientNameChange;
import eu.untill.license.shared.DbHwmInvoice;
import eu.untill.license.shared.DbLicense;
import eu.untill.license.shared.DbLicenseOnline;
import eu.untill.license.shared.FiscalOrganisation;
import eu.untill.license.shared.ShProductInfo;
import eu.untill.license.shared.SuHost;
import eu.untill.license.shared.SuLicense;
import eu.untill.license.shared.SuTaskBundle;
import eu.untill.license.shared.SuTaskListOrder;

/**
 * The server-side implementation of the RPC service.
 */
@SuppressWarnings("serial")
public class LicenseServiceImpl extends RemoteServiceServlet implements LicenseService {

	static final Logger LOG = LoggerFactory.getLogger(LicenseServiceImpl.class);

	@Override
	public void init() throws ServletException {
		super.init();
		Common.init(getServletContext());
	}

	@Override
	public void destroy() {
		Common.destroy();
		super.destroy();
	}

//	private LicenseHandler getLicenseHandler(Connection conn) {
//		LOG.debug("ENTRY");
//		LicenseHandler result;
//		result = new LicenseHandler(conn, Common.getEmailHelper(),
//				Common.getUntillLicensesPluginHelper(), null);
//		return result;
//	}

//	private LicenseHandler getLicenseHandler(Connection conn, AuthScheme authScheme)
//			throws LicenseServiceException
//	{
//		LOG.debug("ENTRY ({})", authScheme);
//
//		LicenseHandler result;
//		Dealer dealer = null;
//		// Check parameters
//		if (authScheme == null) {
//			LOG.warn("Attempt to log with null authScheme");
//			AuthFailedException ex = new AuthFailedException();
//			LOG.debug("THROW", ex);
//			throw ex;
//		}
//		// Check authentication (and fill Dealer record)
//		try {
//			dealer = new Dealer(conn, authScheme);
//			// Check blocked flag
//			if (!dealer.isActive) {
//				LOG.warn("Attempt to log an inactive dealer");
//				AuthFailedException ex = new AuthFailedException();
//				LOG.debug("THROW", ex);
//				throw ex;
//			}
//		} catch (RequiredDealerIsNotFoundException e) {
//			AuthFailedException ex = new AuthFailedException();
//			LOG.debug("THROW", ex);
//			throw ex;
//		} catch (UnknownAuthSchemeException e) {
//			LOG.error("Unknown authorization scheme", e);
//			ServerErrorException ex = new ServerErrorException("Unknown authorization scheme");
//			LOG.debug("THROW", ex);
//			throw ex;
//		} catch (SQLException e) {
//			LOG.error("SQLException", e);
//			DbErrorException ex = new DbErrorException();
//			LOG.debug("THROW", ex);
//			throw ex;
//		}
//		
//		result = new LicenseHandler(conn, Common.getEmailHelper(),
//				Common.getUntillLicensesPluginHelper(), dealer);
//
//		LOG.debug("RETURN {}", result);
//		return result;
//	}

	private Dealer authorize(Connection conn, AuthScheme authScheme) throws LicenseServiceException {
		return authorize(conn, authScheme, false);
	}
	private Dealer authorize(Connection conn, AuthScheme authScheme, boolean mustBeSuperDealer)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {})", authScheme, mustBeSuperDealer);

		Dealer result;
		// Check parameters
		if (authScheme == null) {
			LOG.warn("Attempt to log with null authScheme");
			AuthFailedException ex = new AuthFailedException();
			LOG.debug("THROW", ex);
			throw ex;
		}
		// Check authentication (and fill Dealer record)
		try {
			result = new Dealer(conn, authScheme);
			// Check blocked flag
			if (mustBeSuperDealer && !result.superDealer) {
				LOG.warn("Attempt to log simple dealer as SuperDealer");
				AuthFailedException ex = new AuthFailedException();
				LOG.debug("THROW", ex);
				throw ex;
			}
		} catch (RequiredDealerIsNotFoundException e) {
			AuthFailedException ex = new AuthFailedException();
			LOG.debug("THROW", ex);
			throw ex;
		} catch (UnknownAuthSchemeException e) {
			LOG.error("Unknown authorization scheme", e);
			ServerErrorException ex = new ServerErrorException("Unknown authorization scheme");
			LOG.debug("THROW", ex);
			throw ex;
		} catch (SQLException e) {
			LOG.error("SQLException", e);
			DbErrorException ex = new DbErrorException();
			LOG.debug("THROW", ex);
			throw ex;
		}
		
		LOG.debug("RETURN {}", result);
		return result;
	}

	  ///////////////////////////////////
	 // LicenseService implementation //
	///////////////////////////////////

	@Override
	public RrDealer login(AuthScheme authScheme) throws DbErrorException, ServerErrorException {
		LOG.debug("ENTRY ({})", authScheme);

		RrDealer result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();
				Dealer dealer = new Dealer(conn, authScheme);
				result = new RrDealer(dealer.id, dealer.name, dealer.country, dealer.superDealer,
						EmailValidator.getInstance(true).isValid(dealer.email),
						dealer.onlyProlong, dealer.country.matches("(?i)" +
						Common.getSettings().getEnablePermanentJLoggingForCountries()),
						dealer.name.matches("(?i)" + Common.getSettings().getDefaultOnlineForDealers()),
						dealer.autoProlongDef, !dealer.pricesPass.isEmpty());
			} catch (RequiredDealerIsNotFoundException e) {
				LOG.warn("Dealer is not found", e);
				result = null;
			} catch (UnknownAuthSchemeException e) {
				LOG.error("Unknown authorization scheme", e);
				throw new ServerErrorException("Unknown authorization scheme");
			} catch (SQLException e) {
				LOG.error("SQLException", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (ServerErrorException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<String> getClientNames(AuthScheme authScheme, long dealerId, boolean onlyOnline) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);

		List<String> result;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getClientNames(conn, authorize(conn, authScheme), dealerId, onlyOnline);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public List<String> getHardCodes(AuthScheme authScheme, long dealerId, boolean onlyOnline) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);

		List<String> result;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getHardCodes(conn, authorize(conn, authScheme), dealerId, onlyOnline);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public List<RrDealer> getAllDealers(AuthScheme authScheme)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({})", authScheme);

		List<RrDealer> result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().getAllDealers(conn, authorize(conn, authScheme));

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (ServerErrorException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<RrDealer> getApostillDealers(AuthScheme authScheme)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({})", authScheme);

		List<RrDealer> result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().getApostillDealers(conn, authorize(conn, authScheme));

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (ServerErrorException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<DbLicense> getLicenseList(AuthScheme authScheme, long dealerId,
			SortingType sortingType, String clientNameFilter, String hardCodeFilter,
			boolean showOnlyTrial, boolean showCanceled, boolean showOnlyNonApproved)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {}, {}, {})", authScheme, dealerId, sortingType,
				clientNameFilter, hardCodeFilter, showOnlyTrial, showCanceled, showOnlyNonApproved);

		List<DbLicense> result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().getLicenseList(conn, authorize(conn, authScheme), dealerId, sortingType,
						clientNameFilter, hardCodeFilter, showOnlyTrial, showCanceled,
						showOnlyNonApproved, null, false, false);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (ServerErrorException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public LicensePeriod getNewLicensePeriod(LicenseType licenseType) {
		LOG.debug("ENTRY ({})", licenseType);

		LicensePeriod result = Common.getLicenseHandler().getNewLicensePeriod(licenseType);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public LicensePeriod getProlongLicensePeriod(LicenseType licenseType,
			LicensePeriod prolongedLicensePeriod)
	{
		LOG.debug("ENTRY ({}, {})", licenseType, prolongedLicensePeriod);

		LicensePeriod result = Common.getLicenseHandler().getProlongLicensePeriod(licenseType,
				prolongedLicensePeriod);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public LicensePeriod getEmergencyLicensePeriod(Date baseExpiredDate) {
		LOG.debug("ENTRY ({})", baseExpiredDate);

		LicensePeriod result = Common.getLicenseHandler().getEmergencyLicensePeriod(baseExpiredDate);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean validLicenseHardCode(String hardCode) {
		LOG.debug("ENTRY ({})", hardCode);

		boolean result = Common.getLicenseHandler().validLicenseHardCode(hardCode);

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public RequestLicenseResult requestLicense(AuthScheme authScheme, DbLicense license,
			RequestType requestType) throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {}, {})", authScheme, license, requestType);

		RequestLicenseResult result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().requestLicense(conn, authorize(conn, authScheme), license,
						requestType);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public AddApostillHddCodesResult addApostillHddCodes(AuthScheme authScheme,
			long apostillDealerId, String hddCodes) throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {}, {})", authScheme, apostillDealerId, hddCodes);

		AddApostillHddCodesResult result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().addApostillHddCodes(conn, authorize(conn, authScheme), apostillDealerId,
						hddCodes);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	// XXX This method is not currently used, but it is in working condition
	@Override
	public boolean voidRequest(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		boolean result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().voidRequest(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean resendRequest(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		boolean result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().resendRequest(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean resendLicense(AuthScheme authScheme, long licenseId, Date tempLicenseEndDate)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {}, {})", authScheme, licenseId, tempLicenseEndDate);
		boolean result = false;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().resendLicense(conn, authorize(conn, authScheme), licenseId,
						tempLicenseEndDate);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	  /////////////////////
	 // Service methods //
	/////////////////////

	@Override
	protected void service(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		req.setCharacterEncoding("utf-8");

		Common.setServletRequestUrl(req.getRequestURL().toString());

		String laApprUid = req.getParameter("la_appr_uid");
		if (laApprUid != null) {
			boolean deny = req.getParameter("deny") != null;
			serviceLaApprRequest(resp, laApprUid, deny);
			return;
		}

		String confirmUid = req.getParameter("confirm_uid");
		if (confirmUid != null) {
			boolean cancel = req.getParameter("cancel") != null;
			String comment = req.getParameter("comment");
			serviceConfirmRequest(resp, confirmUid, cancel, comment);
			return;
		}

		String approveUid = req.getParameter("approve_uid");
		if (approveUid != null) {
			boolean toObject = req.getParameter("object") != null;
			String comment = req.getParameter("comment");
			serviceApproveLicense(resp, approveUid, toObject, comment);
			return;
		}

		String apostillHddCode = req.getParameter("apostillHddCode");
		if (apostillHddCode != null) {
			String apostillDealer = req.getParameter("dealer");
			String client = req.getParameter("client");
			String city = req.getParameter("city");
			String activate = req.getParameter("activate"); // null, "trial", "normal"
			String version = req.getParameter("version"); // null, 136 or above
			Connection conn = null;
			try {
				conn = Common.getConnection();
				Common.getLicenseHandler().serviceApostillRequest(conn, apostillHddCode, apostillDealer,
						client, city, activate, version, resp);
			} catch (SQLException e) {
				try { if (conn != null) conn.rollback(); } catch (Exception ee) { }
				// XXX copy of LicenseHandler.serviceApostillRequest_sendError
				LOG.error("SQLException", e);
				resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
				resp.addHeader("Error-Message", "SQLException");
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}
			return;
		}

		String onlineLicenseId = req.getParameter("onlineLicenseId");
		if (onlineLicenseId != null) {
			String productName = req.getParameter("productName");
			String productVersion = req.getParameter("productVersion");
			String databaseInfo = req.getParameter("databaseInfo");
			String previousSignature = req.getParameter("previousSignature");

			try (Connection conn = Common.getConnection()) {

				conn.setAutoCommit(false); // begin transaction
				// TODO: conn.setTransactionIsolation(Connection.TRANSACTION_SERIALIZABLE);

				byte[] licenseContent = Common.getOnlineLicenseHandler().serviceOnlineLicenseRequest(conn,
						onlineLicenseId, productName, productVersion, databaseInfo, previousSignature);
				resp.setContentType("application/octet-stream");
				resp.setContentLength(licenseContent.length);
				ServletOutputStream os = resp.getOutputStream();
				os.write(licenseContent);
				os.flush();

				conn.commit(); // end transaction

			} catch (OnlineLicenseHandlerException e) {
				LOG.warn(e.getClass().getSimpleName() + (e.getMessage() != null ? ": " + e.getMessage() : ""));
				resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
				resp.addHeader("Error-Code", e.getClass().getSimpleName());
				if (e.getMessage() != null)
					resp.addHeader("Error-Message", e.getMessage());
			} catch (Exception e) {
				LOG.error("", e);
				resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
			}
			return;
		}

		super.service(req, resp);
	}

	private void serviceLaApprRequest(HttpServletResponse resp, String laApprUid, boolean deny) throws IOException {
		resp.setContentType("text/html");
		PrintWriter out = resp.getWriter();
		out.println("<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
		out.println("<html>");
		out.println(" <head>");
		out.println("  <title>unTill License Server</title>");
		out.println(" </head>");
		out.println(" <body style=\"font-family:arial,sans-serif;background:#EEEEEE;\">");
		out.println("  <table style=\"width: 100%;\" cellpadding=\"0\" cellspacing=\"0\">");
		out.println("   <tbody>");
		out.println("    <tr>");
		out.println("     <td style=\"background-color:#000000;color:#FFFFFF;font-size:20px;\" width=\"100%\" height=\"30px\">&nbsp;unTill License Server</td>");
		out.println("    </tr>");
		out.println("    <tr>");
		out.println("     <td style=\"background-color:#808080;\" width=\"100%\" height=\"30px\">&nbsp;</td>");
		out.println("    </tr>");
		out.println("   </tbody>");
		out.println("  </table>");
		out.println("  <br/>");

		try {
			if (!approveLA(laApprUid, deny)) {
				out.println("<p style=\"font-weight:bold;color:#FF0000;\">" +
						"Decision already taken</p>");
			} else {
				if (deny) {
					out.println("<p style=\"font-weight:bold;color:#0000FF;\">Large Account successfully denied</p>");
				} else {
					out.println("<p style=\"font-weight:bold;color:#008800;\">Large Account successfully approved</p>");
				}
			}
		} catch (LicenseServiceException e) {
			// TODO ...
			out.println("<p style=\"font-weight:bold;color:#FF0000;\">An error occurred on server</p>");
			if (e.getMessage() != null)
				out.println("<p>" + e.getMessage() + "</p>");
			out.println("<div>Try again later</div>");
		}
//		// XXX Incorrect ?
//		out.println("<p><a href=\"..\">Main page</a></p>");
		out.println(" </body>");
		out.println("</html>");
	}

	private boolean approveLA(String laApprUid, boolean deny) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", laApprUid, deny);
		boolean result;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().approveLAByLink(conn, laApprUid, deny);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	private void serviceConfirmRequest(HttpServletResponse resp, String confirmUid, boolean cancel,
			String comment) throws IOException
	{
		resp.setContentType("text/html");
		PrintWriter out = resp.getWriter();
		out.println("<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
		out.println("<html>");
		out.println(" <head>");
		out.println("  <title>unTill License Server</title>");
		out.println(" </head>");
		out.println(" <body style=\"font-family:arial,sans-serif;background:#EEEEEE;\">");
		out.println("  <table style=\"width: 100%;\" cellpadding=\"0\" cellspacing=\"0\">");
		out.println("   <tbody>");
		out.println("    <tr>");
		out.println("     <td style=\"background-color:#000000;color:#FFFFFF;font-size:20px;\" width=\"100%\" height=\"30px\">&nbsp;unTill License Server</td>");
		out.println("    </tr>");
		out.println("    <tr>");
		out.println("     <td style=\"background-color:#808080;\" width=\"100%\" height=\"30px\">&nbsp;</td>");
		out.println("    </tr>");
		out.println("   </tbody>");
		out.println("  </table>");
		out.println("  <br/>");

		// TODO Check that the license request is still active
		if (cancel && comment == null) {
			// Request comment
			out.println("  <form method=\"get\">");
			out.println("   <p style=\"font-weight:bold;\">Please enter comment:</p>");
			out.println("   <input type=\"hidden\" name=\"confirm_uid\" value=\"" +
					StringEscapeUtils.escapeHtml(confirmUid) + "\"/>");
			out.println("   <input type=\"hidden\" name=\"cancel\" value=\"1\"/>");
			out.println("   <p><textarea rows=\"10\" cols=\"45\" autofocus required name=\"comment\"></textarea></p>");
			out.println("   <p><input type=\"submit\" value=\"Submit\"></p>");
			out.println(" </body>");
			out.println("</html>");
			return;
		}
		
		ConfirmOrderResult result = confirmOrder(confirmUid, cancel, comment);

		if (cancel) {
			switch (result.result) {
			case SUCCESS:
				out.println("<p style=\"font-weight:bold;color:#0000FF;\">Request successfully canceled</p>");
				break;
			case ERROR:
			case FAIL:
				out.println("<p style=\"font-weight:bold;color:#FF0000;\">Error cancel request</p>");
				out.println("<p>" + result.errorMessage + "</p>");
				break;
			default:
				out.println("<p style=\"font-weight:bold;color:#FF0000;\">Error cancel request</p>");
			}
		} else {
			switch (result.result) {
			case SUCCESS:
				out.println("<p style=\"font-weight:bold;color:#008800;\">Request successfully confirmed"
						+ (result.email.isEmpty() ? "" : ", and license is sent to " + result.email) + "</p>");
				break;
			case SUCCESS_BUT_FAIL_SEND_MESSAGE:
				out.println("<p style=\"font-weight:bold;color:#008800;\">Request successfully confirmed, "
						+ "but sending license <span style=\"color:#FF0000;\">by email failed</span></p>");
				out.println("<p>Try to resend again from License Server</p>");
				break;
			case ERROR:
			case FAIL:
				out.println("<p style=\"font-weight:bold;color:#FF0000;\">Error confirmation request</p>");
				out.println("<p>" + result.errorMessage + "</p>");
//				out.println("<p>Perhaps, this request has already been confirmed / canceled</p>");
				break;
			default:
				out.println("<p style=\"font-weight:bold;color:#FF0000;\">Error confirmation request</p>");
			}
		}
//		// XXX Incorrect ?
//		out.println("<p><a href=\"..\">Main page</a></p>");
		out.println(" </body>");
		out.println("</html>");
	}

	// TODO Split this method to: confirmLicense and rejectLicense (cancelLicense)
	private ConfirmOrderResult confirmOrder(String confirmUid, boolean cancel, String comment) {
		LOG.debug("ENTRY ({}, {}, {})", confirmUid, cancel, comment);
		ConfirmOrderResult result;

		Connection conn = null;
		try {
			conn = Common.getConnection();

			result = Common.getLicenseHandler().confirmOrder(conn, confirmUid, cancel, comment, false);

		} catch (SQLException e) {
			LOG.error("", e);
			result = new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "", "DB error");
		} catch (RuntimeException e) {
			LOG.error("", e);
			result = new ConfirmOrderResult(ConfirmOrderResult.Result.ERROR, "", "Server error");
		} finally {
			if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
		}

		LOG.debug("RETURN {}", result);
		return result;
	}

	private void serviceApproveLicense(HttpServletResponse resp, String approveUid, boolean toObject,
			String comment) throws IOException
	{
		// TODO
		resp.setContentType("text/html");
		PrintWriter out = resp.getWriter();
		out.println("<!DOCTYPE html PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
		out.println("<html>");
		out.println(" <head>");
		out.println("  <title>unTill License Server</title>");
		out.println(" </head>");
		out.println(" <body style=\"font-family:arial,sans-serif;background:#EEEEEE;\">");
		out.println("  <table style=\"width: 100%;\" cellpadding=\"0\" cellspacing=\"0\">");
		out.println("   <tbody>");
		out.println("    <tr>");
		out.println("     <td style=\"background-color:#000000;color:#FFFFFF;font-size:20px;\" width=\"100%\" height=\"30px\">&nbsp;unTill License Server</td>");
		out.println("    </tr>");
		out.println("    <tr>");
		out.println("     <td style=\"background-color:#808080;\" width=\"100%\" height=\"30px\">&nbsp;</td>");
		out.println("    </tr>");
		out.println("   </tbody>");
		out.println("  </table>");
		out.println("  <br/>");

		// TODO Check that the license request is still active
		if (toObject && comment == null) {
			// Request comment
			out.println("  <form method=\"get\">");
			out.println("   <p style=\"font-weight:bold;\">Please enter comment:</p>");
			out.println("   <input type=\"hidden\" name=\"approve_uid\" value=\"" +
					StringEscapeUtils.escapeHtml(approveUid) + "\"/>");
			out.println("   <input type=\"hidden\" name=\"object\" value=\"1\"/>");
			out.println("   <p><textarea rows=\"10\" cols=\"45\" autofocus required name=\"comment\"></textarea></p>");
			out.println("   <p><input type=\"submit\" value=\"Submit\"></p>");
			out.println(" </body>");
			out.println("</html>");
			return;
		}

		try {
			if (!approveLicense(approveUid, toObject, comment)) {
				out.println("<p style=\"font-weight:bold;color:#FF0000;\">" +
						"Decision for this license already taken</p>");
			} else {
				if (toObject) {
					// TODO ...
					out.println("<p style=\"font-weight:bold;color:#0000FF;\">License proforma successfully objected</p>");
				} else {
					// TODO ...
					out.println("<p style=\"font-weight:bold;color:#008800;\">License successfully approved</p>");
				}
			}
		} catch (LicenseServiceException e) {
			// TODO ...
			out.println("<p style=\"font-weight:bold;color:#FF0000;\">An error occurred on server</p>");
			if (e.getMessage() != null)
				out.println("<p>" + e.getMessage() + "</p>");
			out.println("<div>Try again later</div>");
		}
//		// XXX Incorrect ?
//		out.println("<p><a href=\"..\">Main page</a></p>");
		out.println(" </body>");
		out.println("</html>");
	}

	private boolean approveLicense(String approveUid, boolean toObject, String comment) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {})", approveUid, toObject, comment);
		boolean result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().approveLicenseByLink(conn, approveUid, toObject, comment);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public RrOrder getLicenseOrder(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		RrOrder result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().getLicenseOrder(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public void addDiscount(AuthScheme authScheme, long licenseId, double price,
			String text) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {}, {})", authScheme, licenseId, price, text);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getLicenseHandler().addDiscount(conn, authorize(conn, authScheme), licenseId, price, text);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public void addNegativeArticle(AuthScheme authScheme, long licenseId,
			long articleId, int quantity, String text) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {}, {}, {})", authScheme, licenseId, articleId, quantity, text);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getLicenseHandler().addNegativeArticle(conn, authorize(conn, authScheme), licenseId, articleId, quantity, text);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public void addArticle(AuthScheme authScheme, long licenseId, long articleId, int quantity, double manualPrice,
			String text) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {}, {}, {}, {})", authScheme, licenseId, articleId, quantity, manualPrice, text);
		try {

			try (Connection conn = Common.getConnection()) {

				Common.getLicenseHandler().addArticle(conn, authorize(conn, authScheme, true), licenseId,
						articleId, quantity, manualPrice, text);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}


	@Override
	public void approveLicense(AuthScheme authScheme, long licenseId, boolean noInvoice)
			throws LicenseServiceException
	{
		LOG.debug("ENTRY ({}, {}, {})", authScheme, licenseId, noInvoice);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getLicenseHandler().approveLicenseByGui(conn, authorize(conn, authScheme), licenseId, noInvoice);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public void orderManualLicense(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getLicenseHandler().orderManualLicense(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public void recreateInvoice(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getLicenseHandler().recreateInvoice(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public CommonRpcResult reorderLicense(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		CommonRpcResult result;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().reorderLicense(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public void disapproveLicense(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getLicenseHandler().disapproveLicense(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public int getMaxDefinitiveMaxVersion(AuthScheme authScheme) throws LicenseServiceException {
		LOG.debug("ENTRY ({})", authScheme);

		int result = 0;
		try {

			try {

				result = Common.getLicenseHandler().getMaxDefinitiveMaxVersion();

			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public CommonRpcResult createCreditInvoice(AuthScheme authScheme, long dealerId,
			List<RrOrderItem> orderItems, String clientName) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {}, {})", authScheme, dealerId, orderItems, clientName);

		CommonRpcResult result = null;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().createCreditInvoice(conn,
						authorize(conn, authScheme, true), dealerId, orderItems, clientName);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<RrArticle> getManualArticles(AuthScheme authScheme) throws LicenseServiceException {
		LOG.debug("ENTRY ({})", authScheme);

		List<RrArticle> result = null;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getLicenseHandler().getManualArticles(conn,
						authorize(conn, authScheme, true),
						Common.getSettings().getManualArticlesDepartment());

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public void reinstallLicense(AuthScheme authScheme, long licenseId) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				Common.getOnlineLicenseHandler().reinstallLicense(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public DbLicenseOnline getLicenseOnlineDetails(AuthScheme authScheme, long licenseId)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);

		DbLicenseOnline result = null;
		try {

			Connection conn = null;
			try {
				conn = Common.getConnection();

				result = Common.getOnlineLicenseHandler().getLicenseOnlineDetails(conn, authorize(conn, authScheme), licenseId);

			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (RuntimeException e) {
				LOG.error("", e);
				throw new ServerErrorException();
			} finally {
				if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
			}

		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<String> getChains(AuthScheme authScheme, long dealerId, boolean onlyOnline) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);

		List<String> result = null;
		try {
			try (Connection conn = Common.getConnection()) {
	
				result = Common.getLicenseHandler().getChains(conn, authorize(conn, authScheme), dealerId, onlyOnline);
	
			} catch (SQLException e) {
				LOG.error("", e);
				throw new DbErrorException();
			} catch (Exception e) {
				LOG.error("", e);
				throw new ServerErrorException();
			}
		} catch (LicenseServiceException e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public Map<String, ShProductInfo> getProductList() throws LicenseServiceException {
		LOG.debug("ENTRY ()");
		Map<String, ShProductInfo> result = null;
		try {

			try {

				result = Common.getAgentCommandHandler().getProductList();

			} catch (Exception e) {
				LOG.error("", e);
				throw new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public Map<String, Boolean> getProductVersionList(String product) throws LicenseServiceException {
		LOG.debug("ENTRY ()");
		Map<String, Boolean> result = null;
		try {

			try {

				result = Common.getAgentCommandHandler().getProductVersionList(product);

			} catch (Exception e) {
				LOG.error("", e);
				throw new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public int getSuTaskBundleCount(AuthScheme authScheme, long dealerId) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);
		int result;
		try (Connection conn = Common.getConnection()) {

			result = Common.getSuHandler().getSuTaskBundleCount(conn, authorize(conn, authScheme), dealerId);

		} catch (Exception e) {
			LOG.error("", e);
			throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<SuTaskBundle> getSuTaskBundleList(AuthScheme authScheme, long dealerId, SuTaskListOrder order,
			int from, int to) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {}, {}, {})", authScheme, dealerId, order, from, to);
		List<SuTaskBundle> result = null;
		try (Connection conn = Common.getConnection()) {

			result = Common.getSuHandler().getSuTaskBundleList(conn, authorize(conn, authScheme), dealerId, order,
					from, to);

		} catch (Exception e) {
			LOG.error("", e);
			throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public void createSuTaskBundle(AuthScheme authScheme, SuTaskBundle taskBundle)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, taskBundle);
		try (Connection conn = Common.getConnection()) {
			boolean oldAutoCommit = conn.getAutoCommit();
			conn.setAutoCommit(false);
			try {

				Common.getSuHandler().createSuTaskBundle(conn, authorize(conn, authScheme), taskBundle);

				conn.commit();
			} catch (Exception e) {
				conn.rollback();
				throw e;
			} finally {
				conn.setAutoCommit(oldAutoCommit);
			}
		} catch (Exception e) {
			LOG.error("", e);
			throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
		}
		LOG.debug("RETURN");

	}

	@Override
	public List<SuHost> getSuHostsByLicenseIds(AuthScheme authScheme, Set<Long> licenseIds)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseIds);
		List<SuHost> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getSuHandler().getSuHostsByLicenseIds(conn, authorize(conn, authScheme), licenseIds);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public List<SuHost> getSuHostsByHardCodes(AuthScheme authScheme, Set<String> licenseHardCodes)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseHardCodes);
		List<SuHost> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getSuHandler().getSuHostsByHardCodes(conn, authorize(conn, authScheme), licenseHardCodes);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public List<SuHost> getSuHostsByDealerId(AuthScheme authScheme, long dealerId) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);
		List<SuHost> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getSuHandler().getSuHostsByDealerId(conn, authorize(conn, authScheme), dealerId);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public SuLicense getSuLicense(AuthScheme authScheme, String hardCode) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, hardCode);
		SuLicense result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getSuHandler().getSuLicense(conn, authorize(conn, authScheme), hardCode);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<SuHost> getSuHostsByHardCode(AuthScheme authScheme, String hardCode) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, hardCode);
		List<SuHost> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getSuHandler().getSuHostsByHardCode(conn, authorize(conn, authScheme), hardCode);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public void unregisterSuHost(AuthScheme authScheme, long hostId) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, hostId);
		try {

			try (Connection conn = Common.getConnection()) {
				boolean oldAutoCommit = conn.getAutoCommit();
				conn.setAutoCommit(false);
				try {

					Common.getSuHandler().unregisterSuHost(conn, authorize(conn, authScheme), hostId);

					conn.commit();
				} catch (Exception e) {
					conn.rollback();
					throw e;
				} finally {
					conn.setAutoCommit(oldAutoCommit);
				}
			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e); // TODO: (?) remove all LOG.debug("THROW", e) LOG.error - enough
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public void setSuLicenseHostRegPass(AuthScheme authScheme, String hardCode, String hostRegPass)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {})", authScheme, hardCode, hostRegPass);
		try {

			try (Connection conn = Common.getConnection()) {
				boolean oldAutoCommit = conn.getAutoCommit();
				conn.setAutoCommit(false);
				try {

					Common.getSuHandler().setSuLicenseHostRegPass(conn, authorize(conn, authScheme), hardCode, hostRegPass);

					conn.commit();
				} catch (Exception e) {
					conn.rollback();
					throw e;
				} finally {
					conn.setAutoCommit(oldAutoCommit);
				}
			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e); // TODO: (?) remove all LOG.debug("THROW", e) LOG.error - enough
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public List<ClientNameChange> getClientNameChanges(AuthScheme authScheme, long dealerId, String filter)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {})", authScheme, dealerId, filter);
		List<ClientNameChange> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getClientNameChanges(conn, authorize(conn, authScheme, true), dealerId, filter);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override
	public List<DbLicense> getLicenseBoosts(AuthScheme authScheme, String licenseUid) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseUid);
		List<DbLicense> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getLicenseBoosts(conn, authorize(conn, authScheme), licenseUid);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}

	@Override	
	public RrArticle getCreditArticle(AuthScheme authScheme) throws LicenseServiceException {
		LOG.debug("ENTRY ({})", authScheme);
		RrArticle result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getCreditArticle(conn, authorize(conn, authScheme, true));

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public RrArticle getHandlingFeeArticle(AuthScheme authScheme) throws LicenseServiceException {
		LOG.debug("ENTRY ({})", authScheme);
		RrArticle result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getHandlingFeeArticle(conn, authorize(conn, authScheme, true));

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<DbHwmInvoice> getHwmInvoiceReport(AuthScheme authScheme, Date month) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, month);
		List<DbHwmInvoice> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getHwmInvoiceReport(conn, authorize(conn, authScheme, true), month);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public List<ArticleWithPrices> getDealerPrices(AuthScheme authScheme, long dealerId, String pricesPass) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);
		List<ArticleWithPrices> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getDealerPrices(conn, authorize(conn, authScheme), dealerId, pricesPass);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public String getLicenseComment(AuthScheme authScheme, long licenseId) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, licenseId);
		String result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getLicenseHandler().getLicenseComment(conn, authorize(conn, authScheme, true), licenseId);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public void setLicenseComment(AuthScheme authScheme, long licenseId, String licenseComment)
			throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {}, {})", authScheme, licenseId, licenseComment);
		try {

			try (Connection conn = Common.getConnection()) {

				Common.getLicenseHandler().setLicenseComment(conn, authorize(conn, authScheme, true), licenseId, licenseComment);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN");
	}

	@Override
	public List<FiscalOrganisation> getFiscalOrganisations(AuthScheme authScheme, long dealerId) throws LicenseServiceException {
		LOG.debug("ENTRY ({}, {})", authScheme, dealerId);
		List<FiscalOrganisation> result = null;
		try {

			try (Connection conn = Common.getConnection()) {

				result = Common.getFiscalHandler().getFiscalOrganisations(conn, authorize(conn, authScheme), dealerId);

			} catch (Exception e) {
				LOG.error("", e);
				throw e instanceof SQLException ? new DbErrorException() : new ServerErrorException();
			}

		} catch (Exception e) {
			LOG.debug("THROW", e);
			throw e;
		}
		LOG.debug("RETURN {}", result == null ? null : result.size());
		return result;
	}
}
