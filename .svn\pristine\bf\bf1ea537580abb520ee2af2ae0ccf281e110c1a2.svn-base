# License Request swim lane diagram

```plantuml
@startuml
|Owner|
|LS|
|Dealer|
start
:Request license;
|LS|
:Create\nlicense-request;
:Send license\nconfirmation email;
|Dealer|
:Read license\nconfirmation email;
if () then
	|Dealer|
	:Reject (cancel)\nlicense request +comment;
	|LS|
	:Send License reject\nemail +comment;
	|Owner|
	:Read, just for info;
	stop
endif
	|Dealer|
	:Confirm license\nrequest;
	|LS|
	:Create License and\nsend to Dealer;
    split
        |LS|
    	:Send Proforma\n(order text) to Tab7;
    split again
        |Dealer|
        stop
    end split
|Invoice emails|
:Read proforma;
split
	|Invoice emails|
	:Object;
	|LS|
	:Send Invoice\nobjected email to\nSuper +comment;
	|Owner|
	:Change something in\nLicense?;
	:Invoice corrections\n(negative articles,\ndiscounts);
    split again
    |Invoice emails|
    :Approve;
    split again
    |Invoice emails|
    :Do nothing in 7 days;
end split
|LS|
:“Pay” order and send\nInvoice to Tab7;
|Invoice emails|
:Read invoice;
stop
@enduml
```
