package eu.untill.license.server;

import java.util.ArrayList;
import java.util.List;

import com.tpapipos.ITPAPIPOS;

import TPAPIPosIntfU.TCloseOrderRequest;
import TPAPIPosIntfU.TCloseOrderResponse;
import TPAPIPosIntfU.TCreateOrderRequest;
import TPAPIPosIntfU.TCreateOrderResponse;
import TPAPIPosIntfU.TGetArticlesInfoRequest;
import TPAPIPosIntfU.TGetArticlesInfoResponse;
import TPAPIPosIntfU.TGetBOStatusRequest;
import TPAPIPosIntfU.TGetBOStatusResponse;
import TPAPIPosIntfU.TGetSalesAreasInfoRequest;
import TPAPIPosIntfU.TGetSalesAreasInfoResponse;
import TPAPIPosIntfU.TPrintProformaRequest;
import TPAPIPosIntfU.TPrintProformaResponse;
import TPAPIPosTypesU.TArticleInfo;
import TPAPIPosTypesU.TExtraInfo;
import TPAPIPosTypesU.TOrderItem;
import TPAPIPosTypesU.TSalesAreaInfo;

public class UntillTpapiWrapper {

	public static class Credential {
		private String userName;
		private String password;
		public Credential(String userName, String password) {
			this.userName = userName;
			this.password = password;
		}
		public String getUserName() {
			return userName;
		}
		public String getPassword() {
			return password;
		}
	}

	public static final String TPAPI_APP_NAME = "LicenseServer";
	public final static String TPAPI_APP_TOKEN = "LUxUdzLUGzwn";

	private ITPAPIPOS pos;
	private Credential credential;

	public UntillTpapiWrapper(ITPAPIPOS pos, Credential credential) {
		this.pos = pos;
		this.credential = credential;
	}

	public String getBOStatus() throws Exception {
		TGetBOStatusRequest request = new TGetBOStatusRequest(
				credential.getPassword(), credential.getUserName(),
				TPAPI_APP_TOKEN, TPAPI_APP_NAME,
				new TExtraInfo[0]);
		TGetBOStatusResponse response = pos.getBOStatus(request);
		if (response.getReturnCode() != 0)
			throw new Exception(String.format("TPAPI-POS GetBOStatus error: (%d) %s", response.getReturnCode(),
					response.getReturnMessage()));
		return response.getCurrentSignature();
	}

	public TArticleInfo[] getArticlesInfo() throws Exception {
		TGetArticlesInfoRequest request = new TGetArticlesInfoRequest(
				credential.getPassword(), credential.getUserName(),
				TPAPI_APP_TOKEN, TPAPI_APP_NAME,
				0, 0, false, new TExtraInfo[0]);
		TGetArticlesInfoResponse response = pos.getArticlesInfo(request);
		if (response.getReturnCode() != 0)
			throw new Exception(String.format("TPAPI-POS GetArticlesInfo error: (%d) %s", response.getReturnCode(),
					response.getReturnMessage()));
		return response.getArticles();
	}

	public TSalesAreaInfo[] getSalesAreasInfo() throws Exception {
		TGetSalesAreasInfoRequest request = new TGetSalesAreasInfoRequest(
				credential.getPassword(), credential.getUserName(),
				TPAPI_APP_TOKEN, TPAPI_APP_NAME);
		TGetSalesAreasInfoResponse response = pos.getSalesAreasInfo(request);
		if (response.getReturnCode() != 0)
			throw new Exception(String.format("TPAPI-POS GetSalesAreasInfo error: (%d) %s", response.getReturnCode(),
					response.getReturnMessage()));
		return response.getSalesAreas();
	}

	public void createOrder(int tableNumber, TOrderItem[] orderItems, long clientId) throws Exception {
		TCreateOrderRequest request = new TCreateOrderRequest(
				credential.getPassword(), credential.getUserName(),
				TPAPI_APP_TOKEN, TPAPI_APP_NAME,
				tableNumber, "a",
				"", "", "", // clientName, orderName, orderDescr
				orderItems,
				0, // covers
				new TExtraInfo[0],
				clientId);
		TCreateOrderResponse response = pos.createOrder(request);
		if (response.getReturnCode() != 0) {
			throw new Exception(String.format("TPAPI-POS CreateOrder(table: %d) error: (%d) %s", tableNumber,
					response.getReturnCode(), response.getReturnMessage()));
		}
	}

	public String printProforma(int tableNumber, long layoutId) throws Exception {
		List<TExtraInfo> requestExtraInfo = new ArrayList<TExtraInfo>();
		requestExtraInfo.add(new TExtraInfo("get_receipt", "", new TExtraInfo[0])); 
		if (layoutId != 0)
			requestExtraInfo.add(new TExtraInfo("layout_id", Long.toString(layoutId), new TExtraInfo[0]));
		TPrintProformaRequest request = new TPrintProformaRequest(
				credential.getPassword(), credential.getUserName(),
				TPAPI_APP_TOKEN, TPAPI_APP_NAME,
				tableNumber, "a",
				requestExtraInfo.toArray(new TExtraInfo[0]));
		TPrintProformaResponse response = pos.printProforma(request);
		if (response.getReturnCode() != 0)
			throw new Exception(String.format("TPAPI-POS PrintProforma(table: %d) error: (%d) %s", tableNumber,
					response.getReturnCode(), response.getReturnMessage()));
		String orderReport = null;
		if (response.getExtra() != null)
			for (TExtraInfo extraInfo : response.getExtra())
				if ("receipt_text".equalsIgnoreCase(extraInfo.getKey())) {
					orderReport = extraInfo.getValue();
					break;
				}
		return orderReport;
	}

	public void closeOrder(int tableNumber, long paymentId) throws Exception {
		TCloseOrderRequest request = new TCloseOrderRequest(
				credential.getPassword(), credential.getUserName(),
				TPAPI_APP_TOKEN, TPAPI_APP_NAME,
				tableNumber, "a",
				paymentId,
				new TExtraInfo[0]);
		TCloseOrderResponse response = pos.closeOrder(request);
		if (response.getReturnCode() != 0) {
			throw new Exception(String.format("TPAPI-POS CloseOrder(table: %d) error: (%d) %s", tableNumber,
					response.getReturnCode(), response.getReturnMessage()));
		}
	}
	
}
