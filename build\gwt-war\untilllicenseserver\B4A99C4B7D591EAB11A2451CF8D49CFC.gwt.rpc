@FinalFields, false
com.google.gwt.i18n.shared.impl.DateRecord, true, true, true, true, com.google.gwt.i18n.shared.impl.DateRecord/**********, **********
com.google.gwt.user.client.rpc.IncompatibleRemoteServiceException, true, true, true, true, com.google.gwt.user.client.rpc.IncompatibleRemoteServiceException/**********, **********
com.google.gwt.user.client.rpc.RpcTokenException, true, true, false, false, com.google.gwt.user.client.rpc.RpcTokenException/**********, **********
com.google.gwt.user.client.rpc.XsrfToken, false, false, true, true, com.google.gwt.user.client.rpc.XsrfToken/**********, **********
eu.untill.license.client.AuthScheme, false, false, true, false, eu.untill.license.client.AuthScheme/**********, **********
eu.untill.license.client.LicenseService, false, false, false, false, _, **********
eu.untill.license.client.LicenseService$AddApostillHddCodesResult, true, true, false, false, eu.untill.license.client.LicenseService$AddApostillHddCodesResult/886809794, 886809794
eu.untill.license.client.LicenseService$AuthFailedException, true, true, false, false, eu.untill.license.client.LicenseService$AuthFailedException/216725473, 216725473
eu.untill.license.client.LicenseService$CommonRpcResult, true, true, false, false, eu.untill.license.client.LicenseService$CommonRpcResult/**********, **********
eu.untill.license.client.LicenseService$DbErrorException, true, true, false, false, eu.untill.license.client.LicenseService$DbErrorException/172546405, 172546405
eu.untill.license.client.LicenseService$InvalidArgumentException, true, true, false, false, eu.untill.license.client.LicenseService$InvalidArgumentException/**********, **********
eu.untill.license.client.LicenseService$LicensePeriod, true, true, true, true, eu.untill.license.client.LicenseService$LicensePeriod/**********, **********
eu.untill.license.client.LicenseService$LicenseServiceException, true, false, false, false, eu.untill.license.client.LicenseService$LicenseServiceException/**********, **********
eu.untill.license.client.LicenseService$LicenseType, false, false, true, true, eu.untill.license.client.LicenseService$LicenseType/**********, **********
eu.untill.license.client.LicenseService$RequestLicenseResult, true, true, false, false, eu.untill.license.client.LicenseService$RequestLicenseResult/**********, **********
eu.untill.license.client.LicenseService$RequestLicenseResult$Results, true, true, false, false, eu.untill.license.client.LicenseService$RequestLicenseResult$Results/**********, **********
eu.untill.license.client.LicenseService$RequestType, false, false, true, true, eu.untill.license.client.LicenseService$RequestType/**********, **********
eu.untill.license.client.LicenseService$RrArticle, true, true, false, false, eu.untill.license.client.LicenseService$RrArticle/**********, **********
[Leu.untill.license.client.LicenseService$RrArticle;, true, true, false, false, [Leu.untill.license.client.LicenseService$RrArticle;/**********, **********
eu.untill.license.client.LicenseService$RrDealer, true, true, false, false, eu.untill.license.client.LicenseService$RrDealer/**********, **********
[Leu.untill.license.client.LicenseService$RrDealer;, true, true, false, false, [Leu.untill.license.client.LicenseService$RrDealer;/**********, **********
eu.untill.license.client.LicenseService$RrOrder, true, true, false, false, eu.untill.license.client.LicenseService$RrOrder/**********, **********
eu.untill.license.client.LicenseService$RrOrderItem, true, true, true, true, eu.untill.license.client.LicenseService$RrOrderItem/**********, **********
[Leu.untill.license.client.LicenseService$RrOrderItem;, true, true, true, true, [Leu.untill.license.client.LicenseService$RrOrderItem;/434751357, 434751357
eu.untill.license.client.LicenseService$ServerErrorException, true, true, false, false, eu.untill.license.client.LicenseService$ServerErrorException/683076255, 683076255
eu.untill.license.client.LicenseService$SortingType, false, false, true, true, eu.untill.license.client.LicenseService$SortingType/523605105, 523605105
eu.untill.license.client.PlainAuthScheme, false, false, true, true, eu.untill.license.client.PlainAuthScheme/**********, **********
eu.untill.license.shared.ArticleWithPrices, true, true, false, false, eu.untill.license.shared.ArticleWithPrices/**********, **********
[Leu.untill.license.shared.ArticleWithPrices;, true, true, false, false, [Leu.untill.license.shared.ArticleWithPrices;/**********, **********
eu.untill.license.shared.ClientNameChange, true, true, false, false, eu.untill.license.shared.ClientNameChange/**********, **********
[Leu.untill.license.shared.ClientNameChange;, true, true, false, false, [Leu.untill.license.shared.ClientNameChange;/**********, **********
eu.untill.license.shared.DbHwmInvoice, true, true, false, false, eu.untill.license.shared.DbHwmInvoice/**********, **********
[Leu.untill.license.shared.DbHwmInvoice;, true, true, false, false, [Leu.untill.license.shared.DbHwmInvoice;/**********, **********
eu.untill.license.shared.DbLicense, true, true, true, true, eu.untill.license.shared.DbLicense/**********, **********
eu.untill.license.shared.DbLicenseOnline, true, true, false, false, eu.untill.license.shared.DbLicenseOnline/589960766, 589960766
[Leu.untill.license.shared.DbLicense;, true, true, false, false, [Leu.untill.license.shared.DbLicense;/**********, **********
eu.untill.license.shared.Fiscal, true, true, true, true, eu.untill.license.shared.Fiscal/**********, **********
eu.untill.license.shared.Fiscal$Address, true, true, true, true, eu.untill.license.shared.Fiscal$Address/**********, **********
eu.untill.license.shared.Fiscal$Terminal, true, true, true, true, eu.untill.license.shared.Fiscal$Terminal/**********, **********
[Leu.untill.license.shared.Fiscal$Terminal;, true, true, true, true, [Leu.untill.license.shared.Fiscal$Terminal;/**********, **********
eu.untill.license.shared.FiscalOrganisation, true, true, false, false, eu.untill.license.shared.FiscalOrganisation/**********, **********
[Leu.untill.license.shared.FiscalOrganisation;, true, true, false, false, [Leu.untill.license.shared.FiscalOrganisation;/**********, **********
eu.untill.license.shared.License, true, false, true, false, eu.untill.license.shared.License/**********, **********
eu.untill.license.shared.ProlongParams, true, true, true, true, eu.untill.license.shared.ProlongParams/**********, **********
eu.untill.license.shared.ShProductInfo, true, true, false, false, eu.untill.license.shared.ShProductInfo/**********, **********
eu.untill.license.shared.SuHost, true, true, true, true, eu.untill.license.shared.SuHost/**********, **********
[Leu.untill.license.shared.SuHost;, true, true, false, false, [Leu.untill.license.shared.SuHost;/**********, **********
eu.untill.license.shared.SuLicense, true, true, true, true, eu.untill.license.shared.SuLicense/**********, **********
eu.untill.license.shared.SuSubTask, true, true, true, true, eu.untill.license.shared.SuSubTask/**********, **********
eu.untill.license.shared.SuSubTask$Status, true, true, true, true, eu.untill.license.shared.SuSubTask$Status/316543895, 316543895
[Leu.untill.license.shared.SuSubTask;, true, true, true, true, [Leu.untill.license.shared.SuSubTask;/382544268, 382544268
eu.untill.license.shared.SuTask, true, true, true, true, eu.untill.license.shared.SuTask/**********, **********
eu.untill.license.shared.SuTaskBundle, true, true, true, true, eu.untill.license.shared.SuTaskBundle/**********, **********
[Leu.untill.license.shared.SuTaskBundle;, true, true, false, false, [Leu.untill.license.shared.SuTaskBundle;/85168166, 85168166
eu.untill.license.shared.SuTaskListOrder, false, false, true, true, eu.untill.license.shared.SuTaskListOrder/**********, **********
[Leu.untill.license.shared.SuTask;, true, true, true, true, [Leu.untill.license.shared.SuTask;/181685233, 181685233
java.lang.Boolean, true, true, true, true, java.lang.Boolean/476441737, 476441737
java.lang.Double, true, true, true, true, java.lang.Double/858496421, 858496421
java.lang.Exception, true, false, true, false, java.lang.Exception/**********, **********
java.lang.Long, false, false, true, true, java.lang.Long/**********, **********
java.lang.Number, true, false, true, false, java.lang.Number/300033342, 300033342
java.lang.RuntimeException, true, false, true, false, java.lang.RuntimeException/515124647, 515124647
java.lang.Short, true, true, true, true, java.lang.Short/551743396, 551743396
java.lang.String, true, true, true, true, java.lang.String/**********, **********
[Ljava.lang.String;, true, true, true, true, [Ljava.lang.String;/**********, **********
java.lang.Throwable, true, false, true, false, java.lang.Throwable/**********, **********
java.sql.Date, true, true, true, true, java.sql.Date/730999118, 730999118
java.sql.Time, true, true, true, true, java.sql.Time/1816797103, 1816797103
java.sql.Timestamp, true, true, true, true, java.sql.Timestamp/3040052672, 3040052672
java.util.ArrayList, true, true, true, true, java.util.ArrayList/4159755760, 4159755760
java.util.Arrays$ArrayList, true, true, true, true, java.util.Arrays$ArrayList/2507071751, 2507071751
java.util.Collections$EmptyList, true, true, true, true, java.util.Collections$EmptyList/4157118744, 4157118744
java.util.Collections$EmptyMap, true, true, true, true, java.util.Collections$EmptyMap/4174664486, 4174664486
java.util.Collections$EmptySet, false, false, true, true, java.util.Collections$EmptySet/3523698179, 3523698179
java.util.Collections$SingletonList, true, true, true, true, java.util.Collections$SingletonList/1586180994, 1586180994
java.util.Date, true, true, true, true, java.util.Date/3385151746, 3385151746
java.util.HashMap, true, true, true, true, java.util.HashMap/1797211028, 1797211028
java.util.HashSet, false, false, true, true, java.util.HashSet/3273092938, 3273092938
java.util.IdentityHashMap, true, true, true, true, java.util.IdentityHashMap/1839153020, 1839153020
java.util.LinkedHashMap, true, true, true, true, java.util.LinkedHashMap/3008245022, 3008245022
java.util.LinkedHashSet, false, false, true, true, java.util.LinkedHashSet/95640124, 95640124
java.util.LinkedList, true, true, true, true, java.util.LinkedList/3953877921, 3953877921
java.util.Stack, true, true, true, true, java.util.Stack/1346942793, 1346942793
java.util.TreeMap, true, true, true, true, java.util.TreeMap/1493889780, 1493889780
java.util.TreeSet, false, false, true, true, java.util.TreeSet/4043497002, 4043497002
java.util.Vector, true, true, true, true, java.util.Vector/3057315478, 3057315478
