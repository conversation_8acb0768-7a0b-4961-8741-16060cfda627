/**
 * 
 */
package eu.untill.license.server;


import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.io.Writer;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.sax.SAXResult;
import javax.xml.transform.sax.SAXTransformerFactory;
import javax.xml.transform.sax.TransformerHandler;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

import org.apache.fop.apps.Fop;
import org.apache.fop.apps.FopFactory;
import org.apache.fop.apps.MimeConstants;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.util.PDFTextStripper;
import org.junit.Assert;
import org.junit.Test;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.xml.sax.SAXException;
import org.xml.sax.helpers.AttributesImpl;

/**
 * <AUTHOR>
 *
 */
public class XmlFoTest {

	final static String DOC_CONTENT_DATE = "20 maart 2009";
	final static String DOC_CONTENT_NUMBER = "2425";
	final static String DOC_CONTENT_DEALER_NAME = "de heer A. Mul";
	final static String DOC_CONTENT_DEALER_ADDRESS_LINE_1 = "De Trompet 1730";
	final static String DOC_CONTENT_DEALER_ADDRESS_LINE_2 = "1967 DB HEEMSKERK";

	@Test
	public void createPdfFile() throws Exception {
		File xmlFile = createXmlFile_DOM();
		File xslFile = createXslFile_SAX();
		File foFile = createFoFile_XML2FO(xmlFile, xslFile);
		File pdfFile = createPdfFile_FOP(foFile);

		String[] expecteds = new String[] {DOC_CONTENT_DATE, DOC_CONTENT_NUMBER,
				DOC_CONTENT_DEALER_NAME,
				DOC_CONTENT_DEALER_ADDRESS_LINE_1, DOC_CONTENT_DEALER_ADDRESS_LINE_2};
		PDDocument document = PDDocument.load(pdfFile);
		PDFTextStripper stripper = new PDFTextStripper();
		Assert.assertArrayEquals(expecteds, stripper.getText(document).split("(?:\\r)?\\n"));
		
		System.out.println(stripper.getText(document));
	}

	private File createXmlFile_DOM() throws IOException, ParserConfigurationException, TransformerException {
		// Create xml file
		File xmlFile = File.createTempFile("xml", ".xml");
		xmlFile.deleteOnExit();

		/// Generate xml document
		Document xmlDoc = null;
		Element root = null, ele1 = null, ele2 = null, ele3 = null;

		DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
		//dbf.setNamespaceAware(true);
		DocumentBuilder db = dbf.newDocumentBuilder();
		xmlDoc = db.newDocument();

		root = xmlDoc.createElement("invoice");
		xmlDoc.appendChild(root);

		ele1 = xmlDoc.createElement("date");
		ele1.setTextContent(DOC_CONTENT_DATE);
		//ele1.setNodeValue(DOC_CONTENT_DATE);
		root.appendChild(ele1);

		ele1 = xmlDoc.createElement("number");
		ele1.setTextContent(DOC_CONTENT_NUMBER);
		root.appendChild(ele1);

		ele1 = xmlDoc.createElement("dealer");
		root.appendChild(ele1);
		ele2 = xmlDoc.createElement("name");
		ele1.appendChild(ele2);
		ele2.setTextContent(DOC_CONTENT_DEALER_NAME);
		ele2 = xmlDoc.createElement("address");
		ele1.appendChild(ele2);

		ele3 = xmlDoc.createElement("line");
		ele2.appendChild(ele3);
		ele3.setTextContent(DOC_CONTENT_DEALER_ADDRESS_LINE_1);
		ele3 = xmlDoc.createElement("line");
		ele2.appendChild(ele3);
		ele3.setTextContent(DOC_CONTENT_DEALER_ADDRESS_LINE_2);


		// Write result xmlDoc to file
		DOMSource source = new DOMSource(xmlDoc);
		StreamResult result = new StreamResult(new FileOutputStream(xmlFile));
		TransformerFactory transFactory = TransformerFactory.newInstance();
		Transformer transformer = transFactory.newTransformer();
		transformer.transform(source, result);

		return xmlFile;
	}

	private File createXslFile_SAX() throws IOException, TransformerConfigurationException, SAXException {
		File xslFile = File.createTempFile("xsl", ".xsl");
		xslFile.deleteOnExit();

		String xslNs = "http://www.w3.org/1999/XSL/Transform";
		String foNs = "http://www.w3.org/1999/XSL/Format";

		// define the output
		//Writer writer = new StringWriter();
		Writer writer = new FileWriter(xslFile);
		// holder for the transformation result
		StreamResult streamResult = new StreamResult(writer);

		SAXTransformerFactory transformerFactory = (SAXTransformerFactory) SAXTransformerFactory.newInstance();

		TransformerHandler transformerHandler = transformerFactory.newTransformerHandler();

//		Transformer serializer = transformerHandler.getTransformer();
//		// Document type declarations
//		serializer.setOutputProperty(OutputKeys.DOCTYPE_PUBLIC, "Public");
//		serializer.setOutputProperty(OutputKeys.DOCTYPE_SYSTEM, "System");

		transformerHandler.setResult(streamResult);
		// Start the document
		transformerHandler.startDocument();
		// list all the attributes for element
		AttributesImpl attr = new AttributesImpl();

		attr.addAttribute(null, null, "version", null, "1.1");
		attr.addAttribute(null, null, "xmlns:fo", null, "http://www.w3.org/1999/XSL/Format");
		attr.addAttribute(null, null, "exclude-result-prefixes", null, "fo");
		transformerHandler.startElement(xslNs, "stylesheet", "xsl:stylesheet", attr);

		attr.clear();
		attr.addAttribute(null, null, "method", null, "xml");
		attr.addAttribute(null, null, "version", null, "1.0");
		attr.addAttribute(null, null, "omit-xml-declaration", null, "no");
		attr.addAttribute(null, null, "indent", null, "yes");
		transformerHandler.startElement(xslNs, "output", "xsl:output", attr);
		transformerHandler.endElement(xslNs, "output", "xsl:output");


		attr.clear();
		attr.addAttribute(null, null, "name", null, "versionParam");
		attr.addAttribute(null, null, "select", null, "'1.0'");
		transformerHandler.startElement(xslNs, "param", "xsl:param", attr);
		transformerHandler.endElement(xslNs, "param", "xsl:param");

		attr.clear();
		attr.addAttribute(null, null, "match", null, "invoice");
		transformerHandler.startElement(xslNs, "template", "xsl:template", attr);

			// Fix for Bug 47279 - Spurious warnings about unavailable Symbol bold or ZapfDingbats bold for text that can be rendered with sans-serif 
			// https://issues.apache.org/bugzilla/show_bug.cgi?id=47279
			attr.clear();
			attr.addAttribute(null, null, "font-family", null, "sans-serif");
			transformerHandler.startElement(foNs, "root", "fo:root", attr);

				transformerHandler.startElement(foNs, "layout-master-set", "fo:layout-master-set", null);

					attr.clear();
					attr.addAttribute(null, null, "master-name", null, "simpleA4");
					attr.addAttribute(null, null, "page-height", null, "29.7cm");
					attr.addAttribute(null, null, "page-width", null, "21cm");
					transformerHandler.startElement(foNs, "simple-page-master", "fo:simple-page-master", attr);

					transformerHandler.startElement(foNs, "region-body", "fo:region-body", null);
					transformerHandler.endElement(foNs, "region-body", "fo:region-body");

					transformerHandler.endElement(foNs, "simple-page-master", "fo:simple-page-master");

				transformerHandler.endElement(foNs, "layout-master-set", "fo:layout-master-set");

				attr.clear();
				attr.addAttribute(null, null, "master-reference", null, "simpleA4");
				transformerHandler.startElement(foNs, "page-sequence", "fo:page-sequence", attr);

					attr.clear();
					attr.addAttribute(null, null, "flow-name", null, "xsl-region-body");
					transformerHandler.startElement(foNs, "flow", "fo:flow", attr);

						attr.clear();
						attr.addAttribute(null, null, "font-size", null, "16pt");
						attr.addAttribute(null, null, "font-weight", null, "bold");
						attr.addAttribute(null, null, "space-after", null, "5mm");
						transformerHandler.startElement(foNs, "block", "fo:block", attr);

							attr.clear();
							attr.addAttribute(null, null, "select", null, "date");
							transformerHandler.startElement(xslNs, "value-of", "xsl:value-of", attr);
							transformerHandler.endElement(xslNs, "value-of", "xsl:value-of");

						transformerHandler.endElement(foNs, "block", "fo:block");

						attr.clear();
						attr.addAttribute(null, null, "font-size", null, "10pt");
						attr.addAttribute(null, null, "space-after", null, "10mm");
						transformerHandler.startElement(foNs, "block", "fo:block", attr);

							attr.clear();
							attr.addAttribute(null, null, "select", null, "number");
							transformerHandler.startElement(xslNs, "value-of", "xsl:value-of", attr);
							transformerHandler.endElement(xslNs, "value-of", "xsl:value-of");

						transformerHandler.endElement(foNs, "block", "fo:block");

						attr.clear();
						transformerHandler.startElement(foNs, "block", "fo:block", attr);

							attr.clear();
							attr.addAttribute(null, null, "select", null, "dealer/name");
							transformerHandler.startElement(xslNs, "value-of", "xsl:value-of", attr);
							transformerHandler.endElement(xslNs, "value-of", "xsl:value-of");

						transformerHandler.endElement(foNs, "block", "fo:block");

						attr.clear();
						attr.addAttribute(null, null, "select", null, "dealer/address/line");
						transformerHandler.startElement(xslNs, "for-each", "xsl:for-each", attr);

							attr.clear();
							transformerHandler.startElement(foNs, "block", "fo:block", attr);

								attr.clear();
								attr.addAttribute(null, null, "select", null, ".");
								transformerHandler.startElement(xslNs, "value-of", "xsl:value-of", attr);
								transformerHandler.endElement(xslNs, "value-of", "xsl:value-of");

							transformerHandler.endElement(foNs, "block", "fo:block");

						transformerHandler.endElement(xslNs, "for-each", "xsl:for-each");
					
					transformerHandler.endElement(foNs, "flow", "fo:flow");

				transformerHandler.endElement(foNs, "page-sequence", "fo:page-sequence");


			transformerHandler.endElement(foNs, "root", "fo:root");

		transformerHandler.endElement(xslNs, "template", "xsl:template");

		transformerHandler.endElement(xslNs, "stylesheet", "xsl:stylesheet");
		transformerHandler.endDocument();

		return xslFile;
	}

	public File createFoFile_XML2FO(File xmlFile, File xslFile) throws Exception {
		File foFile = File.createTempFile("fof", ".fo");
		foFile.deleteOnExit();

		//Setup output
		OutputStream out = new java.io.FileOutputStream(foFile);
		try {
			//Setup XSLT
			TransformerFactory factory = TransformerFactory.newInstance();
			Transformer transformer = factory.newTransformer(new StreamSource(xslFile));

			//Setup input for XSLT transformation
			Source src = new StreamSource(xmlFile);

			//Resulting SAX events (the generated FO) must be piped through to FOP
			Result res = new StreamResult(out);

			//Start XSLT transformation and FOP processing
			transformer.transform(src, res);
		} finally {
			out.close();
		}
		return foFile;
	}

	public File createPdfFile_FOP(File foFile) throws Exception {
		File pdfFile = File.createTempFile("pdf", ".pdf");
		pdfFile.deleteOnExit();

		// Step 1: Construct a FopFactory
		FopFactory fopFactory = FopFactory.newInstance();

		// Step 2: Set up output stream.
		// Note: Using BufferedOutputStream for performance reasons (helpful with FileOutputStreams).
		OutputStream out = new BufferedOutputStream(new FileOutputStream(pdfFile));

		try {
			// Step 3: Construct fop with desired output format
			Fop fop = fopFactory.newFop(MimeConstants.MIME_PDF, out);

			// Step 4: Setup JAXP using identity transformer
			TransformerFactory factory = TransformerFactory.newInstance();
			Transformer transformer = factory.newTransformer(); // identity
																// transformer

			// Step 5: Setup input and output for XSLT transformation
			// Setup input stream
			Source src = new StreamSource(foFile);

			// Resulting SAX events (the generated FO) must be piped through to
			// FOP
			Result res = new SAXResult(fop.getDefaultHandler());

			// Step 6: Start XSLT transformation and FOP processing
			transformer.transform(src, res);

		} finally {
			// Clean-up
			out.close();
		}

		return pdfFile;
	}

}
