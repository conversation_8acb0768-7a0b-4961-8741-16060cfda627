package eu.untill.license.shared;

import com.google.gwt.user.client.rpc.IsSerializable;

public class SuLicense implements IsSerializable {

	public long licenseId;
	public String licenseUid;
	public String hostRegPass;

	public String clientName;
	public String chain;

//	// XXX
//	public SuLicense(long licenseId, String clientName, String hardCode) {
//		this.licenseId = licenseId;
//		this.clientName = clientName;
//		this.hardCode = hardCode;
//	}
	
}
