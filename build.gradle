plugins {
	id 'war'
	id 'maven-publish'
	id 'eclipse'
	id 'io.github.scm4j.scm4j-releaser-gradle-plugin' version '0.3.0'
	id 'org.hidetake.swagger.generator' version '2.19.2'

}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(8)
	}
}

tasks.withType(JavaCompile).configureEach {
	options.encoding = 'UTF-8'
	sourceCompatibility = JavaVersion.VERSION_1_8
	targetCompatibility = JavaVersion.VERSION_1_8
	options.compilerArgs += ['-Xlint:all,-rawtypes', '-Werror']
}

group = 'eu.untill.license'

ext.buildDate = new Date()
ext.buildNumber = System.env['BUILD_NUMBER']?.toLong() ?: (long) buildDate.time.intdiv(1000).intdiv(60)

apply from: 'config.gradle'

ext.gwtModuleName = 'untilllicenseserver'
ext.gwtCompileOutputDir = new File(buildDir, 'gwt-war')

configurations {
	gwtUser
	gwtDev
	gwtServlet
	gwtCodeserver
	gwtLibSource
	compileOnly.extendsFrom gwtUser
	runtimeOnly.extendsFrom gwtServlet
	testImplementation.extendsFrom gwtUser
}

ext.gwtVersion = '2.8.2'

dependencies {
	gwtUser "com.google.gwt:gwt-user:$gwtVersion"
	gwtDev "com.google.gwt:gwt-dev:$gwtVersion"
	gwtServlet "com.google.gwt:gwt-servlet:$gwtVersion"
	gwtCodeserver "com.google.gwt:gwt-codeserver:$gwtVersion@jar"

	implementation 'eu.untill:TPAPIJavaProxy:10.0'

	implementation 'org.firebirdsql.jdbc:jaybird-jdk18:2.2.15'
	implementation 'commons-dbcp:commons-dbcp:1.4'
	implementation 'commons-pool:commons-pool:1.6'
	implementation 'javax.mail:mail:1.4.5'
	implementation 'commons-lang:commons-lang:2.6'
	implementation 'org.apache.xmlgraphics:fop:1.0'
	implementation 'commons-validator:commons-validator:1.4.0'
	implementation 'org.ini4j:ini4j:0.5.4'
	//implementation 'org.apache.velocity:velocity:1.7'
	implementation 'com.microsoft.sqlserver:mssql-jdbc:6.1.0.jre8'

	implementation 'ch.qos.logback:logback-classic:1.2.3'

	implementation 'commons-io:commons-io:2.6'
	implementation 'com.google.code.gson:gson:2.8.5'

	implementation 'com.github.scm4j:scm4j-deployer-engine:0.34.0'

	implementation 'org.apache.maven:maven-artifact:3.6.0'

	compileOnly 'javax.websocket:javax.websocket-api:1.1'

	testImplementation 'junit:junit:4.11'

	testImplementation 'org.apache.commons:commons-email:1.2'

	testImplementation 'org.subethamail:subethasmtp:3.1.7'
	testImplementation 'org.apache.pdfbox:pdfbox:1.8.1'

	testImplementation 'org.mockito:mockito-core:1.9.5'
	testImplementation 'com.h2database:h2:1.3.174'

	testImplementation 'org.dbunit:dbunit:2.4.9'

	testImplementation 'org.mockito:mockito-core:1.9.5'

	swaggerCodegen 'org.openapitools:openapi-generator-cli:6.6.0'
	//implementation 'io.swagger:swagger-annotations:1.6.8'
	implementation "com.google.code.findbugs:jsr305:3.0.2"
	implementation 'com.squareup.okhttp3:okhttp:4.8.1' // 4.10.0
	implementation 'com.squareup.okhttp3:logging-interceptor:4.8.1' // 4.10.0
	//implementation 'com.google.code.gson:gson:2.9.1'
	implementation 'io.gsonfire:gson-fire:1.8.3' // 1.8.5
	//implementation 'javax.ws.rs:jsr311-api:1.1.1'
	implementation 'javax.ws.rs:javax.ws.rs-api:2.1' // 2.1.1
	//implementation group: 'org.apache.commons', name: 'commons-lang3', version: '3.12.0'
	//implementation 'jakarta.annotation:jakarta.annotation-api:1.3.5'
}

configurations.all {
	resolutionStrategy {
		force 'xml-apis:xml-apis:1.4.01'
	}
}

repositories {
	maven {
		url = untillMavenResolveUrl
		credentials {
			username = untillMavenUsername
			password = untillMavenPassword
		}
	}
}

configurations.all {
	resolutionStrategy {
		cacheDynamicVersionsFor 0, 'seconds'
		cacheChangingModulesFor 0, 'seconds'
	}
}

sourceSets {
	main.java {
		srcDirs = ['src']
		exclude 'eu/untill/license/client/images/'
	}
	main.resources {
		srcDirs = ['resources']
	}
	test.java {
		srcDirs = ['test']
	}
	test.resources {
		srcDirs = ['test-resources']
	}
}

swaggerSources {
	retailforce {
		inputFile = file("retailforce/swagger.json") // https://api.retailforce.cloud/swagger/v1.0/swagger.json
		code {
			language = 'java'
			configFile = file('retailforce/config.json') // https://openapi-generator.tech/docs/generators/java/
		}
	}
}
compileJava.dependsOn swaggerSources.retailforce.code
processResources.dependsOn swaggerSources.retailforce.code
sourceSets.main.java.srcDir "${swaggerSources.retailforce.code.outputDir}/src/main/java"
sourceSets.main.resources.srcDir "${swaggerSources.retailforce.code.outputDir}/src/main/resources"

task copyLibs(type: Copy) {
	group = 'IDE'
	description = 'copy necessary libs to war/WEB-INF/lib'
	from configurations.runtimeClasspath
	into 'war/WEB-INF/lib'
}

task eclipseInitPreferences {
	doLast {
		def prefsFile = file('.settings/org.eclipse.core.resources.prefs')
		if (!prefsFile.exists()) {
			prefsFile.parentFile.mkdirs()
			def jdt = ['eclipse.preferences.version=1', 'encoding/<project>=UTF-8']
			file('src/eu/untill/license/client').eachFileMatch(~/.*\.properties/) {
				jdt << "encoding//src/eu/untill/license/client/$it.name=UTF-8"
			}
			jdt << 'encoding//war/WEB-INF/templates=UTF-8'
			file('war/WEB-INF/templates').eachFileMatch(~/.*\.html/) {
				jdt << "encoding//war/WEB-INF/templates/$it.name=UTF-8"
			}
			prefsFile.write jdt.join('\n') + '\n'
		}
	}
}
task eclipseGenerateLaunches {
	doLast {
		file("${project.name}.launch").withWriter { writer ->
		def xml = new groovy.xml.MarkupBuilder(writer)
			xml.doubleQuotes = true
			xml.mkp.xmlDeclaration version: '1.0', encoding: 'UTF-8'
			xml.launchConfiguration type: 'org.eclipse.jdt.launching.localJavaApplication', {
				booleanAttribute key: 'org.eclipse.jdt.launching.DEFAULT_CLASSPATH', value: false
				stringAttribute key: 'org.eclipse.jdt.launching.MAIN_TYPE', value: 'com.google.gwt.dev.DevMode'
				listAttribute key: 'org.eclipse.jdt.launching.CLASSPATH', {
					listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\n<runtimeClasspathEntry containerPath="org.eclipse.jdt.launching.JRE_CONTAINER" javaProject="${project.name}" path="1" type="4"/>\n"""
					listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\n<runtimeClasspathEntry externalArchive="${configurations.gwtCodeserver.singleFile}" path="3" type="2"/>\n"""
					listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\n<runtimeClasspathEntry internalArchive="/${project.name}/src" path="3" type="2"/>\n"""
					listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\n<runtimeClasspathEntry id="org.eclipse.jdt.launching.classpathentry.defaultClasspath">\n<memento project="${project.name}"/>\n</runtimeClasspathEntry>\n"""
				}
				stringAttribute key: 'org.eclipse.jdt.launching.PROGRAM_ARGUMENTS', value: "-war war -startupUrl ${project.name}.html ${project.group}.${project.name}"
				stringAttribute key: 'org.eclipse.jdt.launching.PROJECT_ATTR', value: project.name
				booleanAttribute key: 'org.eclipse.debug.core.appendEnvironmentVariables', value: 'true'
				stringAttribute key: 'org.eclipse.jdt.launching.VM_ARGUMENTS', value: '-Xmx1g'
			}
		}
		[dev: '-devMode ', prod: ''].each { type, arg ->
			file("${project.name}Suite-${type}.launch").withWriter { writer ->
				def xml = new groovy.xml.MarkupBuilder(writer)
				xml.doubleQuotes = true
				xml.mkp.xmlDeclaration version: '1.0', encoding: 'UTF-8'
				xml.launchConfiguration type: 'org.eclipse.jdt.junit.launchconfig', {
					booleanAttribute key: 'org.eclipse.jdt.junit.KEEPRUNNING_ATTR', value: false
					booleanAttribute key: 'org.eclipse.jdt.launching.DEFAULT_CLASSPATH', value: false
					stringAttribute key: 'org.eclipse.jdt.launching.MAIN_TYPE', value: "${project.group}.${project.name}Suite"
					stringAttribute key: 'org.eclipse.jdt.junit.TESTNAME', value: ''
					listAttribute key: 'org.eclipse.jdt.launching.CLASSPATH', {
						listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\r\n<runtimeClasspathEntry containerPath="org.eclipse.jdt.launching.JRE_CONTAINER" javaProject="${project.name}" path="1" type="4"/>\r\n"""
						listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\r\n<runtimeClasspathEntry internalArchive="/${project.name}/src" path="3" type="2"/>\r\n"""
						listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\r\n<runtimeClasspathEntry internalArchive="/${project.name}/test" path="3" type="2"/>\r\n"""
						listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\r\n<runtimeClasspathEntry id="org.eclipse.jdt.launching.classpathentry.defaultClasspath">\r\n<memento project="${project.name}"/>\r\n</runtimeClasspathEntry>\r\n"""
						listEntry value: """<?xml version="1.0" encoding="UTF-8"?>\r\n<runtimeClasspathEntry externalArchive="${configurations.gwtCodeserver.singleFile}" path="3" type="2"/>\r\n"""
					}
					stringAttribute key: 'org.eclipse.jdt.launching.PROJECT_ATTR', value: project.name
					stringAttribute key: 'org.eclipse.jdt.launching.VM_ARGUMENTS', value: "-Dgwt.args=\"$arg-logLevel WARN -war www-test\""
					booleanAttribute key: 'org.eclipse.debug.core.appendEnvironmentVariables', value: true
				}
			}
		}
	}
}
eclipse {
	classpath {
		downloadJavadoc = true
		minusConfigurations += [configurations.gwtServlet]
		plusConfigurations += [configurations.gwtDev, configurations.gwtLibSource]
		defaultOutputDir = file('war/WEB-INF/classes')
		file {
			whenMerged {
				entries.each { source ->
					if (source.kind == 'src' && source.path.contains('swagger-code-retailforce')) {
						source.entryAttributes['ignore_optional_problems'] = 'true'
					}
				}
			}
		}
	}
	project {
		comment = "${project.name} project"
		buildCommands.removeAll { it.name == 'org.eclipse.wst.common.project.facet.core.builder' }
		buildCommands.removeAll { it.name == 'org.eclipse.wst.validation.validationbuilder' }
		natures.remove 'org.eclipse.wst.common.project.facet.core.nature'
		natures.remove 'org.eclipse.wst.common.modulecore.ModuleCoreNature'
		natures.remove 'org.eclipse.jem.workbench.JavaEMFNature'
		natures 'com.google.gwt.eclipse.core.gwtNature'
		buildCommand 'com.google.gdt.eclipse.core.webAppProjectValidator'
		buildCommand 'com.google.gwt.eclipse.core.gwtProjectValidator'
	}
	// only for Eclipse Buildship
	synchronizationTasks eclipseInitPreferences
	synchronizationTasks eclipseGenerateLaunches
	synchronizationTasks swaggerSources.retailforce.code
}
// only for eclipse task
tasks.eclipse.dependsOn cleanEclipse
eclipseJdt.dependsOn eclipseInitPreferences
eclipseJdt.dependsOn eclipseGenerateLaunches
tasks.eclipse.dependsOn swaggerSources.retailforce.code
eclipseClasspath.dependsOn copyLibs
[eclipseWtp, eclipseWtpFacet, eclipseWtpComponent]*.enabled = false

task gwtDevmode(type: JavaExec, dependsOn: [classes, copyLibs]) {
	description = 'Run development mode'
	group = 'GWT'
	mainClass = 'com.google.gwt.dev.DevMode'
	classpath sourceSets.main.java.srcDirs
	classpath configurations.gwtCodeserver
	classpath configurations.gwtUser, configurations.gwtDev
	classpath configurations.gwtLibSource
	args '-startupUrl', "${project.name}.html", '-war', 'war'
	args "${project.group}.${project.name}", "${project.group}.${project.name}"
	maxHeapSize = '1g'
}

task gwtCompile(type: JavaExec) {
	description = 'GWT compile to JavaScript'
	group = 'GWT'
	outputs.dir gwtCompileOutputDir
	systemProperty 'gwt.persistentunitcache', 'false'
	mainClass = 'com.google.gwt.dev.Compiler'
	classpath sourceSets.main.java.srcDirs
	classpath configurations.gwtUser, configurations.gwtDev
	classpath configurations.gwtLibSource
	args '-war', gwtCompileOutputDir, "${project.group}.${project.name}"
}
gwtCompile.dependsOn swaggerSources.retailforce.code

task gwtTestDev(type: Test, dependsOn: [testClasses, copyLibs]) {
	description = 'Run development mode tests'
	group = 'GWT'
	systemProperty 'gwt.args', '-devMode -logLevel WARN -war www-test'
	systemProperty 'java.awt.headless', 'true'
	classpath += sourceSets.main.java.sourceDirectories
	classpath += sourceSets.test.java.sourceDirectories
	classpath += configurations.gwtUser
	classpath += configurations.gwtDev
	classpath += configurations.gwtLibSource
	scanForTestClasses = false
	include '**/*Suite.*'
}

task gwtTestProd(type: Test, dependsOn: [testClasses, copyLibs]) {
	description = 'Run production mode tests'
	group = 'GWT'
	systemProperty 'gwt.args', '-logLevel WARN -war www-test'
	systemProperty 'java.awt.headless', 'true'
	classpath += sourceSets.main.java.sourceDirectories
	classpath += sourceSets.test.java.sourceDirectories
	classpath += configurations.gwtUser
	classpath += configurations.gwtDev
	classpath += configurations.gwtLibSource
	scanForTestClasses = false
	include '**/*Suite.*'
}

task gwtTest(dependsOn: [gwtTestDev, gwtTestProd]) {
	description = 'Run development and production mode tests'
	group = 'GWT'
}
check.dependsOn(gwtTest)

test {
	filter {
		excludeTestsMatching "eu.untill.license.client.*"
	}
}

war {
	dependsOn gwtCompile
	duplicatesStrategy DuplicatesStrategy.EXCLUDE
	from(gwtCompileOutputDir) {
		exclude 'WEB-INF/deploy/**'
	}
	from('war') {
		exclude "${gwtModuleName}/**"
		exclude 'WEB-INF/classes/**'
		exclude 'WEB-INF/deploy/**'
		exclude 'WEB-INF/lib/**'
		exclude "${project.name}.html"
	}
	from("war/${project.name}.html") {
		// Set fake parameter to prevent caching
		filter {String line ->
			line.replaceAll('(\\.nocache\\.js)(\\\")', "\$1?version=${project.version}&build=${buildNumber}\$2")
		}
	}
	manifest.attributes(
		'Specification-Title': project.name,
		'Specification-Version': version.replaceAll(/-SNAPSHOT$/, ''),
		'Implementation-Title': project.name,
		'Implementation-Version': "$version build $buildNumber (${buildDate.format('yyyy-MM-dd')})",
	)
}

test.testLogging.exceptionFormat = 'full'

publish.dependsOn check

publishing {
	publications {
		maven(MavenPublication) {
			from components.web
		}
	}
	repositories {
		maven {
			url = version.endsWith('-SNAPSHOT') ? untillMavenPublishSnapshotUrl : untillMavenPublishUrl
			credentials {
				username = untillMavenUsername
				password = untillMavenPassword
			}
		}
	}
}

clean {
	delete 'war/WEB-INF/lib'
}
