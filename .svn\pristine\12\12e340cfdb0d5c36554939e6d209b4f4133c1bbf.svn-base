/**
 * 
 */
package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.EventListener;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;

import eu.untill.license.client.UntillLicenseServerConstants;
import eu.untill.license.client.UntillLicenseServerImages;
import eu.untill.license.client.UntillLicenseServer;

/**
 * <AUTHOR>
 *
 */
public class MessageDialog extends DialogBox implements ClickHandler {

	public static interface MessageDialogListener extends EventListener {
		void onSelectButton(SelectedButton selectedButton);
	}

	ArrayList<MessageDialogListener> messageDialogListenerList = null;

	public void addMessageDialogListener(MessageDialogListener listener) {
		if (messageDialogListenerList == null)
			messageDialogListenerList = new ArrayList<MessageDialogListener> ();
		messageDialogListenerList.add(listener);
	}

	public void removeMessageDialogListener(MessageDialogListener listener) {
		if (messageDialogListenerList != null)
			messageDialogListenerList.remove(listener);
	}

	// TODO Use own constants

	private UntillLicenseServerConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

//	public interface UlsImages extends ImageBundle {
//		AbstractImagePrototype information();
//		//AbstractImagePrototype question();
//		AbstractImagePrototype warning();
//		AbstractImagePrototype exclamation();
//	}
	private UntillLicenseServerImages images = UntillLicenseServer.getUntillLicenseServerImages();

	private FlexTable table = new FlexTable();
	private Label lblMessage = new Label();
	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk, btnCancel, btnRetry, btnYes, btnNo;

	private int defaultButtonNo = 0;

	Focusable focusObjectOnHide = null;
	
	public enum Style { INFORMATION, QUESTION, WARNING, EXCLAMATION }
	public enum Buttons { OK, OKCANCEL, RETRYCANCEL, YESNO, YESNOCANCEL }
	public enum SelectedButton { OK, CANCEL, RETRY, YES, NO }

	public MessageDialog(String message, Style style) {
		this(message, null, style);
	}
	public MessageDialog(String message, Style style, Buttons buttons) {
		this(message, null, style, buttons);
	}
	public MessageDialog(String message, Style style, Buttons buttons,
			MessageDialogListener listener) {
		this(message, null, style, buttons);
		this.addMessageDialogListener(listener);
	}
	public MessageDialog(String message, Style style, Focusable focusObjectOnHide) {
		this(message, null, style, Buttons.OK);
		this.focusObjectOnHide = focusObjectOnHide;
	}
	public MessageDialog(String message, String title, Style style) {
		this(message, title, style, Buttons.OK);
	}
	public MessageDialog(String message, String title, Style style, Buttons buttons,
			MessageDialogListener listener) {
		this(message, title, style, buttons);
		this.addMessageDialogListener(listener);
	}
	public MessageDialog(String message, String title, Style style, Focusable focusObjectOnHide) {
		this(message, title, style, Buttons.OK);
		this.focusObjectOnHide = focusObjectOnHide;
	}

	public MessageDialog(String message, String title, Style style, Buttons buttons) {

		if (title == null)
			title = constants.mainTitle();

		this.setText(title);

		Image image;
		switch (style) {
		case QUESTION: image = new Image(images.question()); break;
		case WARNING: image = new Image(images.warning()); break;
		case EXCLAMATION: image = new Image(images.exclamation()); break;
		default: image = new Image(images.information()); break;
		}

		lblMessage.setText(message);

		if (buttons == Buttons.OK || buttons == Buttons.OKCANCEL) {
			btnOk = new Button(constants.okButton(), this);
			pnlButtons.add(btnOk);
		} else if (buttons == Buttons.RETRYCANCEL) {
			btnRetry = new Button(constants.retryButton(), this);
			pnlButtons.add(btnRetry);
		} else if (buttons == Buttons.YESNO || buttons == Buttons.YESNOCANCEL) {
			btnYes = new Button(constants.yesButton(), this);
			btnNo = new Button(constants.noButton(), this);
			pnlButtons.add(btnYes);
			pnlButtons.add(btnNo);
		}
		if (buttons == Buttons.OKCANCEL || buttons == Buttons.RETRYCANCEL
				|| buttons == Buttons.YESNOCANCEL )
		{
			btnCancel = new Button(constants.cancelButton(), this);
			pnlButtons.add(btnCancel);
		}

		if (buttons == Buttons.OKCANCEL || buttons == Buttons.YESNO)
			defaultButtonNo = 1;
		else if (buttons == Buttons.YESNOCANCEL)
			defaultButtonNo = 2;
		else
			defaultButtonNo = 0;

		pnlButtons.setSpacing(2);

		FlexCellFormatter formatter = table.getFlexCellFormatter();
		table.setWidget(0, 0, image);
		formatter.setVerticalAlignment(0, 0, HasVerticalAlignment.ALIGN_MIDDLE);
		table.setCellSpacing(5);
		table.setWidget(0, 1, lblMessage);
		formatter.setVerticalAlignment(0, 1, HasVerticalAlignment.ALIGN_MIDDLE);
		table.setWidget(1, 0, pnlButtons);
		table.getFlexCellFormatter().setColSpan(1, 0, 2);
		formatter.setHorizontalAlignment(1, 0, HasHorizontalAlignment.ALIGN_CENTER);

		table.setWidth("100%");
		formatter.setWidth(0, 0, "1");

		this.setWidget(table);
	}

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();
		SelectedButton selectedButton = SelectedButton.CANCEL;
		if (sender == btnOk)
			selectedButton = SelectedButton.OK;
		else if (sender == btnRetry)
			selectedButton = SelectedButton.RETRY;
		else if (sender == btnYes)
			selectedButton = SelectedButton.YES;
		else if (sender == btnNo)
			selectedButton = SelectedButton.NO;
		this.hide();
		if (focusObjectOnHide != null) {
			focusObjectOnHide.setFocus(true);
		}
		if (messageDialogListenerList != null)
			for (MessageDialogListener messageDialogListener : messageDialogListenerList) {
				messageDialogListener.onSelectButton(selectedButton);
			}
	}

	@Override
	public void show() {
		super.show();
		this.center();
		((Button) pnlButtons.getWidget(defaultButtonNo)).setFocus(true);
	}

}
