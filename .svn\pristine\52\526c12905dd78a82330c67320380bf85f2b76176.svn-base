/**
 * 
 */
package eu.untill.license.client.ui;

import com.google.gwt.i18n.client.Constants;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.DockPanel;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.gwt.user.client.ui.TabPanel;
import com.google.gwt.user.client.ui.Widget;

import eu.untill.license.client.UntillLicenseServer;

/**
 * <AUTHOR>
 *
 */
public class MainPage implements AutoCloseable {

	public static interface UlsConstants extends Constants {
		String licensesTabTitle();
		String tasksTabTitle();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();

	private DockPanel pnlBase = new DockPanel();
	private TabPanel tabPanel = new TabPanel();
	private TitleWidget titleWidget = new TitleWidget();
	private LicenseListWidget licenseListWidget = new LicenseListWidget();
	private SuTaskListWidget taskListWidget = new SuTaskListWidget();

	private Timer timer;

	private MainPage() {
		pnlBase.setWidth("100%");
		pnlBase.add(titleWidget, DockPanel.NORTH);
		pnlBase.add(tabPanel, DockPanel.CENTER);

		tabPanel.setWidth("100%");
		tabPanel.add(licenseListWidget, constants.licensesTabTitle());
		tabPanel.add(taskListWidget, constants.tasksTabTitle());
		tabPanel.selectTab(0);

		tabPanel.addSelectionHandler((event) -> {
			refresh();
		});

		timer = new Timer() {
			@Override
			public void run() {
				if (UntillLicenseServer.getAuthScheme() != null) {
					refresh(true);
				}
			}
		};
		timer.scheduleRepeating(30 * 1000);
	}

	@Override
	public void close() {
		timer.cancel();
		timer = null;
	}

	public void show() {
		titleWidget.refresh();
		licenseListWidget.refresh();
		taskListWidget.refresh();

		RootPanel rootPanel = RootPanel.get();
		rootPanel.clear();
		rootPanel.add(pnlBase);
	}

	public void refresh() {
		refresh(false);
	}

	public void refresh(boolean fromTimer) {
		titleWidget.refresh();
		Widget selectedWidget = tabPanel.getWidget(tabPanel.getTabBar().getSelectedTab());
		if (selectedWidget == licenseListWidget) {
			if (!fromTimer)
				licenseListWidget.refresh(true);
		} else if (selectedWidget == taskListWidget) {
			taskListWidget.refresh(true);
		}
	}

	/* Singleton implementation */

	private static MainPage ref = null;

	public static MainPage get() {
		if (ref == null)
			ref = new MainPage();
		return ref;
	}

	public static void clear() {
		if (ref != null) {
			ref.close();
			ref = null;
		}
	}

//	public Object clone() throws CloneNotSupportedException	{
//		throw new CloneNotSupportedException();
//	}
}
