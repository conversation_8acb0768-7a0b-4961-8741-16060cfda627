package eu.untill.license.server;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializer;
import com.google.gson.reflect.TypeToken;
import com.untill.su.api.entity.DeployedProduct;
import com.untill.su.api.entity.HostTask;
import com.untill.su.api.entity.Result;
import com.untill.su.api.entity.ShortSysInfo;
import com.untill.su.api.entity.Status;

public final class SuCommon {

	private static final Gson gson = new GsonBuilder().setPrettyPrinting()
			.registerTypeAdapter(Status.class,
					(JsonSerializer<Status>) (src, typeOfSrc, context) -> new JsonPrimitive(src.ordinal()))
			.registerTypeAdapter(Instant.class,
					(JsonDeserializer<Status>) (json, typeOfT, context) -> statusFromInt(json.getAsInt()))
			.registerTypeAdapter(Instant.class,
					(JsonSerializer<Instant>) (src, typeOfSrc, context) -> new JsonPrimitive(src.toString()))
			.registerTypeAdapter(Instant.class,
					(JsonDeserializer<Instant>) (json, typeOfT, context) -> Instant.parse(json.getAsString()))
			.create();

	public static Status statusFromInt(int statusNo) {
		if (statusNo < 0 || statusNo > Status.FAILURE.ordinal())
			return null;
		return Status.values()[statusNo];
	}

	public static String shortSysInfoToJson(ShortSysInfo shortSysInfo) {
		if (shortSysInfo == null)
			return null;
		return gson.toJson(shortSysInfo);
	}

	public static ShortSysInfo shortSysInfoFromJson(String shortSysInfoJson) {
		return gson.fromJson(shortSysInfoJson, ShortSysInfo.class);
	}

	public static String deployedProductsToJson(List<DeployedProduct> deployedProducts) {
		if (deployedProducts == null)
			return null;
		return gson.toJson(deployedProducts);
	}

	public static List<DeployedProduct> deployedProductsFromJson(String deployedProductsJson) {
		return gson.fromJson(deployedProductsJson, new TypeToken<List<DeployedProduct>>(){}.getType());
	}

	public static String resultToJson(Result result) {
		return gson.toJson(result);
	}

	public static Result resultFromJson(String resultJson) {
		return gson.fromJson(resultJson, Result.class);
	}

	public static String paramsToJson(Map<String, String> params) {
		return gson.toJson(params);
	}

	public static Map<String, String> paramsFromJson(String json) {
		return gson.fromJson(json, new TypeToken<Map<String, String>>(){}.getType());
	}

	public static String hostTasksToJson(List<HostTask> hostTaskList) {
		return gson.toJson(hostTaskList);
	}

}
