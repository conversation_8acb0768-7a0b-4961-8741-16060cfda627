package eu.untill.license.server;

import java.sql.Connection;
import java.util.List;

import TPAPIPosTypesU.TOrderItem;
import eu.untill.license.client.LicenseService.RequestType;
import eu.untill.license.shared.ArticleWithPrices;
import eu.untill.license.shared.DbLicense;

public interface UntillTpapiHelper {


	/**
	 * 
	 * @param conn
	 * @param order
	 * @param dealerId
	 * @return tableNo
	 * @throws Exception
	 */
	int createOrderOnNewTable(Connection conn, List<TOrderItem> order, long dealerId) throws Exception;

	/**
	 * 
	 * @param conn
	 * @param tableNo
	 * @return billId
	 * @throws Exception
	 */
	long getActiveBillIdByTableNo(Connection conn, int tableNo) throws Exception;

	/**
	 * 
	 * @param conn
	 * @param tableNo
	 * @throws Exception
	 */
	void closeOrder(Connection conn, int tableNo) throws Exception;

	/**
	 * 
	 * @param conn
	 * @param order
	 * @param dealerId
	 * @return billId
	 * @throws Exception
	 */
	long orderAndPay(Connection conn, List<TOrderItem> order, long dealerId) throws Exception;

	long getArticleIdByBarcode(Connection conn, String barcode) throws Exception;

	long getArticleIdByBarcodeIfExist(Connection conn, String barcode) throws Exception;

	/**
	 * Order license via TPAPI (faster replacement for UntillLicensesPluginHelper.orderLicense())
	 * @param conn
	 * @param requestType
	 * @param license
	 * @param prevLicense
	 * @throws Exception
	 */
	void orderLicense(Connection conn, RequestType requestType, DbLicense license, DbLicense prevLicense) throws Exception;

	/**
	 * 
	 * @param conn
	 * @param tableNo
	 * @param dealerId
	 * @param articleId
	 * @param quantity
	 * @param manualPrice
	 * @param text
	 * @throws Exception
	 */
	void addArticleToTable(Connection conn, int tableNo, long dealerId, long articleId, int quantity, 
			double manualPrice, String text) throws Exception;

	List<ArticleWithPrices> getDealerPrices(Connection conn, long dealerId) throws Exception;

}
