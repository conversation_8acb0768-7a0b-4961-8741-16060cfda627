/**
 *
 */
package eu.untill.license.shared;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * <AUTHOR>
 *
 */
public class DbLicense extends License implements IsSerializable {
	private static final long serialVersionUID = 634668498829370805L;

	public long id;                  // BIGINT NOT NULL

	public String hardCode;          // ++ VARCHAR(30) NOT NULL

	public String dealerComments;    // BLOB SUB_TYPE 0 SEGMENT SIZE 10
	public long idDealers;           // BIGINT
	public String requestType;       // CHAR(10)
	public boolean unconfirmed;      // SMALLINT, SMALLINT
	public short emergencyAvailable; // SMALLINT
	public boolean nonProlongable;   // SMALLINT
	public short nonApproved;        // SMALLINT
	public boolean invoiceRequired;  // SMALLINT
	public Timestamp invoiceDate;    // TIMESTAMP
	public long invoiceNumber;       // BIGINT
	public Date tempExpiredDate;     // DATE

	public int availableReinstallations; // INTEGER
	public Timestamp installationTime;  // TIMESTAMP

	public String chain;                // VARCHAR(50)

	public boolean autoProlongable;     // SMALLINT
	public ProlongParams prolongParams; // BLOB

	public int minVersion;              // INTEGER

	public boolean requestLargeAccount; // SMALLINT
	public boolean largeAccount;        // SMALLINT

	public boolean hwmYearly;           // SMALLINT
	public boolean hwmFirst;            // SMALLINT

	public boolean needApprove() { return nonApproved != 0; }

	public static boolean isHiddenFunc(int func) {
		if (func == FUNC_KITCHENSCREEN)
			return true;
		if (func == FUNC_TAKEAWAY_COM)
			return true;
		if (func == FUNC_TAPP)
			return true;
		return License.isHiddenFunc(func);
	}

	public boolean isOldLicReq() {
		if (getLicVer() > LICENSE_MIN_VERSION)
			return false;
		return minVersion < License.FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[4];
	}

	public int getOldLicVer() {
		if (minVersion >= License.FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[3]
				|| getPsConnections() > 0)
			return 3;
		if (minVersion >= License.FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[2]
				|| isModuled() || isDefinitive() && !isNeverExpired())
			return 2;
		return 1;
	}

	public int getLicVer() {
		int licVer = LICENSE_MIN_VERSION;
		for (; licVer + 1 < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION.length; licVer++)
			if (minVersion < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[licVer + 1])
				break;
		if (licVer < 5 && getHeConnections() > 0)
			licVer = 5;
		if (licVer < 6 && getFunction(FUNC_FRENCH_FDM))
			licVer = 6;
		if (licVer < 7 && getDrivers() != null && getDrivers().size() > 0)
			licVer = 7;
		return licVer;
	}

	public static int verToLicVer(int version) {
		if (version == 0)
			return 0;
		int licVer = 0;
		for (; licVer + 1 < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION.length; licVer++)
			if (version < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[licVer + 1])
				break;
		return licVer;
	}

	/**
	 * Combines drivers from two licenses, removing duplicates and preserving order.
	 *
	 * @param firstDrivers drivers from the first license (can be null)
	 * @param secondDrivers drivers from the second license (can be null)
	 * @return combined list of drivers without duplicates, or null if both inputs are null or empty
	 */
	public static List<String> combineDrivers(List<String> firstDrivers, List<String> secondDrivers) {
		Set<String> driversSet = firstDrivers == null ? new LinkedHashSet<>() : new LinkedHashSet<>(firstDrivers);
		if (secondDrivers != null)
			driversSet.addAll(secondDrivers);
		return driversSet.isEmpty() ? null : new ArrayList<>(driversSet);
	}

	/**
	 * Gets drivers that are in the second list but not in the first list.
	 *
	 * @param firstDrivers drivers from the first license (can be null)
	 * @param secondDrivers drivers from the second license (can be null)
	 * @return list of drivers that are in secondDrivers but not in firstDrivers
	 */
	public static List<String> getDriversDifference(List<String> firstDrivers, List<String> secondDrivers) {
		List<String> newDrivers = new ArrayList<>();
		if (secondDrivers != null) {
			Set<String> firstDriversSet = firstDrivers == null ? new HashSet<>() : new HashSet<>(firstDrivers);
			for (String driver : secondDrivers) {
				if (!firstDriversSet.contains(driver)) {
					newDrivers.add(driver);
				}
			}
		}
		return newDrivers.isEmpty() ? null : newDrivers;
	}

	/**
	 * Compares two lists of drivers, ignoring order.
	 *
	 * @param firstDrivers drivers from the first license (can be null)
	 * @param secondDrivers drivers from the second license (can be null)
	 * @return true if the lists contain the same drivers, false otherwise
	 */
	public static boolean driversEqual(List<String> firstDrivers, List<String> secondDrivers) {
		if (firstDrivers == null && secondDrivers == null) {
			return true;
		}
		if (firstDrivers == null || secondDrivers == null) {
			return false;
		}
		Set<String> firstDriversSet = new HashSet<>(firstDrivers);
		Set<String> secondDriversSet = new HashSet<>(secondDrivers);
		return firstDriversSet.equals(secondDriversSet);
	}

	/**
	 * Converts list of drivers to string representation.
	 *
	 * @param drivers list of drivers (can be null)
	 * @return string representation of the list or null if the list is null
	 */
	public static String driversToText(List<String> drivers) {
		if (drivers == null)
			return null;
		StringBuilder sb = new StringBuilder();
		for (String driver : drivers) {
			sb.append(driver);
			sb.append("\r\n");
		}
		return sb.toString();
	}

	/**
	 * Converts string representation of drivers to list.
	 *
	 * @param text string representation of drivers (can be null)
	 * @return list of drivers or null if the string is null or empty
	 */
	public static List<String> textToDrivers(String text) {
		if (text != null) {
			List<String> drivers = new ArrayList<>();
			for (String driver : text.split("\r?\n")) {
				driver = driver.trim();
				if (!driver.isEmpty()) {
					drivers.add(driver);
				}
			}
			return drivers.isEmpty() ? null : drivers;
		}
		return null;
	}

	@Override
	public String toString() {
		// TODO use toString() from LicenseData
		return "[DbLicense: id=" + Long.toString(id) + "; client=\"" + getClientName() + "\""
				+ "; hardCode=" + hardCode
				+ "; connections=" + (isUnlimited() ? "unlimited" : Short.toString(getLbConnections())
						+ "," + Short.toString(getRemConnections()) + "," + Short.toString(getOmConnections())
						+ "," + Short.toString(getPsConnections()) + "," + Short.toString(getHeConnections()))
				+ "; dates=" + (getStartDate() == null ? "null" : Long.toString(getStartDate().getTime())) + ","
						+ (getIssueDate() == null ? "null" : Long.toString(getIssueDate().getTime()))
						+ (isNeverExpired() ? "" : ","
								+ (getExpiredDate() == null ? "null" : Long.toString(getExpiredDate().getTime())))
						+ (tempExpiredDate == null ? "" : "," + Long.toString(tempExpiredDate.getTime()))
				+ "; funcLimited=" + Long.toString(getFuncLimited())
				+ "; status=" + Short.toString(getStatus())
				+ "; drivers=" + (getDrivers() == null ? "null" : getDrivers().size())
				+ "]";
	}

}
