/**
 *
 */
package eu.untill.license.shared;

import java.sql.Timestamp;
import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

/**
 * <AUTHOR>
 *
 */
public class DbLicense extends License implements IsSerializable {
	private static final long serialVersionUID = 634668498829370805L;

	public long id;                  // BIGINT NOT NULL

	public String hardCode;          // ++ VARCHAR(30) NOT NULL

	public String dealerComments;    // BLOB SUB_TYPE 0 SEGMENT SIZE 10
	public long idDealers;           // BIGINT
	public String requestType;       // CHAR(10)
	public boolean unconfirmed;      // SMALLINT, SMALLINT
	public short emergencyAvailable; // SMALLINT
	public boolean nonProlongable;   // SMALLINT
	public short nonApproved;        // SMALLINT
	public boolean invoiceRequired;  // SMALLINT
	public Timestamp invoiceDate;    // TIMESTAMP
	public long invoiceNumber;       // BIGINT
	public Date tempExpiredDate;     // DATE

	public int availableReinstallations; // INTEGER
	public Timestamp installationTime;  // TIMESTAMP

	public String chain;                // VARCHAR(50)

	public boolean autoProlongable;     // SMALLINT
	public ProlongParams prolongParams; // BLOB

	public int minVersion;              // INTEGER

	public boolean requestLargeAccount; // SMALLINT
	public boolean largeAccount;        // SMALLINT

	public boolean hwmYearly;           // SMALLINT
	public boolean hwmFirst;            // SMALLINT

	public boolean needApprove() { return nonApproved != 0; }

	public static boolean isHiddenFunc(int func) {
		if (func == FUNC_KITCHENSCREEN)
			return true;
		if (func == FUNC_TAKEAWAY_COM)
			return true;
		if (func == FUNC_TAPP)
			return true;
		return License.isHiddenFunc(func);
	}

	public boolean isOldLicReq() {
		if (getLicVer() > LICENSE_MIN_VERSION)
			return false;
		return minVersion < License.FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[4];
	}

	public int getOldLicVer() {
		if (minVersion >= License.FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[3]
				|| getPsConnections() > 0)
			return 3;
		if (minVersion >= License.FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[2]
				|| isModuled() || isDefinitive() && !isNeverExpired())
			return 2;
		return 1;
	}

	public int getLicVer() {
		int licVer = LICENSE_MIN_VERSION;
		for (; licVer + 1 < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION.length; licVer++)
			if (minVersion < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[licVer + 1])
				break;
		if (licVer < 5 && getHeConnections() > 0)
			licVer = 5;
		if (licVer < 6 && getFunction(FUNC_FRENCH_FDM))
			licVer = 6;
		if (licVer < 7 && getDrivers() != null && getDrivers().size() > 0)
			licVer = 7;
		return licVer;
	}

	public static int verToLicVer(int version) {
		if (version == 0)
			return 0;
		int licVer = 0;
		for (; licVer + 1 < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION.length; licVer++)
			if (version < FIRST_VERSION_THAT_SUPPORTS_LICENSE_VERSION[licVer + 1])
				break;
		return licVer;
	}

	@Override
	public String toString() {
		// TODO use toString() from LicenseData
		return "[DbLicense: id=" + Long.toString(id) + "; client=\"" + getClientName() + "\""
				+ "; hardCode=" + hardCode
				+ "; connections=" + (isUnlimited() ? "unlimited" : Short.toString(getLbConnections())
						+ "," + Short.toString(getRemConnections()) + "," + Short.toString(getOmConnections())
						+ "," + Short.toString(getPsConnections()) + "," + Short.toString(getHeConnections()))
				+ "; dates=" + (getStartDate() == null ? "null" : Long.toString(getStartDate().getTime())) + ","
						+ (getIssueDate() == null ? "null" : Long.toString(getIssueDate().getTime()))
						+ (isNeverExpired() ? "" : ","
								+ (getExpiredDate() == null ? "null" : Long.toString(getExpiredDate().getTime())))
						+ (tempExpiredDate == null ? "" : "," + Long.toString(tempExpiredDate.getTime()))
				+ "; funcLimited=" + Long.toString(getFuncLimited())
				+ "; status=" + Short.toString(getStatus())
				+ "; drivers=" + (getDrivers() == null ? "null" : getDrivers().size())
				+ "]";
	}

}
