package eu.untill.license.server;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.SortedMap;
import java.util.TimerTask;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EventsStorageSyncTimerTask extends TimerTask {

	static final Logger LOG = LoggerFactory.getLogger(EventsStorageSyncTimerTask.class);

	private static class HardCodeInfo {
		public HardCodeInfo(String clientName, long idDealer, boolean isActive, Double modTime) {
			this.clientName = clientName;
			this.idDealer = idDealer;
			this.isActive = isActive;
			this.modTime = modTime;
		}
		public String clientName;
		public long idDealer;
		public boolean isActive;
		public Double modTime;
	}

	private static class DealerInfo {
		public DealerInfo(String name, String email) {
			this.name = name;
			this.email = email;
		}
		public String name;
		public String email;
	}

	@Override
	public void run() {
		LOG.debug("ENTRY");

		Connection conn = null;
		try {
			conn = Common.getConnection();

			Settings settings = Common.getSettings();
			String eventsStorageJdbcUrl = settings.getEventsStorageJdbcUrl();
			String eventsStorageJdbcUsername = settings.getEventsStorageJdbcUsername();
			String eventsStorageJdbcPassword = settings.getEventsStorageJdbcPassword();

			if (eventsStorageJdbcUrl != null && !eventsStorageJdbcUrl.isEmpty()) { 

				String lastSyncDataString = settings.getEventsStorageLastSyncData();
				boolean firstRun = lastSyncDataString == null;
				SortedMap<Integer, Integer> lastSyncData = decodeLastSyncData(lastSyncDataString);
				SortedMap<Integer, Integer> curSyncData = getCurrentSyncData(conn);

				Map<Long, DealerInfo> dealers = getNewOrModifiedDealers(conn, lastSyncData, curSyncData, firstRun);
				Map<String, HardCodeInfo> hardCodes = getNewOrModifiedHardCodes(conn, lastSyncData, curSyncData, firstRun);

				if (!dealers.isEmpty() || !hardCodes.isEmpty())
					updateEventStorage(eventsStorageJdbcUrl, eventsStorageJdbcUsername, eventsStorageJdbcPassword,
							dealers, hardCodes);

				if (!dealers.isEmpty() || !hardCodes.isEmpty() || firstRun) {
					synchronized (Common.getSettingsLock()) {
						settings = Common.getSettings();
						String curSyncDataString = encodeLastSyncData(curSyncData);
						if (!curSyncDataString.equals(settings.getEventsStorageLastSyncData())) {
							settings.setEventsStorageLastSyncData(curSyncDataString);
							settings.save();
						}
					}
				}

			}

		} catch (SQLException e) {
			LOG.error("update event storage error", e);
		} finally {
			if (conn != null) { try { conn.close(); } catch (SQLException e) { } conn = null; }
		}

		LOG.debug("RETURN");
	}

	private SortedMap<Integer, Integer> getCurrentSyncData(Connection conn) throws SQLException {
		SortedMap<Integer, Integer> result = new TreeMap<Integer, Integer>();
		try (
			PreparedStatement stmt = conn.prepareStatement(
					"SELECT SER_SYNC_DB, MAX(SER_SYNC_LOG) MAX_SER_SYNC_LOG\n"
					+ "FROM SYNC_REC_STAT2\n"
					+ "GROUP BY SER_SYNC_DB");
			ResultSet rs = stmt.executeQuery();
		) {
			while (rs.next()) {
				result.put(rs.getInt("SER_SYNC_DB"), rs.getInt("MAX_SER_SYNC_LOG"));
			}
		}
		return result;
	}

	private SortedMap<Integer, Integer> decodeLastSyncData(String lastSyncDataString) {
		Pattern pattern = Pattern.compile("([-+]?\\d+):([-+]?\\d+)");
		SortedMap<Integer, Integer> result = new TreeMap<Integer, Integer>();
		if (lastSyncDataString != null && !lastSyncDataString.isEmpty()) {
			for (String s: lastSyncDataString.split(",")) {
				Matcher matcher = pattern.matcher(s);
				if (!matcher.matches()) {
					LOG.warn("Invalid part of LastSyncData: \"{}\"", s);
					continue;
				}
				result.put(Integer.parseInt(matcher.group(1)), Integer.parseInt(matcher.group(2)));
			}
		}
		return result;
	}

	private String encodeLastSyncData(SortedMap<Integer, Integer> lastSyncData) {
		StringBuilder result = new StringBuilder();
		boolean first = true;
		for (Entry<Integer, Integer> lastSync: lastSyncData.entrySet()) {
			if (first) first = false; else result.append(",");
			result.append(lastSync.getKey().intValue());
			result.append(":");
			result.append(lastSync.getValue().intValue());
		}
		return result.toString();
	}

	private Map<Long, DealerInfo> getNewOrModifiedDealers(Connection conn, SortedMap<Integer, Integer> lastSyncData, 
			SortedMap<Integer, Integer> curSyncData, boolean firstRun) throws SQLException {
		Map<Long, DealerInfo> result = new HashMap<Long, DealerInfo>();
		final String baseSelectStmt = "SELECT DISTINCT DEALERS.ID, COALESCE(CLIENTS.NAME, DEALERS.LOGIN) NAME, CLIENTS.EMAIL\n"
				+ "FROM DEALERS\n"
				+ "  LEFT OUTER JOIN CLIENTS ON CLIENTS.ID = DEALERS.ID_CLIENTS\n"
				+ "  LEFT JOIN SYNC_REC_STAT2 ON\n"
				+ "    TID = (SELECT SER FROM SYNC_TABLES\n"
				+ "      WHERE UPPER(CAST(TABLE_NAME AS VARCHAR(50))) = 'DEALERS'\n"
				+ "    ) AND SYNC_REC_STAT2.ID = DEALERS.ID\n"
				+ "    OR\n"
				+ "    TID = (SELECT SER FROM SYNC_TABLES\n"
				+ "      WHERE UPPER(CAST(TABLE_NAME AS VARCHAR(50))) = 'CLIENTS'\n"
				+ "    ) AND SYNC_REC_STAT2.ID = CLIENTS.ID\n";
		StringBuilder where = new StringBuilder();
		if (firstRun) {
			where.append("WHERE SER_SYNC_DB IS NULL\n");
		} else {
			if (curSyncData.isEmpty())
				return result;
		}
		for (Integer serSyncDb: curSyncData.keySet()) {
			where.append(where.length() == 0 ? "WHERE" : "  OR");
			where.append(" SER_SYNC_DB = ?");
			if (lastSyncData.containsKey(serSyncDb))
				where.append(" AND SER_SYNC_LOG > ?");
			where.append(" AND SER_SYNC_LOG <= ?\n");
		}
		try (PreparedStatement stmt = conn.prepareStatement(baseSelectStmt + where.toString())) {
			int p = 1;
			for (Integer serSyncDb: curSyncData.keySet()) {
				stmt.setInt(p++, serSyncDb);
				if (lastSyncData.containsKey(serSyncDb))
					stmt.setInt(p++, lastSyncData.get(serSyncDb));
				stmt.setInt(p++, curSyncData.get(serSyncDb));
			}
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					long dealerId = rs.getLong("ID");
					DealerInfo dealerInfo = new DealerInfo(rs.getString("NAME"), rs.getString("EMAIL"));
					if (result.containsKey(dealerId)) continue; //skip if already exists
					result.put(dealerId, dealerInfo);
				}
			}
		}
		return result;
	}

	private Map<String, HardCodeInfo> getNewOrModifiedHardCodes(Connection conn, SortedMap<Integer, Integer> lastSyncData, 
			SortedMap<Integer, Integer> curSyncData, boolean firstRun) throws SQLException {
		Map<String, HardCodeInfo> result = new HashMap<String, HardCodeInfo>();
		// TODO probably worth ignoring BOOST records
		final String baseSelectStmt = "SELECT HARD_CODE, CLIENT_NAME, ID_DEALERS, IS_ACTIVE, MOD_TIME\n"
				+ "FROM LICENSES\n"
				+ "  LEFT JOIN SYNC_REC_STAT2 ON\n"
				+ "    TID = (SELECT SER FROM SYNC_TABLES\n"
				+ "      WHERE UPPER(CAST(TABLE_NAME AS VARCHAR(50))) = 'LICENSES'\n"
				+ "    ) AND SYNC_REC_STAT2.ID = LICENSES.ID\n";
		StringBuilder where = new StringBuilder();
		if (firstRun) {
			where.append("WHERE SER_SYNC_DB IS NULL\n");
		} else {
			if (curSyncData.isEmpty())
				return result;
		}
		for (Integer serSyncDb: curSyncData.keySet()) {
			where.append(where.length() == 0 ? "WHERE" : "  OR");
			where.append(" SER_SYNC_DB = ?");
			if (lastSyncData.containsKey(serSyncDb))
				where.append(" AND SER_SYNC_LOG > ?");
			where.append(" AND SER_SYNC_LOG <= ?\n");
		}
		try (PreparedStatement stmt = conn.prepareStatement(baseSelectStmt + where.toString())) {
			int p = 1;
			for (Integer serSyncDb: curSyncData.keySet()) {
				stmt.setInt(p++, serSyncDb);
				if (lastSyncData.containsKey(serSyncDb))
					stmt.setInt(p++, lastSyncData.get(serSyncDb));
				stmt.setInt(p++, curSyncData.get(serSyncDb));
			}
			try (ResultSet rs = stmt.executeQuery()) {
				while (rs.next()) {
					String hardCode = rs.getString("HARD_CODE");
					Double recModTime = rs.getDouble("MOD_TIME");
					if (rs.wasNull()) recModTime = null;
					HardCodeInfo hardCodeInfo = new HardCodeInfo(rs.getString("CLIENT_NAME"),
							rs.getLong("ID_DEALERS"), rs.getShort("IS_ACTIVE") != 0, recModTime);
					//skip if already exists
					if (result.containsKey(hardCode)) {
						HardCodeInfo exHardCodeInfo = result.get(hardCode);
						if (!hardCodeInfo.isActive && exHardCodeInfo.isActive
								|| hardCodeInfo.modTime == null
								|| exHardCodeInfo.modTime != null 
									&& exHardCodeInfo.modTime >= hardCodeInfo.modTime)
							continue;
					}
					result.put(hardCode, hardCodeInfo);
				}
			}
		}
		return result;
	}

	private void updateEventStorage(String url, String username, String password, Map<Long, DealerInfo> dealers, Map<String, HardCodeInfo> hardCodes)
			throws SQLException {

		try (Connection conn = DriverManager.getConnection(url, username, password)) {
			conn.setAutoCommit(false);

			try (CallableStatement addDealer = conn.prepareCall("{call addDealer(?, ?, ?)}")) {
				for (Entry<Long, DealerInfo> dealer: dealers.entrySet()) {
					addDealer.setLong(1, dealer.getKey());
					addDealer.setString(2, dealer.getValue().name);
					addDealer.setString(3, dealer.getValue().email);
					addDealer.execute();
					// TODO use batch ?
					addDealer.clearParameters();
				}
			}

			try (CallableStatement addClient = conn.prepareCall("{call addClient(?, ?, ?)}")) {
				for (Entry<String, HardCodeInfo> hardCode: hardCodes.entrySet()) {
					addClient.setString(1, hardCode.getKey());
					addClient.setString(2, hardCode.getValue().clientName);
					addClient.setLong(3, hardCode.getValue().idDealer);
					addClient.execute();
					// TODO use batch ?
					addClient.clearParameters();
				}
			}

			conn.commit();
		}

	}

}
