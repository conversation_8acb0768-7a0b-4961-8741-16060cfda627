package eu.untill.license.server;

import java.util.Map;

import org.junit.Ignore;
import org.junit.Test;

import eu.untill.license.shared.ShProductInfo;

public class DeployerEngineHandlerTest {

	@Ignore
	@Test public void getProductList() {
		DeployerEngineHandler deployerEngineHandler = new DeployerEngineHandler();
		Map<String, ShProductInfo> productList = deployerEngineHandler.getProductList();

		for (String product : productList.keySet()) {
			System.out.println(product);
			deployerEngineHandler.getProductVersionList(product);
		}
		
	}

}
