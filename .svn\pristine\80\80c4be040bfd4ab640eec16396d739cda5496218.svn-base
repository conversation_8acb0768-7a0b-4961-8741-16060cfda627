/**
 *
 */
package eu.untill.license.server;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 *
 */
public class UntillLicensesPluginHelperImpl implements UntillLicensesPluginHelper {

	static final Logger LOG = LoggerFactory.getLogger(UntillLicensesPluginHelperImpl.class);

	  ///////////////////////////////////////////////
	 // UntillLicensesPluginHelper implementation //
	///////////////////////////////////////////////

	@Override
	public File generateLicenseFile(long licenseId, int licenseVersion, boolean temp) throws Exception {
		LOG.debug("ENTRY ({}, {}, {})", licenseId, licenseVersion, temp);

		// Create temporary key file
		File licenseFile = File.createTempFile("unT", licenseVersion < 4 ? ".lic" : ".li4");
		licenseFile.deleteOnExit();

		// Invoke Licenses unTill plug-in to get license
		if (!invokeLicensesPlugin((temp ? "GenerateTempLicenseFile;" : "GenerateLicenseFile;")
				+ Long.toString(licenseId) + ";"
				+ licenseFile.getAbsolutePath() + ";"
				+ Integer.toString(licenseVersion) + ";")) {
			throw new Exception("Error invoking licenses plugin"); // TODO use exception in invokeLicensesPlugin
		}
		if (!licenseFile.exists() || !licenseFile.canRead() || licenseFile.length() == 0)
			throw new Exception("Received license file is missing, empty or no access to it");

		LOG.debug("RETURN {}", licenseFile);
		return licenseFile;
	}

	@Override
	public boolean voidOrder(int tableNo) {
		LOG.debug("ENTRY ({})", tableNo);

		// Invoke Licenses unTill plug-in
		boolean result = invokeLicensesPlugin("VoidOrder;" + Integer.toString(tableNo) + ";");

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean apostillGenerateHardCode(long apostillHddCodeId) {
		LOG.debug("ENTRY ({})", apostillHddCodeId);

		// Invoke Licenses unTill plug-in
		boolean result = invokeLicensesPlugin("ApostillGenerateHardCode;"
				+ Long.toString(apostillHddCodeId) + ";");

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean licensesPluginAddDiscount(int tableNo, double price, String text)
			throws UnsupportedEncodingException {
		LOG.debug("ENTRY ({}, {}, {})", tableNo, price, text);

		// Invoke Licenses unTill plug-in
		boolean result = invokeLicensesPlugin("AddDiscount;"
				+ Integer.toString(tableNo) + ";"
				+ Double.toString(price) + ";"
				+ EncodeDollarSignEncodingUTF8String(text) + ";");

		LOG.debug("RETURN {}", result);
		return result;
	}


	@Override
	public boolean licensesPluginAddNegativeArticle(int tableNo, long articleId, int quantity,
			String text) throws UnsupportedEncodingException {
		LOG.debug("ENTRY ({}, {}, {}, {})", tableNo, articleId, quantity, text);

		// Invoke Licenses unTill plug-in
		boolean result = invokeLicensesPlugin("AddNegativeArticleWQ;"
				+ Integer.toString(tableNo) + ";"
				+ Long.toString(articleId) + ";"
				+ Integer.toString(quantity) + ";"
				+ EncodeDollarSignEncodingUTF8String(text) + ";");

		LOG.debug("RETURN {}", result);
		return result;
	}

	@Override
	public boolean reopenBill(int pbillNumber, String pbillSuffix)
			throws UnsupportedEncodingException {
		LOG.debug("ENTRY ({}, {})", pbillNumber, pbillSuffix);

		// Invoke Licenses unTill plug-in
		boolean result = invokeLicensesPlugin("ReopenBill;"
				+ Integer.toString(pbillNumber) + ";"
				+ EncodeDollarSignEncodingUTF8String(pbillSuffix) + ";");

		LOG.debug("RETURN {}", result);
		return result;
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private boolean invokeLicensesPlugin(String params) {
		LOG.debug("ENTRY ({})", params);

		// Get settings
		Settings settings = Common.getSettings();

		boolean result = false;
		// Invoke Licenses unTill plug-in
		try {
			String[] cmdArray = new String[] {
					settings.getUntillPath(),
					"invoke", "Licenses",
					"/db=" + settings.getUntillDb(),
					"/params=" + params
			};
			Process process = Runtime.getRuntime().exec(cmdArray);
			if (process.waitFor() == 0) {
				result = true;
			} else {
				LOG.error("Error invoking licenses plugin from unTill (cmdArray = {})", (Object) cmdArray);
			}
		} catch (IOException e) {
			LOG.error("IOException", e);
		} catch (InterruptedException e) {
			LOG.error("InterruptedException", e);
		}

		LOG.debug("RETURN {}", result);
		return result;
	}

	private String EncodeDollarSignEncodingUTF8String(String text) throws UnsupportedEncodingException {
		if (text == null)
			return "";

		byte[] utf8Text = text.getBytes("UTF-8");
		Charset asciiCharset = Charset.forName("US-ASCII");
		StringBuffer result = new StringBuffer();
		for (int i = 0; i < utf8Text.length; ++i) {
			if (utf8Text[i] >= 48 && utf8Text[i] <= 57 // '0'-'9'
					|| utf8Text[i] >= 65 && utf8Text[i] <= 90 // 'A'-'Z'
					|| utf8Text[i] >= 97 && utf8Text[i] <= 122) { // 'a'-'z'
				result.append(new String(utf8Text, i, 1, asciiCharset));
			} else {
				result.append("$");
				String hex = Integer.toHexString((utf8Text[i] + 0x100) & 0xff);
				result.append((hex.length() < 2 ? "0" : "") + hex);
			}
		}
		return result.toString();
	}

}
