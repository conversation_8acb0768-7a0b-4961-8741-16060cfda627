package eu.untill.license.shared;

import java.util.List;

import com.google.gwt.user.client.rpc.IsSerializable;

import eu.untill.license.shared.Fiscal.Address;

/**
 * Represents fiscal organisation (based on Fiscal class)
 */
public class FiscalOrganisation implements IsSerializable {
	private String vatNumber;
	private String businessIdentificationNumber;

	private String fiscalCountry;
	private String fiscalYearStartMonth;

	private String organisationCaption;
	private Address organisationAddress;
	private String organisationId;

	private List<String> storeNumbers;

	public String getVatNumber() {
		return vatNumber;
	}

	public void setVatNumber(String vatNumber) {
		this.vatNumber = vatNumber;
	}

	public String getBusinessIdentificationNumber() {
		return businessIdentificationNumber;
	}

	public void setBusinessIdentificationNumber(String businessIdentificationNumber) {
		this.businessIdentificationNumber = businessIdentificationNumber;
	}

	public String getFiscalCountry() {
		return fiscalCountry;
	}

	public void setFiscalCountry(String fiscalCountry) {
		this.fiscalCountry = fiscalCountry;
	}

	public String getFiscalYearStartMonth() {
		return fiscalYearStartMonth;
	}

	public void setFiscalYearStartMonth(String fiscalYearStartMonth) {
		this.fiscalYearStartMonth = fiscalYearStartMonth;
	}

	public String getOrganisationCaption() {
		return organisationCaption;
	}

	public void setOrganisationCaption(String organisationCaption) {
		this.organisationCaption = organisationCaption;
	}

	public Address getOrganisationAddress() {
		return organisationAddress;
	}

	public void setOrganisationAddress(Address organisationAddress) {
		this.organisationAddress = organisationAddress;
	}

	public String getOrganisationId() {
		return organisationId;
	}

	public void setOrganisationId(String organisationId) {
		this.organisationId = organisationId;
	}

	public List<String> getStoreNumbers() {
		return storeNumbers;
	}

	public void setStoreNumbers(List<String> storeNumbers) {
		this.storeNumbers = storeNumbers;
	}
}
