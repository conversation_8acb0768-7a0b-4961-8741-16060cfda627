package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.LocaleInfo;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasVerticalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.shared.Fiscal;
import eu.untill.license.shared.Fiscal.Address;
import eu.untill.license.shared.Fiscal.Terminal;
import eu.untill.license.shared.FiscalOrganisation;

public class FiscalDialog extends DialogBox implements ClickHandler {

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Fiscal parameters")
		String fiscalDialogHeader();

		String okButton();
		String cancelButton();
	}

	public static interface UlsMessages extends Messages {
		@DefaultMessage("Please enter VAT number")
		String pleaseEnterVatNumbereMessage();
		@DefaultMessage("Please enter CVR number")
		String pleaseEnterCvrNumbereMessage();
		@DefaultMessage("Please enter Organisation Caption")
		String pleaseEnterOrganisationCaptionMessage();
		@DefaultMessage("Please enter Organisation City")
		String pleaseEnterOrganisationCityMessage();
		@DefaultMessage("Please enter Organisation Street")
		String pleaseEnterOrganisationStreetMessage();
		@DefaultMessage("Please enter Organisation Street number")
		String pleaseEnterOrganisationStreetNumberMessage();
		@DefaultMessage("Please enter Organisation Postal code")
		String pleaseEnterOrganisationPostalCodeMessage();
		@DefaultMessage("Please enter Store Number")
		String pleaseEnterStoreNumberMessage();
		@DefaultMessage("Store with same Number already exists")
		String storeNumberAlreadyExistsMessage();
		@DefaultMessage("Please enter Store Caption")
		String pleaseEnterStoreCaptionMessage();
		@DefaultMessage("Please enter Store City")
		String pleaseEnterStoreCityMessage();
		@DefaultMessage("Please enter Store Street")
		String pleaseEnterStoreStreetMessage();
		@DefaultMessage("Please enter Store Street number")
		String pleaseEnterStoreStreetNumberMessage();
		@DefaultMessage("Please enter Store Postal code")
		String pleaseEnterStorePostalCodeMessage();
		@DefaultMessage("Please enter at least one Terminal")
		String pleaseEnterAtLeastOneTerminalMessage();
		@DefaultMessage("Terminals should not exceed the number of Local Databases")
		String terminalLimitMessage();
		@DefaultMessage("Please enter Terminal Number")
		String pleaseEnterTerminalNumbereMessage();
		@DefaultMessage("Terminal with same Number already exists")
		String terminalNumberAlreadyExistsMessage();
	}
	
	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();


	  /////////////
	 // Members //
	/////////////

	private LicenseDialog licenseDialog;
	private Fiscal fiscal;
	private List<Terminal> newTerminals;
	private List<FiscalOrganisation> fiscalOrganisations;

	  //////////////////
	 // Constructors //
	//////////////////

	public FiscalDialog(LicenseDialog licenseDialog, Fiscal fiscal) {
		this.licenseDialog = licenseDialog;
		this.fiscal = fiscal;

		newTerminals = new ArrayList<>();
		if (fiscal != null && fiscal.getTerminals() != null) {
			for (Terminal terminal : fiscal.getTerminals()) {
				Terminal newTerminal = new Terminal(); // TODO: maybe clone from fiscal.getTerminals()
				newTerminal.setTerminalNumber(terminal.getTerminalNumber());
				newTerminal.setCaption(terminal.getCaption());
				newTerminal.setTerminalId(terminal.getTerminalId());
				newTerminal.setRequiresUpdate(terminal.getRequiresUpdate());
				newTerminal.setRequiresDelete(terminal.getRequiresDelete());
				newTerminals.add(newTerminal);
			}
		}

		// Generate base interface
		generateUI();

		if (fiscal == null) {
			txtStoreNumber.setValue("1");
			chkNoStoreAddress.setValue(true);
			chkNoStoreAddress_click();
		} else {
			txtVatNumber.setValue(fiscal.getVatNumber());
			txtCvrNumber.setValue(fiscal.getBusinessIdentificationNumber());
			//txtFiscalCountry.setValue(fiscal.getFiscalCountry());
			txtOrganisationCaption.setValue(fiscal.getOrganisationCaption());
			String fiscalYearStartMonth = fiscal.getFiscalYearStartMonth();
			if (fiscalYearStartMonth != null)
				lstFiscalYearStartMonth.setSelectedIndex(Integer.parseInt(fiscalYearStartMonth) - 1);
			if (fiscal.getOrganisationAddress() != null) {
				//txtOrganisationCountryCode.setValue(fiscal.getOrganisationAddress().getCountryCode());
				txtOrganisationCity.setValue(fiscal.getOrganisationAddress().getCity());
				txtOrganisationStreet.setValue(fiscal.getOrganisationAddress().getStreet());
				txtOrganisationStreetNumber.setValue(fiscal.getOrganisationAddress().getStreetNumber());
				txtOrganisationPostalCode.setValue(fiscal.getOrganisationAddress().getPostalCode());
			}
			txtStoreNumber.setValue(fiscal.getStoreNumber());
			txtStoreCaption.setValue(fiscal.getStoreCaption());
			if (fiscal.getStoreAddress() != null) {
				chkNoStoreAddress.setValue(false);
				chkNoStoreAddress_click();
				//txtStoreCountryCode.setValue(fiscal.getStoreAddress().getCountryCode());
				txtStoreCity.setValue(fiscal.getStoreAddress().getCity());
				txtStoreStreet.setValue(fiscal.getStoreAddress().getStreet());
				txtStoreStreetNumber.setValue(fiscal.getStoreAddress().getStreetNumber());
				txtStorePostalCode.setValue(fiscal.getStoreAddress().getPostalCode());
			} else {
				chkNoStoreAddress.setValue(true);
				chkNoStoreAddress_click();
			}
		}

		refreshTerminalsTable();

		this.setText(constants.fiscalDialogHeader());

		requestFiscalOrganisations();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();
	private HorizontalPanel dataPanel = new HorizontalPanel();

	private EntitledDecoratorPanel dpnOrganisation = new EntitledDecoratorPanel("Organisation"); // TODO: to constants
	private FlexTable tblOrganisation = new FlexTable();
	private ListBox lstExistingOrganisations = new ListBox();
	private Label lblVatNumber = new Label("VAT number" + ":", false); // TODO: to constants
	private TextBox txtVatNumber = new TextBox();
	private Label lblCvrNumber = new Label("CVR number" + ":", false); // TODO: to constants
	private TextBox txtCvrNumber = new TextBox();
	private Label lblFiscalYearStartMonth = new Label("Fiscal year start month" + ":", false); // TODO: to constants
	private ListBox lstFiscalYearStartMonth = new ListBox();
//	private Label lblFiscalCountry = new Label("FiscalCountry" + ":", false); // TODO: to constants
//	private TextBox txtFiscalCountry = new TextBox(); // TODO: from Dealer country
	private Label lblOrganisationCaption = new Label("Caption" + ":", false); // TODO: to constants
	private TextBox txtOrganisationCaption = new TextBox();

	// OrganisationAddress
	//private Label lblOrganisationCountryCode = new Label("Country code" + ":", false); // TODO: to constants
	//private TextBox txtOrganisationCountryCode = new TextBox(); // TODO: from Dealer country
	private Label lblOrganisationCity = new Label("City" + ":", false); // TODO: to constants
	private TextBox txtOrganisationCity = new TextBox();
	private Label lblOrganisationStreet = new Label("Street" + ":", false); // TODO: to constants
	private TextBox txtOrganisationStreet = new TextBox();
	private Label lblOrganisationStreetNumber = new Label("Street number" + ":", false); // TODO: to constants
	private TextBox txtOrganisationStreetNumber = new TextBox();
	private Label lblOrganisationPostalCode = new Label("Postal code" + ":", false); // TODO: to constants
	private TextBox txtOrganisationPostalCode = new TextBox();

	private EntitledDecoratorPanel dpnStore = new EntitledDecoratorPanel("Store"); // TODO: to constants
	private FlexTable tblStore = new FlexTable();

	private Label lblStoreNumber = new Label("Number" + ":", false); // TODO: to constants
	private TextBox txtStoreNumber = new TextBox();
	private Label lblStoreCaption = new Label("Caption" + ":", false); // TODO: to constants
	private TextBox txtStoreCaption = new TextBox();
	
	private CheckBox chkNoStoreAddress = new CheckBox("Same address as organisation");
	// storeAddress
//	private Label lblStoreCountryCode = new Label("Store CountryCode" + ":", false); // TODO: to constants
//	private TextBox txtStoreCountryCode = new TextBox(); // TODO: from Dealer country
	private Label lblStoreCity = new Label("City" + ":", false); // TODO: to constants
	private TextBox txtStoreCity = new TextBox();
	private Label lblStoreStreet = new Label("Street" + ":", false); // TODO: to constants
	private TextBox txtStoreStreet = new TextBox();
	private Label lblStoreStreetNumber = new Label("Street number" + ":", false); // TODO: to constants
	private TextBox txtStoreStreetNumber = new TextBox();
	private Label lblStorePostalCode = new Label("Postal code" + ":", false); // TODO: to constants
	private TextBox txtStorePostalCode = new TextBox();

	private EntitledDecoratorPanel dpnTerminals = new EntitledDecoratorPanel("Terminals"); // TODO: to constants 
	private FlexTable tblTerminals = new FlexTable();
	private ScrollPanel scrTerminals = new ScrollPanel(tblTerminals);

//	// TODO ...
//	private Label lblTerminal0Number = new Label("Terminal 1 Number" + ":", false);
//	private TextBox txtTerminal0Number = new TextBox();
//	private Label lblTerminal0Caption = new Label("Terminal 1 Caption" + ":", false);
//	private TextBox txtTerminal0Caption = new TextBox();
//	private Label lblTerminal1Number = new Label("Terminal 2 Number" + ":", false);
//	private TextBox txtTerminal1Number = new TextBox();
//	private Label lblTerminal1Caption = new Label("Terminal 2 Caption" + ":", false);
//	private TextBox txtTerminal1Caption = new TextBox();

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnAddTerminal = new Button("Add terminal...", this); // TODO: to constants
	private Button btnOk = new Button(constants.okButton(), this);

	private void generateUI() {

		// Assemble Organisation panel
		lstExistingOrganisations.addItem("New organisation"); // TODO: to constants
		lstExistingOrganisations.setWidth("100%");
		txtVatNumber.setMaxLength(100);
		txtCvrNumber.setMaxLength(100);
		for (String month : LocaleInfo.getCurrentLocale().getDateTimeFormatInfo().monthsFull())
			lstFiscalYearStartMonth.addItem(month);
		txtOrganisationCaption.setMaxLength(50);
		txtOrganisationCity.setMaxLength(100);
		txtOrganisationStreet.setMaxLength(100);
		txtOrganisationStreetNumber.setMaxLength(20);
		txtOrganisationPostalCode.setMaxLength(12);
		FlexCellFormatter tblOrganisationCF = tblOrganisation.getFlexCellFormatter();
		int row = 0;
		if (fiscal == null) {
			tblOrganisationCF.setColSpan(row, 0, 2);
			tblOrganisation.setWidget(row++, 0, lstExistingOrganisations);
			lstExistingOrganisations.addChangeHandler(event -> { lstExistingOrganisations_click(); });
		} else {
			txtVatNumber.setEnabled(false);
			txtCvrNumber.setEnabled(false);
			txtOrganisationCaption.setEnabled(false);
			lstFiscalYearStartMonth.setEnabled(false);
			txtOrganisationCity.setEnabled(false);
			txtOrganisationStreet.setEnabled(false);
			txtOrganisationStreetNumber.setEnabled(false);
			txtOrganisationPostalCode.setEnabled(false);
		}
		tblOrganisation.setWidget(row, 0, lblVatNumber);
		tblOrganisation.setWidget(row++, 1, txtVatNumber);
		tblOrganisation.setWidget(row, 0, lblCvrNumber);
		tblOrganisation.setWidget(row++, 1, txtCvrNumber);
//		tblOrganisation.setWidget(row, 0, lblFiscalCountry);
//		tblOrganisation.setWidget(row++, 1, txtFiscalCountry);		
		tblOrganisation.setWidget(row, 0, lblOrganisationCaption);
		tblOrganisation.setWidget(row++, 1, txtOrganisationCaption);
		tblOrganisation.setWidget(row, 0, lblFiscalYearStartMonth);
		tblOrganisation.setWidget(row++, 1, lstFiscalYearStartMonth);
//		tblOrganisation.setWidget(row, 0, lblOrganisationCountryCode);
//		tblOrganisation.setWidget(row++, 1, txtOrganisationCountryCode);		
		tblOrganisation.setWidget(row, 0, lblOrganisationCity);
		tblOrganisation.setWidget(row++, 1, txtOrganisationCity);		
		tblOrganisation.setWidget(row, 0, lblOrganisationStreet);
		tblOrganisation.setWidget(row++, 1, txtOrganisationStreet);		
		tblOrganisation.setWidget(row, 0, lblOrganisationStreetNumber);
		tblOrganisation.setWidget(row++, 1, txtOrganisationStreetNumber);		
		tblOrganisation.setWidget(row, 0, lblOrganisationPostalCode);
		tblOrganisation.setWidget(row++, 1, txtOrganisationPostalCode);		
		dpnOrganisation.setWidget(tblOrganisation);

		// Assemble Store panel
		txtStoreNumber.setMaxLength(50);
		txtStoreCaption.setMaxLength(100);
		txtStoreCity.setMaxLength(100);
		txtStoreStreet.setMaxLength(100);
		txtStoreStreetNumber.setMaxLength(20);
		txtStorePostalCode.setMaxLength(12);
		FlexCellFormatter tblStoreCF = tblStore.getFlexCellFormatter();
		row = 0;
		tblStore.setWidget(row, 0, lblStoreNumber);
		tblStore.setWidget(row++, 1, txtStoreNumber);		
		tblStore.setWidget(row, 0, lblStoreCaption);
		tblStore.setWidget(row++, 1, txtStoreCaption);
		tblStoreCF.setColSpan(row, 0, 2);
		tblStore.setWidget(row++, 0, chkNoStoreAddress);
//		tblStore.setWidget(row, 0, lblStoreCountryCode);
//		tblStore.setWidget(row++, 1, txtStoreCountryCode);		
		tblStore.setWidget(row, 0, lblStoreCity);
		tblStore.setWidget(row++, 1, txtStoreCity);		
		tblStore.setWidget(row, 0, lblStoreStreet);
		tblStore.setWidget(row++, 1, txtStoreStreet);		
		tblStore.setWidget(row, 0, lblStoreStreetNumber);
		tblStore.setWidget(row++, 1, txtStoreStreetNumber);		
		tblStore.setWidget(row, 0, lblStorePostalCode);
		tblStore.setWidget(row++, 1, txtStorePostalCode);		
		dpnStore.setWidget(tblStore);

		chkNoStoreAddress.addValueChangeHandler(value -> { chkNoStoreAddress_click(); });
		
		// Create terminals table
		tblTerminals.addStyleName("al-Table");
		tblTerminals.setWidth("99%");
		tblTerminals.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tblTerminalsCF = tblTerminals.getFlexCellFormatter();
		int col = 0;
		//tblTerminalsCF.setWidth(0, col, "160px");
		tblTerminalsCF.setWidth(0, col, "30%");
		tblTerminals.setText(0, col++, "Number"); // TODO: to constants
		//tblTerminalsCF.setWidth(0, col, "160px");
		tblTerminalsCF.setWidth(0, col, "30%");
		tblTerminals.setText(0, col++, "Caption"); // TODO: to constants
		//tblTerminalsCF.setColSpan(0, col, 2);
		//tblTerminalsCF.setWidth(0, col++, "80px");
		tblTerminalsCF.setWidth(0, col++, "20%");
		//tblTerminalsCF.setWidth(0, col++, "80px");
		tblTerminalsCF.setWidth(0, col++, "20%");
		//tblTerminals.setText(0, col, "Actions"); // TODO: to constants
		//col += 2;

		// Assemble terminals panel with scroll
		scrTerminals.setSize("520", "200px");
		dpnTerminals.setWidget(scrTerminals);

		// Assemble button panel
		btnAddTerminal.setWidth("120px");
		btnOk.setWidth("80px");
		pnlButtons.add(btnAddTerminal);
		pnlButtons.setCellHorizontalAlignment(btnAddTerminal, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.setCellWidth(btnAddTerminal, "100%");
		pnlButtons.setWidth("100%");
		//pnlButtons.setCellWidth(btnOk, "100%");
		pnlButtons.add(btnOk);
		pnlButtons.setCellHorizontalAlignment(btnOk, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		dataPanel.add(dpnOrganisation);
		dataPanel.add(dpnStore);
		
		// Assemble main panel
		mainPanel.add(dataPanel);
		mainPanel.add(dpnTerminals);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnAddTerminal) {

			new TerminalDialog().show();

		} else if (sender == btnOk) {

			this.hide();
			
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void chkNoStoreAddress_click() {
//		txtStoreCountryCode.setEnabled(!chkNoStoreAddress.getValue());
		txtStoreCity.setEnabled(!chkNoStoreAddress.getValue());
		txtStoreStreet.setEnabled(!chkNoStoreAddress.getValue());
		txtStoreStreetNumber.setEnabled(!chkNoStoreAddress.getValue());
		txtStorePostalCode.setEnabled(!chkNoStoreAddress.getValue());
	}

	private void refreshTerminalsTable() {
		scrTerminals.remove(tblTerminals);
		try {
			clearTerminalsTable();
			fillTerminalsTable();
		} finally {
			scrTerminals.setWidget(tblTerminals);
		}
	}

	private void clearTerminalsTable() {
		for (int r = tblTerminals.getRowCount() - 1; r > 0; --r)
			tblTerminals.removeRow(r);
	}

	private void fillTerminalsTable() {
		RowFormatter tblTerminalsRF = tblTerminals.getRowFormatter();
		FlexCellFormatter tblTerminalsCF = tblTerminals.getFlexCellFormatter();

		// Add licenses into table
		if (newTerminals.isEmpty()) {
			tblTerminalsCF.setColSpan(1, 0, tblTerminals.getCellCount(0));
			tblTerminals.setText(1, 0, "No terminals"); // TODO: to constants
			tblTerminalsCF.setAlignment(1, 0, HasHorizontalAlignment.ALIGN_CENTER,
					HasVerticalAlignment.ALIGN_MIDDLE);
		} else {
			int row = 1;

			for (Terminal terminal : newTerminals) {

				// set row style classes
				tblTerminalsRF.addStyleName(row, "al-Row");

				// fill row
				int col = 0;
				//tblTerminalsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblTerminals.setText(row, col++, terminal.getTerminalNumber());
//				tblTerminalsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblTerminals.setText(row, col++, terminal.getCaption());

				if (Boolean.parseBoolean(terminal.getRequiresDelete())) {
					tblTerminalsRF.addStyleName(row, "strike");
					tblTerminals.setText(row, col++, " ");
				} else {
					tblTerminalsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
					tblTerminals.setWidget(row, col++, new TerminalEditButton(terminal));
				}
				tblTerminalsCF.setHorizontalAlignment(row, col, HasHorizontalAlignment.ALIGN_CENTER);
				tblTerminals.setWidget(row, col++, new TerminalDeleteButton(terminal));

				++row;
			}
		}
	}

	private void requestFiscalOrganisations() {
		UntillLicenseServer.getLicenseServiceAsync().getFiscalOrganisations(UntillLicenseServer.getAuthScheme(),
				licenseDialog.getDealer().id, new AsyncCallback<List<FiscalOrganisation>>() {
					@Override
					public void onSuccess(List<FiscalOrganisation> result) {
						fiscalOrganisations = result;
						refreshFiscalOrganisations();
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show(caught);
					}
				});
	}

	private void refreshFiscalOrganisations() {
		String lastSelecteValue = null;
		if (lstExistingOrganisations.getSelectedIndex() > 0)
			lastSelecteValue = lstExistingOrganisations.getSelectedValue();
		lstExistingOrganisations.clear();
		lstExistingOrganisations.addItem("New organisation"); // TODO: to constants
		if (fiscalOrganisations != null) {
			for (FiscalOrganisation fiscalOrganisation : fiscalOrganisations) {
				lstExistingOrganisations.addItem(fiscalOrganisation.getOrganisationCaption());
//						String.format("%s (%s, %s)", fiscalOrganisation.getOrganisationCaption(),
//						fiscalOrganisation.getVatNumber(), fiscalOrganisation.getBusinessIdentificationNumber()));
				if (lastSelecteValue != null && lastSelecteValue
						.equals(lstExistingOrganisations.getItemText(lstExistingOrganisations.getItemCount() - 1)))
					lstExistingOrganisations.setSelectedIndex(lstExistingOrganisations.getItemCount() - 1);
			}
		}
		if (lstExistingOrganisations.getSelectedIndex() < 0)
			lstExistingOrganisations.setSelectedIndex(0);
		// TODO refresh fields
	}

	private int lastExistingOrganisationIndex = -1;
	private String lastVatNumber = null;
	private String lastCvrNumber = null;
	private String lastOrganisationCaption = null;
	private int lastFiscalYearStartMonth = 0;
	private String lastOrganisationCity = null;
	private String lastOrganisationStreet = null;
	private String lastOrganisationStreetNumber = null;
	private String lastOrganisationPostalCode = null;
	private String lastStoreNumber = null;
	private Boolean lastNoStoreAddress = null;
	
	private void lstExistingOrganisations_click() {
		if (lstExistingOrganisations.getSelectedIndex() > 0) {
			FiscalOrganisation fiscalOrganisation = fiscalOrganisations.get(lstExistingOrganisations.getSelectedIndex() - 1);
			if (lastExistingOrganisationIndex <= 0) {
				lastVatNumber = txtVatNumber.getValue();
				lastCvrNumber = txtCvrNumber.getValue();
				lastOrganisationCaption = txtOrganisationCaption.getValue();
				lastFiscalYearStartMonth = lstFiscalYearStartMonth.getSelectedIndex() + 1;
				lastOrganisationCity = txtOrganisationCity.getValue();
				lastOrganisationStreet = txtOrganisationStreet.getValue();
				lastOrganisationStreetNumber = txtOrganisationStreetNumber.getValue();
				lastOrganisationPostalCode = txtOrganisationPostalCode.getValue();
				lastStoreNumber = txtStoreNumber.getValue();
				lastNoStoreAddress = chkNoStoreAddress.getValue();
			}
			txtVatNumber.setValue(fiscalOrganisation.getVatNumber());
			txtCvrNumber.setValue(fiscalOrganisation.getBusinessIdentificationNumber());
			//txtFiscalCountry.setValue(fiscalOrganisation.getFiscalCountry());
			txtOrganisationCaption.setValue(fiscalOrganisation.getOrganisationCaption());
			String fiscalYearStartMonth = fiscalOrganisation.getFiscalYearStartMonth();
			if (fiscalYearStartMonth != null)
				lstFiscalYearStartMonth.setSelectedIndex(Integer.parseInt(fiscalYearStartMonth) - 1);
			if (fiscalOrganisation.getOrganisationAddress() != null) {
				// txtOrganisationCountryCode.setValue(fiscalOrganisation.getOrganisationAddress().getCountryCode());
				txtOrganisationCity.setValue(fiscalOrganisation.getOrganisationAddress().getCity());
				txtOrganisationStreet.setValue(fiscalOrganisation.getOrganisationAddress().getStreet());
				txtOrganisationStreetNumber.setValue(fiscalOrganisation.getOrganisationAddress().getStreetNumber());
				txtOrganisationPostalCode.setValue(fiscalOrganisation.getOrganisationAddress().getPostalCode());
			} else {
				// txtOrganisationCountryCode.setValue("");
				txtOrganisationCity.setValue("");
				txtOrganisationStreet.setValue("");
				txtOrganisationStreetNumber.setValue("");
				txtOrganisationPostalCode.setValue("");
			}
			// get max store number and increment
			fiscalOrganisation.getStoreNumbers().stream()
				.map(s -> { try { return Integer.parseInt(s); } catch (NumberFormatException e) { return null; } })
				.filter(num -> num != null)
				.mapToInt(Integer::intValue).max()
				.ifPresent(max -> txtStoreNumber.setValue(String.valueOf(max + 1)));
			chkNoStoreAddress.setValue(false);
			chkNoStoreAddress_click();
		} else {
			if (lastExistingOrganisationIndex > 0) {
				txtVatNumber.setValue(lastVatNumber);
				txtCvrNumber.setValue(lastCvrNumber);
				txtOrganisationCaption.setValue(lastOrganisationCaption);
				lstFiscalYearStartMonth.setSelectedIndex(lastFiscalYearStartMonth - 1);
				txtOrganisationCity.setValue(lastOrganisationCity);
				txtOrganisationStreet.setValue(lastOrganisationStreet);
				txtOrganisationStreetNumber.setValue(lastOrganisationStreetNumber);
				txtOrganisationPostalCode.setValue(lastOrganisationPostalCode);
				txtStoreNumber.setValue(lastStoreNumber);
				chkNoStoreAddress.setValue(lastNoStoreAddress);
				chkNoStoreAddress_click();
			}
		}
		txtVatNumber.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		txtCvrNumber.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		txtOrganisationCaption.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		lstFiscalYearStartMonth.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		txtOrganisationCity.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		txtOrganisationStreet.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		txtOrganisationStreetNumber.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		txtOrganisationPostalCode.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);
		chkNoStoreAddress.setEnabled(lstExistingOrganisations.getSelectedIndex() <= 0);

		lastExistingOrganisationIndex = lstExistingOrganisations.getSelectedIndex(); 
	}

	  ////////////////////
	 // Public methods //
	////////////////////

	public boolean checkParameters() {
		if (fiscal == null && lstExistingOrganisations.getSelectedIndex() <= 0) {
			if (txtVatNumber.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterVatNumbereMessage(), Style.WARNING, txtVatNumber).show();
				return false;
			}
			if (txtCvrNumber.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterCvrNumbereMessage(), Style.WARNING, txtCvrNumber).show();
				return false;
			}
			if (txtOrganisationCaption.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterOrganisationCaptionMessage(), Style.WARNING, txtOrganisationCaption).show();
				return false;
			}
	
			if (txtOrganisationCity.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterOrganisationCityMessage(), Style.WARNING, txtOrganisationCity).show();
				return false;
			}
			if (txtOrganisationStreet.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterOrganisationStreetMessage(), Style.WARNING, txtOrganisationStreet).show();
				return false;
			}
			if (txtOrganisationStreetNumber.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterOrganisationStreetNumberMessage(), Style.WARNING, txtOrganisationStreetNumber).show();
				return false;
			}
			if (txtOrganisationPostalCode.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterOrganisationPostalCodeMessage(), Style.WARNING, txtOrganisationPostalCode).show();
				return false;
			}
		} else if (fiscal == null) {
			FiscalOrganisation fiscalOrganisation = fiscalOrganisations.get(lstExistingOrganisations.getSelectedIndex() - 1);
			if (fiscalOrganisation.getStoreNumbers().contains(txtStoreNumber.getValue())) {
				if (!isShowing()) show();
				new MessageDialog(messages.storeNumberAlreadyExistsMessage(), Style.WARNING, txtStoreNumber).show();
				return false;
			}
		}

		if (txtStoreNumber.getValue().isEmpty()) {
			if (!isShowing()) show();
			new MessageDialog(messages.pleaseEnterStoreNumberMessage(), Style.WARNING, txtStoreNumber).show();
			return false;
		}
		if (txtStoreCaption.getValue().isEmpty()) {
			if (!isShowing()) show();
			new MessageDialog(messages.pleaseEnterStoreCaptionMessage(), Style.WARNING, txtStoreCaption).show();
			return false;
		}

		if (!chkNoStoreAddress.getValue()) {
			if (txtStoreCity.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterStoreCityMessage(), Style.WARNING, txtStoreCity).show();
				return false;
			}
			if (txtStoreStreet.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterStoreStreetMessage(), Style.WARNING, txtStoreStreet).show();
				return false;
			}
			if (txtStoreStreetNumber.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterStoreStreetNumberMessage(), Style.WARNING, txtStoreStreetNumber).show();
				return false;
			}
			if (txtStorePostalCode.getValue().isEmpty()) {
				if (!isShowing()) show();
				new MessageDialog(messages.pleaseEnterStorePostalCodeMessage(), Style.WARNING, txtStorePostalCode).show();
				return false;
			}
		}

		// Check number of terminals
		int terminalCount = 0;
		for (Terminal terminal : newTerminals)
			if (!Boolean.parseBoolean(terminal.getRequiresDelete()))
				terminalCount++;
		if (terminalCount == 0) {
			if (!isShowing()) show();
			new MessageDialog(messages.pleaseEnterAtLeastOneTerminalMessage(), Style.WARNING, btnAddTerminal).show();
			return false;
		}
		try {
			short lbCons = Short.parseShort(licenseDialog.txtLbConnections.getText());
			if (terminalCount > lbCons) {
				if (!isShowing()) show();
				new MessageDialog(messages.terminalLimitMessage(), Style.WARNING).show();
				return false;
			}
		} catch (NumberFormatException e) {
			// Do nothing
		}

		return true;
	}

	public Fiscal getFiscal() {

		Fiscal newFiscal = new Fiscal(); // TODO: maybe clone from fiscal

		if (fiscal == null && lstExistingOrganisations.getSelectedIndex() <= 0) {
			newFiscal.setVatNumber(txtVatNumber.getValue());
			newFiscal.setBusinessIdentificationNumber(txtCvrNumber.getValue());
			//newfiscal.setFiscalCountry(txtFiscalCountry.getValue());
			newFiscal.setFiscalCountry("[4] = Denmark"); // XXX Hardcoded
			newFiscal.setOrganisationCaption(txtOrganisationCaption.getValue());
			newFiscal.setFiscalYearStartMonth(Integer.toString(lstFiscalYearStartMonth.getSelectedIndex() + 1));
			newFiscal.setOrganisationAddress(new Address());
			//newfiscal.getOrganisationAddress().setCountryCode(txtOrganisationCountryCode.getValue());
			newFiscal.getOrganisationAddress().setCountryCode("DNK");
			newFiscal.getOrganisationAddress().setCity(txtOrganisationCity.getValue());
			newFiscal.getOrganisationAddress().setStreet(txtOrganisationStreet.getValue());
			newFiscal.getOrganisationAddress().setStreetNumber(txtOrganisationStreetNumber.getValue());
			newFiscal.getOrganisationAddress().setPostalCode(txtOrganisationPostalCode.getValue());
		} else if (fiscal == null) {
			FiscalOrganisation baseFiscal = fiscalOrganisations.get(lstExistingOrganisations.getSelectedIndex() - 1);
			newFiscal.setVatNumber(baseFiscal.getVatNumber());
			newFiscal.setBusinessIdentificationNumber(baseFiscal.getBusinessIdentificationNumber());
			newFiscal.setFiscalCountry(baseFiscal.getFiscalCountry());
			newFiscal.setOrganisationCaption(baseFiscal.getOrganisationCaption());
			newFiscal.setFiscalYearStartMonth(baseFiscal.getFiscalYearStartMonth());
			if (baseFiscal.getOrganisationAddress() != null) {
				newFiscal.setOrganisationAddress(new Address());
				newFiscal.getOrganisationAddress().setCountryCode(baseFiscal.getOrganisationAddress().getCountryCode());
				newFiscal.getOrganisationAddress().setCity(baseFiscal.getOrganisationAddress().getCity());
				newFiscal.getOrganisationAddress().setStreet(baseFiscal.getOrganisationAddress().getStreet());
				newFiscal.getOrganisationAddress().setStreetNumber(baseFiscal.getOrganisationAddress().getStreetNumber());
				newFiscal.getOrganisationAddress().setPostalCode(baseFiscal.getOrganisationAddress().getPostalCode());
			}
			newFiscal.setOrganisationId(baseFiscal.getOrganisationId());
		} else {
			Fiscal baseFiscal = fiscal;
			newFiscal.setVatNumber(baseFiscal.getVatNumber());
			newFiscal.setBusinessIdentificationNumber(baseFiscal.getBusinessIdentificationNumber());
			newFiscal.setFiscalCountry(baseFiscal.getFiscalCountry());
			newFiscal.setOrganisationCaption(baseFiscal.getOrganisationCaption());
			newFiscal.setFiscalYearStartMonth(baseFiscal.getFiscalYearStartMonth());
			if (baseFiscal.getOrganisationAddress() != null) {
				newFiscal.setOrganisationAddress(new Address());
				newFiscal.getOrganisationAddress().setCountryCode(baseFiscal.getOrganisationAddress().getCountryCode());
				newFiscal.getOrganisationAddress().setCity(baseFiscal.getOrganisationAddress().getCity());
				newFiscal.getOrganisationAddress().setStreet(baseFiscal.getOrganisationAddress().getStreet());
				newFiscal.getOrganisationAddress().setStreetNumber(baseFiscal.getOrganisationAddress().getStreetNumber());
				newFiscal.getOrganisationAddress().setPostalCode(baseFiscal.getOrganisationAddress().getPostalCode());
			}
			newFiscal.setOrganisationId(baseFiscal.getOrganisationId());
			newFiscal.setRequiresUpdateOrganisation(baseFiscal.getRequiresUpdateOrganisation());
		}

		newFiscal.setStoreNumber(txtStoreNumber.getValue());
		newFiscal.setStoreCaption(txtStoreCaption.getValue());
		if (chkNoStoreAddress.getValue()) {
			newFiscal.setStoreAddress(null);
		} else {
			newFiscal.setStoreAddress(new Address());
			//newfiscal.getStoreAddress().setCountryCode(txtStoreCountryCode.getValue());
			newFiscal.getStoreAddress().setCountryCode("DNK");
			newFiscal.getStoreAddress().setCity(txtStoreCity.getValue());
			newFiscal.getStoreAddress().setStreet(txtStoreStreet.getValue());
			newFiscal.getStoreAddress().setStreetNumber(txtStoreStreetNumber.getValue());
			newFiscal.getStoreAddress().setPostalCode(txtStorePostalCode.getValue());
		}

		newFiscal.setTerminals(newTerminals);

		// Copy rest fields (except terminals)
		if (fiscal != null) {
			//newFiscal.setOrganisationId(fiscal.getOrganisationId());
			newFiscal.setStoreId(fiscal.getStoreId());
			newFiscal.setApiKey(fiscal.getApiKey());
			newFiscal.setApiSecret(fiscal.getApiSecret());
			//newFiscal.setRequiresUpdateOrganisation(fiscal.getRequiresUpdateOrganisation());
			newFiscal.setRequiresUpdateStore(fiscal.getRequiresUpdateStore());
		}

//		if (newFiscal.getOrganisationId() != null)
//			newFiscal.setRequiresUpdateOrganisation(Boolean.TRUE.toString()); // XXX TODO check modifications
		if (newFiscal.getStoreId() != null)
			newFiscal.setRequiresUpdateStore(Boolean.TRUE.toString()); // XXX TODO check modifications

		return newFiscal;
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		if (fiscal == null && txtOrganisationCaption.getValue().isEmpty())
			txtOrganisationCaption.setValue(licenseDialog.txtClientName.getValue());
		if (fiscal == null && txtStoreCaption.getValue().isEmpty())
			txtStoreCaption.setValue(licenseDialog.txtClientName.getValue());
		if (txtVatNumber.isEnabled())
			txtVatNumber.setFocus(true);
		else
			txtStoreNumber.setFocus(true);
		this.center();
	}

	  /////////////////////
	 // Private classes //
	/////////////////////

	private class TerminalEditButton extends Button implements ClickHandler {
		Terminal terminal;

		public TerminalEditButton(Terminal terminal) {
			super("Edit..."); // TODO to constants
			this.terminal = terminal;
			setWidth("80px");
			addClickHandler(this);
			addStyleName("al-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {
			new TerminalDialog(terminal).show();
		}
	}

	private class TerminalDeleteButton extends Button implements ClickHandler {
		Terminal terminal;

		public TerminalDeleteButton(Terminal terminal) {
			super(Boolean.parseBoolean(terminal.getRequiresDelete()) ? "Undelete" : "Delete"); // TODO to constants
			this.terminal = terminal;
			setWidth("80px");
			addClickHandler(this);
			addStyleName("al-RowButton");
		}

		@Override
		public void onClick(ClickEvent event) {
			if (terminal.getTerminalId() != null) {
				if (Boolean.parseBoolean(terminal.getRequiresDelete())) {
					for (Terminal itTerminal : newTerminals) {
						if (itTerminal == terminal || Boolean.parseBoolean(itTerminal.getRequiresDelete()))
							continue;
						if (Objects.equals(terminal.getTerminalNumber(), itTerminal.getTerminalNumber())) {
							new MessageDialog(messages.terminalNumberAlreadyExistsMessage(), Style.WARNING).show();
							return;
						}
					}
					terminal.setRequiresDelete(null);
				} else {
					terminal.setRequiresDelete(Boolean.toString(true));
				}
			} else {
				newTerminals.remove(terminal);
			}
			refreshTerminalsTable();
		}
	}

	private class TerminalDialog extends DialogBox implements ClickHandler {

		  /////////////
		 // Members //
		/////////////

		private Terminal terminal;

		  //////////////////
		 // Constructors //
		//////////////////

		// create new terminal
		public TerminalDialog() {
			this.terminal = null;
			generateUI();
			this.setText("Terminal"); // TODO: to constants
		}

		// edit terminal
		public TerminalDialog(Terminal terminal) {
			this.terminal = terminal;
			generateUI();
			txtTerminalNumber.setText(terminal.getTerminalNumber());
			txtTerminalCaption.setText(terminal.getCaption());
			this.setText("Terminal"); // TODO: to constants
		}

		  ////////
		 // UI //
		////////

		private VerticalPanel mainPanel = new VerticalPanel();

		private EntitledDecoratorPanel dpnTerminal = new EntitledDecoratorPanel();
		private FlexTable tblTerminal = new FlexTable();
		private Label lblTerminalNumber = new Label("Number" + ":", false); // TODO: to constants
		private TextBox txtTerminalNumber = new TextBox();
		private Label lblTerminalCaption = new Label("Caption" + ":", false); // TODO: to constants
		private TextBox txtTerminalCaption = new TextBox();

		private HorizontalPanel pnlButtons = new HorizontalPanel();
		private Button btnOk = new Button(constants.okButton(), this);
		private Button btnCancel = new Button(constants.cancelButton(), this);

		private void generateUI() {

			// Assemble terminal panel
			txtTerminalNumber.setMaxLength(10);
			txtTerminalCaption.setMaxLength(50);
			int row = 0;
			tblTerminal.setWidget(row, 0, lblTerminalNumber);
			tblTerminal.setWidget(row++, 1, txtTerminalNumber);		
			tblTerminal.setWidget(row, 0, lblTerminalCaption);
			tblTerminal.setWidget(row++, 1, txtTerminalCaption);
			dpnTerminal.setWidget(tblTerminal);

			// Assemble button panel
			btnOk.setWidth("80px");
			btnCancel.setWidth("80px");
			pnlButtons.add(btnOk);
//			pnlButtons.setCellHorizontalAlignment(btnOk, HasHorizontalAlignment.ALIGN_RIGHT);
			pnlButtons.setCellWidth(btnOk, "100%");
			pnlButtons.setWidth("100%");
			pnlButtons.add(btnCancel);
//			pnlButtons.setCellHorizontalAlignment(btnCancel, HasHorizontalAlignment.ALIGN_RIGHT);
			pnlButtons.setSpacing(3);

			// Assemble main panel
			mainPanel.add(dpnTerminal);
			//mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
			mainPanel.add(pnlButtons);

			this.setWidget(mainPanel);
		}

		  ////////////////////
		 // Event handlers //
		////////////////////

		@Override
		public void onClick(ClickEvent event) {
			Object sender = event.getSource();

			if (sender == btnCancel) {

				this.hide();

			} else if (sender == btnOk) {

				// check parameters
				if (txtTerminalNumber.getValue().isEmpty()) {
					new MessageDialog(messages.pleaseEnterTerminalNumbereMessage(), Style.WARNING,
							txtTerminalNumber).show();
					return;
				}
				for (Terminal itTerminal : newTerminals) {
					if (itTerminal == terminal || Boolean.parseBoolean(itTerminal.getRequiresDelete()))
						continue;
					if (txtTerminalNumber.getValue().equals(itTerminal.getTerminalNumber())) {
						txtTerminalNumber.selectAll();
						new MessageDialog(messages.terminalNumberAlreadyExistsMessage(), Style.WARNING,
								txtTerminalNumber).show();
						return;
					}
				}

				if (terminal == null) {
					Terminal newTerminal = new Terminal();
					newTerminal.setTerminalNumber(txtTerminalNumber.getText());
					newTerminal.setCaption(txtTerminalCaption.getText());
					newTerminals.add(newTerminal);
				} else {
					terminal.setTerminalNumber(txtTerminalNumber.getText());
					terminal.setCaption(txtTerminalCaption.getText());
					if (terminal.getTerminalId() != null) {
						terminal.setRequiresUpdate(Boolean.toString(true));
					}
				}

				refreshTerminalsTable();

				this.hide();
			}

		}

		  //////////////////////
		 // Override methods //
		//////////////////////

		@Override
		public void show() {
			super.show();
			this.center();
			txtTerminalNumber.setFocus(true);
		}


	}

}
