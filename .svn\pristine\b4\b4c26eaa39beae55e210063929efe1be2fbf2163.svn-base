package eu.untill.license.server;

import org.junit.Test;
import static org.junit.Assert.*;

public class CommonTest {

	@Test
	public void fixFileName() throws Exception {
		assertEquals("_Client!", Common.fixFileName(":Client!"));
		assertEquals("_CON", Common.fixFileName("CON"));
		assertEquals("_COM1", Common.fixFileName("COM1"));
		assertNull(Common.fixFileName(null));
		assertEquals("Amsterdam_Red-light.jpg", Common.fixFileName("Amsterdam\\Red-light.jpg"));
		assertEquals("Belgi\u00eb_.txt", Common.fixFileName("Belgi\u00eb|.txt "));
	}

}
