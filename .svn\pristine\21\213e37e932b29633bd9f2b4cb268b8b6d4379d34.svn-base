package eu.untill.license.server;

import java.io.File;
import java.sql.Connection;
import java.util.Date;

import eu.untill.license.shared.DbLicense;

public interface InvoiceHelper {

	public static class Invoice {
		Date date;
		String emails;
		boolean positive;
		long number;
		String fullNumber;
		File file;
		public Invoice(Date date) {
			this.date = date;
		}
		public Date getDate() { return date; }
		public String getEmails() { return emails; }
		public boolean isPositive() { return positive; }
		public long getNumber() { return number; }
		public String getFullNumber() { return fullNumber; }
		public File getFile() { return file; }
	}

	Invoice generateInvoiceForLicense(Connection conn, Settings settings, Date date, long billId,
			DbLicense license);

	Invoice generateInvoiceForDealer(Connection conn, Settings settings, Date date, long billId,
			long dealerId, String reference);

}
