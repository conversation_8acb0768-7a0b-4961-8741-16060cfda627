/**
 * 
 */
package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.Date;
import java.util.EventListener;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.i18n.client.DateTimeFormat.PredefinedFormat;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.datepicker.client.CalendarUtil;
import com.google.gwt.user.datepicker.client.DateBox;
import com.google.gwt.user.datepicker.client.DatePicker;

import eu.untill.license.client.Common;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Style;
import eu.untill.license.shared.DbLicense;

/**
 * <AUTHOR>
 *
 */
public class ResendLicenseDialog extends DialogBox implements ValueChangeHandler<Boolean>, ClickHandler {

	public static interface DialogListener extends EventListener {
		void onOk(Date tempExpiredDate);
		void onCancel();
	}

	ArrayList<DialogListener> dialogListenerList = null;

	public void addDialogListener(DialogListener listener) {
		if (dialogListenerList == null)
			dialogListenerList = new ArrayList<DialogListener> ();
		dialogListenerList.add(listener);
	}

	public void removeDialogListener(DialogListener listener) {
		if (dialogListenerList != null)
			dialogListenerList.remove(listener);
	}

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		String okButton();
		String cancelButton();
		String resendLicenseDialogHeader();
		String fullLicenseFileLabel();
		String temporaryLicenseFileLabel();
		String temporaryEndDateLabel();
	}

	public static interface UlsMessages extends Messages {
		String incorrectTemporaryEndDate();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  /////////////
	 // Members //
	/////////////

	private DbLicense license;

	  //////////////////
	 // Constructors //
	//////////////////

	public ResendLicenseDialog(DbLicense license, DialogListener listener) {
		this.license = license;

		addDialogListener(listener);
		
		generateUI();
		if (license.tempExpiredDate != null) {
			txtTempExpiredDate.setValue(Common.toLocalDateOnly(license.tempExpiredDate));
		} else {
			Date startDate = Common.toLocalDateOnly(license.getStartDate());
			Date tempExpiredDate = new Date();
			CalendarUtil.resetTime(tempExpiredDate);
			if (tempExpiredDate.before(startDate))
				tempExpiredDate = startDate;
			CalendarUtil.addMonthsToDate(tempExpiredDate, 1);
			if (tempExpiredDate.after(Common.toLocalDateOnly(license.getExpiredDate())))
				tempExpiredDate = Common.toLocalDateOnly(license.getExpiredDate());
			txtTempExpiredDate.setValue(tempExpiredDate);
		}

		this.setText(constants.resendLicenseDialogHeader());
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnLicenseType = new EntitledDecoratorPanel();
	private FlexTable tblLicenseType = new FlexTable();
	private RadioButton rdbFullLicense = new RadioButton("type", constants.fullLicenseFileLabel());
	private RadioButton rdbTempLicense = new RadioButton("type", constants.temporaryLicenseFileLabel());
	private Label lblTempExpiredDate = new Label(constants.temporaryEndDateLabel() + ":", false);
	private DateBox txtTempExpiredDate = new DateBox(new DatePicker(), null,
			new DateBox.DefaultFormat(DateTimeFormat.getFormat(PredefinedFormat.DATE_SHORT)));

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		rdbFullLicense.setValue(true);
		txtTempExpiredDate.setEnabled(false);
		//rdbFullLicense.addValueChangeHandler(this);
		rdbTempLicense.addValueChangeHandler(this);
		
		// Assemble license type panel
		FlexCellFormatter tblLicenseTypeCF = tblLicenseType.getFlexCellFormatter();
		int row = 0;
		tblLicenseTypeCF.setColSpan(row, 0, 3);
		tblLicenseType.setWidget(row++, 0, rdbFullLicense);
		tblLicenseTypeCF.setColSpan(row, 0, 3);
		tblLicenseType.setWidget(row++, 0, rdbTempLicense);
		tblLicenseType.setText(row, 0, " ");
		tblLicenseTypeCF.setWidth(row, 0, "30px");
		tblLicenseType.setWidget(row, 1, lblTempExpiredDate);
		tblLicenseType.setWidget(row, 2, txtTempExpiredDate);
		dpnLicenseType.setWidget(tblLicenseType);

		// Assemble button panel
		btnOk.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.add(btnOk);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnLicenseType);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////


	@Override
	public void onValueChange(ValueChangeEvent<Boolean> event) {
		txtTempExpiredDate.setEnabled(rdbTempLicense.getValue());
	}

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			this.hide();

			if (dialogListenerList != null)
				for (DialogListener dialogListener : dialogListenerList) {
					dialogListener.onCancel();
				}

		} else if (sender == btnOk) {

			Date tempExpiredDate = null;
			if (rdbTempLicense.getValue()) {
				tempExpiredDate = Common.toUtcDateOnly(txtTempExpiredDate.getValue());
				if (tempExpiredDate == null
						|| tempExpiredDate.before(license.getStartDate())
						|| !license.isNeverExpired()
						&& tempExpiredDate.after(license.getExpiredDate()))
				{
					txtTempExpiredDate.setFocus(true);
					new MessageDialog(messages.incorrectTemporaryEndDate(), Style.WARNING).show();
					return;
				}
			}

			this.hide();

			if (dialogListenerList != null)
				for (DialogListener dialogListener : dialogListenerList) {
					dialogListener.onOk(tempExpiredDate);
				}
		}

	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		rdbFullLicense.setFocus(true);
	}

}
