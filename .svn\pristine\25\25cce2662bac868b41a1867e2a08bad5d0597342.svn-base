package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.List;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.i18n.client.NumberFormat;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.FlexTable.FlexCellFormatter;
import com.google.gwt.user.client.ui.HTMLTable.RowFormatter;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ScrollPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.LicenseService.AuthFailedException;
import eu.untill.license.client.LicenseService.CommonRpcResult;
import eu.untill.license.client.LicenseService.RrArticle;
import eu.untill.license.client.LicenseService.RrOrderItem;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.AddCreditDialog.AddCreditDialogListener;
import eu.untill.license.client.ui.AddHandlingFeeDialog.AddHandlingFeeDialogListener;
import eu.untill.license.client.ui.AddManualArticleDialog.AddManualArticleDialogListener;
import eu.untill.license.client.ui.MessageDialog.Style;

/**
 * <AUTHOR>
 *
 */
public class CreateManualInvoiceDialog extends DialogBox implements ClickHandler,
		AddCreditDialogListener, AddManualArticleDialogListener, AsyncCallback<CommonRpcResult>, AddHandlingFeeDialogListener
{

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Create manual invoice")
		String createManualInvoiceHeader();
		@DefaultStringValue("Price")
		String priceColumnHeader();
		@DefaultStringValue("Order")
		String orderLabel();

		@DefaultStringValue("Add credit")
		String addCreditButton();
		@DefaultStringValue("Add manual")
		String addManualArticleButton();
		@DefaultStringValue("Add handling fee")
		String addHandlingFeeArticleButton();
		@DefaultStringValue("Create")
		String createButton();
		String cancelButton();

		@DefaultStringValue("Client")
		String clientPanel();
//		String dealerLabel();
		String clientNameLabel();

		String quantityColumnHeader();
		String articleNumberColumnHeader();
		String articleNameColumnHeader();

		@DefaultStringValue("Credit")
		String creditArticleName();
	}

	public static interface UlsMessages extends Messages {
		@DefaultMessage("Credit invoice successfully created")
		String creditInvoiceSuccessfullyCreated();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  /////////////
	 // Members //
	/////////////

	private long dealerId;
	RrOrderItem d;
	private List<RrOrderItem> creditRecordList = new ArrayList<RrOrderItem>();
	private int oiCurRow;
//	private String currencyCode = null;
//	private float currencyRate = 1;
	private RrArticle creditArticle = null;
	private RrArticle handlingFeeArticle = null;

	  //////////////////
	 // Constructors //
	//////////////////

	public CreateManualInvoiceDialog(long dealerId) {
		this.dealerId = dealerId;
		// Generate base interface
		generateUI();

		this.setText(constants.createManualInvoiceHeader());

		requestCreditArticle();
		requestHandlingFeeArticle();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private EntitledDecoratorPanel dpnClient = new EntitledDecoratorPanel(constants.clientPanel());
	private FlexTable tblClient = new FlexTable();
//	private Label lblDealerName = new Label(constants.dealerLabel() + ":", false);
//	private TextBox txtDealerName = new TextBox();
	private Label lblClientName = new Label(constants.clientNameLabel() + ":", false);
	private TextBox txtClientName = new TextBox();

	private EntitledDecoratorPanel dpnOrder = new EntitledDecoratorPanel(constants.orderLabel());
	//private TextArea txtOrder = new TextArea();
	private FlexTable tblOrder = new FlexTable();
	private ScrollPanel scrOrder = new ScrollPanel(tblOrder);

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnAddCredit = new Button(constants.addCreditButton(), this);
	private Button btnAddManualArticle = new Button(constants.addManualArticleButton(), this);
	private Button btnAddHandlingFee = new Button(constants.addHandlingFeeArticleButton(), this);
	private Button btnCreate = new Button(constants.createButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		// Assemble client panel
		txtClientName.setVisibleLength(70);
//		txtDealerName.setReadOnly(true);
		int row = 0;
//		tblClient.setWidget(row, 0, lblDealerName);
//		tblClient.setWidget(row++, 1, txtDealerName);
		tblClient.setWidget(row, 0, lblClientName);
		tblClient.setWidget(row++, 1, txtClientName);
		dpnClient.setWidget(tblClient);

		// Create order table
		tblOrder.addStyleName("al-Table"); 
		tblOrder.getRowFormatter().addStyleName(0, "al-Header"); 
		FlexCellFormatter tableCF = tblOrder.getFlexCellFormatter();
		int col = 0;
		tableCF.setWidth(0, col, "24px");
		tblOrder.setText(0, col++, constants.quantityColumnHeader());
		tableCF.setWidth(0, col, "24px");
		tblOrder.setText(0, col++, constants.articleNumberColumnHeader());
		tableCF.setWidth(0, col, "400px");
		tblOrder.setText(0, col++, constants.articleNameColumnHeader());
		tableCF.setWidth(0, col, "60px");
		tblOrder.setText(0, col++, constants.priceColumnHeader());
//		tableCF.setWidth(0, col, "80px");
//		tblOrder.setText(0, col++, constants.addNegativeColumnHeader());

		oiCurRow = 1;

		// Assemble order panel with scroll
		scrOrder.setSize("520", "200px");
		dpnOrder.setWidget(scrOrder);

		// Assemble button panel
		btnAddCredit.setEnabled(false);
		btnAddCredit.setWidth("120px");
		btnAddManualArticle.setWidth("120px");
		btnAddHandlingFee.setEnabled(false);
		btnAddHandlingFee.setWidth("120px");
		btnCreate.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.setWidth("100%");
		pnlButtons.add(btnAddCredit);
		pnlButtons.setCellHorizontalAlignment(btnAddCredit, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.add(btnAddManualArticle);
		pnlButtons.setCellHorizontalAlignment(btnAddManualArticle, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.add(btnAddHandlingFee);
		pnlButtons.setCellHorizontalAlignment(btnAddHandlingFee, HasHorizontalAlignment.ALIGN_LEFT);
		pnlButtons.setCellWidth(btnAddHandlingFee, "100%");
		pnlButtons.add(btnCreate);
		pnlButtons.setCellHorizontalAlignment(btnCreate, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.add(btnCancel);
		pnlButtons.setCellHorizontalAlignment(btnCancel, HasHorizontalAlignment.ALIGN_RIGHT);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(dpnClient);
		mainPanel.add(dpnOrder);
		//mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnAddCredit) {

			new AddCreditDialog(this).show();

		} if (sender == btnAddManualArticle) {

			new AddManualArticleDialog(this).show();

		} if (sender == btnAddHandlingFee) {

			new AddHandlingFeeDialog(this).show();

		} else if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnCreate){
	
			// Call RPC LicenseService.createCreditInvoice
			UntillLicenseServer.getLicenseServiceAsync().createCreditInvoice(
					UntillLicenseServer.getAuthScheme(), dealerId, creditRecordList,
					txtClientName.getText(), this);

			WaitDialog.show();

		}
	}

	@Override
	public void onFailure(Throwable caught) {
		WaitDialog.hideAll();
		if (caught instanceof AuthFailedException) {
			this.hide();
			LoginPage.get().show();
		} else {
			ErrorDialog.show(caught);
		}
	}

	@Override
	public void onSuccess(CommonRpcResult result) {
		WaitDialog.hide();
		if (result.isSuccess()) {
			new MessageDialog(messages.creditInvoiceSuccessfullyCreated() +
					(result.getErrorMessage() == null ? "" : " (" + result.getErrorMessage() + ")"),
					Style.INFORMATION
			).show();
			this.hide();
		} else {
			new MessageDialog(result.getErrorMessage(), Style.WARNING).show();
		}
	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void requestCreditArticle() {
		// Call RPC
		UntillLicenseServer.getLicenseServiceAsync().getCreditArticle(UntillLicenseServer.getAuthScheme(),
				new AsyncCallback<RrArticle>() {
					@Override
					public void onSuccess(RrArticle article) {
						if (article != null) {
							creditArticle = article;
							btnAddCredit.setEnabled(true);
						} else {
							btnAddCredit.setTitle("Credit article is not found");
						}
							
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error getting Credit article", caught);
					}
				});
	}

	private void requestHandlingFeeArticle() {
		// Call RPC
		UntillLicenseServer.getLicenseServiceAsync().getHandlingFeeArticle(UntillLicenseServer.getAuthScheme(),
				new AsyncCallback<RrArticle>() {
					@Override
					public void onSuccess(RrArticle article) {
						if (article != null) {
							handlingFeeArticle = article;
							btnAddHandlingFee.setEnabled(true);
						} else {
							btnAddHandlingFee.setTitle("Handling Fee article is not found");
						}
							
					}
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show("Error getting Handling Fee article", caught);
					}
				});
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		btnAddManualArticle.setFocus(true);
	}

	@Override
	public void onAddCredit(double price, String description) {
		creditRecordList.add(new RrOrderItem(1, creditArticle.id, creditArticle.number, creditArticle.name,
				price, description));
		addOrderItemRow(creditRecordList.get(creditRecordList.size() - 1));
	}

	@Override
	public void onAddManualArticle(int quantity, long articleId, int articleNumber,
			String articleName, double price, String text) {
		creditRecordList.add(new RrOrderItem(quantity, articleId, articleNumber, articleName, 
				price, text));
		addOrderItemRow(creditRecordList.get(creditRecordList.size() - 1));
	};

	@Override
	public void onAddHandlingFee(double price, String description) {
		creditRecordList.add(new RrOrderItem(1, handlingFeeArticle.id, handlingFeeArticle.number, handlingFeeArticle.name,
				price, description));
		addOrderItemRow(creditRecordList.get(creditRecordList.size() - 1));
	}

	private void addOrderItemRow(RrOrderItem orderItem) {
		RowFormatter tblOrderRF = tblOrder.getRowFormatter();
		FlexCellFormatter tblOrderCF = tblOrder.getFlexCellFormatter();

		// create numberFormat
		NumberFormat numberFormat = NumberFormat.getFormat("######0.00");
		tblOrderRF.addStyleName(oiCurRow, "al-Row");

		// fill row
		int col = 0;
		tblOrderCF.setHorizontalAlignment(oiCurRow, col, HasHorizontalAlignment.ALIGN_CENTER);
		tblOrder.setText(oiCurRow, col++, Integer.toString(orderItem.quantity));
		tblOrderCF.setHorizontalAlignment(oiCurRow, col, HasHorizontalAlignment.ALIGN_CENTER);
		if (orderItem.articleId != 0) {
			tblOrder.setText(oiCurRow, col++, Integer.toString(orderItem.articleNumber));
			tblOrder.setText(oiCurRow, col++, orderItem.articleName);
		} else {
			tblOrder.setText(oiCurRow, col++, " ");
			tblOrder.setText(oiCurRow, col++, constants.creditArticleName());
		}
		tblOrderCF.setHorizontalAlignment(oiCurRow, col, HasHorizontalAlignment.ALIGN_RIGHT);
		tblOrder.setText(oiCurRow, col++, numberFormat.format(orderItem.price));

		if (orderItem.text != null && !orderItem.text.isEmpty()) {
			++oiCurRow;
			tblOrderRF.addStyleName(oiCurRow, "al-TextRow");
			tblOrderCF.setColSpan(oiCurRow, 0, tblOrder.getCellCount(0));
			tblOrder.setText(oiCurRow, 0, orderItem.text);
		}

		++oiCurRow;
	}

}
