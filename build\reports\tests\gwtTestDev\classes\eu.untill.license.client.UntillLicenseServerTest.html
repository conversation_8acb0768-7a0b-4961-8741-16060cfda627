<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class eu.untill.license.client.UntillLicenseServerTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class eu.untill.license.client.UntillLicenseServerTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/eu.untill.license.client.html">eu.untill.license.client</a> &gt; UntillLicenseServerTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">2</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">5.269s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Tests</a>
</li>
<li>
<a href="#">Standard output</a>
</li>
<li>
<a href="#">Standard error</a>
</li>
</ul>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testSimple</td>
<td class="success">0.021s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidLicenseHardCode</td>
<td class="success">5.248s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>16:08:45,097 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/C:/PRODUCTS/UntillLicenseServer/build/resources/test/logback-test.xml]
16:08:45,097 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Resource [logback-test.xml] occurs multiple times on the classpath.
16:08:45,097 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Resource [logback-test.xml] occurs at [file:/C:/PRODUCTS/UntillLicenseServer/test/logback-test.xml]
16:08:45,097 |-WARN in ch.qos.logback.classic.LoggerContext[default] - Resource [logback-test.xml] occurs at [file:/C:/PRODUCTS/UntillLicenseServer/build/resources/test/logback-test.xml]
16:08:45,137 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - debug attribute not set
16:08:45,137 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
16:08:45,142 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [STDOUT]
16:08:45,148 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
16:08:45,160 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
16:08:45,160 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [STDERR]
16:08:45,161 |-INFO in ch.qos.logback.core.joran.action.NestedComplexPropertyIA - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
16:08:45,161 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to INFO
16:08:45,161 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [STDOUT] to Logger[ROOT]
16:08:45,161 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [STDERR] to Logger[ROOT]
16:08:45,161 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - End of configuration.
16:08:45,162 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@6dab9b6d - Registering current configuration as safe fallback point

16:08:45.170 [Test worker] INFO  org.eclipse.jetty.util.log initialized : Logging initialized @961ms
16:08:45.221 [Test worker] INFO  org.eclipse.jetty.server.Server doStart : jetty-9.2.14.v20151106
16:08:45.310 [Test worker] WARN  o.e.j.a.AnnotationConfiguration configure : ServletContainerInitializers: detected. Class hierarchy: empty
16:08:45.409 [Test worker] INFO  o.e.j.server.handler.ContextHandler doStart : Started c.g.g.j.@a43ce46{/,file:/C:/PRODUCTS/UntillLicenseServer/www-test/,AVAILABLE}{C:\PRODUCTS\UntillLicenseServer\www-test}
16:08:45.415 [Test worker] INFO  o.e.jetty.server.ServerConnector doStart : Started ServerConnector@505d8eea{HTTP/1.1}{0.0.0.0:8661}
16:08:45.415 [Test worker] INFO  org.eclipse.jetty.server.Server doStart : Started @1208ms
16:08:49.569 [qtp48361312-37] DEBUG eu.untill.license.server.Common init : ENTRY
16:08:49.570 [qtp48361312-37] INFO  eu.untill.license.server.Common init : Starting unTill License Server: jetty/9.2.14.v20151106
16:08:49.894 [qtp48361312-37] DEBUG eu.untill.license.server.Common init : RETURN
16:08:49.908 [qtp48361312-37] DEBUG e.u.l.server.LicenseServiceImpl validLicenseHardCode : ENTRY (1234567890123456789012342)
16:08:49.908 [qtp48361312-37] DEBUG e.u.l.server.LicenseServiceImpl validLicenseHardCode : RETURN true
</pre>
</span>
</div>
<div class="tab">
<h2>Standard error</h2>
<span class="code">
<pre>16:08:49.687 [qtp48361312-37] ERROR eu.untill.license.server.Common fixArticlesNames : Error fixing articles names
org.firebirdsql.jdbc.FBSQLException: GDS Exception. 335544344. I/O error during &quot;CreateFile (open)&quot; operation for file &quot;C:\UNTILL\DB\LICENSES.FDB&quot;
Error while trying to open file
null
	at org.firebirdsql.jdbc.FBDataSource.getConnection(FBDataSource.java:120)
	at org.firebirdsql.jdbc.AbstractDriver.connect(AbstractDriver.java:136)
	at java.sql.DriverManager.getConnection(DriverManager.java:664)
	at java.sql.DriverManager.getConnection(DriverManager.java:208)
	at org.apache.commons.dbcp.DriverManagerConnectionFactory.createConnection(DriverManagerConnectionFactory.java:78)
	at org.apache.commons.dbcp.PoolableConnectionFactory.makeObject(PoolableConnectionFactory.java:582)
	at org.apache.commons.pool.impl.GenericObjectPool.borrowObject(GenericObjectPool.java:1188)
	at org.apache.commons.dbcp.PoolingDataSource.getConnection(PoolingDataSource.java:106)
	at eu.untill.license.server.Common.getConnection(Common.java:281)
	at eu.untill.license.server.Common.fixArticlesNames(Common.java:201)
	at eu.untill.license.server.Common.init(Common.java:128)
	at eu.untill.license.server.LicenseServiceImpl.init(LicenseServiceImpl.java:56)
	at javax.servlet.GenericServlet.init(GenericServlet.java:244)
	at com.google.gwt.user.server.rpc.RemoteServiceServlet.init(RemoteServiceServlet.java:168)
	at org.eclipse.jetty.servlet.ServletHolder.initServlet(ServletHolder.java:616)
	at org.eclipse.jetty.servlet.ServletHolder.getServlet(ServletHolder.java:472)
	at org.eclipse.jetty.servlet.ServletHolder.ensureInstance(ServletHolder.java:767)
	at org.eclipse.jetty.servlet.ServletHolder.prepare(ServletHolder.java:752)
	at org.eclipse.jetty.servlet.ServletHandler.doHandle(ServletHandler.java:582)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:143)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:577)
	at org.eclipse.jetty.server.session.SessionHandler.doHandle(SessionHandler.java:223)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1127)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:515)
	at org.eclipse.jetty.server.session.SessionHandler.doScope(SessionHandler.java:185)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1061)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:141)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:97)
	at org.eclipse.jetty.server.handler.RequestLogHandler.handle(RequestLogHandler.java:95)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:97)
	at org.eclipse.jetty.server.Server.handle(Server.java:499)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:311)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:257)
	at org.eclipse.jetty.io.AbstractConnection$2.run(AbstractConnection.java:544)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:635)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$3.run(QueuedThreadPool.java:555)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.firebirdsql.gds.GDSException: I/O error during &quot;CreateFile (open)&quot; operation for file &quot;C:\UNTILL\DB\LICENSES.FDB&quot;
Error while trying to open file
null
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.readStatusVector(AbstractJavaGDSImpl.java:2103)
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.receiveResponse(AbstractJavaGDSImpl.java:2053)
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.internalAttachDatabase(AbstractJavaGDSImpl.java:460)
	at org.firebirdsql.gds.impl.wire.AbstractJavaGDSImpl.iscAttachDatabase(AbstractJavaGDSImpl.java:410)
	at org.firebirdsql.jca.FBManagedConnection.&lt;init&gt;(FBManagedConnection.java:105)
	at org.firebirdsql.jca.FBManagedConnectionFactory.createManagedConnection(FBManagedConnectionFactory.java:509)
	at org.firebirdsql.jca.FBStandAloneConnectionManager.allocateConnection(FBStandAloneConnectionManager.java:65)
	at org.firebirdsql.jdbc.FBDataSource.getConnection(FBDataSource.java:118)
	... 36 common frames omitted
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at Aug 6, 2025, 4:08:50 PM</p>
</div>
</div>
</body>
</html>
