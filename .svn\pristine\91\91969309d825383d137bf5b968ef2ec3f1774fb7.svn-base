package eu.untill.license.server;

import static com.untill.su.api.ISuServer.DEPLOYED_PRODUCTS_PAR_NAME;
import static com.untill.su.api.ISuServer.GET_HOST_TASKS_REQUEST_V1;
import static com.untill.su.api.ISuServer.HOST_GUID_PAR_NAME;
import static com.untill.su.api.ISuServer.HOST_NAME_PAR_NAME;
import static com.untill.su.api.ISuServer.LICENSE_UID_PAR_NAME;
import static com.untill.su.api.ISuServer.PASSWORD_PAR_NAME;
import static com.untill.su.api.ISuServer.REGISTER_HOST_REQUEST_V1;
import static com.untill.su.api.ISuServer.RESULT_PAR_NAME;
import static com.untill.su.api.ISuServer.SET_SUB_TASK_RESULT_V1_REQUEST_V1;
import static com.untill.su.api.ISuServer.SET_SUB_TASK_STATUS_V1_REQUEST_V1;
import static com.untill.su.api.ISuServer.SHORT_SYS_INFO_PAR_NAME;
import static com.untill.su.api.ISuServer.STATUS_PAR_NAME;
import static com.untill.su.api.ISuServer.SUB_TASK_ID_PAR_NAME;
import static eu.untill.license.server.SuCommon.deployedProductsFromJson;
import static eu.untill.license.server.SuCommon.hostTasksToJson;
import static eu.untill.license.server.SuCommon.resultFromJson;
import static eu.untill.license.server.SuCommon.shortSysInfoFromJson;
import static eu.untill.license.server.SuCommon.statusFromInt;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.gson.JsonSyntaxException;
import com.untill.su.api.entity.HostTask;
import com.untill.su.api.exceptions.EAlreadyRegisteredException;
import com.untill.su.api.exceptions.EHostIsNotRegistered;
import com.untill.su.api.exceptions.EWrongLicenseUidOrPasswordException;

public class SuServlet extends HttpServlet {
	private static final long serialVersionUID = 1L;

	static final Logger LOG = LoggerFactory.getLogger(SuServlet.class);

	@Override
	protected void service(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String pathInfo = req.getPathInfo();
		try {
			if (pathInfo == null) {
				resp.setContentType("text/plain");
				resp.getWriter().write(String.format("Usage: %s?%s=...&%s=...&%s=...\n", REGISTER_HOST_REQUEST_V1,
						LICENSE_UID_PAR_NAME, PASSWORD_PAR_NAME, HOST_NAME_PAR_NAME));
				resp.getWriter().write(String.format("       %s?%s=...&%s=...&%s=...\n", GET_HOST_TASKS_REQUEST_V1,
						HOST_GUID_PAR_NAME, SHORT_SYS_INFO_PAR_NAME, DEPLOYED_PRODUCTS_PAR_NAME));
				resp.getWriter().write(String.format("       %s?%s=...&%s=...&%s=...\n", SET_SUB_TASK_STATUS_V1_REQUEST_V1,
						HOST_GUID_PAR_NAME, SUB_TASK_ID_PAR_NAME, STATUS_PAR_NAME));
				resp.getWriter().write(String.format("       %s?%s=...&%s=...&%s=...\n", SET_SUB_TASK_RESULT_V1_REQUEST_V1,
						HOST_GUID_PAR_NAME, SUB_TASK_ID_PAR_NAME, RESULT_PAR_NAME));
				resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
			} else if (pathInfo.equals(REGISTER_HOST_REQUEST_V1)) {
				String licenseUid = req.getParameter(LICENSE_UID_PAR_NAME);
				String password = req.getParameter(PASSWORD_PAR_NAME);
				String hostName = req.getParameter(HOST_NAME_PAR_NAME);
				String result = Common.getSuTaskHandler().registerHost(licenseUid, password, hostName);
				byte[] resultConent = result.getBytes(StandardCharsets.UTF_8);
				resp.setContentType("text/plain");
				resp.setContentLength(resultConent.length);
				ServletOutputStream os = resp.getOutputStream();
				os.write(resultConent);
				os.flush();
			} else if (pathInfo.equals(GET_HOST_TASKS_REQUEST_V1)) {
				String requestBody = IOUtils.toString(req.getReader());
				String hostGuid = req.getParameter(HOST_GUID_PAR_NAME);
				String shortSysInfo = req.getParameter(SHORT_SYS_INFO_PAR_NAME);
				String deployedProducts = req.getParameter(DEPLOYED_PRODUCTS_PAR_NAME);
				if (deployedProducts == null)
					deployedProducts = requestBody;
				List<HostTask> result = Common.getSuTaskHandler().getHostTasks(hostGuid,
						shortSysInfoFromJson(shortSysInfo), deployedProductsFromJson(deployedProducts));
				byte[] resultConent = hostTasksToJson(result).getBytes(StandardCharsets.UTF_8);
				resp.setContentType("application/json");
				resp.setContentLength(resultConent.length);
				ServletOutputStream os = resp.getOutputStream();
				os.write(resultConent);
				os.flush();
			} else if (pathInfo.equals(SET_SUB_TASK_STATUS_V1_REQUEST_V1)) {
				String requestBody = IOUtils.toString(req.getReader());
				String hostGuid = req.getParameter(HOST_GUID_PAR_NAME);
				String subTaskId = req.getParameter(SUB_TASK_ID_PAR_NAME);
				String status = req.getParameter(STATUS_PAR_NAME);
				if (status == null)
					status = requestBody;
				Common.getSuTaskHandler().setSubTaskStatus(hostGuid, Long.parseLong(subTaskId),
						statusFromInt(Integer.parseInt(status)));
			} else if (pathInfo.equals(SET_SUB_TASK_RESULT_V1_REQUEST_V1)) {
				String requestBody = IOUtils.toString(req.getReader());
				String hostGuid = req.getParameter(HOST_GUID_PAR_NAME);
				String subTaskId = req.getParameter(SUB_TASK_ID_PAR_NAME);
				String result = req.getParameter(RESULT_PAR_NAME);
				if (result == null)
					result = requestBody;
				Common.getSuTaskHandler().setSubTaskResult(hostGuid, Long.parseLong(subTaskId), resultFromJson(result));
			} else {
				LOG.warn("" + req.getRequestURL() + "?" + req.getQueryString());
				resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
			}
		} catch (EWrongLicenseUidOrPasswordException e) {
			LOG.warn("" + req.getRequestURL() + "?" + req.getQueryString(), e); // XXX password is logged
			resp.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
		} catch (EAlreadyRegisteredException e) {
			LOG.warn("" + req.getRequestURL() + "?" + req.getQueryString(), e); // XXX password is logged
			resp.setStatus(HttpServletResponse.SC_CONFLICT);
		} catch (EHostIsNotRegistered e) {
			LOG.warn("" + req.getRequestURL() + "?" + req.getQueryString(), e);
			resp.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
		} catch (IllegalArgumentException | JsonSyntaxException e) {
			LOG.warn("" + req.getRequestURL() + "?" + req.getQueryString(), e);
			resp.setStatus(HttpServletResponse.SC_BAD_REQUEST);
		} catch (Exception e) {
			LOG.error("" + req.getRequestURL() + "?" + req.getQueryString(), e);
			resp.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
		}
	}

}
