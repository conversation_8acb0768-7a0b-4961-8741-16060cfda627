package eu.untill.license.client;

import com.google.gwt.core.client.GWT;
import com.google.gwt.junit.client.GWTTestCase;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.rpc.ServiceDefTarget;

/**
 * GWT JUnit tests must extend GWTTestCase.
 */
public class UntillLicenseServerTest extends GWTTestCase {

	/**
	 * Must refer to a valid module that sources this class.
	 */
	public String getModuleName() {
		return "eu.untill.license.UntillLicenseServerJUnit";
	}

	/**
	 * Add as many tests as you like.
	 */
	public void testSimple() {
		assertTrue(true);
	}

	/**
	 * This test will send a request to the server using the validLicenseHardCode method in LicenseService and verify
	 * the response.
	 */
	public void testValidLicenseHardCode() {
		// Create the service that we will test.
		LicenseServiceAsync licenseService = GWT.create(LicenseService.class);
		ServiceDefTarget target = (ServiceDefTarget) licenseService;
		target.setServiceEntryPoint(GWT.getModuleBaseURL() + "untilllicenseserver/license");

		// Since RPC calls are asynchronous, we will need to wait for a response
		// after this test method returns. This line tells the test runner to wait
		// up to 10 seconds before timing out.
		delayTestFinish(10000);

		// Send a request to the server.
		licenseService.validLicenseHardCode("1234567890123456789012342", new AsyncCallback<Boolean>() {
			public void onFailure(Throwable caught) {
				// The request resulted in an unexpected error.
				fail("Request failure: " + caught.getMessage());
			}

			public void onSuccess(Boolean result) {
				// Verify that the response is correct.
				assertTrue(result);

				// Now that we have received a response, we need to tell the test runner
				// that the test is complete. You must call finishTest() after an
				// asynchronous test finishes successfully, or the test will time out.
				finishTest();
			}
		});
	}

}
