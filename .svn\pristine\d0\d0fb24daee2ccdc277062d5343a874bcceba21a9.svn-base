package eu.untill.license.server;

import static org.junit.Assert.assertEquals;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.untill.su.api.entity.DeployedProduct;
import com.untill.su.api.entity.HostTask;
import com.untill.su.api.entity.Status;

public class SuCommonTest {

	@Before
	public void setUp() throws Exception {
	}

	@After
	public void tearDown() throws Exception {
	}

	private static String deployedProductsJson = "[\n" + 
			"  {\n" + 
			"    \"productName\": \"Product number one\",\n" + 
			"    \"productVersion\": \"1.2.3\",\n" + 
			"    \"deploymentTime\": \"2010-01-02T13:00:00Z\"\n" + 
			"  },\n" + 
			"  {\n" + 
			"    \"productName\": \"Product number two\",\n" + 
			"    \"productVersion\": \"3.2.1\"\n" + 
			"  }\n" + 
			"]";

	@Test
	public void deployedProductsToJson() {
		List<DeployedProduct> deployedProducts = new ArrayList<>();
		DeployedProduct product1 = new DeployedProduct();
		product1.setProductName("Product number one");
		product1.setProductVersion("1.2.3");
		product1.setDeploymentTime(Instant.parse("2010-01-02T13:00:00Z"));
		DeployedProduct product2 = new DeployedProduct();
		product2.setProductName("Product number two");
		product2.setProductVersion("3.2.1");
		product2.setDeploymentTime(null);
		deployedProducts.add(product1);
		deployedProducts.add(product2);
		assertEquals(deployedProductsJson, SuCommon.deployedProductsToJson(deployedProducts));
	}

	@Test
	public void deployedProductsFromJson() {
		List<DeployedProduct> deployedProducts = SuCommon.deployedProductsFromJson(deployedProductsJson);
		DeployedProduct product1 = deployedProducts.get(0);
		assertEquals("Product number one", product1.getProductName());
		assertEquals("1.2.3", product1.getProductVersion());
		assertEquals(Instant.parse("2010-01-02T13:00:00Z"), product1.getDeploymentTime());
		DeployedProduct product2 = deployedProducts.get(1);
		assertEquals("Product number two", product2.getProductName());
		assertEquals("3.2.1", product2.getProductVersion());
		assertEquals(null, product2.getDeploymentTime());
	}

	private static String hostTasksJson = "[\n" + 
			"  {\n" + 
			"    \"subTaskId\": 123,\n" + 
			"    \"status\": 0,\n" + 
			"    \"action\": \"install\",\n" + 
			"    \"params\": {\n" + 
			"      \"param1\": \"value1\",\n" + 
			"      \"param2\": \"value2\"\n" + 
			"    }\n" + 
			"  },\n" + 
			"  {\n" + 
			"    \"subTaskId\": 321,\n" + 
			"    \"status\": 6,\n" + 
			"    \"action\": \"uninstall\"\n" + 
			"  }\n" + 
			"]";

	@Test
	public void hostTasksToJson() {
		List<HostTask> hostTasks = new ArrayList<>();
		HostTask task1 = new HostTask(123);
		task1.setStatus(Status.CREATED);
		task1.setAction("install");
		Map<String, String> params = new HashMap<>();
		params.put("param1", "value1");
		params.put("param2", "value2");
		task1.setParams(params);
		HostTask task2 = new HostTask(321);
		task2.setStatus(Status.FAILURE);
		task2.setAction("uninstall");
		task2.setParams(null);
		hostTasks.add(task1);
		hostTasks.add(task2);
		assertEquals(hostTasksJson, SuCommon.hostTasksToJson(hostTasks));
	}

}
