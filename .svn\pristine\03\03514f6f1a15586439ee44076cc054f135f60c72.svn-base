package eu.untill.license.shared;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

public class DbLicenseOnline implements IsSerializable {
	private long id;                       // BIGINT NOT NULL
	private long idLicenses;               // BIGINT NOT NULL
	private String partialActiveSignature; // VARCHAR(128)
	private String activeProductName;      // VARCHAR(16)
	private int activeProductVersion;      // INTEGER
	private String activeDatabaseInfo;     // VARCHAR(128)
	private Date activationTime;           // TIMESTAMP
	private int activationCount;           // INTEGER
	private Date activationPeriodBegin;    // TIMESTAMP
	private Date activationPeriodEnd;      // TIMESTAMP
	private int failureCount;              // INTEGER
	private Date lastFailureTime;          // TIMESTAMP
	private int lastFailureCount;          // INTEGER
	private String lastFailureField;       // VARCHAR(24)
	private String lastFailureData;        // VARCHAR(128)

	public long getId() {
		return id;
	}
	public void setId(long id) {
		this.id = id;
	}
	public long getIdLicenses() {
		return idLicenses;
	}
	public void setIdLicenses(long idLicenses) {
		this.idLicenses = idLicenses;
	}
	public String getPartialActiveSignature() {
		return partialActiveSignature;
	}
	public void setPartialActiveSignature(String activeSignature) {
		this.partialActiveSignature = activeSignature;
	}
	public String getActiveProductName() {
		return activeProductName;
	}
	public void setActiveProductName(String activeProductName) {
		this.activeProductName = activeProductName;
	}
	public int getActiveProductVersion() {
		return activeProductVersion;
	}
	public void setActiveProductVersion(int activeProductVersion) {
		this.activeProductVersion = activeProductVersion;
	}
	public String getActiveDatabaseInfo() {
		return activeDatabaseInfo;
	}
	public void setActiveDatabaseInfo(String activeDatabaseInfo) {
		this.activeDatabaseInfo = activeDatabaseInfo;
	}
	public Date getActivationTime() {
		return activationTime;
	}
	public void setActivationTime(Date activationTime) {
		this.activationTime = activationTime;
	}
	public int getActivationCount() {
		return activationCount;
	}
	public void setActivationCount(int activationCount) {
		this.activationCount = activationCount;
	}
	public Date getActivationPeriodBegin() {
		return activationPeriodBegin;
	}
	public void setActivationPeriodBegin(Date activationPeriodBegin) {
		this.activationPeriodBegin = activationPeriodBegin;
	}
	public Date getActivationPeriodEnd() {
		return activationPeriodEnd;
	}
	public void setActivationPeriodEnd(Date activationPeriodEnd) {
		this.activationPeriodEnd = activationPeriodEnd;
	}
	public int getFailureCount() {
		return failureCount;
	}
	public void setFailureCount(int failureCount) {
		this.failureCount = failureCount;
	}
	public Date getLastFailureTime() {
		return lastFailureTime;
	}
	public void setLastFailureTime(Date lastFailureTime) {
		this.lastFailureTime = lastFailureTime;
	}
	public int getLastFailureCount() {
		return lastFailureCount;
	}
	public void setLastFailureCount(int lastFailureCount) {
		this.lastFailureCount = lastFailureCount;
	}
	public String getLastFailureField() {
		return lastFailureField;
	}
	public void setLastFailureField(String lastFailureField) {
		this.lastFailureField = lastFailureField;
	}
	public String getLastFailureData() {
		return lastFailureData;
	}
	public void setLastFailureData(String lastFailureData) {
		this.lastFailureData = lastFailureData;
	}
}
