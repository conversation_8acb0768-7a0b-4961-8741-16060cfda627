<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">64</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">1</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">9</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">1.635s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">98%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Ignored tests</a>
</li>
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/eu.untill.license.server.LicenseHandlerTest.html">LicenseHandlerTest</a>.
<a href="classes/eu.untill.license.server.LicenseHandlerTest.html#testConfirmOrder">testConfirmOrder</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Ignored tests</h2>
<ul class="linkList">
<li>
<a href="classes/eu.untill.license.server.DeployerEngineHandlerTest.html">DeployerEngineHandlerTest</a>.
<a href="classes/eu.untill.license.server.DeployerEngineHandlerTest.html#getProductList">getProductList</a>
</li>
<li>
<a href="classes/eu.untill.license.server.LicenseHandlerTest.html">LicenseHandlerTest</a>.
<a href="classes/eu.untill.license.server.LicenseHandlerTest.html#testRequestLicense_definitive">testRequestLicense_definitive</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#createCredentions">createCredentions</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#createTerminal">createTerminal</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#createTwoOrganisations">createTwoOrganisations</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#getDistributors">getDistributors</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#getOrganizations">getOrganizations</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#getSession">getSession</a>
</li>
<li>
<a href="classes/eu.untill.license.server.RetailForceInv.html">RetailForceInv</a>.
<a href="classes/eu.untill.license.server.RetailForceInv.html#maxFieldSizes">maxFieldSizes</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="failures">
<a href="packages/eu.untill.license.server.html">eu.untill.license.server</a>
</td>
<td>63</td>
<td>1</td>
<td>9</td>
<td>1.633s</td>
<td class="failures">98%</td>
</tr>
<tr>
<td class="success">
<a href="packages/eu.untill.license.shared.html">eu.untill.license.shared</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.CommonTest.html">eu.untill.license.server.CommonTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.099s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="skipped">
<a href="classes/eu.untill.license.server.DeployerEngineHandlerTest.html">eu.untill.license.server.DeployerEngineHandlerTest</a>
</td>
<td>1</td>
<td>0</td>
<td>1</td>
<td>0s</td>
<td class="skipped">-</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.HwmInvoiceHandlerTest.html">eu.untill.license.server.HwmInvoiceHandlerTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.549s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/eu.untill.license.server.LicenseHandlerTest.html">eu.untill.license.server.LicenseHandlerTest</a>
</td>
<td>13</td>
<td>1</td>
<td>1</td>
<td>0.134s</td>
<td class="failures">91%</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.OnlineLicenseHandlerTest.html">eu.untill.license.server.OnlineLicenseHandlerTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.058s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="skipped">
<a href="classes/eu.untill.license.server.RetailForceInv.html">eu.untill.license.server.RetailForceInv</a>
</td>
<td>7</td>
<td>0</td>
<td>7</td>
<td>0s</td>
<td class="skipped">-</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.SuCommonTest.html">eu.untill.license.server.SuCommonTest</a>
</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0.008s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.TestDbTest.html">eu.untill.license.server.TestDbTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.017s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.UntillTpapiHelperImplTest.html">eu.untill.license.server.UntillTpapiHelperImplTest</a>
</td>
<td>23</td>
<td>0</td>
<td>0</td>
<td>0.031s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.server.XmlFoTest.html">eu.untill.license.server.XmlFoTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.737s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/eu.untill.license.shared.VersionTest.html">eu.untill.license.shared.VersionTest</a>
</td>
<td>1</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at Aug 6, 2025, 4:09:02 PM</p>
</div>
</div>
</body>
</html>
