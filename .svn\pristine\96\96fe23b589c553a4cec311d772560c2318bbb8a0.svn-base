/**
 *
 */
package eu.untill.license.client.ui;

import java.util.ArrayList;
import java.util.EventListener;
import java.util.List;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.Constants;
import com.google.gwt.i18n.client.Messages;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.DialogBox;
import com.google.gwt.user.client.ui.FlexTable;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;

import eu.untill.license.client.LicenseService.RrArticle;
import eu.untill.license.client.UntillLicenseServer;
import eu.untill.license.client.ui.MessageDialog.Style;

/**
 * <AUTHOR>
 *
 */
public class AddManualArticleDialog extends DialogBox implements ClickHandler {

	  ///////////
	 // Event //
	///////////

	public static interface AddManualArticleDialogListener extends EventListener {
		void onAddManualArticle(int quantity, long articleId, int articleNumber, 
				String articleName, double price, String text);
	}

	ArrayList<AddManualArticleDialogListener> addManualArticleDialogListenerList = null;

	public void addAddManualArticleDialogListener(AddManualArticleDialogListener listener) {
		if (addManualArticleDialogListenerList == null)
			addManualArticleDialogListenerList = new ArrayList<AddManualArticleDialogListener>();
		addManualArticleDialogListenerList.add(listener);
	}

	public void removeAddManualArticleDialogListener(AddManualArticleDialogListener listener) {
		if (addManualArticleDialogListenerList != null)
			addManualArticleDialogListenerList.remove(listener);
	}

	  ////////////////////////////
	 // Constants and messages //
	////////////////////////////

	public static interface UlsConstants extends Constants {
		@DefaultStringValue("Add manual article...")
		String addManualArticleHeader();
		@DefaultStringValue("Quantity")
		String articleQuantityLabel();
		@DefaultStringValue("Article")
		String articleLabel();
		@DefaultStringValue("Price")
		String articlePriceLabel();
		@DefaultStringValue("Text")
		String articleTextLabel();
		String okButton();
		String cancelButton();
	}

	public static interface UlsMessages extends Messages {
		String unknownNumberFormatOfQuantity();
		String invalidNumberOfQuantity();
		String unknownNumberFormatOfPrice();

		@DefaultMessage("Need to select article")
		String needToSelectArticle();
	}

	private UlsConstants constants = UntillLicenseServer.getUntillLicenseServerConstants();
	private UlsMessages messages = UntillLicenseServer.getUntillLicenseServerMessages();

	  /////////////
	 // Members //
	/////////////

	private List<RrArticle> articles;
	
	  //////////////////
	 // Constructors //
	//////////////////

	public AddManualArticleDialog(AddManualArticleDialogListener listener) {
		// Generate base interface
		generateUI();

		this.addAddManualArticleDialogListener(listener);

		this.setText(constants.addManualArticleHeader());
		requestManualArticles();
	}

	  ////////
	 // UI //
	////////

	private VerticalPanel mainPanel = new VerticalPanel();

	private FlexTable tblParameters = new FlexTable();
	private Label lblQuantity = new Label(constants.articleQuantityLabel() + ":", false);
	private TextBox txtQuantity = new TextBox();
	private Label lblArticle = new Label(constants.articleLabel() + ":", false);
	private ListBox lstArticles = new ListBox();
	private Label lblPrice = new Label(constants.articlePriceLabel() + ":", false);
	private TextBox txtPrice = new TextBox();
	private Label lblText = new Label(constants.articleTextLabel() + ":", false);
	private TextBox txtText = new TextBox();

	private HorizontalPanel pnlButtons = new HorizontalPanel();
	private Button btnOk = new Button(constants.okButton(), this);
	private Button btnCancel = new Button(constants.cancelButton(), this);

	private void generateUI() {

		// Assemble parameters table
		txtQuantity.setVisibleLength(20);
		txtPrice.setVisibleLength(20);
		lstArticles.setWidth("400px");
		txtText.setMaxLength(50);
		txtText.setVisibleLength(60);
		tblParameters.setWidget(0, 0, lblQuantity);
		tblParameters.setWidget(0, 1, txtQuantity);
		tblParameters.setWidget(1, 0, lblArticle);
		tblParameters.setWidget(1, 1, lstArticles);
		tblParameters.setWidget(2, 0, lblPrice);
		tblParameters.setWidget(2, 1, txtPrice);
		tblParameters.setWidget(3, 0, lblText);
		tblParameters.setWidget(3, 1, txtText);

		// Assemble button panel
		btnOk.setWidth("80px");
		btnCancel.setWidth("80px");
		pnlButtons.add(btnOk);
		pnlButtons.add(btnCancel);
		pnlButtons.setSpacing(3);

		// Assemble main panel
		mainPanel.add(tblParameters);
		mainPanel.setHorizontalAlignment(HasHorizontalAlignment.ALIGN_RIGHT);
		mainPanel.add(pnlButtons);

		this.setWidget(mainPanel);
	}

	  ////////////////////
	 // Event handlers //
	////////////////////

	@Override
	public void onClick(ClickEvent event) {
		Object sender = event.getSource();

		if (sender == btnCancel) {

			this.hide();

		} else if (sender == btnOk){

			// Fill and check entered data
			int quantity;
			try {
				quantity = Integer.valueOf(txtQuantity.getText());
			} catch (NumberFormatException e) {
				new MessageDialog(messages.unknownNumberFormatOfQuantity(), Style.WARNING, txtQuantity).show();
				return;
			}
			if (quantity < 1) {
				new MessageDialog(messages.invalidNumberOfQuantity(), Style.WARNING, txtQuantity).show();
				return;
			}

			if (lstArticles.getSelectedIndex() < 0) {
				new MessageDialog(messages.needToSelectArticle(), Style.WARNING, lstArticles).show();
				return;
			}
			RrArticle article = articles.get(Integer.parseInt(lstArticles.getValue(lstArticles.getSelectedIndex())));
			
			Double price;
			try {
				price = Double.valueOf(txtPrice.getText());
			} catch (NumberFormatException e) {
				new MessageDialog(messages.unknownNumberFormatOfPrice(), Style.WARNING, txtPrice).show();
				return;
			}

			if (addManualArticleDialogListenerList != null)
				for (AddManualArticleDialogListener addManualArticleDialogListener : addManualArticleDialogListenerList) {
					addManualArticleDialogListener.onAddManualArticle(quantity, article.id,
							article.number, article.name, price, txtText.getText());
				}

			this.hide();
		}

	}

	  /////////////////////
	 // Private methods //
	/////////////////////

	private void requestManualArticles() {
		btnOk.setEnabled(false);
		lstArticles.setEnabled(false);
		UntillLicenseServer.getLicenseServiceAsync().getManualArticles(UntillLicenseServer.getAuthScheme(),
				new AsyncCallback<List<RrArticle>>() {
					
					@Override
					public void onSuccess(List<RrArticle> result) {
						setManualArticles(result);
						btnOk.setEnabled(true);
						lstArticles.setEnabled(true);
					}
					
					@Override
					public void onFailure(Throwable caught) {
						ErrorDialog.show(caught);
						hide();
					}
				});
	}

	private void setManualArticles(List<RrArticle> articles) {
		this.articles = articles;

		if (articles == null) {
			lstArticles.clear();
		} else {
			lstArticles.clear();
			for (int i = 0; i < articles.size(); ++i) {
				RrArticle article = articles.get(i);
				lstArticles.addItem(String.valueOf(article.number) + ": " + article.name,
						Integer.toString(i));
			}
		}
	}

	  //////////////////////
	 // Override methods //
	//////////////////////

	@Override
	public void show() {
		super.show();
		this.center();
		txtPrice.setFocus(true);
	}

}
