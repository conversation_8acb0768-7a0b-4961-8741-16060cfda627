{"version": "2.0.0", "tasks": [{"label": "gradle: build", "type": "shell", "command": "./gradlew", "args": ["build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "gradle: clean", "type": "shell", "command": "./gradlew", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "gradle: compileJava", "type": "shell", "command": "./gradlew", "args": ["compileJava"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "gradle: test", "type": "shell", "command": "./gradlew", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}}, {"label": "gradle: gwtDev", "type": "shell", "command": "./gradlew", "args": ["gwtDev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gradle"], "options": {"cwd": "${workspaceFolder}"}, "isBackground": true}]}