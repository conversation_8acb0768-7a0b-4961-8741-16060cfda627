package eu.untill.license.shared;

import java.util.Date;

import com.google.gwt.user.client.rpc.IsSerializable;

public class SuHost implements IsSerializable {

	public static final long HOST_OFFLINE_TRESHOLD_MS = 2 * 60 * 1000;

	public long hostId;
	public long licenseId;
	public String name;
	public String guid;
	public Date registrationTime;
	public Date heartbeat;
	public String shortSysInfo;
	public String deployedProducts;

	// auxiliary fields
	public SuLicense license;
	public long offlineDuration;

	@Override
	public String toString() {
		return "[" + getClass().getSimpleName() + ": " + Long.toString(hostId) + ", \"" + name + "\"]";
	}
}
