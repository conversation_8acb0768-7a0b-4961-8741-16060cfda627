<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<target>System.out</target>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>DENY</onMatch>
			<onMismatch>ACCEPT</onMismatch>
		</filter>
		<encoder>
			<pattern>%date{HH:mm:ss.SSS} [%thread] %-5level %logger{36} %method %mdc: %message%n</pattern>
		</encoder>
	</appender>
	<appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
		<target>System.err</target>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<encoder>
			<pattern>%date{HH:mm:ss.SSS} [%thread] %-5level %logger{36} %method %mdc: %message%n</pattern>
		</encoder>
	</appender>

	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="STDERR" />
	</root>

</configuration>
