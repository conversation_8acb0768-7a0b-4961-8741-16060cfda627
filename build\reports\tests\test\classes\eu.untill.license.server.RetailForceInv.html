<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class eu.untill.license.server.RetailForceInv</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class eu.untill.license.server.RetailForceInv</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/eu.untill.license.server.html">eu.untill.license.server</a> &gt; RetailForceInv</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">7</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">7</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox skipped" id="successRate">
<div class="percent">-</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Ignored tests</a>
</li>
<li>
<a href="#">Tests</a>
</li>
</ul>
<div class="tab">
<h2>Ignored tests</h2>
<div class="test">
<a name="createCredentions"></a>
<h3 class="skipped">createCredentions</h3>
</div>
<div class="test">
<a name="createTerminal"></a>
<h3 class="skipped">createTerminal</h3>
</div>
<div class="test">
<a name="createTwoOrganisations"></a>
<h3 class="skipped">createTwoOrganisations</h3>
</div>
<div class="test">
<a name="getDistributors"></a>
<h3 class="skipped">getDistributors</h3>
</div>
<div class="test">
<a name="getOrganizations"></a>
<h3 class="skipped">getOrganizations</h3>
</div>
<div class="test">
<a name="getSession"></a>
<h3 class="skipped">getSession</h3>
</div>
<div class="test">
<a name="maxFieldSizes"></a>
<h3 class="skipped">maxFieldSizes</h3>
</div>
</div>
<div class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="skipped">createCredentions</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="skipped">createTerminal</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="skipped">createTwoOrganisations</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="skipped">getDistributors</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="skipped">getOrganizations</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="skipped">getSession</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="skipped">maxFieldSizes</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at Aug 6, 2025, 4:09:02 PM</p>
</div>
</div>
</body>
</html>
