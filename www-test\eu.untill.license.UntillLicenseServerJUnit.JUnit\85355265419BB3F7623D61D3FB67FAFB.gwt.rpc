@FinalFields, false
com.google.gwt.core.shared.SerializableThrowable, false, false, true, true, com.google.gwt.core.shared.SerializableThrowable/2791287161, 2791287161
com.google.gwt.event.shared.UmbrellaException, false, false, true, true, com.google.gwt.event.shared.UmbrellaException/3104463596, 3104463596
com.google.gwt.http.client.RequestException, false, false, true, true, com.google.gwt.http.client.RequestException/190587325, 190587325
com.google.gwt.json.client.JSONException, false, false, true, true, com.google.gwt.json.client.JSONException/2941795468, 2941795468
com.google.gwt.jsonp.client.TimeoutException, false, false, true, true, com.google.gwt.jsonp.client.TimeoutException/1112787596, 1112787596
com.google.gwt.junit.client.TimeoutException, false, false, true, true, com.google.gwt.junit.client.TimeoutException/1599913304, 1599913304
com.google.gwt.logging.shared.RemoteLoggingService, false, false, false, false, _, 2602557223
com.google.gwt.user.client.rpc.IncompatibleRemoteServiceException, true, true, true, true, com.google.gwt.user.client.rpc.IncompatibleRemoteServiceException/3936916533, 3936916533
com.google.gwt.user.client.rpc.InvocationException, false, false, true, false, com.google.gwt.user.client.rpc.InvocationException/1510557512, 1510557512
com.google.gwt.user.client.rpc.RpcTokenException, true, true, true, true, com.google.gwt.user.client.rpc.RpcTokenException/2345075298, 2345075298
com.google.gwt.user.client.rpc.SerializableException, false, false, true, true, com.google.gwt.user.client.rpc.SerializableException/3047383460, 3047383460
com.google.gwt.user.client.rpc.SerializationException, false, false, true, true, com.google.gwt.user.client.rpc.SerializationException/2836333220, 2836333220
com.google.gwt.user.client.rpc.SerializedTypeViolationException, false, false, true, true, com.google.gwt.user.client.rpc.SerializedTypeViolationException/914601580, 914601580
com.google.gwt.user.client.rpc.ServiceDefTarget$NoServiceEntryPointSpecifiedException, false, false, true, true, com.google.gwt.user.client.rpc.ServiceDefTarget$NoServiceEntryPointSpecifiedException/**********, **********
com.google.gwt.user.client.rpc.XsrfToken, false, false, true, true, com.google.gwt.user.client.rpc.XsrfToken/**********, **********
com.google.gwt.useragent.client.UserAgentAsserter$UserAgentAssertionError, false, false, true, true, com.google.gwt.useragent.client.UserAgentAsserter$UserAgentAssertionError/**********, **********
com.google.web.bindery.event.shared.UmbrellaException, false, false, true, true, com.google.web.bindery.event.shared.UmbrellaException/**********, **********
eu.untill.license.client.LicenseService$AuthFailedException, false, false, true, true, eu.untill.license.client.LicenseService$AuthFailedException/216725473, 216725473
eu.untill.license.client.LicenseService$DbErrorException, false, false, true, true, eu.untill.license.client.LicenseService$DbErrorException/172546405, 172546405
eu.untill.license.client.LicenseService$InvalidArgumentException, false, false, true, true, eu.untill.license.client.LicenseService$InvalidArgumentException/**********, **********
eu.untill.license.client.LicenseService$LicenseServiceException, false, false, true, false, eu.untill.license.client.LicenseService$LicenseServiceException/**********, **********
eu.untill.license.client.LicenseService$ServerErrorException, false, false, true, true, eu.untill.license.client.LicenseService$ServerErrorException/683076255, 683076255
java.io.IOException, false, false, true, true, java.io.IOException/**********, **********
java.io.UnsupportedEncodingException, false, false, true, true, java.io.UnsupportedEncodingException/**********, **********
java.lang.ArithmeticException, false, false, true, true, java.lang.ArithmeticException/**********, **********
java.lang.ArrayIndexOutOfBoundsException, false, false, true, true, java.lang.ArrayIndexOutOfBoundsException/600550433, 600550433
java.lang.ArrayStoreException, false, false, true, true, java.lang.ArrayStoreException/**********, **********
java.lang.AssertionError, false, false, true, true, java.lang.AssertionError/**********, **********
java.lang.Boolean, false, false, true, true, java.lang.Boolean/476441737, 476441737
java.lang.ClassCastException, false, false, true, true, java.lang.ClassCastException/702295179, 702295179
java.lang.CloneNotSupportedException, false, false, true, true, java.lang.CloneNotSupportedException/1114494199, 1114494199
java.lang.Error, false, false, true, true, java.lang.Error/1331973429, 1331973429
java.lang.Exception, true, false, true, true, java.lang.Exception/1920171873, 1920171873
java.lang.IllegalArgumentException, false, false, true, true, java.lang.IllegalArgumentException/1755012560, 1755012560
java.lang.IllegalStateException, false, false, true, true, java.lang.IllegalStateException/1972187323, 1972187323
java.lang.IndexOutOfBoundsException, false, false, true, true, java.lang.IndexOutOfBoundsException/2489527753, 2489527753
java.lang.InterruptedException, false, false, true, true, java.lang.InterruptedException/3896610207, 3896610207
java.lang.NegativeArraySizeException, false, false, true, true, java.lang.NegativeArraySizeException/3846860241, 3846860241
java.lang.NoSuchMethodException, false, false, true, true, java.lang.NoSuchMethodException/260969707, 260969707
java.lang.NullPointerException, false, false, true, true, java.lang.NullPointerException/3618884511, 3618884511
java.lang.NumberFormatException, false, false, true, true, java.lang.NumberFormatException/3305228476, 3305228476
java.lang.RuntimeException, true, false, true, true, java.lang.RuntimeException/515124647, 515124647
java.lang.SecurityException, false, false, true, true, java.lang.SecurityException/2381737870, 2381737870
java.lang.StackTraceElement, false, false, true, true, java.lang.StackTraceElement/455763907, 455763907
[Ljava.lang.StackTraceElement;, false, false, true, true, [Ljava.lang.StackTraceElement;/3867167983, 3867167983
java.lang.String, true, true, true, true, java.lang.String/2004016611, 2004016611
java.lang.StringIndexOutOfBoundsException, false, false, true, true, java.lang.StringIndexOutOfBoundsException/500777603, 500777603
java.lang.Throwable, true, false, true, true, java.lang.Throwable/2953622131, 2953622131
java.lang.UnsupportedOperationException, false, false, true, true, java.lang.UnsupportedOperationException/3744010015, 3744010015
java.lang.annotation.AnnotationFormatError, false, false, true, true, java.lang.annotation.AnnotationFormatError/2257184627, 2257184627
java.lang.annotation.AnnotationTypeMismatchException, false, false, true, true, java.lang.annotation.AnnotationTypeMismatchException/976205828, 976205828
java.security.DigestException, false, false, true, true, java.security.DigestException/629316798, 629316798
java.security.GeneralSecurityException, false, false, true, true, java.security.GeneralSecurityException/2669239907, 2669239907
java.security.NoSuchAlgorithmException, false, false, true, true, java.security.NoSuchAlgorithmException/2892037213, 2892037213
java.util.Collections$EmptySet, false, false, true, true, java.util.Collections$EmptySet/3523698179, 3523698179
java.util.ConcurrentModificationException, false, false, true, true, java.util.ConcurrentModificationException/2717383897, 2717383897
java.util.EmptyStackException, false, false, true, true, java.util.EmptyStackException/89438517, 89438517
java.util.HashSet, false, false, true, true, java.util.HashSet/3273092938, 3273092938
java.util.LinkedHashSet, false, false, true, true, java.util.LinkedHashSet/95640124, 95640124
java.util.NoSuchElementException, false, false, true, true, java.util.NoSuchElementException/1559248883, 1559248883
java.util.TooManyListenersException, false, false, true, true, java.util.TooManyListenersException/2023078032, 2023078032
java.util.TreeMap, false, false, true, true, java.util.TreeMap/1493889780, 1493889780
java.util.TreeSet, false, false, true, true, java.util.TreeSet/4043497002, 4043497002
java.util.concurrent.CancellationException, false, false, true, true, java.util.concurrent.CancellationException/1029019779, 1029019779
java.util.concurrent.ExecutionException, false, false, true, true, java.util.concurrent.ExecutionException/1787452083, 1787452083
java.util.concurrent.RejectedExecutionException, false, false, true, true, java.util.concurrent.RejectedExecutionException/680785068, 680785068
java.util.concurrent.TimeoutException, false, false, true, true, java.util.concurrent.TimeoutException/3757923520, 3757923520
java.util.logging.Level, false, false, true, true, java.util.logging.Level/2839552483, 2839552483
java.util.logging.LogRecord, false, false, true, true, java.util.logging.LogRecord/2492345967, 2492345967
javax.validation.ConstraintDeclarationException, false, false, true, true, javax.validation.ConstraintDeclarationException/3610544007, 3610544007
javax.validation.ConstraintDefinitionException, false, false, true, true, javax.validation.ConstraintDefinitionException/3732439488, 3732439488
javax.validation.ConstraintViolationException, false, false, true, true, javax.validation.ConstraintViolationException/1185386591, 1185386591
javax.validation.GroupDefinitionException, false, false, true, true, javax.validation.GroupDefinitionException/4024780846, 4024780846
javax.validation.UnexpectedTypeException, false, false, true, true, javax.validation.UnexpectedTypeException/593026390, 593026390
javax.validation.ValidationException, false, false, true, true, javax.validation.ValidationException/1570221831, 1570221831
junit.framework.AssertionFailedError, false, false, true, true, junit.framework.AssertionFailedError/3756236039, 3756236039
